package com.yuelan.plugins.redisson.client;


import com.fasterxml.jackson.core.type.TypeReference;
import com.yuelan.plugins.redisson.key.ICacheKey;
import com.yuelan.plugins.redisson.util.RedisUtils;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.codec.TypedJsonJacksonCodec;

public class RedissonMapClient {

    private RedissonClient redissonClient;

    public RedissonMapClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 获取RMap
     */
    public <K, V> RMap<K, V> getMap(ICacheKey iCacheKey, Codec codec) {
        String cacheKey = RedisUtils.getCacheKey(iCacheKey);
        return redissonClient.getMap(cacheKey, codec);
    }

    /**
     * 获取RMap
     */
    public RMap<String, String> getMap(ICacheKey iCacheKey) {
        return getMap(iCacheKey, "");
    }


    /**
     * 获取RMap
     */
    public RMap<String, String> getMap(ICacheKey iCacheKey, String key) {
        return getMap(iCacheKey, key, String.class, String.class);
    }

    /**
     * 获取RMap
     */
    public <V> RMap<String, V> getMap(ICacheKey iCacheKey, Class<?> mapValueClass) {
        return getMap(iCacheKey, "", mapValueClass);
    }

    /**
     * 获取RMap
     */
    public <V> RMap<String, V> getMap(ICacheKey iCacheKey, String key, Class<?> mapValueClass) {
        return getMap(iCacheKey, key, String.class, mapValueClass);
    }

    /**
     * 获取RMap
     */
    public <K, V> RMap<K, V> getMap(ICacheKey iCacheKey, Class<?> mapKeyClass, Class<?> mapValueClass) {
        return getMap(iCacheKey, "", mapKeyClass, mapValueClass);
    }

    /**
     * 获取RMap
     */
    public <K, V> RMap<K, V> getMap(ICacheKey iCacheKey, String key, Class<?> mapKeyClass, Class<?> mapValueClass) {
        String cacheKey = RedisUtils.getCacheKey(iCacheKey, key);
        Codec codec = new TypedJsonJacksonCodec(mapKeyClass, mapValueClass);
        return redissonClient.getMap(cacheKey, codec);
    }

    /**
     * 获取RMap
     */
    public <K, V> RMap<K, V> getMap(ICacheKey iCacheKey, TypeReference<?> mapKeyTypeReference, TypeReference<?> mapValueTypeReference) {
        return getMap(iCacheKey, "", mapKeyTypeReference, mapValueTypeReference);
    }

    /**
     * 获取RMap
     */
    public <K, V> RMap<K, V> getMap(ICacheKey iCacheKey, String key, TypeReference<?> mapKeyTypeReference, TypeReference<?> mapValueTypeReference) {
        String cacheKey = RedisUtils.getCacheKey(iCacheKey, key);
        Codec codec = new TypedJsonJacksonCodec(mapKeyTypeReference, mapValueTypeReference);
        return redissonClient.getMap(cacheKey, codec);
    }
}
