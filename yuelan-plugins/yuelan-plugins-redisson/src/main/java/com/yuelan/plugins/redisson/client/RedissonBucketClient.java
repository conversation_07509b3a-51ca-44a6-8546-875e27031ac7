package com.yuelan.plugins.redisson.client;


import com.fasterxml.jackson.core.type.TypeReference;
import com.yuelan.plugins.redisson.key.ICacheKey;
import com.yuelan.plugins.redisson.util.RedisUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.codec.TypedJsonJacksonCodec;

public class RedissonBucketClient {

    private RedissonClient redissonClient;

    public RedissonBucketClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 获取Bucket
     */
    public <V> RBucket<V> getBucket(ICacheKey iCacheKey, Codec codec) {
        String cacheKey = RedisUtils.getCacheKey(iCacheKey);
        return redissonClient.getBucket(cacheKey, codec);
    }

    /**
     * 获取Bucket
     */
    public <V> RBucket<V> getBucket(ICacheKey iCacheKey, Class<V> valueClass) {
        return getBucket(iCacheKey, null, valueClass);
    }

    /**
     * 获取Bucket
     */
    public <V> RBucket<V> getBucket(ICacheKey iCacheKey, String key, Class<V> valueClass) {
        String cacheKey = RedisUtils.getCacheKey(iCacheKey, key);
        Codec codec = new TypedJsonJacksonCodec(valueClass);
        return redissonClient.getBucket(cacheKey, codec);
    }

    /**
     * 获取Bucket
     */
    public RBucket<?> getBucket(ICacheKey iCacheKey, TypeReference<?> valueTypeReference) {
        return getBucket(iCacheKey, null, valueTypeReference);
    }

    /**
     * 获取Bucket
     */
    public RBucket<?> getBucket(ICacheKey iCacheKey, String key, TypeReference<?> valueTypeReference) {
        String cacheKey = RedisUtils.getCacheKey(iCacheKey, key);
        Codec codec = new TypedJsonJacksonCodec(valueTypeReference);
        return redissonClient.getBucket(cacheKey, codec);
    }

    /**
     * 获取Bucket并赋值
     */
    public <V> RBucket<V> getAndSet(ICacheKey iCacheKey, V value, Class<V> valueClass) {
        return getAndSet(iCacheKey, null, value, valueClass);
    }

    /**
     * 获取Bucket并赋值
     */
    public <V> RBucket<V> getAndSet(ICacheKey iCacheKey, String key, V value, Class<V> valueClass) {
        RBucket<V> bucket = getBucket(iCacheKey, key, valueClass);
        bucket.set(value);
        return bucket;
    }

    /**
     * 获取Bucket并赋值设置过期时间
     */
    public <V> RBucket<V> setAndExpire(ICacheKey iCacheKey, V value, Class<V> valueClass) {
        return setAndExpire(iCacheKey, null, value, valueClass);
    }

    /**
     * 获取Bucket并赋值设置过期时间
     */
    public <V> RBucket<V> setAndExpire(ICacheKey iCacheKey, String key, V value, Class<V> valueClass) {
        RBucket<V> bucket = getBucket(iCacheKey, key, valueClass);
        bucket.set(value, iCacheKey.getExpire(), iCacheKey.getUnit());
        return bucket;
    }

    /**
     * 设置过期时间
     */
    public <V> boolean expire(ICacheKey iCacheKey, Class<V> valueClass) {
        return expire(iCacheKey, null, valueClass);
    }

    /**
     * 设置过期时间
     */
    public <V> boolean expire(ICacheKey iCacheKey, String key, Class<V> valueClass) {
        RBucket<V> bucket = getBucket(iCacheKey, key, valueClass);
        return RedisUtils.expire(bucket, iCacheKey);
    }

    /**
     * 设置过期时间
     */
    public <V> boolean expire(ICacheKey iCacheKey, TypeReference<?> valueTypeReference) {
        return expire(iCacheKey, null, valueTypeReference);
    }

    /**
     * 设置过期时间
     */
    public boolean expire(ICacheKey iCacheKey, String key, TypeReference<?> valueTypeReference) {
        RBucket<?> bucket = getBucket(iCacheKey, key, valueTypeReference);
        return RedisUtils.expire(bucket, iCacheKey);
    }

    /**
     * 删除缓存
     */
    public <V> boolean delete(ICacheKey iCacheKey, Class<V> valueClass) {
        return delete(iCacheKey, null, valueClass);
    }

    /**
     * 删除缓存
     */
    public <V> boolean delete(ICacheKey iCacheKey, String key, Class<V> valueClass) {
        RBucket<V> bucket = getBucket(iCacheKey, key, valueClass);
        return bucket.delete();
    }

    /**
     * 删除缓存
     */
    public <V> boolean delete(ICacheKey iCacheKey, TypeReference<?> valueTypeReference) {
        return delete(iCacheKey, null, valueTypeReference);
    }

    /**
     * 删除缓存
     */
    public <V> boolean delete(ICacheKey iCacheKey, String key, TypeReference<?> valueTypeReference) {
        RBucket<?> bucket = getBucket(iCacheKey, key, valueTypeReference);
        return bucket.delete();
    }
}
