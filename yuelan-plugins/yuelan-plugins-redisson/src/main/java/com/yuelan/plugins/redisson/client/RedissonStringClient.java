package com.yuelan.plugins.redisson.client;


import com.yuelan.plugins.redisson.key.ICacheKey;
import com.yuelan.plugins.redisson.util.RedisUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;

public class RedissonStringClient {

    private RedissonClient redissonClient;

    public RedissonStringClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }


    /**
     * 获取Bucket
     */
    public RBucket<String> getBucket(ICacheKey iCacheKey, String... key) {
        String cacheKey = RedisUtils.getCacheKey(iCacheKey, key);
        return redissonClient.getBucket(cacheKey, new StringCodec());
    }

    /**
     * 获取Bucket并赋值
     */
    public RBucket<String> getAndSet(ICacheKey iCacheKey, String value) {
        return getAndSet(iCacheKey, null, value);
    }

    /**
     * 获取Bucket并赋值
     */
    public RBucket<String> getAndSet(ICache<PERSON><PERSON> iCacheKey, String key, String value) {
        RBucket<String> bucket = getBucket(iCacheKey, key);
        bucket.set(value);
        return bucket;
    }

    /**
     * 获取Bucket并赋值设置过期时间
     */
    public RBucket<String> setAndExpire(ICacheKey iCacheKey, String value) {
        return setAndExpire(iCacheKey, null, value);
    }

    /**
     * 获取Bucket并赋值设置过期时间
     */
    public RBucket<String> setAndExpire(ICacheKey iCacheKey, String key, String value) {
        RBucket<String> bucket = getBucket(iCacheKey, key);
        bucket.set(value, iCacheKey.getExpire(), iCacheKey.getUnit());
        return bucket;
    }

    /**
     * 设置过期时间
     */
    public boolean expire(ICacheKey iCacheKey, String... key) {
        RBucket<String> bucket = getBucket(iCacheKey, key);
        return RedisUtils.expire(bucket, iCacheKey);
    }

    /**
     * 删除缓存
     */
    public boolean delete(ICacheKey iCacheKey, String... key) {
        RBucket<String> bucket = getBucket(iCacheKey, key);
        return bucket.delete();
    }


}
