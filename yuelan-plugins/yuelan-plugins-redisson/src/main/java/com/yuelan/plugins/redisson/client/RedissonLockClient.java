package com.yuelan.plugins.redisson.client;


import com.yuelan.plugins.redisson.key.ILockKey;
import com.yuelan.plugins.redisson.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RedissonClient;

@Slf4j
public class RedissonLockClient {

    private RedissonClient redissonClient;

    public RedissonLockClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 尝试加锁
     */
    public static boolean tryLock(RLock rLock, ILockKey iLockKey) {
        try {
            return rLock.tryLock(iLockKey.getWaitTime(), iLockKey.getExpire(), iLockKey.getUnit());
        } catch (InterruptedException e) {
            log.error(" tryLock error, lock:{}", rLock.getName(), e);
        }
        return false;
    }

    /**
     * 获取读写锁
     */
    public RLock getLock(ILockKey iLockKey, String... key) {
        String cacheKey = RedisUtils.getCacheKey(iLockKey, key);
        return redissonClient.getLock(cacheKey);
    }

    /**
     * 获取并尝试加锁
     */
    public RLock getLockAndTry(ILockKey iLockKey, String... key) {
        RLock rLock = getLock(iLockKey, key);
        boolean result = tryLock(rLock, iLockKey);
        return result ? rLock : null;
    }

    /**
     * 获取读写锁
     */
    public RReadWriteLock getReadWriteLock(ILockKey iLockKey, String... key) {
        String cacheKey = RedisUtils.getCacheKey(iLockKey, key);
        return redissonClient.getReadWriteLock(cacheKey);
    }
}
