package com.yuelan.plugins.mp.component;

import com.yuelan.core.bean.WxMp;
import com.yuelan.plugins.redisson.annotation.RedisLock;
import com.yuelan.plugins.redisson.util.RedisUtils;
import me.chanjar.weixin.common.bean.WxAccessToken;
import me.chanjar.weixin.common.enums.TicketType;
import me.chanjar.weixin.mp.config.impl.WxMpDefaultConfigImpl;

import java.time.Duration;

/**
 * 基于Redis的微信配置provider.
 * <p>
 * 已加入分布式锁的实现
 */
public class WxMpInRedisConfigStorage extends WxMpDefaultConfigImpl {

    private static final String ACCESS_TOKEN_KEY = "wxMp:access_token:";

    private String accessTokenKey;

    public WxMpInRedisConfigStorage(WxMp wxMp) {
        this.setAppId(wxMp.getAppId());
        this.setSecret(wxMp.getSecret());
    }

    /**
     * 每个公众号生成独有的存储key.
     */
    @Override
    public void setAppId(String appId) {
        super.setAppId(appId);
        this.accessTokenKey = ACCESS_TOKEN_KEY.concat(appId);
    }

    private String getTicketRedisKey(TicketType type) {
        return String.format("wx:ticket:key:%s:%s", this.appId, type.getCode());
    }

    @Override
    public String getAccessToken() {
        return RedisUtils.getCacheObject(accessTokenKey);
    }

    @Override
    public boolean isAccessTokenExpired() {
        return !RedisUtils.hasKey(accessTokenKey);
    }

    @Override
    @RedisLock(lockName = "updateMpAccessToken")
    public void updateAccessToken(WxAccessToken accessToken) {
        updateAccessToken(accessToken.getAccessToken(), accessToken.getExpiresIn());
    }


    @Override
    @RedisLock(lockName = "updateMpAccessToken")
    public void updateAccessToken(String accessToken, int expiresInSeconds) {
        RedisUtils.setCacheObject(accessTokenKey, accessToken, Duration.ofSeconds(expiresInSeconds - 200));
    }

    @Override
    public void expireAccessToken() {
        RedisUtils.deleteObject(accessTokenKey);
    }

    @Override
    public String getTicket(TicketType type) {
        return RedisUtils.getCacheObject(this.getTicketRedisKey(type));
    }

    @Override
    public boolean isTicketExpired(TicketType type) {
        return !RedisUtils.hasKey(this.getTicketRedisKey(type));
    }

    @Override
    @RedisLock(lockName = "updateMpJsapiTicket")
    public void updateTicket(TicketType type, String jsapiTicket, int expiresInSeconds) {
        RedisUtils.setCacheObject(this.getTicketRedisKey(type), jsapiTicket, Duration.ofSeconds(expiresInSeconds - 200));
    }

    @Override
    public void expireTicket(TicketType type) {
        RedisUtils.deleteObject(this.getTicketRedisKey(type));
    }

}
