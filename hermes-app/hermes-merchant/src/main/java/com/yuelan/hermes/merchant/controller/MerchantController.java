package com.yuelan.hermes.merchant.controller;

import com.yuelan.hermes.merchant.biz.service.MerchantService;
import com.yuelan.hermes.merchant.config.satoken.StpMerchantUtil;
import com.yuelan.hermes.merchant.config.satoken.context.MerchantContext;
import com.yuelan.hermes.merchant.controller.req.MerchantModifyPasswordReq;
import com.yuelan.hermes.merchant.controller.rsp.MerchantRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;


@Tag(name = "商户信息")
@RestController
@RequestMapping("/m")
public class MerchantController extends MerchantBaseController {

    @Resource
    private MerchantService merchantService;


    @Operation(summary = "获取用户信息")
    @GetMapping("/userinfo")
    public BizResult<MerchantRsp> getUserInfo() {
        MerchantContext loginUser = this.getLoginUser();
        MerchantRsp userInfo = merchantService.getUserInfo(loginUser.getMerchantId());
        return BizResult.create(userInfo);
    }

    @Log(title = "修改密码", type = OperationType.UPDATE)
    @Operation(summary = "修改密码")
    @PostMapping("/password/modify")
    public BizResult<Boolean> modify(@Valid @RequestBody MerchantModifyPasswordReq passwordReq) {
        MerchantContext loginUser = this.getLoginUser();
        return BizResult.create(merchantService.modify(passwordReq, loginUser));
    }

    @Log(title = "查询秘钥", type = OperationType.QUERY)
    @Operation(summary = "查询秘钥")
    @GetMapping("/secretKey")
    public BizResult<String> secretKey() {
        long merchantId = StpMerchantUtil.getLoginIdAsLong();
        return BizResult.create(merchantService.secretKey(merchantId));
    }
}
