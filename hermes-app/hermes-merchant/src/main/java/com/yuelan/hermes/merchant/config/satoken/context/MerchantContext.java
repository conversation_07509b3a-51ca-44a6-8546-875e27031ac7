package com.yuelan.hermes.merchant.config.satoken.context;


import com.yuelan.plugins.satoken.context.LoginContext;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class MerchantContext extends LoginContext {
    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 商户号
     */
    private String mchId;
}
