package com.yuelan.hermes.merchant.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yuelan.result.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 商户配置表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "merchant_access")
public class MerchantAccessDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户ID
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    /**
     * 加密算法类型 0md5签名1RSA
     */
    @TableField(value = "algorithm")
    private Integer algorithm;

    /**
     * 秘钥
     */
    @TableField(value = "secret_key")
    private String secretKey;

    /**
     * 公钥地址
     */
    @TableField(value = "public_key_path")
    private String publicKeyPath;

    /**
     * 私钥地址
     */
    @TableField(value = "private_key_path")
    private String privateKeyPath;

    /**
     * 第三方公钥地址
     */
    @TableField(value = "third_public_key_path")
    private String thirdPublicKeyPath;
}