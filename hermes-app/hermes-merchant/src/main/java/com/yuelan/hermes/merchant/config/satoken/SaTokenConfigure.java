package com.yuelan.hermes.merchant.config.satoken;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * [Sa-Token 权限认证] 配置类
 *
 * <AUTHOR>
 */
@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {

    // 注册 Sa-Token 拦截器，打开注解式鉴权功能
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，校验规则为 StpUtil.checkLogin() 登录校验。
        registry.addInterceptor(new SaInterceptor(handle -> StpMerchantUtil.checkLogin()))
                .addPathPatterns("/m/**");
    }

    @Autowired
    public void setSaTokenConfig() {
        StpUtil.stpLogic.setConfig(StpMerchantUtil.config);
    }

    @Autowired
    public void setMerchantStpLogic() {
        StpMerchantUtil.setStpLogic(StpMerchantUtil.stpLogic);
    }
}
