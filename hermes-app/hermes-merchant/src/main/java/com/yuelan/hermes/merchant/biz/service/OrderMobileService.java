package com.yuelan.hermes.merchant.biz.service;


import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.yuelan.hermes.commons.constant.EmailConstants;
import com.yuelan.hermes.commons.util.EasyExcelUtil;
import com.yuelan.hermes.merchant.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.merchant.controller.req.OrderMobileListReq;
import com.yuelan.hermes.merchant.controller.rsp.OrderMobileRsp;
import com.yuelan.hermes.merchant.mapper.OrderMobileMapper;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Slf4j
@Service
public class OrderMobileService {

    @Autowired
    private OrderMobileMapper orderMobileMapper;

    /**
     * 订单列表
     */
    public PageData<OrderMobileRsp> list(OrderMobileListReq req) {
        long count = orderMobileMapper.countByOrderMobileListReq(req);
        if (count == 0) {
            return PageData.create(Lists.newArrayList());
        }
        List<OrderMobileRsp> list = orderMobileMapper.pageByOrderMobileListReq(req);
        return PageData.create(list, count, req.getPage(), req.getSize());
    }

    /**
     * 导出订单
     */
    public BizResult<Boolean> export(OrderMobileListReq req, HttpServletResponse response) throws IOException {
        Long count = orderMobileMapper.countByOrderMobileListReq(req);
        if (count == 0) {
            throw BizException.create(BizErrorCodeEnum.ORDER_NOT_EXISTS);
        }
        //数量超过预设值，报错
        if (count > EmailConstants.EXPORT_MAX_LIMIT) {
            throw BizException.create(BizErrorCodeEnum.EXPORT_LIMIT_ERROR);
        }

        List<OrderMobileRsp> list = cursorByOrderMobileListReq(req);
        EasyExcelUtil.download(response, "话费充值订单", OrderMobileRsp.class, list);
        return BizResult.create(Boolean.TRUE).setMsg("处理中请稍后");
    }

    private List<OrderMobileRsp> cursorByOrderMobileListReq(OrderMobileListReq req) {
        List<OrderMobileRsp> list = Lists.newArrayList();
        Long maxId = null;
        while (true) {
            List<OrderMobileRsp> subList = orderMobileMapper.cursorByOrderMobileListReq(req, maxId);
            if (CollectionUtil.isEmpty(subList)) {
                break;

            }
            list.addAll(subList);
            maxId = CollectionUtil.getLast(subList).getId();
        }
        return list;
    }
}