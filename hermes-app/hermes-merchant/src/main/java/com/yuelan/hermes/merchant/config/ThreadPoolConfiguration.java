package com.yuelan.hermes.merchant.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

@Configuration
@EnableAsync
public class ThreadPoolConfiguration {

//    @Bean("commonPool")
//    public ExecutorService commonPool() {
//        int poolSize = Runtime.getRuntime().availableProcessors();
//        return ThreadUtil.newFixedExecutor(poolSize, Integer.MAX_VALUE, "common-pool-", true);
//    }


}