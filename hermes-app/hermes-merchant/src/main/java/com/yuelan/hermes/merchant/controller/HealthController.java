package com.yuelan.hermes.merchant.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "健康检查")
@Slf4j
@RestController
public class HealthController {

    @GetMapping(value = "/")
    public String isAvailable() {
        return "ok";
    }

    @GetMapping(value = "/monitor/check")
    public String check() {
        return "ok";
    }
}
