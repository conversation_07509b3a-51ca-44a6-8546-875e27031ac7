package com.yuelan.hermes.merchant.controller;

import com.yuelan.hermes.commons.util.EasyExcelUtil;
import com.yuelan.hermes.merchant.biz.service.MchAccountService;
import com.yuelan.hermes.merchant.config.satoken.StpMerchantUtil;
import com.yuelan.hermes.merchant.config.satoken.context.MerchantContext;
import com.yuelan.hermes.merchant.controller.req.MchAccountStatementReq;
import com.yuelan.hermes.merchant.controller.rsp.MchAccountBalanceRsp;
import com.yuelan.hermes.merchant.controller.rsp.MchAccountStatementRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Tag(name = "商家账户")
@RequestMapping("/m/account")
@RestController
public class MchAccountController extends MerchantBaseController {

    @Autowired
    private MchAccountService mchAccountService;

    @Operation(summary = "账户余额")
    @GetMapping(value = "/balance")
    public BizResult<MchAccountBalanceRsp> accountBalance() {
        MchAccountBalanceRsp result = mchAccountService.accountBalance(StpMerchantUtil.getLoginIdAsLong());
        return BizResult.create(result);
    }

    @Operation(summary = "交易明细")
    @PostMapping(value = "/stream", consumes = MediaType.APPLICATION_JSON_VALUE)
    public BizResult<PageData<MchAccountStatementRsp>> stream(@RequestBody MchAccountStatementReq req) {
        MerchantContext loginUser = this.getLoginUser();
        req.setMerchantId(loginUser.getMerchantId());
        PageData<MchAccountStatementRsp> pageData = mchAccountService.stream(req);
        return BizResult.create(pageData);
    }

    @Log(title = "导出交易明细", type = OperationType.OTHER)
    @Operation(summary = "导出交易明细")
    @PostMapping("/export")
    public void export(@RequestBody MchAccountStatementReq req, HttpServletResponse response) throws IOException {
        MerchantContext loginUser = this.getLoginUser();
        req.setMerchantId(loginUser.getMerchantId());
        List<MchAccountStatementRsp> list = mchAccountService.export(req);
        EasyExcelUtil.download(response, "交易明细", MchAccountStatementRsp.class, list);
    }
}
