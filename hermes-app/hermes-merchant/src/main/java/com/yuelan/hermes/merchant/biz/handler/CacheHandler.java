package com.yuelan.hermes.merchant.biz.handler;

import com.yuelan.hermes.merchant.common.enums.CacheEnum;
import com.yuelan.plugins.redisson.client.RedissonLockClient;
import com.yuelan.plugins.redisson.client.RedissonMapClient;
import com.yuelan.plugins.redisson.client.RedissonStringClient;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class CacheHandler {


    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedissonMapClient redissonMapClient;
    @Resource
    private RedissonLockClient redissonLockClient;
    @Resource
    private RedissonStringClient redissonStringClient;


    /**
     * 获取商户登录随机码
     */
    public RBucket<String> getMerchantRandomCode(String code) {
        return redissonStringClient.getBucket(CacheEnum.MERCHANT_RANDOM_CODE, code);
    }


}
