package com.yuelan.hermes.merchant.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yuelan.result.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 商户操作日志
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "merchant_operation_log")
public class MerchantOperationLogDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 操作类型值
     */
    @TableField(value = "type_value")
    private Integer typeValue;

    /**
     * 操作类型名称
     */
    @TableField(value = "type_name")
    private String typeName;

    /**
     * 请求url
     */
    @TableField(value = "url")
    private String url;

    /**
     * 操作ip
     */
    @TableField(value = "ip")
    private String ip;

    /**
     * 请求参数
     */
    @TableField(value = "request_data")
    private String requestData;

    /**
     * 返回参数
     */
    @TableField(value = "response_data")
    private String responseData;

    /**
     * 操作状态（0正常 1异常）
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 错误信息
     */
    @TableField(value = "error_msg")
    private String errorMsg;

}