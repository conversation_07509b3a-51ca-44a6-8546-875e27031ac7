package com.yuelan.hermes.merchant.controller.rsp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuelan.hermes.commons.excel.NotifyStatusEnumConverter;
import com.yuelan.hermes.commons.excel.OrderStatusEnumConverter;
import com.yuelan.hermes.commons.excel.RechargeTypeEnumConverter;
import com.yuelan.hermes.commons.excel.SpEnumConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ContentRowHeight(15)
@HeadRowHeight(20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class OrderMobileRsp {

    @ExcelIgnore
    @Schema(description = "订单ID")
    private Long id;

    @ColumnWidth(28)
    @ExcelProperty(value = "订单号")
    @Schema(description = "订单号")
    private String orderNo;

    @ColumnWidth(28)
    @ExcelProperty(value = "平台订单号")
    @Schema(description = "平台订单号")
    private String outTradeNo;

    @ColumnWidth(15)
    @ExcelProperty(value = "充值类型", converter = RechargeTypeEnumConverter.class)
    @Schema(description = "充值类型")
    private Integer hasSlow;

    @ColumnWidth(15)
    @ExcelProperty(value = "运营商", converter = SpEnumConverter.class)
    @Schema(description = "运营商")
    private Integer sp;

    @ColumnWidth(20)
    @ExcelProperty(value = "手机号")
    @Schema(description = "手机号")
    private String mobile;

    @ColumnWidth(15)
    @ExcelProperty(value = "充值面额")
    @Schema(description = "充值面额")
    private BigDecimal rechargeAmount;

    @ColumnWidth(15)
    @ExcelProperty(value = "扣款金额")
    @Schema(description = "扣款金额")
    private BigDecimal orderAmount;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "下单时间")
    @Schema(description = "下单时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ColumnWidth(15)
    @ExcelProperty(value = "订单状态", converter = OrderStatusEnumConverter.class)
    @Schema(description = "订单状态")
    private Integer orderStatus;

    @ColumnWidth(15)
    @ExcelProperty(value = "回调状态", converter = NotifyStatusEnumConverter.class)
    @Schema(description = "回调状态")
    private Integer notifyStatus;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "到账时间")
    @Schema(description = "到账时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finishTime;

    @ExcelIgnore
    @Schema(description = "充值明细链接")
    private String voucher;


}