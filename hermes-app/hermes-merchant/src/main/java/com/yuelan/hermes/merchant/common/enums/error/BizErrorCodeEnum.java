package com.yuelan.hermes.merchant.common.enums.error;

import com.yuelan.hermes.merchant.common.constant.CommonConstants;
import com.yuelan.result.able.IErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BizErrorCodeEnum implements IErrorCode<BizErrorCodeEnum> {

    MERCHANT_NOT_EXISTS("10000", "商户不存在"),
    MERCHANT_IS_EXISTS("10001", "商户已存在"),
    MERCHANT_STATUS_ABNORMITY("10002", "商户状态异常"),
    INCORRECT_PASSWORD("10003", "请输入正确的密码"),
    CODE_EXPIRED("10004", "随机码已过期"),
    PERMISSION_DENIED("10005", "没有权限"),

    ORDER_NOT_EXISTS("11000", "订单不存在"),

    EXPORT_LIMIT_ERROR("20000", "导出数量超过限定值"),
    ;

    private String code;
    private String desc;

    @Override
    public String getCode() {
        return CommonConstants.NAMESPACE_UPPER_CASE.concat(code);
    }
}
