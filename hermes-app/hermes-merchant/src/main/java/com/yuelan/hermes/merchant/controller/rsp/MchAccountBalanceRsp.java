package com.yuelan.hermes.merchant.controller.rsp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MchAccountBalanceRsp {

    @Schema(description = "账户余额")
    private BigDecimal amountBalance;

    @Schema(description = "充值总额")
    private BigDecimal totalIn;

    @Schema(description = "支出总额")
    private BigDecimal totalOut;
}
