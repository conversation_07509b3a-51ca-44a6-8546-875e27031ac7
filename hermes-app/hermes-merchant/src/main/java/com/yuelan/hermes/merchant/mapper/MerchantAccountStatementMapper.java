package com.yuelan.hermes.merchant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.merchant.common.pojo.domain.MerchantAccountStatementDO;
import com.yuelan.hermes.merchant.controller.req.MchAccountStatementReq;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MerchantAccountStatementMapper extends BaseMapper<MerchantAccountStatementDO> {
    Long countByMchAccountStatementReq(@Param("req") MchAccountStatementReq req);

    List<MerchantAccountStatementDO> pageByMchAccountStatementReq(@Param("req") MchAccountStatementReq req);

    List<MerchantAccountStatementDO> cursorByMchAccountStatementReq(@Param("req") MchAccountStatementReq req, @Param("maxId") Long maxId);
}