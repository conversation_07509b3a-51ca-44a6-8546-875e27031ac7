package com.yuelan.hermes.merchant.controller.req;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yuelan.hermes.commons.constant.CommonConstants;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 话费订单
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrderMobileListReq extends PageRequest {

    @Schema(description = "订单ID")
    private List<Long> orderIds;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "平台订单号")
    private String outTradeNo;

    @Schema(description = "运营商")
    private Integer sp;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "下单开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderStartTime;

    @Schema(description = "下单结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderEndTime;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "充值类型0快充,1慢充")
    private Integer hasSlow;

    @Schema(description = "回调状态")
    private Integer notifyStatus;

    @JsonIgnore
    private Long merchantId;

    public void check() {
        if (Objects.nonNull(orderEndTime)) {
            orderEndTime = DateUtil.endOfDay(orderEndTime);
        }
        if (Objects.equals(CommonConstants.ALL_OPTION, sp)) {
            this.sp = null;
        }
        if (Objects.equals(CommonConstants.ALL_OPTION, orderStatus)) {
            this.orderStatus = null;
        }
        if (Objects.equals(CommonConstants.ALL_OPTION, notifyStatus)) {
            this.notifyStatus = null;
        }
        if (Objects.equals(CommonConstants.ALL_OPTION, hasSlow)) {
            this.hasSlow = null;
        }
    }
}