package com.yuelan.hermes.merchant.controller.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MchAccountStatementReq extends PageRequest {

    @Schema(description = "业务类型")
    private Integer bizType;

    @Schema(description = "业务单号")
    private String bizNo;

    @JsonIgnore
    private Long merchantId;
}
