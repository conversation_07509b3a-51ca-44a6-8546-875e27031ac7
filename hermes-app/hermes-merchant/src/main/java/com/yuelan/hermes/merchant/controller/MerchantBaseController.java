package com.yuelan.hermes.merchant.controller;


import cn.dev33.satoken.session.SaSession;
import com.yuelan.hermes.merchant.config.satoken.StpMerchantUtil;
import com.yuelan.hermes.merchant.config.satoken.context.MerchantContext;
import com.yuelan.plugins.satoken.able.IController;
import com.yuelan.plugins.satoken.utils.SaUtils;

/**
 * <p>用户</p>
 *
 * <AUTHOR>
 * @date 2021/12/10
 */
public abstract class MerchantBaseController implements IController<MerchantContext> {

    @Override
    public MerchantContext getLoginUser() {
        SaSession session = StpMerchantUtil.getSession();
        return SaUtils.getUserInfo(session, MerchantContext.class);
    }
}
