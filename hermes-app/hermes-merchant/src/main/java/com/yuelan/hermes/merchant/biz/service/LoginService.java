package com.yuelan.hermes.merchant.biz.service;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.SaTokenInfo;
import com.yuelan.hermes.commons.util.PasswordUtil;
import com.yuelan.hermes.merchant.biz.handler.CacheHandler;
import com.yuelan.hermes.merchant.biz.manager.MerchantManager;
import com.yuelan.hermes.merchant.common.constant.CommonConstants;
import com.yuelan.hermes.merchant.common.enums.CacheEnum;
import com.yuelan.hermes.merchant.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.merchant.common.pojo.domain.MerchantDO;
import com.yuelan.hermes.merchant.config.satoken.StpMerchantUtil;
import com.yuelan.hermes.merchant.config.satoken.context.MerchantContext;
import com.yuelan.hermes.merchant.controller.req.MerchantLoginReq;
import com.yuelan.plugins.satoken.utils.SaUtils;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class LoginService {

    @Autowired
    private CacheHandler cacheHandler;
    @Autowired
    private MerchantManager merchantManager;

    /**
     * 获取登录随机码
     */
    public String generateKey() {
        String uuid = PasswordUtil.generateKey();
        RBucket<String> rBucket = cacheHandler.getMerchantRandomCode(uuid);
        rBucket.set(uuid, CacheEnum.MERCHANT_RANDOM_CODE.getExpire(), CacheEnum.MERCHANT_RANDOM_CODE.getUnit());
        return uuid;
    }

    /**
     * 管理员登录
     */
    public SaTokenInfo login(MerchantLoginReq loginReq) {
        MerchantDO merchantDO = getLoginAdmin(loginReq.getUsername(), loginReq.getPassword());
        //记录登录日志
        MerchantContext context = new MerchantContext();
        context.setMerchantId(merchantDO.getId());
        context.setMerchantName(merchantDO.getName());
        context.setMchId(merchantDO.getMchId());

        //创建token
        StpMerchantUtil.login(context.getMerchantId());
        SaSession saSession = StpMerchantUtil.getSession();
        SaUtils.setUserInfo(saSession, context);
        return StpMerchantUtil.getTokenInfo();
    }

    private MerchantDO getLoginAdmin(String username, String password) {
        String decryptPassword = "";
        try {
            decryptPassword = PasswordUtil.decrypt(password, CommonConstants.PASSWORD_PRIVATE_KEY);
        } catch (Exception e) {
            throw BizException.create(BizErrorCodeEnum.INCORRECT_PASSWORD);
        }
        //校验随机码
        String key = PasswordUtil.getKey(decryptPassword);
        RBucket<String> rBucket = cacheHandler.getMerchantRandomCode(key);
        String code = rBucket.get();
        if (Objects.isNull(code)) {
            log.error("商户登录，随机码不正确 username:{},key:{}", username, key);
            throw BizException.create(BizErrorCodeEnum.CODE_EXPIRED);
        }
        //通过验证立刻删除
        rBucket.delete();
        //校验用户
        MerchantDO merchantDO = merchantManager.findByUsername(username);
        //校验密码
        boolean verify = PasswordUtil.verify(decryptPassword, merchantDO.getPassword(), key);
        if (!verify) {
            throw BizException.create(BizErrorCodeEnum.INCORRECT_PASSWORD);
        }
        return merchantDO;
    }


}
