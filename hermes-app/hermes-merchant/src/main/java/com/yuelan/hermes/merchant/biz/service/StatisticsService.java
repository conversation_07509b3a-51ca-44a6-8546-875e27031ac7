package com.yuelan.hermes.merchant.biz.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.merchant.common.pojo.bo.StatisticsBO;
import com.yuelan.hermes.merchant.controller.req.OrderMobileStatisticsReq;
import com.yuelan.hermes.merchant.controller.rsp.OrderMobileStatisticsRsp;
import com.yuelan.hermes.merchant.mapper.OrderMobileMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class StatisticsService {
    @Autowired
    private OrderMobileMapper orderMobileMapper;

    /**
     * 话费订单统计
     */
    public List<OrderMobileStatisticsRsp> orderMobile(OrderMobileStatisticsReq req) {
        //下单
        List<StatisticsBO> statistics = orderMobileMapper.statistics(req, null);
        if (CollectionUtil.isEmpty(statistics)) {
            return Lists.newArrayList();
        }
        //成功
        Map<String, Integer> successCount = Maps.newHashMap();
        List<StatisticsBO> success = orderMobileMapper.statistics(req, OrderStatusEnum.SUCCESS.getCode());
        if (CollectionUtil.isNotEmpty(success)) {
            successCount = success.stream().collect(Collectors.toMap(obj -> {
                return DateUtil.formatDate(obj.getStatisticsDate());
            }, StatisticsBO::getCount));
        }
        //失败
        Map<String, Integer> failCount = Maps.newHashMap();
        List<StatisticsBO> fail = orderMobileMapper.statistics(req, OrderStatusEnum.FAIL.getCode());
        if (CollectionUtil.isNotEmpty(fail)) {
            failCount = fail.stream().collect(Collectors.toMap(obj -> {
                return DateUtil.formatDate(obj.getStatisticsDate());
            }, StatisticsBO::getCount));
        }
        List<OrderMobileStatisticsRsp> list = Lists.newLinkedList();
        for (StatisticsBO obj : statistics) {
            String date = DateUtil.formatDate(obj.getStatisticsDate());
            OrderMobileStatisticsRsp rsp = new OrderMobileStatisticsRsp();
            rsp.setStatisticsDate(date);
            rsp.setOrderNum(obj.getCount());
            rsp.setSuccessNum(successCount.get(date));
            rsp.setFailNum(failCount.get(date));
            list.add(rsp);
        }
        return list;
    }
}
