package com.yuelan.hermes.merchant.common.pojo.converter;

import com.yuelan.core.util.LocalStreamUtils;
import com.yuelan.hermes.commons.enums.AccountStatementBizTypeEnum;
import com.yuelan.hermes.commons.enums.TradeTypeEnum;
import com.yuelan.hermes.merchant.common.pojo.domain.MerchantAccountStatementDO;
import com.yuelan.hermes.merchant.controller.rsp.MchAccountStatementRsp;

import java.util.List;
import java.util.Objects;

public class MchAccountStatementConverter {

    public static MchAccountStatementRsp toMchAccountStatementRsp(MerchantAccountStatementDO merchantAccountStatementDO) {
        if (Objects.isNull(merchantAccountStatementDO)) {
            return null;
        }
        MchAccountStatementRsp mchAccountStatementRsp = new MchAccountStatementRsp();
        mchAccountStatementRsp.setId(merchantAccountStatementDO.getId());
        mchAccountStatementRsp.setBizType(merchantAccountStatementDO.getBizType());
        mchAccountStatementRsp.setBizNo(merchantAccountStatementDO.getBizNo());
        String tradeType = "";
        if (Objects.equals(merchantAccountStatementDO.getBizType(), AccountStatementBizTypeEnum.ACCOUNT_RECHARGE.getCode())) {
            if (Objects.equals(merchantAccountStatementDO.getTradeType(), TradeTypeEnum.IN.getCode())) {
                tradeType = "充值";
            } else {
                tradeType = "退款";
            }
        } else if (Objects.equals(merchantAccountStatementDO.getBizType(), AccountStatementBizTypeEnum.PREPAID_RECHARGE.getCode())) {
            if (Objects.equals(merchantAccountStatementDO.getTradeType(), TradeTypeEnum.IN.getCode())) {
                tradeType = "退款";
            } else {
                tradeType = "扣款";
            }
        }
        mchAccountStatementRsp.setTradeType(tradeType);
        mchAccountStatementRsp.setAmount(merchantAccountStatementDO.getAmount());
        mchAccountStatementRsp.setBalance(merchantAccountStatementDO.getBalance());
        mchAccountStatementRsp.setCreateTime(merchantAccountStatementDO.getCreateTime());
        return mchAccountStatementRsp;
    }

    public static List<MchAccountStatementRsp> toMchAccountStatementRspList(List<MerchantAccountStatementDO> list) {
        return LocalStreamUtils.toList(list, MchAccountStatementConverter::toMchAccountStatementRsp);
    }
}
