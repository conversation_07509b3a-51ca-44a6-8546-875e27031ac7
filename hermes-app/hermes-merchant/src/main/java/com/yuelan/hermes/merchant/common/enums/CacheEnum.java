package com.yuelan.hermes.merchant.common.enums;

import com.yuelan.plugins.redisson.key.ICacheKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.concurrent.TimeUnit;

@Getter
@AllArgsConstructor
public enum CacheEnum implements ICacheKey {

    MERCHANT_RANDOM_CODE("mrc:", "商户登录随机码缓存", 5L, TimeUnit.MINUTES),
    //
    ;

    private String key;
    private String desc;
    private Long expire;
    private TimeUnit unit;


}

