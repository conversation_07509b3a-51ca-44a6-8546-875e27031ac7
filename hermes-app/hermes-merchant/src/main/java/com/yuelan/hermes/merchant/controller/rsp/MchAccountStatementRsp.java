package com.yuelan.hermes.merchant.controller.rsp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yuelan.hermes.commons.excel.AccountStatementBizTypeEnumConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ContentRowHeight(15)
@HeadRowHeight(20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class MchAccountStatementRsp {

    @ExcelIgnore
    @JsonIgnore
    private Long id;

    @ColumnWidth(22)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "交易时间")
    @Schema(description = "交易时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "业务类型", converter = AccountStatementBizTypeEnumConverter.class)
    @Schema(description = "业务类型")
    private Integer bizType;

    @ColumnWidth(30)
    @ExcelProperty(value = "业务单号")
    @Schema(description = "业务单号")
    private String bizNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "收支类型")
    @Schema(description = "收支类型")
    private String tradeType;

    @ColumnWidth(20)
    @ExcelProperty(value = "交易金额")
    @Schema(description = "交易金额")
    private BigDecimal amount;

    @ColumnWidth(20)
    @ExcelProperty(value = "账户余额")
    @Schema(description = "账户余额")
    private BigDecimal balance;


}
