package com.yuelan.hermes.merchant;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableTransactionManagement
@SpringBootApplication
@EnableConfigurationProperties
@MapperScan("com.yuelan.hermes.merchant.mapper")
public class MerchantApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(MerchantApplication.class);
        ConfigurableApplicationContext context = application.run(args);
        // 注册关闭钩子
        context.registerShutdownHook();
    }
}

