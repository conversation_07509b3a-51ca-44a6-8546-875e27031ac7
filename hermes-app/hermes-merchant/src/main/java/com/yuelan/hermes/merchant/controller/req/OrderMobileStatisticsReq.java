package com.yuelan.hermes.merchant.controller.req;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * 话费订单
 */
@Data
public class OrderMobileStatisticsReq {

    @Schema(description = "下单开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderStartTime;

    @Schema(description = "下单结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderEndTime;

    @JsonIgnore
    private Long merchantId;


    public void check() {
        if (Objects.nonNull(orderEndTime)) {
            orderEndTime = DateUtil.endOfDay(orderEndTime);
        }
    }
}