package com.yuelan.hermes.merchant.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yuelan.result.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商家账户流水
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "merchant_account_statement")
public class MerchantAccountStatementDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户ID
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    /**
     * 交易金额
     */
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 账户余额
     */
    @TableField(value = "balance")
    private BigDecimal balance;

    /**
     * 1 收入 2 支出
     */
    @TableField(value = "trade_type")
    private Integer tradeType;

    /**
     * 业务类型，1充值2话费订单
     */
    @TableField(value = "biz_type")
    private Integer bizType;

    /**
     * 业务单号
     */
    @TableField(value = "biz_no")
    private String bizNo;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

}