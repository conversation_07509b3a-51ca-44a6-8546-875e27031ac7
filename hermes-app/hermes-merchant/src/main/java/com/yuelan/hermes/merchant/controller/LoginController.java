package com.yuelan.hermes.merchant.controller;

import cn.dev33.satoken.stp.SaTokenInfo;
import com.yuelan.hermes.merchant.biz.service.LoginService;
import com.yuelan.hermes.merchant.config.satoken.StpMerchantUtil;
import com.yuelan.hermes.merchant.controller.req.MerchantLoginReq;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Tag(name = "商户登录")
@RestController
public class LoginController {

    @Resource
    private LoginService loginService;

    @Operation(summary = "获取随机码")
    @GetMapping("/merchant/loginKey")
    public BizResult<String> generateKey() {
        String key = loginService.generateKey();
        return BizResult.create(key);
    }

    @Operation(summary = "用户登录")
    @PostMapping("/merchant/login")
    public BizResult<SaTokenInfo> login(@Valid @RequestBody MerchantLoginReq loginReq) {
        SaTokenInfo tokenInfo = loginService.login(loginReq);
        return BizResult.create(tokenInfo);
    }

    @Log(title = "用户登出", type = OperationType.LOGOUT)
    @Operation(summary = "用户登出")
    @GetMapping("/m/logout")
    public BizResult<Boolean> logout() {
        StpMerchantUtil.logout();
        return BizResult.create(true);
    }


}
