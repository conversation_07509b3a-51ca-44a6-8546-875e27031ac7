package com.yuelan.hermes.merchant.biz.handler;

import com.yuelan.hermes.merchant.common.pojo.domain.MerchantOperationLogDO;
import com.yuelan.hermes.merchant.config.satoken.StpMerchantUtil;
import com.yuelan.hermes.merchant.mapper.MerchantOperationLogMapper;
import com.yuelan.plugins.log.context.LogContext;
import com.yuelan.plugins.log.listener.LogListener;
import com.yuelan.plugins.log.utils.LogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Component
public class MerchantLogHandler implements LogListener {
    @Autowired
    private MerchantOperationLogMapper merchantOperationLogMapper;


    @Override
    public String getLoginType() {
        return StpMerchantUtil.TYPE;
    }

    @Override
    public void listener(LogContext logContext, HttpServletRequest httpServletRequest) {
        MerchantOperationLogDO logDO = new MerchantOperationLogDO();
        logDO.init();
        logDO.setMerchantId(StpMerchantUtil.getLoginIdAsLong());
        logDO.setTitle(logContext.getTitle());
        logDO.setTypeValue(logContext.getType().getCode());
        logDO.setTypeName(logContext.getType().getDesc());
        logDO.setUrl(logContext.getRequestURI());
        logDO.setIp(LogUtil.getIpAddr(httpServletRequest));
        logDO.setRequestData(logContext.getRequestData());
        logDO.setResponseData(logContext.getResponseData());
        logDO.setStatus(logContext.getStatus().getCode());
        logDO.setErrorMsg(logContext.getMessage());
        merchantOperationLogMapper.insert(logDO);
    }
}
