package com.yuelan.hermes.merchant.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yuelan.result.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 话费订单报表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "order_mobile_report")
public class OrderMobileReportDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计日期
     */
    @TableField(value = "report_date")
    private Integer reportDate;

    /**
     * 商户ID
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    /**
     * 订单数
     */
    @TableField(value = "num")
    private Integer num;

    /**
     * 成功订单数
     */
    @TableField(value = "success")
    private Integer success;

    /**
     * 失败订单数
     */
    @TableField(value = "fail")
    private Integer fail;

    /**
     * 移动订单数
     */
    @TableField(value = "cmcc")
    private Integer cmcc;

    /**
     * 移动成功订单数
     */
    @TableField(value = "cmcc_success")
    private Integer cmccSuccess;

    /**
     * 联调订单数
     */
    @TableField(value = "unicom")
    private Integer unicom;

    /**
     * 联调成功订单数
     */
    @TableField(value = "unicom_success")
    private Integer unicomSuccess;

    /**
     * 电信订单数
     */
    @TableField(value = "telecom")
    private Integer telecom;

    /**
     * 电信成功订单数
     */
    @TableField(value = "telecom_success")
    private Integer telecomSuccess;
}