package com.yuelan.hermes.merchant.biz.service;

import com.yuelan.hermes.commons.util.PasswordUtil;
import com.yuelan.hermes.merchant.biz.handler.CacheHandler;
import com.yuelan.hermes.merchant.biz.manager.MerchantManager;
import com.yuelan.hermes.merchant.common.constant.CommonConstants;
import com.yuelan.hermes.merchant.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.merchant.common.pojo.converter.MerchantConverter;
import com.yuelan.hermes.merchant.common.pojo.domain.MerchantAccessDO;
import com.yuelan.hermes.merchant.common.pojo.domain.MerchantDO;
import com.yuelan.hermes.merchant.config.satoken.context.MerchantContext;
import com.yuelan.hermes.merchant.controller.req.MerchantModifyPasswordReq;
import com.yuelan.hermes.merchant.controller.rsp.MerchantRsp;
import com.yuelan.hermes.merchant.mapper.MerchantAccessMapper;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class MerchantService {

    @Autowired
    private MerchantManager merchantManager;
    @Autowired
    private CacheHandler cacheHandler;
    @Autowired
    private MerchantAccessMapper merchantAccessMapper;

    /**
     * 获取用户信息
     */
    public MerchantRsp getUserInfo(Long merchantId) {
        MerchantDO merchantDO = merchantManager.findById(merchantId);
        if (Objects.isNull(merchantDO)) {
            throw BizException.create(BizErrorCodeEnum.MERCHANT_NOT_EXISTS);
        }
        return MerchantConverter.toMerchantRsp(merchantDO);
    }

    /**
     * 修改密码
     */
    public boolean modify(MerchantModifyPasswordReq passwordReq, MerchantContext loginUser) {
        Long merchantId = loginUser.getMerchantId();
        MerchantDO merchantDO = merchantManager.findById(merchantId);
        //校验原始密码
        String oldPassword = passwordReq.getOldPassword();
        String decryptPassword = "";
        try {
            decryptPassword = PasswordUtil.decrypt(oldPassword, CommonConstants.PASSWORD_PRIVATE_KEY);
        } catch (Exception e) {
            throw BizException.create(BizErrorCodeEnum.INCORRECT_PASSWORD);
        }
        //校验随机码
        String key = PasswordUtil.getKey(decryptPassword);
        RBucket<String> rBucket = cacheHandler.getMerchantRandomCode(key);
        String code = rBucket.get();
        if (Objects.isNull(code)) {
            log.error("商户修改密码，随机码不正确 passwordReq:{},loginUser:{}", passwordReq, loginUser);
            throw BizException.create(BizErrorCodeEnum.CODE_EXPIRED);
        }
        //通过验证立刻删除
        rBucket.delete();
        //校验密码
        boolean verify = PasswordUtil.verify(decryptPassword, merchantDO.getPassword(), key);
        if (!verify) {
            throw BizException.create(BizErrorCodeEnum.INCORRECT_PASSWORD);
        }
        //解析新密码
        String newPassword = null;
        try {
            newPassword = PasswordUtil.decrypt(passwordReq.getNewPassword(), CommonConstants.PASSWORD_PRIVATE_KEY);
        } catch (Exception e) {
            throw BizException.create(BizErrorCodeEnum.INCORRECT_PASSWORD);
        }
        String password = PasswordUtil.getPassword(newPassword, key);
        return merchantManager.modifyPassword(merchantId, password, loginUser.getMerchantName());
    }

    /**
     * 查询秘钥
     */
    public String secretKey(Long merchantId) {
        MerchantAccessDO merchantAccessDO = merchantAccessMapper.findByMerchantId(merchantId);
        if (Objects.isNull(merchantAccessDO)) {
            return "";
        }
        return merchantAccessDO.getSecretKey();
    }
}
