package com.yuelan.hermes.merchant.biz.manager;

import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.MerchantStatusEnum;
import com.yuelan.hermes.merchant.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.merchant.common.pojo.domain.MerchantDO;
import com.yuelan.hermes.merchant.mapper.MerchantMapper;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class MerchantManager {

    @Autowired
    private MerchantMapper merchantMapper;

    public MerchantDO findByUsername(String username) {
        if (StringUtils.isBlank(username)) {
            return null;
        }
        MerchantDO merchantDO = merchantMapper.findByUsername(username);
        check(merchantDO);
        return merchantDO;
    }

    public MerchantDO findById(Long merchantId) {
        if (Objects.isNull(merchantId)) {
            return null;
        }
        MerchantDO merchantDO = merchantMapper.findOne(merchantId);
        check(merchantDO);
        return merchantDO;
    }

    public boolean check(MerchantDO merchantDO) {
        if (Objects.isNull(merchantDO)) {
            throw BizException.create(BizErrorCodeEnum.MERCHANT_NOT_EXISTS);
        }
        MerchantStatusEnum statusEnum = LocalEnumUtils.findByCodeWithoutDefault(MerchantStatusEnum.class, merchantDO.getStatus());
        if (!Objects.equals(statusEnum, MerchantStatusEnum.ENABLE)) {
            throw BizException.create(BizErrorCodeEnum.MERCHANT_STATUS_ABNORMITY, "商户账号已" + statusEnum.getDesc());
        }
        return true;
    }

    /**
     * 修改密码
     */
    public boolean modifyPassword(Long merchantId, String password, String operator) {
        log.info("商户修改密码, merchantId:{}, operator:{}", merchantId, operator);
        return merchantMapper.modifyPassword(merchantId, password, operator) > 0;
    }
}
