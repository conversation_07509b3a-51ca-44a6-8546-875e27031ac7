package com.yuelan.hermes.merchant.controller;

import com.yuelan.hermes.merchant.biz.service.StatisticsService;
import com.yuelan.hermes.merchant.config.satoken.context.MerchantContext;
import com.yuelan.hermes.merchant.controller.req.OrderMobileStatisticsReq;
import com.yuelan.hermes.merchant.controller.rsp.OrderMobileStatisticsRsp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "统计")
@RequestMapping("/m/s")
@RestController
public class StatisticsController extends MerchantBaseController {

    @Autowired
    private StatisticsService statisticsService;

    @Operation(summary = "话费订单统计")
    @PostMapping("/orderMobile")
    public BizResult<List<OrderMobileStatisticsRsp>> orderMobile(@RequestBody OrderMobileStatisticsReq req) {
        MerchantContext loginUser = this.getLoginUser();
        req.setMerchantId(loginUser.getMerchantId());
        req.check();
        List<OrderMobileStatisticsRsp> list = statisticsService.orderMobile(req);
        return BizResult.create(list);
    }

}
