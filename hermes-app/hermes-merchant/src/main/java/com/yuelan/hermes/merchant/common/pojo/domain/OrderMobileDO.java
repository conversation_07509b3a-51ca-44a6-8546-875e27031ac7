package com.yuelan.hermes.merchant.common.pojo.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yuelan.result.entity.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 话费订单
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "order_mobile")
public class OrderMobileDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    @TableField(value = "order_no")
    private String orderNo;

    /**
     * 第三方订单号
     */
    @TableField(value = "out_trade_no")
    private String outTradeNo;

    /**
     * 明细单号
     */
    @TableField(value = "item_no")
    private String itemNo;

    /**
     * 商户ID
     */
    @TableField(value = "merchant_id")
    private Long merchantId;

    /**
     * 商户名称
     */
    @TableField(value = "merchant_name")
    private String merchantName;

    /**
     * 商户号
     */
    @TableField(value = "mch_id")
    private String mchId;

    /**
     * 充值手机号
     */
    @TableField(value = "mobile")
    private String mobile;

    /**
     * 运营商0未知1移动2联调3电信4广电
     */
    @TableField(value = "sp")
    private Integer sp;

    /**
     * 充值金额
     */
    @TableField(value = "recharge_amount")
    private BigDecimal rechargeAmount;

    /**
     * 商品ID
     */
    @TableField(value = "goods_id")
    private Long goodsId;

    /**
     * 商品名称
     */
    @TableField(value = "goods_name")
    private String goodsName;

    /**
     * SKU ID
     */
    @TableField(value = "sku_id")
    private Long skuId;

    /**
     * SKU编号
     */
    @TableField(value = "sku_no")
    private String skuNo;

    /**
     * SKU名称
     */
    @TableField(value = "sku_name")
    private String skuName;

    /**
     * 订单金额
     */
    @TableField(value = "order_amount")
    private BigDecimal orderAmount;

    /**
     * 订单状态0处理中1交易成功2交易失败3订单异常
     */
    @TableField(value = "order_status")
    private Integer orderStatus;

    /**
     * 是否慢充0否1是
     */
    @TableField(value = "has_slow")
    private Integer hasSlow;

    /**
     * 完成时间
     */
    @TableField(value = "finish_time")
    private Date finishTime;

    /**
     * 通知地址
     */
    @TableField(value = "notify_url")
    private String notifyUrl;

    /**
     * 通知状态0等待通知1通知成功2通知失败
     */
    @TableField(value = "notify_status")
    private Integer notifyStatus;

    /**
     * 充值明细链接
     */
    @TableField(value = "voucher")
    private String voucher;

}