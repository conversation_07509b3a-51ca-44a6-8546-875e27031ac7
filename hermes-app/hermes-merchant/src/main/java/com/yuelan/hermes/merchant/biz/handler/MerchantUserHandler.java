package com.yuelan.hermes.merchant.biz.handler;

import com.google.common.collect.Lists;
import com.yuelan.hermes.merchant.config.satoken.StpMerchantUtil;
import com.yuelan.plugins.satoken.able.IAuthorization;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MerchantUserHandler implements IAuthorization {

    @Override
    public String getLoginType() {
        return StpMerchantUtil.TYPE;
    }

    @Override
    public List<String> getPermissionList(Object loginId) {
        return Lists.newArrayList();
    }

    @Override
    public List<String> getRoleList(Object loginId) {
        return Lists.newArrayList();
    }
}
