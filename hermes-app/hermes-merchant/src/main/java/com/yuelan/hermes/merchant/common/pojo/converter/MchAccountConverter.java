package com.yuelan.hermes.merchant.common.pojo.converter;


import com.yuelan.hermes.merchant.common.pojo.domain.MerchantAccountDO;
import com.yuelan.hermes.merchant.controller.rsp.MchAccountBalanceRsp;

public class MchAccountConverter {

    public static MchAccountBalanceRsp toMchAccountBalanceRsp(MerchantAccountDO merchantAccountDO) {
        MchAccountBalanceRsp mchAccountBalanceRsp = new MchAccountBalanceRsp();
        mchAccountBalanceRsp.setAmountBalance(merchantAccountDO.getBalance());
        mchAccountBalanceRsp.setTotalIn(merchantAccountDO.getTotalIn());
        mchAccountBalanceRsp.setTotalOut(merchantAccountDO.getTotalOut());
        return mchAccountBalanceRsp;
    }


}
