package com.yuelan.hermes.merchant.controller;


import com.yuelan.hermes.merchant.biz.service.OrderMobileService;
import com.yuelan.hermes.merchant.config.satoken.context.MerchantContext;
import com.yuelan.hermes.merchant.controller.req.OrderMobileListReq;
import com.yuelan.hermes.merchant.controller.rsp.OrderMobileRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Tag(name = "话费订单")
@RequestMapping("/m/om")
@RestController
public class OrderMobileController extends MerchantBaseController {

    @Resource
    private OrderMobileService orderMobileService;

    @Operation(summary = "订单列表")
    @PostMapping("/list")
    public BizResult<PageData<OrderMobileRsp>> list(@RequestBody OrderMobileListReq req) {
        MerchantContext loginUser = this.getLoginUser();
        req.setMerchantId(loginUser.getMerchantId());
        req.check();
        PageData<OrderMobileRsp> pageData = orderMobileService.list(req);
        return BizResult.create(pageData);
    }

    @Log(title = "导出话费订单", type = OperationType.OTHER)
    @Operation(summary = "导出订单")
    @PostMapping("/export")
    public BizResult<Boolean> export(@RequestBody OrderMobileListReq req, HttpServletResponse response) throws IOException {
        MerchantContext loginUser = this.getLoginUser();
        req.setMerchantId(loginUser.getMerchantId());
        req.check();
        return orderMobileService.export(req, response);
    }
}
