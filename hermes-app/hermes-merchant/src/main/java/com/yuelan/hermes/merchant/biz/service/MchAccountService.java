package com.yuelan.hermes.merchant.biz.service;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.yuelan.hermes.merchant.common.pojo.converter.MchAccountConverter;
import com.yuelan.hermes.merchant.common.pojo.converter.MchAccountStatementConverter;
import com.yuelan.hermes.merchant.common.pojo.domain.MerchantAccountDO;
import com.yuelan.hermes.merchant.common.pojo.domain.MerchantAccountStatementDO;
import com.yuelan.hermes.merchant.controller.req.MchAccountStatementReq;
import com.yuelan.hermes.merchant.controller.rsp.MchAccountBalanceRsp;
import com.yuelan.hermes.merchant.controller.rsp.MchAccountStatementRsp;
import com.yuelan.hermes.merchant.mapper.MerchantAccountMapper;
import com.yuelan.hermes.merchant.mapper.MerchantAccountStatementMapper;
import com.yuelan.result.entity.PageData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class MchAccountService {
    @Autowired
    private MerchantAccountMapper merchantAccountMapper;
    @Autowired
    private MerchantAccountStatementMapper merchantAccountStatementMapper;

    /**
     * 账户余额
     */
    public MchAccountBalanceRsp accountBalance(Long merchantId) {
        MerchantAccountDO merchantAccountDO = merchantAccountMapper.findByMerchantId(merchantId);
        if (Objects.isNull(merchantAccountDO)) {
            merchantAccountDO = new MerchantAccountDO();
            merchantAccountDO.setMerchantId(merchantId);
            merchantAccountDO.setBalance(BigDecimal.ZERO);
            merchantAccountDO.setTotalIn(BigDecimal.ZERO);
            merchantAccountDO.setTotalOut(BigDecimal.ZERO);
        }
        return MchAccountConverter.toMchAccountBalanceRsp(merchantAccountDO);
    }

    /**
     * 账户流水
     */
    public PageData<MchAccountStatementRsp> stream(MchAccountStatementReq req) {
        Long count = merchantAccountStatementMapper.countByMchAccountStatementReq(req);
        if (count == 0) {
            return PageData.create(Lists.newArrayList());
        }
        List<MerchantAccountStatementDO> list = merchantAccountStatementMapper.pageByMchAccountStatementReq(req);
        List<MchAccountStatementRsp> reList = MchAccountStatementConverter.toMchAccountStatementRspList(list);
        return PageData.create(reList, count, req.getPage(), req.getSize());
    }

    /**
     * 导出账户流水
     */
    public List<MchAccountStatementRsp> export(MchAccountStatementReq req) {
        List<MchAccountStatementRsp> list = Lists.newArrayList();
        Long maxId = null;
        while (true) {
            List<MerchantAccountStatementDO> subList = merchantAccountStatementMapper.cursorByMchAccountStatementReq(req, maxId);
            if (CollectionUtil.isEmpty(subList)) {
                break;
            }
            maxId = CollectionUtil.getLast(subList).getId();
            list.addAll(MchAccountStatementConverter.toMchAccountStatementRspList(subList));
        }
        return list;
    }

}
