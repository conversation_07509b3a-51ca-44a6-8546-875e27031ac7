package com.yuelan.hermes.merchant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.merchant.common.pojo.domain.MerchantDO;
import org.apache.ibatis.annotations.Param;

public interface MerchantMapper extends BaseMapper<MerchantDO> {
    MerchantDO findByUsername(String username);

    MerchantDO findOne(Long merchantId);

    int modifyPassword(@Param("merchantId") Long merchantId, @Param("password") String password, @Param("updateBy") String updateBy);
}