package com.yuelan.hermes.merchant.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yuelan.hermes.merchant.common.pojo.bo.StatisticsBO;
import com.yuelan.hermes.merchant.common.pojo.domain.OrderMobileDO;
import com.yuelan.hermes.merchant.controller.req.OrderMobileListReq;
import com.yuelan.hermes.merchant.controller.req.OrderMobileStatisticsReq;
import com.yuelan.hermes.merchant.controller.rsp.OrderMobileRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderMobileMapper extends BaseMapper<OrderMobileDO> {
    long countByOrderMobileListReq(@Param("req") OrderMobileListReq req);

    List<OrderMobileRsp> pageByOrderMobileListReq(@Param("req") OrderMobileListReq req);

    List<OrderMobileRsp> cursorByOrderMobileListReq(@Param("req") OrderMobileListReq req, @Param("maxId") Long maxId);

    List<StatisticsBO> statistics(@Param("req") OrderMobileStatisticsReq req, @Param("orderStatus") Integer orderStatus);

}