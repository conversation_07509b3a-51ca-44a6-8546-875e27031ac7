package com.yuelan.hermes.merchant.common.pojo.converter;

import com.yuelan.hermes.merchant.common.pojo.domain.MerchantDO;
import com.yuelan.hermes.merchant.controller.rsp.MerchantRsp;

import java.util.Objects;

public class MerchantConverter {

    public static MerchantRsp toMerchantRsp(MerchantDO merchantDO) {
        if (Objects.isNull(merchantDO)) {
            return null;
        }
        MerchantRsp merchantRsp = new MerchantRsp();
        merchantRsp.setMerchantId(merchantDO.getId());
        merchantRsp.setMerchantName(merchantDO.getName());
        merchantRsp.setMchId(merchantDO.getMchId());
        merchantRsp.setPhone(merchantDO.getPhone());
        merchantRsp.setEmail(merchantDO.getEmail());
        return merchantRsp;
    }
}
