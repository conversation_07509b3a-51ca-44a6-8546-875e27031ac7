<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.merchant.mapper.MerchantAccessMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, merchant_id, algorithm, secret_key, public_key_path, private_key_path, third_public_key_path,
        create_time, update_time, deleted
    </sql>

    <select id="findByMerchantId" resultType="com.yuelan.hermes.merchant.common.pojo.domain.MerchantAccessDO">
        select
        <include refid="Base_Column_List"/>
        from merchant_access
        where merchant_id =#{merchantId}
        order by id desc
        limit 1
    </select>
</mapper>