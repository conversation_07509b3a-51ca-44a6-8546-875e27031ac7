<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.merchant.mapper.MerchantMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, mch_id, `name`, username, `password`, phone, email, `status`, create_by, create_time,
        update_by, update_time, deleted
    </sql>

    <select id="findByUsername" resultType="com.yuelan.hermes.merchant.common.pojo.domain.MerchantDO">
        select
        <include refid="Base_Column_List"/>
        from merchant
        where username =#{username}
        and deleted = 0
        limit 1
    </select>

    <select id="findOne" resultType="com.yuelan.hermes.merchant.common.pojo.domain.MerchantDO">
        select
        <include refid="Base_Column_List"/>
        from merchant
        where id =#{merchantId}
        and deleted = 0
        limit 1
    </select>

    <update id="modifyPassword">
        update `merchant`
        set `password` = #{password},
        update_by = #{updateBy},
        update_time = now()
        where id = #{merchantId}
    </update>
</mapper>