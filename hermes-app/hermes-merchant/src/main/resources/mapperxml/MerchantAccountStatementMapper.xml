<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.merchant.mapper.MerchantAccountStatementMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, merchant_id, amount, balance, trade_type, biz_type, biz_no, remark, create_time,
        update_time, deleted
    </sql>

    <sql id="MchAccountStatementReqWhere">
        <where>
            merchant_id = #{req.merchantId}
            <if test="req.bizNo != null and req.bizNo != ''">and biz_no =#{req.bizNo}</if>
            <if test="req.bizType != null">and biz_type =#{req.bizType}</if>
            and deleted = 0
        </where>
    </sql>

    <select id="countByMchAccountStatementReq" resultType="java.lang.Long">
        select count(*)
        from merchant_account_statement
        <include refid="MchAccountStatementReqWhere"/>
    </select>

    <select id="pageByMchAccountStatementReq" resultType="com.yuelan.hermes.merchant.common.pojo.domain.MerchantAccountStatementDO">
        select
        <include refid="Base_Column_List"/>
        from merchant_account_statement
        <include refid="MchAccountStatementReqWhere"/>
        order by id desc
        limit #{req.offset},#{req.size}
    </select>

    <select id="cursorByMchAccountStatementReq" resultType="com.yuelan.hermes.merchant.common.pojo.domain.MerchantAccountStatementDO">
        select
        <include refid="Base_Column_List"/>
        from merchant_account_statement
        <include refid="MchAccountStatementReqWhere"/>
        <if test="maxId != null">and id <![CDATA[ > ]]> #{maxId}</if>
        order by id
        limit 500
    </select>
</mapper>