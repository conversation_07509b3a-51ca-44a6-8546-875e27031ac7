<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.merchant.mapper.MerchantAccountMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, merchant_id, balance, total_in, total_out, create_time, update_time, deleted
    </sql>

    <select id="findByMerchantId" resultType="com.yuelan.hermes.merchant.common.pojo.domain.MerchantAccountDO">
        select
        <include refid="Base_Column_List"/>
        from merchant_account
        where merchant_id =#{merchantId}
        limit 1
    </select>
</mapper>