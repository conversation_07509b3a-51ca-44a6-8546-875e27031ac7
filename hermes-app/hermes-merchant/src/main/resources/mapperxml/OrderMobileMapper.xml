<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.merchant.mapper.OrderMobileMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, order_no, out_trade_no, item_no, merchant_id, merchant_name, mch_id, mobile,
        sp, recharge_amount, goods_id, goods_name, sku_id, sku_no, sku_name, order_amount,
        order_status, has_slow, finish_time, notify_url, notify_status, voucher, create_time,
        update_time, deleted
    </sql>

    <sql id="OrderMobileListReqWhere">
        <where>
            merchant_id = #{req.merchantId}
            <if test="req.orderIds != null and req.orderIds.size() &gt; 0">
                and id in
                <foreach close=")" collection="req.orderIds" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.orderNo != null and req.orderNo != ''">and order_no = #{req.orderNo}</if>
            <if test="req.outTradeNo != null and req.outTradeNo != ''">and out_trade_no = #{req.outTradeNo}</if>
            <if test="req.sp != null">and sp = #{req.sp}</if>
            <if test="req.mobile != null and req.mobile != ''">and mobile =#{req.mobile}</if>
            <if test="req.orderStartTime != null">and create_time <![CDATA[ >= ]]> #{req.orderStartTime}</if>
            <if test="req.orderEndTime != null">and create_time <![CDATA[ <= ]]> #{req.orderEndTime}</if>
            <if test="req.orderStatus != null">and order_status =#{req.orderStatus}</if>
            <if test="req.hasSlow != null">and has_slow = #{req.hasSlow}</if>
            <if test="req.notifyStatus != null">and notify_status = #{req.notifyStatus}</if>
            and deleted = 0
        </where>
    </sql>

    <select id="countByOrderMobileListReq" resultType="long">
        select count(*)
        from order_mobile
        <include refid="OrderMobileListReqWhere"/>
    </select>

    <select id="pageByOrderMobileListReq" resultType="com.yuelan.hermes.merchant.controller.rsp.OrderMobileRsp">
        select
        <include refid="Base_Column_List"/>
        from order_mobile
        <include refid="OrderMobileListReqWhere"/>
        order by id desc
        limit #{req.offset},#{req.size}
    </select>

    <select id="cursorByOrderMobileListReq" resultType="com.yuelan.hermes.merchant.controller.rsp.OrderMobileRsp">
        select
        <include refid="Base_Column_List"/>
        from order_mobile
        <include refid="OrderMobileListReqWhere"/>
        <if test="maxId != null">and id <![CDATA[ > ]]> #{maxId}</if>
        order by id
        limit 500
    </select>

    <select id="statistics" resultType="com.yuelan.hermes.merchant.common.pojo.bo.StatisticsBO">
        SELECT DATE( create_time ) AS statisticsDate,
        count(*) as `count`
        FROM
        order_mobile
        WHERE
        merchant_id = #{req.merchantId}
        AND create_time <![CDATA[ >= ]]> #{req.orderStartTime}
        AND create_time <![CDATA[ <= ]]> #{req.orderEndTime}
        <if test="orderStatus != null">AND order_status = #{orderStatus}</if>
        GROUP BY statisticsDate
    </select>
</mapper>