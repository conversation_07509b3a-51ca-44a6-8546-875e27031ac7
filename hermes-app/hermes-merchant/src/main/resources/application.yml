server:
  port: 22000
  shutdown: graceful
spring:
  application:
    name: hermes-merchant
  lifecycle:
    timeout-per-shutdown-phase: 30s
  jackson:
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false
  main:
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
management:
  server:
    port: 22008
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: "*"
        exclude: shutdown
  endpoint:
    health:
      show-details: ALWAYS
  trace:
    http:
      enabled: true
mybatis-plus:
  mapper-locations: classpath:mapperxml/*.xml
logging:
  file:
    name: ${logging.file.path}/${spring.application.name}.log
    path: ${user.home}/logs/${spring.application.name}
  level:
    com.yuelan.hermes.merchant: debug
knife4j:
  enable: true