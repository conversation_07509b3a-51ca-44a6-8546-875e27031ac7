<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.yuelan</groupId>
        <artifactId>hermes-app</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>hermes-merchant</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>商户站</description>

    <dependencies>
        <!-- 工具包 -->
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>hermes-commons</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-commons-boot</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-plugins-redisson</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-plugins-doc</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-plugins-log</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.yuelan</groupId>
            <artifactId>yuelan-plugins-token</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- spring -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!--DB -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${springboot.version}</version>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>