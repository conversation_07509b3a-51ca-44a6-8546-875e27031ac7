# 基于Redis计数器的精确扣量算法

## 📋 算法概述

新的扣量算法使用Redis计数器配合预定义的扣量位置表，实现精确的扣量比例控制，替代了原有的随机数扣量方式。

## 🎯 核心原理

### 1. 计数器机制
- 每个渠道维护一个独立的Redis计数器
- 每次订单处理时，计数器自动递增
- 计数器键格式：`deduction_counter:channel:{channelId}`

### 2. 位置映射
- 将计数器值对20取模，得到余数（0-19）
- 根据扣量比例查找预定义的扣量位置列表
- 如果当前余数在扣量位置列表中，则执行扣量

### 3. 扣量位置表

| 扣量比例 | 扣量位置(余数0-19) | 说明 |
|---------|------------------|------|
| 5% | [0] | 每20个订单扣量1个 |
| 10% | [0, 10] | 每20个订单扣量2个 |
| 15% | [0, 6, 13] | 每20个订单扣量3个 |
| 20% | [0, 5, 10, 15] | 每20个订单扣量4个 |
| 25% | [0, 4, 8, 12, 16] | 每20个订单扣量5个 |
| 30% | [0, 3, 7, 9, 13, 17] | 每20个订单扣量6个 |
| ... | ... | ... |
| 95% | [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18] | 每20个订单扣量19个 |

## 🔧 算法实现

### 核心代码逻辑

```java
private boolean performDeduction(EccOuterChannelDO channel) {
    // 1. 获取扣量比例
    Integer deductionRate = channel.getDeductionRate();
    
    // 2. 获取扣量位置列表
    List<Integer> deductionPositions = DEDUCTION_POSITION_MAP.get(deductionRate);
    
    // 3. 递增Redis计数器
    String redisKey = "deduction_counter:channel:" + channel.getOuterChannelId();
    Long currentCount = incrementCounter(redisKey);
    
    // 4. 计算当前位置（取模20）
    int currentPosition = (int) ((currentCount - 1) % 20) + 1;
    
    // 5. 判断是否扣量
    return deductionPositions.contains(currentPosition);
}
```

## 📊 算法优势

### 1. 精确性
- **精确扣量比例**：每20个订单为一个周期，严格按照设定比例扣量
- **无随机性**：完全消除随机数带来的偏差
- **可预测性**：可以准确预测下一个订单是否会被扣量

### 2. 一致性
- **周期一致**：每个20订单周期的扣量次数完全一致
- **分布均匀**：扣量位置经过优化，分布相对均匀
- **可重现**：相同的计数器状态产生相同的扣量结果

### 3. 性能
- **高效计算**：只需要一次Redis操作和简单的取模运算
- **内存友好**：Redis计数器占用内存极小
- **可扩展**：支持大量渠道并发扣量计算

## 🛠️ 使用示例

### 1. 基本扣量计算
```java
// 创建渠道对象
EccOuterChannelDO channel = new EccOuterChannelDO();
channel.setOuterChannelId(123L);
channel.setDeductionRate(20); // 20%扣量

// 执行扣量计算
boolean deducted = deductionCalculationService.performDeduction(channel);
```

### 2. 获取扣量统计
```java
// 获取渠道扣量统计信息
Map<String, Object> stats = deductionCalculationService
    .getChannelDeductionStats(channelId, deductionRate);

System.out.println("当前计数器值: " + stats.get("currentCount"));
System.out.println("当前位置: " + stats.get("currentPosition"));
System.out.println("已完成周期数: " + stats.get("completedCycles"));
```

### 3. 预测下一个订单
```java
// 预测下一个订单是否会被扣量
boolean willDeduct = deductionCalculationService
    .predictNextDeduction(channelId, deductionRate);

System.out.println("下一个订单" + (willDeduct ? "会" : "不会") + "被扣量");
```

## 🔍 监控和管理

### 1. 管理接口
- `GET /api/deduction/counter/{channelId}` - 获取计数器值
- `POST /api/deduction/counter/{channelId}/reset` - 重置计数器
- `GET /api/deduction/stats/{channelId}` - 获取统计信息
- `GET /api/deduction/predict/{channelId}` - 预测下一个订单

### 2. 监控指标
- **计数器值**：当前渠道的订单计数
- **当前位置**：在20个位置中的当前位置
- **周期数**：已完成的完整周期数
- **扣量次数**：当前周期内的扣量次数

## ⚠️ 注意事项

### 1. Redis依赖
- 算法依赖Redis服务，需要确保Redis可用性
- 提供了降级方案：Redis失败时使用时间戳作为计数器

### 2. 计数器管理
- Redis计数器设置7天过期时间，避免内存无限增长
- 可以手动重置计数器来重新开始扣量周期

### 3. 扣量比例限制
- 目前支持5%-95%的扣量比例（5%递增）
- 不支持的扣量比例会记录警告日志并跳过扣量

## 🧪 测试验证

### 1. 精确度测试
```bash
# 运行精确度测试
mvn test -Dtest=DeductionCalculationServiceTest#testDeductionAccuracy
```

### 2. 一致性测试
```bash
# 运行多周期一致性测试
mvn test -Dtest=DeductionCalculationServiceTest#testMultipleCycles
```

### 3. 预测测试
```bash
# 运行预测准确性测试
mvn test -Dtest=DeductionCalculationServiceTest#testPredictNextDeduction
```

## 📈 性能对比

| 指标 | 原随机算法 | 新计数器算法 |
|------|-----------|-------------|
| 精确度 | 约±5% | 100%精确 |
| 可预测性 | 不可预测 | 完全可预测 |
| 性能 | 本地随机数 | Redis操作 |
| 一致性 | 无保证 | 严格一致 |
| 可监控性 | 难以监控 | 完全可监控 |

## 🔄 迁移指南

### 1. 部署前准备
- 确保Redis服务正常运行
- 配置Redis连接参数
- 备份现有扣量数据

### 2. 平滑迁移
- 新算法向下兼容，可以直接替换
- 建议在低峰期进行部署
- 部署后监控扣量比例是否正常

### 3. 验证步骤
- 使用测试接口验证算法正确性
- 监控实际扣量比例是否符合预期
- 检查Redis计数器是否正常递增
