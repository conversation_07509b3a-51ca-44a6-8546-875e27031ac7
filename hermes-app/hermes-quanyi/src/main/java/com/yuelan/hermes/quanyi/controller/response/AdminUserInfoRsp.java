package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class AdminUserInfoRsp {

    @Schema(description = "用户ID")
    private Long adminId;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "临时的权限解决方法，role=1表示管理员，2号卡渠道")
    private Integer role;

    @Schema(description = "是否是最底层外部渠道")
    private Boolean isBottomOutChannel;

}
