package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccRedeemCodeDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> 2024/5/14 下午7:53
 */
@Data
public class EccRedeemResp {

    @Schema(description = "兑换码")
    private String redeemCodeKey;

    @Schema(description = "兑换码卡密")
    private String redeemCodePassword;

    public static EccRedeemResp buildResp(EccRedeemCodeDO redeemCodeDO) {
        EccRedeemResp resp = new EccRedeemResp();
        if (Objects.nonNull(redeemCodeDO)) {
            resp.setRedeemCodeKey(redeemCodeDO.getRedeemCodeKey());
            resp.setRedeemCodePassword(resp.getRedeemCodePassword());
        }
        return resp;
    }

}
