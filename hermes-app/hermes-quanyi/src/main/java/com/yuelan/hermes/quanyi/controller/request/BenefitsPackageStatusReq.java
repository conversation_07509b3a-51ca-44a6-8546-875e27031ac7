package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.commons.enums.UpOffStatusEnum;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class BenefitsPackageStatusReq {

    @NotNull(message = "权益包id不能为空", groups = {EditGroup.class})
    @Schema(description = "主键id")
    private Long packageId;

    @NotNull(message = "权益包状态不能为空", groups = {EditGroup.class})
    @Schema(description = "状态：0-下架，1-上架")
    private Integer status;

    public void checkReq() {
        UpOffStatusEnum statusEnum = LocalEnumUtils.findByCode(UpOffStatusEnum.class, status);
        if (Objects.isNull(statusEnum) || !Objects.equals(statusEnum.getCode(), status)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "上下架状态值不合法");
        }
    }

}
