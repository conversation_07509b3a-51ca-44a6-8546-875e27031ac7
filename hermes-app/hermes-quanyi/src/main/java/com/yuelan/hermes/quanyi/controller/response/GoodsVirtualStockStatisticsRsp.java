package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class GoodsVirtualStockStatisticsRsp {

    @Schema(description = "实际库存")
    private Integer realAmount = 0;

    @Schema(description = "已售数量")
    private Integer sellAmount = 0;

    @Schema(description = "有效库存")
    private Integer effectiveAmount = 0;

    @Schema(description = "即将过期库存")
    private Integer waitExpiresAmount = 0;

    @Schema(description = "已过期库存")
    private Integer expiresAmount = 0;

    @Schema(description = "已禁用库存")
    private Integer disableAmount;

}
