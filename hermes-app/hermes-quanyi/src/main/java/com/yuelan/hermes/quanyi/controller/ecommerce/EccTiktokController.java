package com.yuelan.hermes.quanyi.controller.ecommerce;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.manager.TikTokManager;
import com.yuelan.hermes.quanyi.common.util.NumCardPromptPolishingUtil;
import com.yuelan.hermes.quanyi.controller.request.EccTiktokReceiveCardReq;
import com.yuelan.hermes.quanyi.controller.response.EccTiktokOrderCreateResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/9/6 16:18
 */
@Tag(name = "电商卡/app/抖音领卡")
@SaIgnore
@Slf4j
@Validated
@RestController
@RequestMapping("/ecc/tiktok")
@AllArgsConstructor
public class EccTiktokController {

    private final TikTokManager tikTokManager;

    @Operation(summary = "创建抖音订单", description = "先领取电商卡，再创建订单")
    @PostMapping("/createOrder")
    public BizResult<EccTiktokOrderCreateResp> createOrder(@RequestBody @Validated EccTiktokReceiveCardReq req) {
        EccTiktokOrderCreateResp resp;
        try {
            resp = tikTokManager.buildRequestOrder(req);
        } catch (BizException e) {
            e.setMsg(NumCardPromptPolishingUtil.getPrompt(e.getMsg()));
            throw e;
        }
        return BizResult.create(resp);
    }
}
