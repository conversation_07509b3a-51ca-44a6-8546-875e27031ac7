package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class RechargerOrderUnifiedReq extends SignReq {

    @NotBlank(message = "手机号不能为空")
    @Schema(description = "充值的手机号")
    private String mobile;

    @NotBlank(message = "商户订单号不能为空")
    @Schema(description = "商户订单号")
    private String outTradeNo;

    @NotBlank(message = "商品编号不能为空")
    @Schema(description = "商品编号")
    private String skuNo;

    @NotBlank(message = "回调地址不能为空")
    @Schema(description = "回调地址")
    private String notifyUrl;
}
