package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BWOrderNotifyReq {

    @Schema(description = "商户编号")
    private Long bank_id;

    @Schema(description = "订单创建时间")
    private String create_time;

    @Schema(description = "商品编号")
    private Long goods_id;

    @Schema(description = "商品名称")
    private String goods_name;

    @Schema(description = "商品类型")
    private String goods_type;

    @Schema(description = "是否游戏订单")
    private Integer is_game;

    @Schema(description = "变蛙订单号")
    private String order_no;

    @Schema(description = "订单状态")
    private String order_status;

    @Schema(description = "商户订单号")
    private String out_trade_no;

    @Schema(description = "产品编号")
    private Long product_id;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "结算金额")
    private BigDecimal total_pay_amount;

    @Schema(description = "签名")
    private String sign;

    @Schema(description = "肯德基卡密信息")
    private String coupon;

    @Data
    public static class Coupon {

        @Schema(description = "卡号|券号")
        private String cardId;

        @Schema(description = "券SN,线下核销使用")
        private String snCode;

        @Schema(description = "卡密")
        private String cardPwd;

        @Schema(description = "短网址（仅动态码时启用）")
        private String cardDwz;

        @Schema(description = "生效日期,时间戳（13位）")
        private Long effectStartTime;

        @Schema(description = "失效日期,时间戳（13位）")
        private Long expireTime;

        @Schema(description = "卡券状态")
        private String cardStatus;

        @Schema(description = "券码展示方式")
        private Integer couponDisplayType;

        @Schema(description = "可用门店列表url")
        private String shopUrl;
    }
}
