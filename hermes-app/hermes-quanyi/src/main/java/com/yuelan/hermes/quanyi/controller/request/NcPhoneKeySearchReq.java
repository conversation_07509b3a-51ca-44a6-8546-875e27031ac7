package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/9/10 18:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class NcPhoneKeySearchReq extends PageRequest {
    @NotEmpty(message = "[产品编码]不能为空")
    @Schema(description = "产品编码")
    private String eccProdCode;

    @Schema(description = "归属地省份代码，不传表示随机一个可用的省")
    private String provinceCode;

    @Schema(description = "归属地城市代码，不传表示随机一个可用的市")
    private String cityCode;

    @Schema(description = "查询关键字按实际卡品类规则填写")
    private String phoneKey;
}
