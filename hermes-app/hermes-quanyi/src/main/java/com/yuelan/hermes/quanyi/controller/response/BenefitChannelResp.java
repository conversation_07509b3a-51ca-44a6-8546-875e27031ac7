package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitChannelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> 2024/5/3 下午2:53
 */
@Data
public class BenefitChannelResp {
    @Schema(description = "渠道id")
    private Long channelId;

    @Schema(description = "渠道名字")
    private String channelName;

    public static BenefitChannelResp buildResp(BenefitChannelDO benefitChannelDO) {
        BenefitChannelResp resp = new BenefitChannelResp();
        resp.setChannelId(benefitChannelDO.getChannelId());
        resp.setChannelName(benefitChannelDO.getChannelName());
        return resp;
    }
}
