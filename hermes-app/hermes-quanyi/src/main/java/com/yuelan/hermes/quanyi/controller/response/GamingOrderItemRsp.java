package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.ObtainStatusEnum;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PreorderStatusEnum;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.commons.excel.DeliveryTypeConverter;
import com.yuelan.hermes.commons.excel.KeyValueConverter;
import com.yuelan.hermes.commons.excel.LocalDateTimeConverter;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO;
import com.yuelan.hermes.quanyi.common.util.RedeemCodeDesensitizedUtil;
import com.yuelan.result.entity.KeyValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;

/**
 * 电竞卡订单明细
 */
@Data
public class GamingOrderItemRsp {

    @ExcelIgnore
    @Schema(description = "订单明细ID")
    private Long itemId;

    @ColumnWidth(20)
    @ExcelProperty(value = "商品订单号")
    @Schema(description = "订单明细单号")
    private String itemNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "产品订单号")
    @Schema(description = "产品订单号")
    private String orderNo;

    @ExcelIgnore
    @Schema(description = "商户订单号")
    private String outOrderNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "供应商订单号")
    @Schema(description = "供应商订单号")
    private String supplierOrderNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "产品ID")
    @Schema(description = "产品id")
    private Long productId;

    @ColumnWidth(20)
    @ExcelProperty(value = "产品名称")
    @Schema(description = "产品名字")
    private String productName;

    @ColumnWidth(20)
    @ExcelProperty(value = "商品ID")
    @Schema(description = "商品ID")
    private Long goodsId;

    @ColumnWidth(20)
    @ExcelProperty(value = "商品名称")
    @Schema(description = "商品名称")
    private String goodsName;

    @ColumnWidth(20)
    @ExcelProperty(value = "供应商", converter = KeyValueConverter.class)
    @Schema(description = "供应商类型")
    private KeyValue<Integer, String> supplierType;


    @ColumnWidth(20)
    @ExcelProperty(value = "供应商商品编号")
    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "手机号码")
    @Schema(description = "手机号码")
    private String phone;

    @ColumnWidth(20)
    @ExcelProperty(value = "订单状态", converter = KeyValueConverter.class)
    @Schema(description = "订单状态")
    private KeyValue<Integer, String> orderStatus;

    @ColumnWidth(20)
    @ExcelProperty(value = "预下单状态", converter = KeyValueConverter.class)
    @Schema(description = "下发供应商状态")
    private KeyValue<Integer, String> preorderStatus;

    @ColumnWidth(20)
    @ExcelProperty(value = "领取状态", converter = KeyValueConverter.class)
    @Schema(description = "权益领取状态")
    private KeyValue<Integer, String> obtainStatus;

    @ExcelIgnore
    @Schema(description = "备注")
    private String remark;

    @ColumnWidth(20)
    @ExcelProperty(value = "领取时间", converter = LocalDateTimeConverter.class)
    @Schema(description = "权益领取时间")
    private LocalDateTime obtainTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "发货方式", converter = DeliveryTypeConverter.class)
    @Schema(description = "发放方式：1-直充，2-兑换码")
    private Integer deliveryType;

    @ColumnWidth(20)
    @ExcelProperty(value = "兑换码")
    @Schema(description = "领取的兑换码（已脱敏）")
    private String redeemCode;

    @ColumnWidth(20)
    @ExcelProperty(value = "兑换码过期时间")
    @Schema(description = "兑换码过期时间")
    private LocalDate redeemCodeExpireDate;

    @ColumnWidth(20)
    @ExcelProperty(value = "领取备注")
    @Schema(description = "权益领取备注")
    private String obtainRemark;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间")
    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "更新时间")
    @Schema(description = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    public static GamingOrderItemRsp buildRsp(GamingOrderItemDO obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        GamingOrderItemRsp gamingOrderItemRsp = new GamingOrderItemRsp();
        gamingOrderItemRsp.setItemId(obj.getId());
        gamingOrderItemRsp.setItemNo(obj.getItemNo());
        gamingOrderItemRsp.setOrderNo(obj.getOrderNo());
        gamingOrderItemRsp.setOutOrderNo(obj.getOutOrderNo());
        gamingOrderItemRsp.setProductId(obj.getProductId());
        gamingOrderItemRsp.setProductName(obj.getProductName());
        gamingOrderItemRsp.setGoodsId(obj.getGoodsId());
        gamingOrderItemRsp.setGoodsName(obj.getGoodsName());
        gamingOrderItemRsp.setSupplierType(LocalEnumUtils.getEnumKeyValue(SupplierEnum.class, obj.getSupplierType()));
        gamingOrderItemRsp.setSupplierOrderNo(obj.getSupplierOrderNo());
        gamingOrderItemRsp.setSupplierGoodsNo(obj.getSupplierGoodsNo());
        gamingOrderItemRsp.setOrderStatus(LocalEnumUtils.getEnumKeyValue(OrderStatusEnum.class, obj.getOrderStatus()));
        gamingOrderItemRsp.setPhone(obj.getPhone());
        gamingOrderItemRsp.setPreorderStatus(LocalEnumUtils.getEnumKeyValue(PreorderStatusEnum.class, obj.getPreorderStatus()));
        gamingOrderItemRsp.setObtainStatus(LocalEnumUtils.getEnumKeyValue(ObtainStatusEnum.class, obj.getObtainStatus()));
        gamingOrderItemRsp.setObtainTime(obj.getObtainTime());
        gamingOrderItemRsp.setDeliveryType(obj.getDeliveryType());
        gamingOrderItemRsp.setRedeemCode(RedeemCodeDesensitizedUtil.desensitized(obj.getRedeemCode()));
        gamingOrderItemRsp.setRedeemCodeExpireDate(obj.getRedeemCodeExpireDate());
        gamingOrderItemRsp.setRemark(obj.getRemark());
        gamingOrderItemRsp.setObtainRemark(obj.getObtainRemark());
        gamingOrderItemRsp.setCreateTime(obj.getCreateTime());
        gamingOrderItemRsp.setUpdateTime(obj.getUpdateTime());
        return gamingOrderItemRsp;
    }

}