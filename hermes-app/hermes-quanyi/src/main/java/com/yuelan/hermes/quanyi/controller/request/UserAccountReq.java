package com.yuelan.hermes.quanyi.controller.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@ContentRowHeight(25)
@HeadRowHeight(35)
public class UserAccountReq {

    @ColumnWidth(20)
    @ExcelProperty(value = "用户手机号")
    @Schema(description = "用户手机号")
    private String phone;

    @ColumnWidth(20)
    @ExcelProperty(value = "充值账号")
    @Schema(description = "充值账号")
    private String userAccount;
}