package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.controller.response.EccPostAreaResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 2024/5/3 下午2:46
 */
@Data
public class EccProdSaveReq {

    /**
     * 产品id
     */
    @NotNull(message = "产品id不能为空", groups = EditGroup.class)
    @Schema(description = "产品id")
    private Long prodId;

    /**
     * 产品名字
     */
    @NotNull(message = "产品名字不能为空", groups = AddGroup.class)
    @Schema(description = "产品名字")
    private String prodName;

    /**
     * 销售价格
     */
    @Schema(description = "销售价格")
    private BigDecimal price;

    /**
     * 运营商产品id
     */
    @Schema(description = "运营商产品id")
    @NotNull(message = "运营商产品id不能为空", groups = AddGroup.class)
    private Integer spProdId;

    /**
     * 运营商商品id
     */
    @Schema(description = "运营商商品id")
    @NotNull(message = "运营商商品id不能为空", groups = AddGroup.class)
    private String spGoodsId;

    /**
     * 基础投放链接
     */
    @NotNull(message = "基础投放链接不能为空", groups = AddGroup.class)
    @Schema(description = "基础投放链接")
    private String distributionUrl;

    /**
     * 禁止区域
     */
    @Schema(title = "禁止区域", description = "逗号分割最后一个字必须表名是省还是市", example = "新疆省,上海市", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String prohibitedAreas;

    /**
     * 归属地类型
     */
    @NotNull(message = "归属地类型不能为空", groups = {AddGroup.class, EditGroup.class})
    @Schema(title = "归属地类型", description = "0-全国 1-分省 2-分市", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer regionType;


    @Schema(title = "勾选的归属地", description = "如果归属地是省，则传入所有省的 numCode，如果是市则所有市区的 numCode（不含省）", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<EccPostAreaResp> regionList;

    public EccProductDO convert() {
        EccProductDO productDO = new EccProductDO();
        productDO.setProdId(prodId);
        productDO.setProdName(prodName);
        productDO.setPrice(price);
        productDO.setSpProdId(spProdId);
        productDO.setSpGoodsId(spGoodsId);
        productDO.setDistributionUrl(distributionUrl);
        productDO.setProhibitedAreas(prohibitedAreas);
        return productDO;
    }
}
