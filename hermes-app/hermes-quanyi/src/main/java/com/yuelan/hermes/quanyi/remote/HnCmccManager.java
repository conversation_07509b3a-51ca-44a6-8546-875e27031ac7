package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.commons.util.ExecutorServiceUtils;
import com.yuelan.hermes.quanyi.biz.manager.AdManager;
import com.yuelan.hermes.quanyi.biz.service.AdChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.biz.service.BenefitPlatformService;
import com.yuelan.hermes.quanyi.common.enums.*;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitUnicomPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.AdChannelDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.HnCmccProperties;
import com.yuelan.hermes.quanyi.common.util.HnRSAUtils;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.controller.response.HnCmccResp;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @version V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HnCmccManager {

    public static final String TAG = "[湖南移动]";
    private static final String TRUE = "0";
    // 发送短信验证码
    private static final String GET_SMS_CODE = "DQ_HT_HNAN_sendSmsVerifyCodeOut";
    // 业务办理
    private static final String ORDER = "DQ_HT_HNAN_ProductChangeOut";

    private static final int TIME_OUT = 10000;
    @Autowired
    private HnCmccProperties hnCmccProperties;
    @Resource
    @Lazy
    private BenefitPlatformService benefitPlatformService;

    @Resource
    private AdChannelDOService adChannelDOService;
    @Resource
    private AdManager adManager;
    @Resource
    private BenefitOrderLogService benefitOrderLogService;

    /**
     * 发送验证码短信
     *
     * @param reqParams 请求参数
     * @param productDO 商品信息
     */
    public boolean orderSendSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        String mobile = reqParams.getMobile();
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        String prodId = hnCmccProperties.getProperties(HnCmccPayPkgEnum.of(productDO.getPayChannelPkgId())).getDISCNT_CODE();
        return this.sendSmsCode(mobile, prodId);
    }

    /**
     * 订阅下单
     *
     * @param req       请求参数
     * @param productDO 商品信息
     * @param orderDO   下单信息
     */
    public BenefitPayResultBO createOrder(UserBenefitOrderReq req, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        String mobile = req.getMobile();
        String smsCode = req.getSmsCode();

        BenefitUnicomPayResultBO payResultBO = new BenefitUnicomPayResultBO();
        payResultBO.setSuccess(false);
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(smsCode)) {
            log.info(TAG + "手机号或者验证码为空");
            payResultBO.setMessage("暂时无法订阅");
        }
        String prodId = hnCmccProperties.getProperties(HnCmccPayPkgEnum.of(productDO.getPayChannelPkgId())).getDISCNT_CODE();
        if (StringUtils.isEmpty(prodId)) {
            log.info(TAG + "业务产品编码为空");
            payResultBO.setMessage("暂时无法订阅");
        }
        HnCmccResp res = this.productOrder(mobile, smsCode, orderDO.getOrderNo(), prodId);
        if (TRUE.equals(res.getRespCode())) {
            String tradeId = res.getResult().get(0).getString("TRADE_ID");
            if (StringUtils.isNotBlank(tradeId)) {
                payResultBO.setSuccess(true);
                // 更新订单状态
                orderDO.setPayStatus(PayStatusEnum.SUCCESS.getCode());
                orderDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
                orderDO.setOutOrderNo(tradeId);
                // 用户支付成功事件
                sendPaySuccessEvent(orderDO);
            } else {
                String resultInfo = res.getResult().get(0).getString("RESULT_INFO");
                payResultBO.setMessage(resultInfo);
                orderDO.setPayStatus(PayStatusEnum.FAIL.getCode());
            }
        } else {
            payResultBO.setMessage(res.getRespDesc());
            orderDO.setPayStatus(PayStatusEnum.FAIL.getCode());
        }
        orderDO.setPayNotifyContent(JSON.toJSONString(res));
        orderDO.setPayNotifyTime(LocalDateTime.now());
        ExecutorServiceUtils.execute(() -> {
            try {
                // 先睡5秒,这里是为了让订单号先返回给媒体,等媒体处理好自己的流程后,再通知给媒体
                Thread.sleep(5000);
                // 通知媒体
                benefitPlatformService.notifyMediaOrderResult(orderDO, res.getRespDesc());
            } catch (Exception e) {
                log.info(TAG + "通知媒体失败.订单id:{}", orderDO.getOrderId(), e);
            }
        });
        return payResultBO;
    }

    /**
     * 业务受理
     *
     * @param mobile   手机号
     * @param randCode 验证码
     * @param orderId  订单号
     */
    private HnCmccResp productOrder(String mobile, String randCode, String orderId, String prodId) {
        TreeMap<String, String> params = new TreeMap<>();
        params.put("SERIAL_NUMBER", mobile);
        params.put("DISCNT_CODE", prodId);
        params.put("SMS_CODE", randCode);
        params.put("OUT_TRADE_ID", orderId);
        HnCmccResp res = this.sendRemoteRequest(ORDER, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER, mobile, orderId));
        if (res == null) {
            throw BizException.create(BizErrorCodeEnum.ORDER_UNIFIED_ERROR);
        }
        return res;
    }

    /**
     * 运营商下发短信验证码
     */
    private boolean sendSmsCode(String mobile, String prodId) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        TreeMap<String, String> params = new TreeMap<>();
        params.put("SERIAL_NUMBER", mobile);
        params.put("TRADE_TYPE_CODE", "110");
        params.put("DISCNT_CODE", prodId);
        HnCmccResp res = this.sendRemoteRequest(GET_SMS_CODE, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_SEND, mobile));
        if (res == null) {
            return false;
        }
        if (TRUE.equals(res.getRespCode()) && res.getResult().get(0).getString("RESULT_CODE").equals(TRUE)) {
            return true;
        } else {
            log.info(TAG + "发送短信验证码失败.手机号:{} 失败原因:{}", mobile, JSON.toJSONString(res));
            throw BizException.create(BizErrorCodeEnum.SMS_SEND_ERROR, StringUtils.defaultString(res.getResult().get(0).getString("RESULT_INFO"), res.getRespDesc()));
        }
    }


    /**
     * 发送远程接口
     *
     * @param path   接口路径
     * @param params 参数
     * @return 发送结果
     */
    private HnCmccResp sendRemoteRequest(String path, TreeMap<String, String> params, BenefitOrderLog.Args args) {
        String url = hnCmccProperties.getHost();
        // 拼接链接固定参数
        Map<String, Object> pathParams = new HashMap<>();
        pathParams.put("method", path);
        pathParams.put("appId", hnCmccProperties.getAppId());
        pathParams.put("flowdId", "YL" + DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_FORMAT));
        pathParams.put("PROVINCE_CODE", "HNAN");
        pathParams.put("IN_MODE_CODE", "3");
        pathParams.put("TRADE_EPARCHY_CODE", "INTF");
        pathParams.put("TRADE_CITY_CODE", "INTF");
        pathParams.put("status", hnCmccProperties.getStatus());
        pathParams.put("TRADE_DEPART_ID", hnCmccProperties.getTRADE_DEPART_ID());
        pathParams.put("TRADE_STAFF_ID", hnCmccProperties.getTRADE_STAFF_ID());
        pathParams.put("TRADE_DEPART_PASSWD", hnCmccProperties.getTRADE_DEPART_PASSWD());
        url = url + "?" + MapUtil.join(pathParams, "&", "=");

        log.info(TAG + "加密前参数:{}", params);
        String reqBody = JSON.toJSONString(params);
        // 对参数进行加密
        params.forEach((k, v) -> {
            try {
                if (!"OUT_TRADE_ID".equals(k)) {
                    String encrypt = HnRSAUtils.encryptByPublicKey(v, hnCmccProperties.getPublicKey());
                    params.replace(k, encrypt);
                }
            } catch (Exception e) {
                log.error(TAG + "加密错误:", e);
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "系统繁忙，请稍后再试");
            }
        });

        args.setReqParams(reqBody);

        String jsonBody = JSON.toJSONString(params);
        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url)
                .contentType(ContentType.JSON.getValue())
                .body(jsonBody)
                .timeout(TIME_OUT)
                .log(BenefitPayChannelEnum.HN_CMCC, args);


        try {
            log.info(TAG + "平台请求运营商之前:{}", requestWrapper.getHttpRequest());
            HttpResponse response = benefitOrderLogService.http(requestWrapper);
            log.info(TAG + "平台请求运营商之后:{}", response);
            if (response.isOk()) {
                return JSON.parseObject(response.body(), HnCmccResp.class);
            }
            return null;
        } catch (Exception e) {
            log.info(TAG + "接口:{} 请求参数:{}", url, JSON.toJSONString(params), e);
            return null;
        }
    }

    /**
     * 支付成功广告追踪转化事件上报
     */
    private void sendPaySuccessEvent(BenefitOrderDO orderDO) {
        if (orderDO == null || orderDO.getAdChannelId() == null || StringUtils.isEmpty(orderDO.getAdExt())) {
            return;
        }
        JSONObject adExt = JSONObject.parseObject(orderDO.getAdExt());
        AdChannelDO adChannel = adChannelDOService.getByModuleAndAdChannelId(SysModuleEnum.BENEFIT, orderDO.getAdChannelId());
        AdChannelCodeEnum adChannelCodeEnum = AdChannelCodeEnum.of(adChannel.getAdChannelCode());
        BenefitTrackingEventEnum paySuccessTrackingEventEnum = BenefitTrackingEventEnum.PAY_SUCCESS;
        adManager.adCallBackHandlerCall(adChannelCodeEnum, adExt, paySuccessTrackingEventEnum, Boolean.FALSE);
    }

}
