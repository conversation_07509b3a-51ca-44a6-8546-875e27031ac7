package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOrderDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/5/4 上午12:58
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class EccOrderListReq extends PageRequest {

    /**
     * 电商卡权益包产品id
     */
    @Schema(description = "电商卡权益包产品id")
    private Long prodId;

    @Schema(description = "电商卡权益包产品名字")
    private String prodName;
    /**
     * 渠道类型
     *
     * @see com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum
     */
    @Schema(description = "渠道类型:1-内部渠道,2-外部渠道")
    private Integer channelType;

    /**
     * 渠道id
     */
    @Schema(title = "渠道id", description = "渠道id搜索时候，必传渠道类型")
    private Long channelId;

    /**
     * 激活时间
     */
    @Schema(description = "激活时间开始，格式：yyyy-MM-dd")
    private LocalDate activationTimeStart;

    /**
     * 激活时间
     */
    @Schema(description = "激活时间结束，格式：yyyy-MM-dd")
    private LocalDate activationTimeEnd;

    /**
     * 月份
     */
    @Schema(description = "月份时间字符串，格式yyyy-MM")
    @NotBlank(message = "月份不能为空")
    private String monthTime;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;

    /**
     * 是否可兑换
     */
    @Schema(description = "是否可兑换：0-不可兑换；1-可兑换")
    private Integer redeemable;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态：0-处理中（可汽换的默认状态）1-交易成功（全部领取成功）2-交易失败（不可兑换的默认状态）3-订单异常（部分领取失败")
    private Integer orderStatus;


    public Wrapper<EccOrderDO> buildQueryWrapper() {
        LocalDateTime activationTimeStart = this.activationTimeStart != null ? this.activationTimeStart.atStartOfDay() : null;
        LocalDateTime activationTimeEnd = this.activationTimeEnd != null ? this.activationTimeEnd.plusDays(1).atStartOfDay() : null;

        return Wrappers.lambdaQuery(EccOrderDO.class)
                .eq(prodId != null, EccOrderDO::getProdId, prodId)
                .like(StrUtil.isNotBlank(prodName), EccOrderDO::getProdName, prodName)
                .eq(channelType != null, EccOrderDO::getChannelType, channelType)
                .eq(channelId != null && channelType != null, EccOrderDO::getChannelId, channelId)
                .eq(StrUtil.isNotBlank(phone), EccOrderDO::getPhone, phone)
                .eq(redeemable != null, EccOrderDO::getRedeemable, redeemable)
                .eq(orderStatus != null, EccOrderDO::getOrderStatus, orderStatus)
                .ge(activationTimeStart != null, EccOrderDO::getActivationTime, activationTimeStart)
                .lt(activationTimeEnd != null, EccOrderDO::getActivationTime, activationTimeEnd)
                .orderByDesc(EccOrderDO::getOrderId);
    }
}
