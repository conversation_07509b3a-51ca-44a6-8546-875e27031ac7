package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.net.URLEncodeUtil;
import com.alibaba.fastjson2.JSONObject;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.request.TmallPurchaseCardBuyRequest;
import com.taobao.api.request.TopAuthTokenCreateRequest;
import com.taobao.api.response.TmallPurchaseCardBuyResponse;
import com.taobao.api.response.TopAuthTokenCreateResponse;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.properties.EccAlibabaTopProperties;
import com.yuelan.hermes.quanyi.remote.request.TMallCardRechargeReq;
import com.yuelan.hermes.quanyi.remote.response.AlibabaTokenResult;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR> 2024/5/6 下午4:38
 * 阿里巴巴TOP接口 对接自：docs/文档/电商卡/淘宝/超市卡TopApi接入手册（对外版）.pdf
 * 流程：
 * 1.兑换H5 跳转淘宝授权登录 前端拿到code
 * 2.前端传code到后端 后端拿到用户access_token 通过access_token获取用户信息 完成登录
 * 3.用户手机验证码绑定当前淘宝账号
 * 4.领取权益下发到淘宝账号
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaoBaoTopManager {

    private final EccAlibabaTopProperties alibabaTopProperties;

    /**
     * 获取淘宝授权登录url
     * 文档：<a href="https://open.taobao.com/doc.htm?docId=102635&docType=1">...</a>
     *
     * @param redirectUrl 前端回调地址
     */
    public String getOauthUrl(String redirectUrl) {
        String oauthUrl = alibabaTopProperties.getOauthUrl();
        oauthUrl += "?client_id=" + alibabaTopProperties.getAppKey()
                + "&response_type=code"
                // 无需urlencode ??
                + "&redirect_uri=" + URLEncodeUtil.encode(redirectUrl, StandardCharsets.UTF_8)
                + "&view=wap";
        return oauthUrl;
    }

    /**
     * 构建猫超卡采购请求
     */
    private static TmallPurchaseCardBuyRequest getTmallPurchaseCardBuyRequest(TMallCardRechargeReq rechargeReq) {
        TmallPurchaseCardBuyRequest request = new TmallPurchaseCardBuyRequest();
        TmallPurchaseCardBuyRequest.CardBuyRequest buyRequest = new TmallPurchaseCardBuyRequest.CardBuyRequest();
        // buyRequest.setRechargeAccount(rechargeReq.getRechargeAccount());
        buyRequest.setAmount(rechargeReq.getAmount());
        // 单位分 测试环境只支持2分
        buyRequest.setParValue(rechargeReq.getParValue());
        buyRequest.setOuterOrderId(rechargeReq.getPreventDuplicationOrderNo());
        buyRequest.setBuyerOpenUid(rechargeReq.getTaobaoOpenUid());
        // 卡类型，必填(1: 猫超卡，2:品类消费金)
        buyRequest.setCardType(1L);
        request.setCardBuyReq(buyRequest);
        return request;
    }

    /**
     * code 来换取access_token
     * 文档：<a href="https://open.taobao.com/v2/doc?spm=a219a.********.0.0.3617669a3fnx2Y#/apiFile?docType=2&docId=25388">...</a>
     *
     * @param code 前端拿到的code
     */
    public AlibabaTokenResult getAccessToken(String code) {
        String url = alibabaTopProperties.getGatewayUrl();
        String appKey = alibabaTopProperties.getAppKey();
        String secret = alibabaTopProperties.getAppSecret();
        TaobaoClient client = new DefaultTaobaoClient(url, appKey, secret);
        TopAuthTokenCreateRequest req = new TopAuthTokenCreateRequest();
        req.setTopApiFormat("json");
        req.setCode(code);
        try {
            TopAuthTokenCreateResponse rsp = client.execute(req);
            if (!rsp.isSuccess()) {
                log.error("获取access_token失败:{}", rsp.getBody());
                throw BizException.create(BizErrorCodeEnum.TAO_BAO_ERROR, "获取access_token失败:isSuccess() =false");
            }
            String tokenResult = rsp.getTokenResult();
            AlibabaTokenResult alibabaTokenResult = JSONObject.parseObject(tokenResult, AlibabaTokenResult.class);
            alibabaTokenResult.setTaoBaoUserNick(URLDecoder.decode(alibabaTokenResult.getTaoBaoUserNick(), StandardCharsets.UTF_8));
            return alibabaTokenResult;
        } catch (Exception e) {
            log.error("获取access_token异常", e);
        }
        return null;

    }

    /**
     * 重要说明：
     * 淘宝接口有订单号去重功能，多次充值如果用同一订单号不会重复发放
     * <p>
     * 猫超卡采购并且发送给指定淘宝用户（tmall.purchase.card.buy）
     * ps:也可以通过buyer_open_uid
     * 文档:<a href="https://open.taobao.com/api.htm?docId=27554&docType=2">...</a>
     *
     * @param rechargeReq 充值请求
     */
    public TmallPurchaseCardBuyResponse purchaseAndRecharge(TMallCardRechargeReq rechargeReq) {
        if (!AppConstants.isReal()) {
            log.info("模拟淘宝充值请求成功{}", JSONObject.toJSONString(rechargeReq));
            return new TmallPurchaseCardBuyResponse();
        }
        String url = alibabaTopProperties.getGatewayUrl();
        String appKey = alibabaTopProperties.getAppKey();
        String secret = alibabaTopProperties.getAppSecret();
        TaobaoClient client = new DefaultTaobaoClient(url, appKey, secret);
        log.info("猫超卡采购请求:{}", JSONObject.toJSONString(rechargeReq));
        TmallPurchaseCardBuyRequest request = getTmallPurchaseCardBuyRequest(rechargeReq);
        try {
            TmallPurchaseCardBuyResponse response = client.execute(request);
            TmallPurchaseCardBuyResponse.PurchaseGatewayResponse result = response.getResult();
            if (!result.getSuccess()) {
                log.error("猫超卡采购失败:{}", response.getBody());
            }
            log.info("猫超卡采购结果:{}", JSONObject.toJSONString(result));
            return response;
        } catch (Exception e) {
            log.error("猫超卡采购异常", e);
        }
        return null;
    }


    //{
    //     "w1_expires_in": 15552000,
    //     "refresh_token_valid_time": 1715172424641,
    //     "taobao_user_nick": "alice9nine",
    //     "re_expires_in": 0,
    //     "expire_time": 1730724424641,
    //     "token_type": "Bearer",
    //     "access_token": "6200811e7645ecc41d7ZZb76aafbad9e4bf1efec8b00d87721561150",
    //     "taobao_open_uid": "AAEFL6ALAOVVkA9sXqMrKCrG",
    //     "w1_valid": 1730724424641,
    //     "refresh_token": "6200f11f17f4f97ad53ZZ278e39d5e4fa1c3cbd37e66da7721561150",
    //     "w2_expires_in": 15552000,
    //     "w2_valid": 1730724424641,
    //     "r1_expires_in": 15552000,
    //     "r2_expires_in": 15552000,
    //     "r2_valid": 1730724424641,
    //     "r1_valid": 1730724424641,
    //     "taobao_user_id": "721561150",
    //     "expires_in": 15552000
    // }

    public void tradeFullInfo() {
        String url = alibabaTopProperties.getGatewayUrl();
        String appKey = alibabaTopProperties.getAppKey();
        String secret = alibabaTopProperties.getAppSecret();
        TaobaoClient client = new DefaultTaobaoClient(url, appKey, secret);
    }

}
