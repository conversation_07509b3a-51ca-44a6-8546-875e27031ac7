package com.yuelan.hermes.quanyi.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.service.SxThirdOrderDOService;
import com.yuelan.hermes.quanyi.biz.service.SxXiMaDataDOService;
import com.yuelan.hermes.quanyi.controller.request.SxThirdOrderReq;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Validated
@RestController
@Tag(name = "电商卡/后台api/权益包订单")
@RequiredArgsConstructor
@RequestMapping("/xima/data")
public class SxXiMaDataController {

    @Resource
    private SxXiMaDataDOService sxXiMaDataDOService;

    @Resource
    private SxThirdOrderDOService sxThirdOrderDOService;

    @Operation(summary = "书香卡喜马拉雅数据回调")
    @SaIgnore
    @PostMapping(value = "/ximalaya/order_status_notify")
    public BizResult<T> orderStatusNotifya(@RequestBody JSONObject params) {
        sxXiMaDataDOService.orderStatusNotify(params);
        return BizResult.ok();
    }
    @Operation(summary = "书香卡三方渠道订单回调")
    @SaIgnore
    @PostMapping(value = "third/order_status_notify")
    public BizResult<T> thirdOrderStatusNotify(@RequestBody @Valid SxThirdOrderReq params) {
        sxThirdOrderDOService.thirdOrderStatusNotify(params);
        return BizResult.ok();
    }
}
