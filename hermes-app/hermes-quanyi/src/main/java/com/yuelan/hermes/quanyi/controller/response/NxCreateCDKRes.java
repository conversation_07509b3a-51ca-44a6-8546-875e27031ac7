package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2025/1/18
 * @description NxCreateCDKRes
 */

@NoArgsConstructor
@Data
public class NxCreateCDKRes {

    @Schema(description = "10000，非10000时详见错误码")
    private String code;
    @Schema(description = "true")
    private boolean success;
    @Schema(description = "业务数据")
    private DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {

        @Schema(description = "属地权益平台分配给业务平台的appid")
        private String appid;
        @Schema(description = "通过签名算法计算出的签名值")
        private String sign;
        @Schema(description = "随机字符串")
        private String nonce_str;
        @Schema(description = "属地权益平台单号")
        private String trade_no;
        @Schema(description = "第三方权益平台订单号")
        private String out_trade_no;
        @Schema(description = "卡券信息")
        private List<CouponListDTO> coupon_list;

        @NoArgsConstructor
        @Data
        public static class CouponListDTO {
            @Schema(description = "券码")
            private String coupon_code;
            @Schema(description = "券码名称")
            private String coupon_name;
            @Schema(description = "券码创建时间 yyyyMMddHHmmss")
            private String create_time;
            @Schema(description = "券码失效时间 yyyyMMddHHmmss")
            private String end_time;
        }
    }
}
