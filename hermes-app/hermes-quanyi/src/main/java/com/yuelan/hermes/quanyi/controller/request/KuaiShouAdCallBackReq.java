package com.yuelan.hermes.quanyi.controller.request;

import lombok.Data;

/**
 * <AUTHOR> 2024/6/17 下午8:19
 */
@Data
public class KuaiShouAdCallBackReq {

    /**
     * 广告ID
     */
    private String callback;

    /**
     * 转化事件类型
     */
    private Integer event_type;

    /**
     * 转化事件时间  13位毫秒级时间戳
     */
    private Long event_time;

    /**
     * 付费金额
     * 付费、授信、商品浏览、添加购物车、提交订单等转化事件需要回传，单位元，保留两位小数
     */
    private Double purchase_amount;

    /**
     * 用户年龄 可选参数，整数，仅限保险行业使用
     */
    private Double user_tags_age;

    /**
     * 预估LTV
     */
    private Double ext_pred_ltv;

    /**
     * 预估付费率
     */
    private Double ext_pred_pay_ratio;

    /**
     * 付费等级
     * S 小R
     * M 中R
     * L 大R
     */
    private String pay_level;

    /**
     * 小说章节 1-1000
     */
    private String novel_chapter_num;

    /**
     * 事件属性
     */
    private String event_props;
}
