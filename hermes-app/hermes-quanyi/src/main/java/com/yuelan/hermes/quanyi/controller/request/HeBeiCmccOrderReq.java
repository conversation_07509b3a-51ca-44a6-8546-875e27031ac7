package com.yuelan.hermes.quanyi.controller.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2025/2/21
 * @description HuBeiCmccOrderReq
 */

@NoArgsConstructor
@Data
public class HeBeiCmccOrderReq {

    /**
     * PTCommonOrderCommit
     */
    private String CMDID;
    /**
     * log
     * true
     */
    private boolean log;
    /**
     * 渠道id
     */
    private String CHANNELID;
    /**
     * 客户订单
     */
    private CustomerOrderDTO customerOrder;
    /**
     * seqid
     */
    private String seqid;
    /**
     * outTime
     */
    private String outTime;

    /**
     * CustomerOrderDTO
     */
    @NoArgsConstructor
    @Data
    public static class CustomerOrderDTO {
        /**
         * 子订单信息
         */
        private List<OrderRecListDTO> orderRecList;
        /**
         * 1
         */
        private String supportProdNotLoadNcodeMap;
        /**
         * 短信验证码
         */
        private String SMSRANDOMPASS;
        /**
         * 操作人id
         */
        private String operID;
        /**
         * 手机号码
         */
        private String serverNumber;
        /**
         * 外部订单号
         */
        private String outerOrderID;
        /**
         * 受理渠道
         */
        private String accessType;
        /**
         * 地域编码
         * 河北 311
         */
        private String region;
        /**
         * 是否短信通知0.不通知、1通知
         */
        private String isNotify;

        /**
         * OrderRecListDTO
         */
        @NoArgsConstructor
        @Data
        public static class OrderRecListDTO {
            /**
             * 子业务类型
             * ChangeProduct
             */
            private String recType;

            private String authType;
            /**
             * 业务扩展信息,通过键值对的形式传递
             */
            private RecExtAttrDTO recExtAttr;
            /**
             * offeringList
             */
            private List<OfferingListDTO> offeringList;

            /**
             * RecExtAttrDTO
             */
            @NoArgsConstructor
            @Data
            public static class RecExtAttrDTO {
                /**
                 * 记录业务投放的具体路径和点位
                 */
                private String extentMsg;
            }

            /**
             * OfferingListDTO
             */
            @NoArgsConstructor
            @Data
            public static class OfferingListDTO {
                /**
                 * 操作：A,M
                 */
                private String actionType;
                /**
                 * 商品类型
                 */
                private String offerType;
                /**
                 * 售货为资源小类
                 */
                private String offerCode;
                /**
                 * 生效方式
                 */
                private String effectType;
            }
        }
    }
}
