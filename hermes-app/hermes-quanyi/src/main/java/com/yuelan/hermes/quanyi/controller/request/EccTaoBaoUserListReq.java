package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccTaoBaoUserDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR> 2024/5/4 上午12:58
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class EccTaoBaoUserListReq extends PageRequest {

    @Schema(description = "淘宝账号（非淘宝昵称）")
    private String account;

    @Schema(description = "绑定的电商卡手机号码")
    private String phone;

    @Schema(description = "是否已绑定手机号码")
    private Boolean isBindPhone;

    @Schema(description = "绑定开始时间")
    private String bindStartTime;

    @Schema(description = "绑定结束时间")
    private String bindEndTime;

    public Wrapper<EccTaoBaoUserDO> buildQueryWrapper() {
        return Wrappers.lambdaQuery(EccTaoBaoUserDO.class)
                .eq(account != null, EccTaoBaoUserDO::getTaobaoUserNick, account)
                .eq(phone != null, EccTaoBaoUserDO::getBindPhone, phone)
                .isNotNull(Boolean.TRUE.equals(isBindPhone), EccTaoBaoUserDO::getBindPhone)
                .isNull(Boolean.FALSE.equals(isBindPhone), EccTaoBaoUserDO::getBindPhone)
                .ge(bindStartTime != null, EccTaoBaoUserDO::getBindTime, bindStartTime)
                .le(bindEndTime != null, EccTaoBaoUserDO::getBindTime, bindEndTime)
                .orderByDesc(EccTaoBaoUserDO::getUserId);
    }
}
