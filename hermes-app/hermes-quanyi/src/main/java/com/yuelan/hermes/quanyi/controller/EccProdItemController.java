package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.hermes.quanyi.biz.service.EccProductItemDOService;
import com.yuelan.hermes.quanyi.controller.request.EccProdItemSaveReq;
import com.yuelan.hermes.quanyi.controller.response.EccProdItemListResp;
import com.yuelan.hermes.quanyi.controller.response.EccProdItemResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> 2024/4/2 15:46
 */
@Validated
@RestController
@Tag(name = "电商卡/后台api/权益包关联权益商品管理")
@RequiredArgsConstructor
@RequestMapping("/a/ecc/productItem")
public class EccProdItemController {

    private final EccProductItemDOService eccProductItemDOService;

    @Operation(summary = "查询电商卡权益包关联商品列表")
    @GetMapping("/list/{prodId}")
    public BizResult<EccProdItemListResp> list(@NotNull(message = "权益包id不能为null") @PathVariable Long prodId) {
        EccProdItemListResp resp = new EccProdItemListResp();
        List<EccProdItemResp> itemRespList = eccProductItemDOService.getProdItemRespList(prodId);
        resp.setItemList(itemRespList);
        resp.setProdId(prodId);
        return BizResult.create(resp);
    }

    @Log(title = "批量新增电商卡权益包关联商品", type = OperationType.INSERT)
    @Operation(summary = "批量新增电商卡权益包关联商品")
    @PostMapping("/add")
    public BizResult<Void> add(@Validated(AddGroup.class) @RequestBody EccProdItemSaveReq req) {
        eccProductItemDOService.add(req);
        return BizResult.ok();
    }


    @Log(title = "删除电商卡权益包关联商品", type = OperationType.DELETE)
    @Operation(summary = "删除电商卡权益包关联商品")
    @DeleteMapping("/delete/{itemId}")
    @Transactional(rollbackFor = Exception.class)
    public BizResult<Boolean> delete(@NotNull(message = "权益包itemId不能为null") @PathVariable Long itemId) {
        boolean success = eccProductItemDOService.removeById(itemId);
        return BizResult.create(success);
    }
}
