package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.remote.LiaoNingCmccManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@Tag(name = "辽宁移动API")
@RequestMapping("/lncmcc")
@SaIgnore
@RestController
public class LiaoNingYdController {
    @Autowired
    private LiaoNingCmccManager liaoNingCmccManager;

    @Operation(summary = "")
    @PostMapping(value = "/order/notify")
    public Map notify(@RequestBody Map<String,String> params) {
        return liaoNingCmccManager.orderNotify(params);
    }

}
