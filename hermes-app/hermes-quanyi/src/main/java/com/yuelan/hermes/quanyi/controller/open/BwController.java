package com.yuelan.hermes.quanyi.controller.open;

import com.yuelan.hermes.quanyi.biz.service.BwService;
import com.yuelan.hermes.quanyi.controller.request.BWOrderNotifyReq;
import com.yuelan.hermes.quanyi.controller.response.BWOrderNotifyRsp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "变蛙API")
@RequestMapping("/bw")
@RestController
public class BwController {
    @Autowired
    private BwService bwService;

    @Operation(summary = "线上订单充值回调")
    @PostMapping(value = "/order/notify", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public BWOrderNotifyRsp orderOnlineNotify(BWOrderNotifyReq bwOrderNotifyReq) {
        log.info("线上订单,变蛙充值回调:{}", bwOrderNotifyReq);
//        BWOrderNotifyReq.Coupon coupon = JSON.parseObject(bwOrderNotifyReq.getCoupon(), BWOrderNotifyReq.Coupon.class);
//        log.info("变蛙充值回调:coupon{}", coupon);
        //TODO 这里我们不验签（正常应验签），直接处理业务逻辑
        boolean result = bwService.orderOnlineNotify(bwOrderNotifyReq);
        return BWOrderNotifyRsp.create(result);
    }

    @Operation(summary = "线下订单充值回调")
    @PostMapping(value = "/orderOffline/notify", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public BWOrderNotifyRsp orderOfflineNotify(BWOrderNotifyReq bwOrderNotifyReq) {
        log.info("线下订单,变蛙充值回调:{}", bwOrderNotifyReq);
//        BWOrderNotifyReq.Coupon coupon = JSON.parseObject(bwOrderNotifyReq.getCoupon(), BWOrderNotifyReq.Coupon.class);
//        log.info("变蛙充值回调:coupon{}", coupon);
        //TODO 这里我们不验签（正常应验签），直接处理业务逻辑
        boolean result = bwService.orderOfflineNotify(bwOrderNotifyReq);
        return BWOrderNotifyRsp.create(result);
    }

//    @Operation(summary = "线下订单充值回调")
//    @PostMapping(value = "/orderOffline/notify1")
//    public BWOrderNotifyRsp orderOfflineNotify1(@RequestBody BWOrderNotifyReq bwOrderNotifyReq) {
//        log.info("线下订单,变蛙充值回调:{}", bwOrderNotifyReq);
////        BWOrderNotifyReq.Coupon coupon = JSON.parseObject(bwOrderNotifyReq.getCoupon(), BWOrderNotifyReq.Coupon.class);
////        log.info("变蛙充值回调:coupon{}", coupon);
//        //TODO 这里我们不验签（正常应验签），直接处理业务逻辑
//        boolean result = bwService.orderOfflineNotify(bwOrderNotifyReq);
//        return BWOrderNotifyRsp.create(result);
//    }
}
