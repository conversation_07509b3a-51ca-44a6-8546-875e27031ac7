package com.yuelan.hermes.quanyi.controller.request;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/6/18 下午3:55
 */
@Data
public class EccDistributionUrlCopyReq {

    @NotNull(message = "电商卡产品id不能为空")
    @Schema(description = "电商卡产品id")
    private Long eccProductId;

    @NotNull(message = "内部渠道Id不能为空")
    @Schema(description = "内部渠道Id")
    private String innerChannelId;

    @Schema(title = "广告渠道Id", description = "可以传空")
    private Long adChannelId;

    @Schema(title = "落地页Id", description = "可以传空,传空时候会使用默认页")
    private Long pageId;

    @Schema(title = "占位符替换参数", description = "可以传空，传了会替换链接中的占位符")
    private JSONObject replaceParams;
}
