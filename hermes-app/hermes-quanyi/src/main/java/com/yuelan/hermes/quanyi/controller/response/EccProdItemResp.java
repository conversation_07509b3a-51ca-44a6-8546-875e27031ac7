package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductItemDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> 2024/5/3 下午10:34
 */
@Data
public class EccProdItemResp {

    /**
     * 主键
     */
    @Schema(description = "关联id")
    private Long prodItemId;

    /**
     * 关联产品id
     */
    @Schema(description = "电商卡权益包id")
    private Long prodId;

    /**
     * 商品详情
     */
    @Schema(description = "商品详情")
    private EccGoodsResp goods;


    public static EccProdItemResp buildResp(EccProductItemDO itemDO, EccGoodsDO eccGoodsDO) {
        if (Objects.isNull(itemDO)) {
            return null;
        }
        EccProdItemResp resp = new EccProdItemResp();
        resp.setProdId(itemDO.getProdId());
        resp.setProdItemId(itemDO.getProdItemId());
        resp.setGoods(EccGoodsResp.buildResp(eccGoodsDO));
        return resp;
    }

}
