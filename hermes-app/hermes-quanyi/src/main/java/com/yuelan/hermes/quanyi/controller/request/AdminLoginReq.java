package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <p> 管理员登录请求体 </p>
 *
 * <AUTHOR>
 * @date 2021/12/10
 */
@Data
public class AdminLoginReq {

    @Schema(description = "用户名")
    @NotBlank(message = "用户名不能为空")
    private String username;

    @Schema(description = "密码")
    @NotBlank(message = "请输入密码")
    private String password;

    @NotBlank(message = "短信验证码不能为空")
    @Schema(description = "短信验证码")
    private String smsCode;

}
