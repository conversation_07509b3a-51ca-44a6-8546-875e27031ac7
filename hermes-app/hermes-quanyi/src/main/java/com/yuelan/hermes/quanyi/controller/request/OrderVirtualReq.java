package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderVirtualReq extends PageRequest {

    /**
     * 虚拟商品名称
     */
    @Schema(description = "虚拟商品名称")
    private String name;

    /**
     * 订单编号
     */
    @Schema(description = "订单编号")
    private String orderNo;

    /**
     * 充值账号
     */
    @Schema(description = "充值账号")
    private String userAccount;

    /**
     * 虚拟商品SKU名称
     */
    @Schema(description = "虚拟商品SKU名称")
    private String skuName;

}

