package com.yuelan.hermes.quanyi.remote;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.common.enums.MgCollaborationModeEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.MgFunProperties;
import com.yuelan.hermes.quanyi.common.util.MiguFunUtils;
import com.yuelan.hermes.quanyi.mapper.GamingProductMapper;
import com.yuelan.hermes.quanyi.remote.request.MgFunBenefitReviveNotifyReq;
import com.yuelan.hermes.quanyi.remote.request.MgFunNotifyReq;
import com.yuelan.hermes.quanyi.remote.response.MiguFunCommonNotifyRsp;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class MgManager {

    @Autowired
    private MgFunProperties mgFunProperties;
    @Autowired
    private GamingProductMapper gamingProductMapper;

    /**
     * 回调通知
     */
    public void orderNotify(GamingOrderDO gamingOrderDO) {
        if (!AppConstants.isReal()) {
            log.info("非生产环境,不进行咪咕回调通知");
            return;
        }
        String signKey = mgFunProperties.getSignKey();
        // todo: 之后需要改造成从订单里面存储合作模式
        GamingProductDO productDO = gamingProductMapper.findByProductId(gamingOrderDO.getProductId());
        Integer mgMode = productDO.getMgMode();
        MgCollaborationModeEnum modeEnum = MgCollaborationModeEnum.of(mgMode);
        if (MgCollaborationModeEnum.HE_CHENG.equals(modeEnum)) {
            signKey = mgFunProperties.getHeChenSignKey();
        } else if (MgCollaborationModeEnum.CHANG_SHA_XM.equals(modeEnum)) {
            signKey = mgFunProperties.getChangShaXiMiSignKey();
        } else if (MgCollaborationModeEnum.HANG_ZHOU_XM.equals(modeEnum)) {
            signKey = mgFunProperties.getHangZhouXiMiSignKey();
        }

        MgFunNotifyReq req = new MgFunNotifyReq();
        req.setOrderId(gamingOrderDO.getOutOrderNo());
        req.setSendTime(gamingOrderDO.getCreateTime().getTime());
        req.setExtrInfo(gamingOrderDO.getAttach());
        req.setTimestamp(System.currentTimeMillis());
        req.setSendResult("1");
        req.setSendMessage("");
        req.setCdKey("");
        req.setCdKeyEncrypt("");
        //计算签名
        List<Object> params = Lists.newArrayList();
        params.add(req.getOrderId());
        params.add(req.getTimestamp());
        String sign = MiguFunUtils.sign(params, signKey);
        String json = JSON.toJSONString(req);
        //发送请求
        String result = HttpRequest.post(mgFunProperties.getNotifyUrl())
                .header("sign", sign)
                .body(json)
                .timeout(10000)
                .execute().body();
        if (log.isDebugEnabled()) {
            log.info("咪咕互娱道具兑换通知。body:{},result:{}", json, result);
        }
        MiguFunCommonNotifyRsp notifyRsp = JSON.parseObject(result, MiguFunCommonNotifyRsp.class);
        if (Objects.isNull(notifyRsp) || !notifyRsp.check()) {
            throw BizException.create(BizErrorCodeEnum.MIGU_HY_ERROR, "咪咕互娱道具下发通知失败");
        }
    }

    public void benefitReceiveNotify(GamingOrderDO gamingOrderDO) {
        if (!AppConstants.isReal()) {
            log.info("非生产环境,不进行[咪咕互娱道具用户领取成功通知]");
            return;
        }
        String signKey = mgFunProperties.getSignKey();
        GamingProductDO productDO = gamingProductMapper.findByProductId(gamingOrderDO.getProductId());
        Integer mgMode = productDO.getMgMode();
        MgCollaborationModeEnum modeEnum = MgCollaborationModeEnum.of(mgMode);
        if (MgCollaborationModeEnum.HE_CHENG.equals(modeEnum)) {
            signKey = mgFunProperties.getHeChenSignKey();
        } else if (MgCollaborationModeEnum.CHANG_SHA_XM.equals(modeEnum)) {
            signKey = mgFunProperties.getChangShaXiMiSignKey();
        } else if (MgCollaborationModeEnum.HANG_ZHOU_XM.equals(modeEnum)) {
            signKey = mgFunProperties.getHangZhouXiMiSignKey();
        }

        MgFunBenefitReviveNotifyReq req = new MgFunBenefitReviveNotifyReq();
        req.setOrderId(gamingOrderDO.getOutOrderNo());
        // todo: 这样写有问题的 方便联调现在还未记录时间
        req.setReceiveTime(System.currentTimeMillis());
        req.setExtrInfo(gamingOrderDO.getAttach());
        req.setSendServer(null);
        req.setSendRole(null);
        req.setTimestamp(System.currentTimeMillis());
        // 计算签名
        List<Object> params = Lists.newArrayList();
        params.add(req.getOrderId());
        params.add(req.getTimestamp());
        String sign = MiguFunUtils.sign(params, signKey);

        String json = JSON.toJSONString(req);
        // 发送请求
        HttpRequest request = HttpRequest.post(mgFunProperties.getBenefitReviveNotifyUrl())
                .header("sign", sign)
                .body(json)
                .timeout(10000);
        log.info("咪咕互娱道具用户领取成功通知{}", request);
        HttpResponse response = request
                .execute();
        log.info("咪咕互娱道具用户领取成功通知{}", response);
        String result = response.body();
        MiguFunCommonNotifyRsp notifyRsp = JSON.parseObject(result, MiguFunCommonNotifyRsp.class);
        if (Objects.isNull(notifyRsp) || !notifyRsp.check()) {
            throw BizException.create(BizErrorCodeEnum.MIGU_HY_ERROR, "咪咕互娱道具用户领取成功通知失败");
        }
    }
}
