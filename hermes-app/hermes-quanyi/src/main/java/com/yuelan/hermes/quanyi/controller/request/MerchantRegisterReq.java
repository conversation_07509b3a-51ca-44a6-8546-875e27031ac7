package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.StrUtil;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class MerchantRegisterReq {

    @NotBlank(message = "商户名称不能为空")
    @Schema(description = "商户名称")
    private String merchantName;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    public void check() {
        if (StrUtil.isBlank(username)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "用户名不能为空");
        }
        if (!Validator.isGeneral(username)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "用户名由英文字母 、数字和下划线组成");
        }
        if (StrUtil.isNotBlank(phone) && !Validator.isMobile(phone)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请填写正确的手机号");
        }
        if (StrUtil.isNotBlank(email) && !Validator.isEmail(email)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请填写正确的邮箱地址");
        }
    }

}
