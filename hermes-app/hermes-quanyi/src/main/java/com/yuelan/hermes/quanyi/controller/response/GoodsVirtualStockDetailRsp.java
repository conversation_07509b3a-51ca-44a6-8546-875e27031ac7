package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuelan.hermes.commons.excel.StockStatusConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
@ContentRowHeight(25)
@HeadRowHeight(30)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class GoodsVirtualStockDetailRsp {

    @ExcelIgnore
    @Schema(description = "库存明细主键")
    private Long stockItemId;

    @ExcelIgnore
    @Schema(description = "商品ID")
    private Long goodsId;

    @ColumnWidth(20)
    @ExcelProperty(value = "批次号")
    @Schema(description = "批次号")
    private String batchNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "商品名称")
    @Schema(description = "商品名称")
    private String goodsName;

    @ColumnWidth(20)
    @ExcelProperty(value = "SKU名称")
    @Schema(description = "SKU名称")
    private String skuName;

    @ExcelIgnore
    @Schema(description = "SKU ID")
    private Long skuId;

    @ColumnWidth(20)
    @ExcelProperty(value = "SKU编号")
    @Schema(description = "SKU编号")
    private String skuNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "供应商")
    @Schema(description = "供应商")
    private String supplier;

    @ColumnWidth(20)
    @ExcelProperty(value = "采购价")
    @Schema(description = "采购价")
    private BigDecimal purchasePrice;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty(value = "采购日期")
    @Schema(description = "采购日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date purchaseDate;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "入库时间")
    @Schema(description = "入库时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "集采客户")
    @Schema(description = "集采客户")
    private String buyer;

    @ColumnWidth(20)
    @ExcelProperty(value = "出库价")
    @Schema(description = "出库价")
    private BigDecimal salePrice;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "出库时间")
    @Schema(description = "出库时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryTime;

    @ColumnWidth(20)
    @Schema(description = "状态 1入库2出库")
    @ExcelProperty(value = "状态", converter = StockStatusConverter.class)
    private Integer status;

    @ColumnWidth(20)
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;
}
