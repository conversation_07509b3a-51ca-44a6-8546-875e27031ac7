package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.util.StrUtil;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.GamingOrderManualResultEnum;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Objects;

@Data
public class GamingOrderManualResultReq {

    @NotNull(message = "请选择订单")
    @Schema(description = "订单ID")
    private Long orderId;

    @NotBlank(message = "请选择明细单号")
    @Schema(description = "订单明细单号")
    private String itemNo;

    @Schema(description = "处理结果")
    private Integer result;

    @Schema(description = "供应商订单号")
    private String supplierOrderNo;

    @Schema(description = "备注")
    private String remark;

    public void check() {
        //根据操作结果处理
        GamingOrderManualResultEnum resultEnum = LocalEnumUtils.findByCode(GamingOrderManualResultEnum.class, result);
        if (Objects.isNull(resultEnum)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请选择处理结果");
        }
        itemNo = StrUtil.cleanBlank(itemNo);
        supplierOrderNo = StrUtil.cleanBlank(supplierOrderNo);
        remark = StrUtil.cleanBlank(remark);
    }
}