package com.yuelan.hermes.quanyi.remote.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <p> 特祯 -虚拟产品异步下单</p>
 *
 * <AUTHOR>
 * @date 2024/2/21
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class VProductRechargeRsp extends TeZhenResult {
    /**
     * 平台订单号
     */
    @JSONField(name = "OrderId")
    private String orderId;
    /**
     * 代理订单号
     */
    @JSONField(name = "LinkId")
    private String linkId;
    /**
     * 充值账号
     */
    @JSONField(name = "Mobile")
    private String mobile;
    /**
     * 销售价格
     */
    @JSONField(name = "SellBalance")
    private BigDecimal sellBalance;
    /**
     * 代理账号
     */
    @JSONField(name = "Account")
    private String account;


}