package com.yuelan.hermes.quanyi.controller.benefit;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.manager.AdManager;
import com.yuelan.hermes.quanyi.biz.manager.BenefitOrderManager;
import com.yuelan.hermes.quanyi.biz.service.BenefitProductDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitProductItemDOService;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.controller.request.BenefitProdItemsReq;
import com.yuelan.hermes.quanyi.controller.request.PageEventUploadReq;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitStatusReq;
import com.yuelan.hermes.quanyi.controller.response.AppBenefitGoodsResp;
import com.yuelan.hermes.quanyi.controller.response.AppBenefitProdItemResp;
import com.yuelan.hermes.quanyi.controller.response.AppBenefitProdResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/4/7 20:16
 */
@Tag(name = "权益N选1/客户端接口/权益包基础信息api")
@Slf4j
@Validated
@RestController
@RequestMapping("/benefit")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
public class AppBenefitController {

    private final BenefitProductDOService benefitProductDOService;
    private final BenefitProductItemDOService benefitProductItemDOService;
    private final BenefitOrderManager benefitOrderManager;
    private final AdManager adManager;

    /**
     * 权益包详情
     *
     * @param prodCode 权益包编码
     * @return 权益包详情
     */
    @SaIgnore
    @Operation(summary = "权益包基础信息")
    @GetMapping("/prodInfo/{prodCode}")
    public BizResult<AppBenefitProdResp> detail(@PathVariable String prodCode) {
        BenefitProductDO productDO = benefitProductDOService.getByProdCodeAndUpStatus(prodCode);
        if (productDO == null) {
            log.error("权益包活动已结束prodCode:{}", prodCode);
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "活动已结束");
        }
        return BizResult.create(AppBenefitProdResp.buildResp(productDO));
    }

    /**
     * 权益包协议文案
     *
     * @param prodCode 权益包编码
     * @return 权益包协议文案
     */
    @SaIgnore
    @Operation(summary = "权益包协议", description = "因为协议文本比较大,单独接口避免影响主页速度")
    @GetMapping("/agreementContent/{prodCode}")
    public BizResult<String> agreementContent(@PathVariable String prodCode) {
        BenefitProductDO productDO = benefitProductDOService.getByProdCode(prodCode);
        if (productDO == null) {
            log.error("权益包活动不存在prodCode:{}", prodCode);
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "权益包活动不存在");
        }
        return BizResult.create(productDO.getAgreementContent());
    }

    /**
     * 权益包商品列表
     */
    @SaIgnore
    @Operation(summary = "权益包商品列表")
    @PostMapping("/prod/items")
    public BizResult<AppBenefitProdItemResp> prodItems(@Validated @RequestBody BenefitProdItemsReq req) {
        BenefitProductDO productDO = benefitProductDOService.getByProdCodeAndUpStatus(req.getProdCode());
        if (productDO == null) {
            log.error("权益包活动已结束prodCode:{}", req.getProdCode());
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "活动已结束");
        }
        List<BenefitGoodsDO> goodsDOS = benefitProductItemDOService.listGoodsByProdId(productDO.getProdId());
        AppBenefitProdItemResp resp = new AppBenefitProdItemResp();
        resp.setProdId(productDO.getProdId());
        resp.setGoodsList(goodsDOS.stream().map(AppBenefitGoodsResp::buildResp).collect(Collectors.toList()));
        return BizResult.create(resp);
    }

    /**
     * 用户权益下单 返回地址
     */
    @SaIgnore
    @Operation(summary = "用户权益下单", description = "返回付款URL等")
    @PostMapping("/order")
    public BizResult<BenefitPayResultBO> order(@Validated @RequestBody UserBenefitOrderReq req) {
        BenefitPayResultBO resultBO = benefitOrderManager.createOrder(req);
        return BizResult.create(resultBO);
    }

    @SaIgnore
    @Operation(summary = "请求运营商发送短信验证码", description = "下单前请求")
    @PostMapping("/order/sendSmsCode")
    public BizResult<BenefitPayResultBO> sendSmsCode(@Validated @RequestBody UserBenefitOrderReq req) {
        BenefitPayResultBO resultBO = benefitOrderManager.sendSmsCode(req);
        return BizResult.create(resultBO);
    }


    @SaIgnore
    @Operation(summary = "页面事件回传", description = "页面事件回传")
    @PostMapping("/ad/eventUpload")
    public BizResult<Void> eventUpload(@Validated @RequestBody PageEventUploadReq req) {
        adManager.adEventUploadByBenefit(req);
        return BizResult.ok();
    }

    @SaIgnore
    @Operation(summary = "用户是否订购", description = "用户是否订购")
    @PostMapping("/prod/user/subscribe")
    public BizResult<Integer> userSubscribeStatus(@Validated @RequestBody UserBenefitStatusReq req) {

        return BizResult.create(benefitOrderManager.userSubscribeStatus(req));
    }

}
