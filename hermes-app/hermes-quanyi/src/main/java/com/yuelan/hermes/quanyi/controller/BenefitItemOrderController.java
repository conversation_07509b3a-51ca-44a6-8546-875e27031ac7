package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.BenefitItemOrderService;
import com.yuelan.hermes.quanyi.controller.request.BenefitItemOrderReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitItemOrderResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2025/4/28
 * @since 2025/4/28
 */
@Validated
@RestController
@Tag(name = "权益组合包/后台接口/权益订单")
@RequiredArgsConstructor
@RequestMapping("/a/benefits/item")
public class BenefitItemOrderController {

    private final BenefitItemOrderService benefitItemOrderService;

    @Operation(summary = "分页")
    @PostMapping("/page")
    public BizResult<PageData<BenefitItemOrderResp>> page(@RequestBody BenefitItemOrderReq req) {
        return BizResult.create(benefitItemOrderService.pageList(req));
    }

    @Operation(summary = "导出")
    @PostMapping("/export")
    public BizResult<FileExportTaskCreateResp> export(@RequestBody BenefitItemOrderReq req) {
        return BizResult.create(benefitItemOrderService.export(req));
    }

}
