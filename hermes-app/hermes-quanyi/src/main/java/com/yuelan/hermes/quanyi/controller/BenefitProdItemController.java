package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.hermes.quanyi.biz.service.BenefitProductItemDOService;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductItemDO;
import com.yuelan.hermes.quanyi.controller.request.BenefitProdItemSaveReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitProdItemUpdateSortReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitProdItemResp;
import com.yuelan.hermes.quanyi.controller.response.BenefitProditemListResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> 2024/4/2 15:46
 */
@Validated
@RestController
@Tag(name = "权益N选1/后台接口/权益产品/权益包 商品关联关系")
@RequiredArgsConstructor
@RequestMapping("/a/benefit/productItem")
public class BenefitProdItemController {

    private final BenefitProductItemDOService benefitProductItemService;

    @Operation(summary = "查询权益包关联商品列表")
    @GetMapping("/list/{prodId}")
    public BizResult<BenefitProditemListResp> list(@NotNull(message = "权益包id不能为null") @PathVariable Long prodId) {
        BenefitProditemListResp resp = new BenefitProditemListResp();
        List<BenefitProdItemResp> itemRespList = benefitProductItemService.listItemsByProdId(prodId);
        resp.setItemList(itemRespList);
        resp.setProdId(prodId);
        return BizResult.create(resp);
    }

    @Log(title = "批量新增权益包关联商品", type = OperationType.INSERT)
    @Operation(summary = "批量新增权益包关联商品")
    @PostMapping("/add")
    public BizResult<Void> add(@Validated(AddGroup.class) @RequestBody BenefitProdItemSaveReq req) {
        benefitProductItemService.add(req);
        return BizResult.ok();
    }

    @Log(title = "更新权益包内商品排序", type = OperationType.UPDATE)
    @Operation(summary = "更新权益包内商品排序")
    @PostMapping("/updateSort")
    public BizResult<Void> updateSort(@Validated @RequestBody BenefitProdItemUpdateSortReq req) {
        benefitProductItemService.updateSort(req);
        return BizResult.ok();
    }

    @Log(title = "删除权益包关联商品", type = OperationType.DELETE)
    @Operation(summary = "删除权益包关联商品")
    @DeleteMapping("/delete/{itemId}")
    @Transactional(rollbackFor = Exception.class)
    public BizResult<Boolean> delete(@NotNull(message = "权益包itemId不能为null") @PathVariable Long itemId) {
        BenefitProductItemDO dbProdItem = benefitProductItemService.getById(itemId);
        boolean success = benefitProductItemService.removeById(itemId);
        // prod下比这个sort大的都更新成减少1
        if (success) {
            benefitProductItemService.reductionSortByDeleteSort(dbProdItem.getProdId(), dbProdItem.getSort());
        }
        return BizResult.create(success);
    }

}
