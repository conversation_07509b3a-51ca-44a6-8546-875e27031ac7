package com.yuelan.hermes.quanyi.remote;

import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.service.EccOuterChannelDOService;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/7/22
 */
@Slf4j
@Component
public class CallbackMsgManager {

    @Autowired
    private EccOuterChannelDOService eccOuterChannelDOService;

    private static final Integer TIME_OUT = 5000;
    private static final Integer NOTIFY_SUCCESS_CODE = 0;

    /**
     * 发送回调消息
     *
     * @param outerChannelId 外部渠道id
     * @param urlPath        回调地址
     * @param contentJson    json内容
     * @return 是否成功
     */
    public Boolean sendCallbackMsg(Long outerChannelId, String urlPath, String contentJson) {
        EccOuterChannelDO channelDO = eccOuterChannelDOService.getById(outerChannelId);
        if (Objects.isNull(channelDO)) {
            log.error("外部渠道回调通知异常 outerChannelId:{}, contentJson:{}", outerChannelId, JSON.toJSONString(contentJson));
            throw BizException.create(BizErrorCodeEnum.MCH_NOT_FOUND, "外部渠道回调通知异常");
        }
        //计算签名
        Map<String, Object> bodyMap = JSON.parseObject(contentJson, Map.class);
        String signStr = bodyMap.entrySet().stream().sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        String sign = DigestUtils.md5Hex(signStr + "&secret=" + channelDO.getApiSecret());
        bodyMap.put("sign", sign);
        String bodyJson = JSON.toJSONString(bodyMap);
        //发送请求
        String result = HttpRequest.post(urlPath)
                .body(bodyJson)
                .contentType(ContentType.JSON.toString())
                .timeout(TIME_OUT)
                .execute().body();
        if (log.isDebugEnabled()) {
            log.info("外部渠道回调通知。body:{},result:{}", bodyJson, result);
        }
        try {
            JSONObject respJson = JSONObject.parseObject(result);
            Integer code = respJson.getInteger("code");
            return NOTIFY_SUCCESS_CODE.equals(code);
        } catch (Exception e) {
            log.error("外部渠道回调通知异常，解析返回值异常", e);
            return false;
        }
    }

}
