package com.yuelan.hermes.quanyi.controller.open;


import com.yuelan.hermes.quanyi.biz.service.MerchantAccountService;
import com.yuelan.hermes.quanyi.controller.request.SignReq;
import com.yuelan.hermes.quanyi.controller.response.MchAccountBalanceRsp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "账户API")
@RequestMapping("/account")
@RestController
public class AccountController extends MchBaseController {

    @Autowired
    private MerchantAccountService merchantAccountService;


    @Operation(summary = "账户余额")
    @PostMapping(value = "/balance", consumes = MediaType.APPLICATION_JSON_VALUE)
    public BizResult<MchAccountBalanceRsp> accountBalance(@RequestBody SignReq req) {
        checkSign(req);
        MchAccountBalanceRsp result = merchantAccountService.accountBalance(req.getMchId());
        return BizResult.create(result);
    }

}
