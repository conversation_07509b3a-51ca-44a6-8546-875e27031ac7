package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.io.IoUtil;
import com.yuelan.hermes.quanyi.biz.manager.TikTokManager;
import com.yuelan.hermes.quanyi.common.pojo.tiktok.ReqInfo;
import com.yuelan.hermes.quanyi.controller.response.TiktokCallbackResp;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR> 2024/9/9 13:59
 */
@Slf4j
@SaIgnore
@RestController
@RequestMapping("/tiktok/callback")
@AllArgsConstructor
public class TiktokCallbackController {

    private final TikTokManager tikTokManager;

    @Operation(summary = "抖音支付回调")
    @PostMapping("/payNotify")
    public TiktokCallbackResp payNotify(HttpServletRequest request) throws IOException {
        ReqInfo reqInfo = resolveReq(request);
        return tikTokManager.tiktokPayCallBack(reqInfo);
    }


    public ReqInfo resolveReq(HttpServletRequest req) throws IOException {
        log.info("receive payNotify");
        String body = IoUtil.read(req.getReader());
        String timestamp = req.getHeader("Byte-Timestamp");
        String nonce = req.getHeader("Byte-Nonce-Str");
        String signature = req.getHeader("Byte-Signature");
        log.info("body = {},timestamp:{}, nonce:{}, signature:{}", body, timestamp, nonce, signature);
        ReqInfo reqInfo = new ReqInfo();
        reqInfo.body = body;
        reqInfo.timestamp = timestamp;
        reqInfo.nonce = nonce;
        reqInfo.signature = signature;
        return reqInfo;
    }

}
