package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.fastjson.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/12/6
 * @description HnCmccResp
 */

@NoArgsConstructor
@Data
public class HnCmccResp {

    @Schema(description = "响应报文")
    private List<JSONObject> result;

    @Schema(description = "响应信息描述")
    private String respDesc;

    @Schema(description = "响应编码 0-成功")
    private String respCode;

}
