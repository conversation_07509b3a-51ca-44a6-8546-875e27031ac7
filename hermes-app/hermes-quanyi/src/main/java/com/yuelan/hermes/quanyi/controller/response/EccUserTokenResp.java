package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.core.util.DesensitizedUtil;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccTaoBaoUserDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> 2024/5/9 下午2:04
 */
@Data
public class EccUserTokenResp {
    @Schema(description = "账号名字")
    private String accountName;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "我方平台token")
    private String token;

    public static EccUserTokenResp buildResp(EccTaoBaoUserDO userDO, String saToken) {
        EccUserTokenResp resp = new EccUserTokenResp();
        if (Objects.nonNull(userDO.getBindPhone())) {
            resp.setPhone(DesensitizedUtil.mobilePhone(userDO.getBindPhone()));
        }
        resp.setAccountName(userDO.getTaobaoUserNick());
        resp.setToken(saToken);
        return resp;
    }
}
