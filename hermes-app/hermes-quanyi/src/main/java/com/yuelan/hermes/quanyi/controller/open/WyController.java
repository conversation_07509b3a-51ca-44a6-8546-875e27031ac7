package com.yuelan.hermes.quanyi.controller.open;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.service.WyService;
import com.yuelan.hermes.quanyi.controller.request.GamingOrderNotifyReq;
import com.yuelan.hermes.quanyi.controller.response.NetEasePartyNotifyResp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Tag(name = "网易API")
@RequestMapping("/wy")
@RestController
public class WyController {
    @Autowired
    private WyService wyService;

    @Operation(summary = "道具下发通知")
    @PostMapping(value = "/vouchercp/notify")
    public BizResult<Boolean> notify(@RequestBody GamingOrderNotifyReq req) {
        if (log.isDebugEnabled()) {
            log.info("网易道具下发通知:{}", JSON.toJSONString(req));
        }
        Boolean result = wyService.notify(req);
        return BizResult.create(result);
    }

    /**
     * 通知游戏服务器发放邮件成功之后，计费通过该接⼝通知给经销商。
     */
    @Operation(summary = "道具下发通知-蛋仔派对")
    @PostMapping(value = "/party/notify")
    public NetEasePartyNotifyResp partyNotify(HttpServletRequest request, @RequestBody String req) {
        final String method = "POST";
        final String path = "/wy/party/notify";
        log.info("网易蛋仔道具下发通知:{}", JSONObject.toJSONString(req));
        try {
            return wyService.partyNotify(request, method, path, req);
        } catch (Exception e) {
            log.error("网易蛋仔道具下发通知异常", e);
            return new NetEasePartyNotifyResp().setFail("系统异常");
        }
    }

}
