package com.yuelan.hermes.quanyi.controller.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/11/5
 * @description:
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WoReadStatusChangeReq {

    @Schema(description = "手机号")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @Schema(description = "状态 1-订购 2-退订")
    @NotBlank(message = "状态不能为空")
    private String status;

    @Schema(description = "订购时为生效时间 时间戳(秒)")
    @NotBlank(message = "订购时为生效时间不能为空")
    private String time;

    @Schema(description = "阅读套餐ID")
    private String productpkgid;

    @Schema(description = "计费点")
    private String serviceid;

    @Schema(description = "签名")
    private String sign;

    @Schema(description = "地址编号")
    private String areanumber;

    @Schema(description = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String businessorderno;

    @Schema(description = "订购关系失效时间")
    private String subscribetime;

    @Schema(description = "失效时间")
    private String expiretime;

}
