package com.yuelan.hermes.quanyi.controller.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <p> 特祯-虚拟产品异步回调 </p>
 *
 * <AUTHOR>
 * @date 2024/2/21
 */
@Data
public class TeZhenNotifyReq {
    /**
     * 返回编码
     * 000.充值成功
     * 410.充值失败
     */
    @JsonProperty("Code")
    private String code;
    /**
     * CODE描述
     */
    @JsonProperty("Msg")
    private String msg;
    /**
     * 平台订单号
     */
    @JsonProperty("OrderId")
    private String orderId;
    /**
     * 代理订单号
     */
    @JsonProperty("LinkId")
    private String linkId;
    /**
     * 卡密信息格式（卡号|卡密|有效期），有效期格式yyyyMMdd
     */
    @JsonProperty("CpId")
    private String cpId;
}