package com.yuelan.hermes.quanyi.controller.open;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.service.KnhqService;
import com.yuelan.hermes.quanyi.controller.request.KnhqOrderNotifyReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Tag(name = "科能恒启API")
@RequestMapping("/knhq")
@RestController
public class KnhqController {

    @Autowired
    private KnhqService knhqService;

    @Operation(summary = "话费充值回调")
    @RequestMapping(value = "/order/notify", method = {RequestMethod.GET, RequestMethod.POST})
    public String notify(HttpServletRequest request, KnhqOrderNotifyReq notifyReq) {
        //TODO 这里我们不验签（正常应验签），直接处理业务逻辑
        if (log.isDebugEnabled()) {
            log.info("科能恒启充值回调:{}", JSON.toJSONString(notifyReq));
        }
        return knhqService.notify(notifyReq);
    }

}
