package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.FileExportTaskDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> 2024/7/11 上午10:43
 */
@Data
public class FileExportTaskPageResp {

    @Schema(description = "任务id")
    private Integer taskId;

    /**
     * 任务名字
     */
    @Schema(description = "任务名字")
    private String taskName;
    /**
     * 文件名字
     */
    @Schema(description = "文件名字")
    private String fileName;

    /**
     * 任务状态
     *
     * @see com.yuelan.hermes.commons.enums.ExecuteStatusEnum
     */
    @Schema(description = "任务状态：0-等待,1-执行中,2-成功，3-失败")
    private Integer taskStatus;

    /**
     * 下载地址
     */
    @Schema(description = "下载地址")
    private String ossUrl;

    /**
     * 创建时间
     */
    private Date createTime;


    public static FileExportTaskPageResp buildResp(FileExportTaskDO fileExportTaskDO) {
        if (Objects.isNull(fileExportTaskDO)) {
            return null;
        }
        FileExportTaskPageResp resp = new FileExportTaskPageResp();
        resp.setTaskId(fileExportTaskDO.getTaskId());
        resp.setTaskName(fileExportTaskDO.getTaskName());
        resp.setFileName(fileExportTaskDO.getFileName());
        resp.setTaskStatus(fileExportTaskDO.getTaskStatus());
        resp.setOssUrl(fileExportTaskDO.getOssUrl());
        resp.setCreateTime(fileExportTaskDO.getCreateTime());
        return resp;
    }
}
