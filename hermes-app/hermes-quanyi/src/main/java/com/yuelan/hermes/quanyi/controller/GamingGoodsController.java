package com.yuelan.hermes.quanyi.controller;


import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.hermes.quanyi.biz.service.GamingGoodsService;
import com.yuelan.hermes.quanyi.controller.request.GamingGoodsOptReq;
import com.yuelan.hermes.quanyi.controller.request.GamingGoodsReq;
import com.yuelan.hermes.quanyi.controller.request.GamingGoodsSaveReq;
import com.yuelan.hermes.quanyi.controller.request.GamingGoodsStatusOptReq;
import com.yuelan.hermes.quanyi.controller.response.GamingGoodsRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Validated
@Tag(name = "电竞卡商品API")
@RequestMapping("/a/gaming/goods")
@RestController
public class GamingGoodsController extends AdminBaseController {

    @Autowired
    private GamingGoodsService gamingGoodsService;

    @Operation(summary = "商品列表")
    @PostMapping("/list")
    public BizResult<PageData<GamingGoodsRsp>> list(@RequestBody GamingGoodsReq req) {
        PageData<GamingGoodsRsp> result = gamingGoodsService.list(req);
        return BizResult.create(result);
    }

    @Operation(summary = "商品列表选项")
    @PostMapping("/listOptions")
    public BizResult<List<GamingGoodsRsp>> listOptions(@RequestBody GamingGoodsReq req) {
        List<GamingGoodsRsp> result = gamingGoodsService.listOptions(req);
        return BizResult.create(result);
    }


    @Operation(summary = "商品详情")
    @GetMapping("/detail")
    public BizResult<GamingGoodsRsp> detail(@RequestParam Long goodsId) {
        GamingGoodsRsp result = gamingGoodsService.detail(goodsId);
        return BizResult.create(result);
    }

    @Log(title = "新增/编辑商品", type = OperationType.INSERT)
    @Operation(summary = "新增/编辑商品")
    @PostMapping("/save")
    public BizResult<Long> save(@Validated(AddGroup.class) @RequestBody GamingGoodsSaveReq req) {
        Long result = gamingGoodsService.save(req);
        return BizResult.create(result);
    }

    @Log(title = "商品上下架", type = OperationType.UPDATE)
    @Operation(summary = "商品上下架")
    @PostMapping("/status")
    public BizResult<Boolean> updateStatus(@Valid @RequestBody GamingGoodsStatusOptReq req) {
        Boolean result = gamingGoodsService.updateStatus(req);
        return BizResult.create(result);
    }


    @Log(title = "删除商品", type = OperationType.DELETE)
    @Operation(summary = "删除商品")
    @PostMapping("/remove")
    public BizResult<Boolean> remove(@Valid @RequestBody GamingGoodsOptReq req) {
        Boolean result = gamingGoodsService.remove(req.getGoodsId());
        return BizResult.create(result);
    }

}
