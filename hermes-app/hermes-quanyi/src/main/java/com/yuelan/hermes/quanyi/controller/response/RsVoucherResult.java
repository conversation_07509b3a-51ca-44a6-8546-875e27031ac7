package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RsVoucherResult {


    @Schema(description = "券码号/卡号")
    private String voucherCode;

    @Schema(description = "卡密")
    private String voucherPassword;

    @Schema(description = "短链接，二维码短链接或者条码短链接")
    private String qrCodeUrl;

    @Schema(description = "有效起始日期格式yyyyMMddHHmmss")
    private String startDate;

    @Schema(description = "有效结束日期格式yyyyMMddHHmmss")
    private String endDate;

}
