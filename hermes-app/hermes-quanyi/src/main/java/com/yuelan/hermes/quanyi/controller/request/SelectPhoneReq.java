package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2024/9/10 18:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SelectPhoneReq extends PageRequest {

    @Schema(description = "产品编号")
    public String eccProdCode;

    @Schema(description = "标签type")
    private Integer tagType;

    @Schema(description = "省份编码-非必填，传了只展示该省份的号码")
    public String provinceCode;

    @Schema(description = "城市编码-非必填，传了只展示该市的号码，传编码市必须传省编码")
    public String cityCode;


}
