package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.yuelan.hermes.commons.enums.ZopOrderStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.ZopStateEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> 2024/7/18 下午3:18
 */
@Deprecated
@Data
public class EccQueryOrderApiResp {

    @Schema(description = "我方订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "产品名字", requiredMode = Schema.RequiredMode.REQUIRED)
    private String prodName;

    @Schema(description = "接入方下单时候订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelOrderNo;

    @Schema(description = "身份证号码，脱敏后的身份证号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String idCard;

    @Schema(description = "身份证名字", requiredMode = Schema.RequiredMode.REQUIRED)
    private String idCardName;

    @Schema(description = "收货手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contactPhone;

    @Schema(description = "手机号码（用户选择/随机选择）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String phone;

    @Schema(title = "订单是否创建成功", description = "true:成功，只代表订单成功同步给运营商", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean createSuccess;

    @Schema(title = "运营商订单号", description = "订单创建成功的时候存在", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String spOrderNo;

    @Schema(title = "订单创建失败原因", description = "例：预下单失败", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String createFailReason;

    @Schema(title = "订单创建失败详细原因", description = "运营商接口返回的错误信息", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String createFailDetailReason;

    @Schema(title = "号码最新状态", description = "1：激活,2：退单(激活前),6,首充数据同步,SX:未支付超期", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String phoneCardStatus;

    @Schema(title = "号码最新状态描述", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String phoneCardStatusDesc;

    @Schema(title = "首冲充值金额", description = "单位分", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer firstRecharge;


    @Schema(title = "订单创建时间", description = "格式：2024-01-01 08:00:00", requiredMode = Schema.RequiredMode.REQUIRED)
    private String createTime;

    @Schema(title = "订单更新时间", description = "格式：2024-01-01 08:00:00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String updateTime;


    public static EccQueryOrderApiResp buildResp(EccZopOrderDO zopOrderDO) {
        if (Objects.isNull(zopOrderDO)) {
            return null;
        }
        EccQueryOrderApiResp resp = new EccQueryOrderApiResp();
        resp.setOrderNo(zopOrderDO.getOrderNo());
        resp.setProdName(zopOrderDO.getProdName());
        resp.setChannelOrderNo(zopOrderDO.getChannelOrderNo());
        resp.setIdCard(zopOrderDO.getIdCard());
        // 脱敏
        if (Objects.nonNull(zopOrderDO.getIdCard())) {
            resp.setIdCard(DesensitizedUtil.idCardNum(zopOrderDO.getIdCard(), 5, 2));
        }
        resp.setIdCardName(zopOrderDO.getIdCardName());
        resp.setContactPhone(zopOrderDO.getContactPhone());
        resp.setPhone(zopOrderDO.getPhone());
        resp.setFirstRecharge(zopOrderDO.getFirstRecharge());
        ZopOrderStatusEnum riskCheckStatus = ZopOrderStatusEnum.of(zopOrderDO.getRiskCheckStatus());
        ZopOrderStatusEnum preOrderStatusEnum = ZopOrderStatusEnum.of(zopOrderDO.getPreOrderStatus());
        ZopOrderStatusEnum selectNumStatus = ZopOrderStatusEnum.of(zopOrderDO.getSelectNumStatus());
        ZopOrderStatusEnum orderSyncStatus = ZopOrderStatusEnum.of(zopOrderDO.getOrderSyncStatus());
        if (ZopOrderStatusEnum.SUCCESS.equals(orderSyncStatus)) {
            resp.setCreateSuccess(Boolean.TRUE);
        } else {
            resp.setCreateSuccess(Boolean.FALSE);
            if (ZopOrderStatusEnum.FAIL.equals(riskCheckStatus)) {
                resp.setCreateFailReason("风控拦截");
                resp.setCreateFailDetailReason(zopOrderDO.getRiskCheckResp());
            }
            if (ZopOrderStatusEnum.FAIL.equals(preOrderStatusEnum)) {
                resp.setCreateFailReason("预下单失败");
                resp.setCreateFailDetailReason(zopOrderDO.getPreOrderResp());
            } else if (ZopOrderStatusEnum.FAIL.equals(selectNumStatus)) {
                resp.setCreateFailReason("选号失败");
                resp.setCreateFailDetailReason(zopOrderDO.getSelectNumResp());
            } else {
                resp.setCreateFailReason("确认单失败");
                resp.setCreateFailDetailReason(zopOrderDO.getOrderSyncResp());
            }
        }
        resp.setPhoneCardStatus(zopOrderDO.getZopOrderState());
        resp.setSpOrderNo(zopOrderDO.getSyncOrderNo());
        ZopStateEnum zopState = ZopStateEnum.getByCode(zopOrderDO.getZopOrderState());
        if (Objects.nonNull(zopState)) {
            resp.setPhoneCardStatusDesc(zopState.getDesc());
        }
        resp.setCreateTime(DateUtil.formatDateTime(zopOrderDO.getCreateTime()));
        if (zopOrderDO.getUpdateTime() != null) {
            resp.setUpdateTime(DateUtil.formatDateTime(zopOrderDO.getUpdateTime()));
        }
        return resp;
    }


}
