package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.AdChannelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2024/6/18 下午3:42
 */
@Data
public class AdChannelOptionsResp {
    @Schema(description = "选项合集")
    private List<SelectOptionResp> options;

    @Data
    public static class SelectOptionResp {
        /**
         * 主键
         */
        @Schema(description = "主键")
        private Long adChannelId;

        /**
         * 产品名字
         */
        @Schema(description = "产品名字")
        private String adChannelName;

        public static AdChannelOptionsResp.SelectOptionResp buildResp(AdChannelDO adChannelDO) {
            AdChannelOptionsResp.SelectOptionResp resp = new AdChannelOptionsResp.SelectOptionResp();
            resp.setAdChannelId(adChannelDO.getAdChannelId());
            resp.setAdChannelName(adChannelDO.getAdChannelName());
            return resp;
        }
    }


}
