package com.yuelan.hermes.quanyi.remote.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> 2024/11/20
 * @since 2024/11/20
 */
@Data
@Accessors(chain = true)
public class WaNumSubmitResp {
    /**
     * 0000:局方下单成功
     * 1002:数据校验未通过，订单没有入我方数据库，可以修改参数后再次推送订单
     * 1003:订单已入我方数据库，局方下单状态未知，需要二次查询或者提供回调地址，我方订单状态变化时主动推送订单状态，详见接口2.9订单状态回调
     * 1004:局方下单失败，不是最终状态，我方二次跟单后可能会变成下单成功，需要二次确认状态
     * 1000:请通知API开发者
     */
    public String status;

    public String message;

    public String data;

    /**
     * 是否是异步订单
     * 异步订单说明提交成功只是提交成功，不代表领卡成功 需要等待异步回调
     */
    public boolean isAsyncOrder() {
        return "1003".equals(status);
    }

}
