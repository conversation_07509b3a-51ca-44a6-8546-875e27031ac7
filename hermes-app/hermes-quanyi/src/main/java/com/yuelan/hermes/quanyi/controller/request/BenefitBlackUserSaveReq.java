package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.validate.AddGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BenefitBlackUserSaveReq {

    /**
     * 支付通道id
     */
    @NotNull(message = "支付通道id不能为空", groups = {AddGroup.class})
    @Schema(description = "支付通道id")
    private Integer payChannelId;

    /**
     * 支付通道包id
     */
    @Schema(description = "支付通道包id")
    private Integer payChannelPkgId;

    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空", groups = {AddGroup.class})
    @Schema(description = "业务类型 1-手机号 2-包名")
    private Integer bizType;

    @NotEmpty(message = "业务值列表不能为空",groups = {AddGroup.class})
    @Schema(description = "业务类型对应的业务值数组(手机号或者报名数组)")
    private List<String> bizValues;

}
