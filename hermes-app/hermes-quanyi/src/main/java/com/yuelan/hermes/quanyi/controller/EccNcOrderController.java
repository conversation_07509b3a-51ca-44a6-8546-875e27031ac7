package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.EccNcOrderService;
import com.yuelan.hermes.quanyi.config.satoken.StpAdminUtil;
import com.yuelan.hermes.quanyi.controller.request.EccNcOrderListReq;
import com.yuelan.hermes.quanyi.controller.response.EccNcOrderResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/5/15 上午10:57
 */
@Validated
@Tag(name = "电商卡/后台api/号卡领卡记录")
@RestController
@RequestMapping("/a/ecc/ncOrder")
@AllArgsConstructor
public class EccNcOrderController {

    private final EccNcOrderService ncOrderService;

    @Operation(summary = "号卡领卡记录")
    @PostMapping("/list")
    public BizResult<PageData<EccNcOrderResp>> list(@RequestBody EccNcOrderListReq req) {
        long adminId = StpAdminUtil.getLoginIdAsLong();
        return BizResult.create(ncOrderService.pageList(req, adminId));
    }

    @Operation(summary = "导出号卡领卡记录")
    @PostMapping("/export")
    public BizResult<FileExportTaskCreateResp> export(@RequestBody EccNcOrderListReq req) {
        long adminId = StpAdminUtil.getLoginIdAsLong();
        return BizResult.create(ncOrderService.export(req, adminId));
    }

}
