package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/5/9 下午3:19
 */
@Data
public class AdminLoginSmsCodeReq {

    @Schema(description = "账号不能为空")
    @NotEmpty(message = "请输入账号")
    private String username;


    @Schema(description = "图形验证码识别结果")
    @NotEmpty(message = "请输入图形验证码")
    private String picCaptcha;

    @NotBlank(message = "验证码请求号不能为空")
    @Schema(description = "验证码请求号")
    private String captchaReqNo;

}
