package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/2 16:24
 */
@Data
public class EccGoodsSaveReq {

    /**
     * 商品id
     */
    @NotNull(message = "商品id不能为空", groups = {EditGroup.class})
    @Schema(description = "商品id")
    private Long goodsId;

    /**
     * 权益商品名字
     */
    @NotEmpty(message = "权益商品名字不能为空", groups = {AddGroup.class})
    @Schema(description = "权益商品名字")
    private String goodsName;

    /**
     * 供应商id
     */
    @NotNull(message = "供应商id不能为空", groups = {AddGroup.class})
    @Schema(description = "供应商id")
    private Integer supplierId;

    /**
     * 供应商商品编号 （兑换方式=直充时，直充面值和供应商商品编码必填其一）
     */
    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;

    /**
     * 充值面值 （兑换方式=直充时，直充面值和供应商商品编码必填其一）
     */
    @Schema(description = "充值面值")
    private BigDecimal parValue;


    /**
     * 兑换方式：1-直充无库存，2-cdKey(兑换码)
     */
    @NotNull(message = "兑换方式不能为空", groups = {AddGroup.class})
    @Schema(description = "兑换方式：1-直充无库存，2-兑换码")
    private Integer redeemType;

    /**
     * 销售价格
     */
    @Schema(description = "销售价格")
    private BigDecimal price;

    /**
     * 成本价格
     */
    @Schema(description = "成本价格")
    private BigDecimal costPrice;


    public EccGoodsDO convert() {
        EccGoodsDO eccGoodsDO = new EccGoodsDO();
        eccGoodsDO.setGoodsId(goodsId);
        eccGoodsDO.setGoodsName(goodsName);
        eccGoodsDO.setSupplierId(supplierId);
        SupplierEnum supplierEnum = SupplierEnum.of(supplierId);
        if (Objects.nonNull(supplierEnum)) {
            eccGoodsDO.setSupplierName(supplierEnum.getDesc());
        }
        eccGoodsDO.setSupplierGoodsNo(supplierGoodsNo);
        eccGoodsDO.setParValue(parValue);
        eccGoodsDO.setRedeemType(redeemType);
        eccGoodsDO.setPrice(price);
        eccGoodsDO.setCostPrice(costPrice);
        return eccGoodsDO;
    }
}
