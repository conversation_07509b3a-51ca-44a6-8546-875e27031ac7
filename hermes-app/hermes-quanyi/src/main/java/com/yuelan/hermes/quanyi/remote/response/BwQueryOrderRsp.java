package com.yuelan.hermes.quanyi.remote.response;


import lombok.Data;

@Data
public class BwQueryOrderRsp {

    private int bank_id;
    private int create_time;
    private String goods_id;
    private int goods_name;
    private String goods_type;
    private String is_game;
    private String order_no;
    private String order_status;
    private int out_trade_no;
    private String product_id;
    private String quantity;
    private String total_pay_amount;

    private Coupon coupon;

    public static class Coupon {
        private int cardId;
        private String snCode;
        private String cardPwd;
        private String cardDwz;
        private String effectStartTime;
        private int expireTime;
        private String cardStatus;
        private String couponDisplayType;
        private String shopUrl;
    }
}
