package com.yuelan.hermes.quanyi.controller.request.temp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/7/29 下午8:03
 */
@Data
public class GamingReqSmsReq {

    @Schema(description = "手机号")
    @NotEmpty(message = "请输入11位手机号码")
    @Length(min = 11, max = 11, message = "请输入11位联通手机号码")
    private String phone;
}
