package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GamingProductListReq extends PageRequest {

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "合作模式")
    private Integer mgModel;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "产品类型")
    private Integer type;

    @Schema(description = "商品ID")
    private Long goodsId;
}