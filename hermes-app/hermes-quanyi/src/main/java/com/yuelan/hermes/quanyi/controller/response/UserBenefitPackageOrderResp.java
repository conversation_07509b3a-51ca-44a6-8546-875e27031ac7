package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 2025/4/24
 * @since 2025/4/24
 */
@Data
public class UserBenefitPackageOrderResp {

    @Schema(description = "订单id")
    public Long packageOrderId;

    @Schema(description = "是否已经退款")
    public boolean refund;

    @Schema(description = "是否已经到期")
    public boolean expired;

    @Schema(description = "权益包订单到期时间")
    public LocalDateTime expiredTime;

    @Schema(description = "订购时间")
    public LocalDateTime orderTime;

    @Schema(description = "N选一权益")
    public NChoseOneBenefitsResp choseBenefits;

    @Schema(description = "即时发放的绑定权益")
    public List<UserBenefitItemStatusResp> instantBenefits;


}
