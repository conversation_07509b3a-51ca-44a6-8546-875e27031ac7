package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> 2024/5/5 下午12:59
 * <p>
 * 用户领取卡片请求
 */
@Data
public abstract class EccBaseGetCardReq {

    @Schema(description = "身份证姓名")
    private String idCardName;

    @Schema(description = "身份证号码")
    private String idCardNo;

    @Schema(description = "联系手机号码")
    private String contactPhone;

    @Schema(description = "省份编码")
    private String postProvinceCode;

    @Schema(description = "省份名")
    private String postProvince;

    @Schema(description = "城市编码")
    private String postCityCode;

    @Schema(description = "城市名")
    private String postCity;

    @Schema(description = "区县编码")
    private String postDistrictCode;

    @Schema(description = "区县名")
    private String postDistrict;

    @Schema(description = "详细收货地址")
    private String address;

    @Schema(description = "我方产品编码")
    private String eccProdCode;

    @Schema(description = "pageUrl")
    private String pageUrl;

    @Schema(description = "订单类型 0-普通方法 1-15天下单限制订单")
    private Integer orderType;

    @Schema(description = "广告回传代理")
    private Integer adAgent;


    public void reqCheck() {
        if (Objects.isNull(idCardName)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "身份证姓名不能为空");
        }
        // 名字里面允许出现·   穆罕默德·买买提·买买提
        String idCardNameCopy = idCardName.replaceAll("·", "");
        if (!Validator.isChineseName(idCardNameCopy)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "身份证姓名识别错误，请重新输入");
        }
        if (Objects.isNull(idCardNo)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "身份证号码不能为空");
        } else if (!IdcardUtil.isValidCard(getIdCardNo())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "身份证号码格式错误");
        }
        if (Objects.isNull(contactPhone)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "联系手机号码不能为空");
        } else if (contactPhone.length() != 11) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请输入11位联系手机号码");
        } else if (!PhoneUtil.isMobile(contactPhone)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "联系手机号码格式错误");
        }
        if (Objects.isNull(postProvinceCode) && StrUtil.isBlankIfStr(postProvince)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请选择省份");
        }
        if (Objects.isNull(postCityCode) && StrUtil.isBlankIfStr(postCity)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请选择城市");
        }
        if (Objects.isNull(postDistrictCode) && StrUtil.isBlankIfStr(postDistrict)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "请选择区县");
        }
        if (Objects.isNull(address)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "详细收货地址不能为空");
        }
        if (address.length() < 4) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "详细收货地址太短，请完善收货地址");
        }
        if (Objects.isNull(eccProdCode)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "缺少产品编码信息");
        }
    }

}
