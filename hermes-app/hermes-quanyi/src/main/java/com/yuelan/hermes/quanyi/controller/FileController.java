package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.plugins.oss.core.OssFileTemplate;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@Tag(name = "文件上传")
@RestController
@Slf4j
public class FileController {

    @Resource
    private OssFileTemplate ossFileTemplate;

    @Operation(summary = "统一文件上传地址")
    @PostMapping(value = "/a/file/upload")
    public BizResult<String> upload(@RequestParam("file") MultipartFile file) {
        try {
            String url = ossFileTemplate.uploadFile(file);
            return BizResult.create(url);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw BizException.create(BizErrorCodeEnum.FILE_UPLOAD_ERROR);
        }
    }
}
