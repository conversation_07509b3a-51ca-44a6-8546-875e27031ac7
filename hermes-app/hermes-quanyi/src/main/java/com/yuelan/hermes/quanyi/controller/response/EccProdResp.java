package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.common.enums.EccSpEnum;
import com.yuelan.hermes.quanyi.common.enums.SpProdEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/5/3 下午2:20
 */
@Data
public class EccProdResp {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long prodId;

    /**
     * 产品名字
     */
    @Schema(description = "产品名字")
    private String prodName;

    /**
     * 权益包编码
     */
    @Schema(description = "权益包编码")
    private String prodCode;

    /**
     * 销售价格
     */
    @Schema(description = "销售价格")
    private BigDecimal price;

    /**
     * 基础投放链接
     */
    @Schema(description = "基础投放链接")
    private String distributionUrl;

    /**
     * 运营商的卡信息-产品id
     */
    @Schema(description = "运营商产品id")
    private Integer spProdId;

    /**
     * 运营商的卡信息-产品名字
     */
    @Schema(description = "运营商产品名字")
    private String spProdName;

    /**
     * 运营商的卡信息-商品id
     */
    @Schema(description = "运营商商品id")
    private String spGoodsId;

    /**
     * 运营商的卡信息-商品名字
     */
    @Schema(description = "运营商商品名字")
    private String spGoodsName;

    /**
     * 禁止区域
     */
    @Schema(description = "禁止区域")
    private String prohibitedAreas;

    /**
     * 关联商品数量
     */
    @Schema(description = "关联商品数量")
    private Integer goodsCount;

    /**
     * 上下架状态：0-下架；1-上架
     */
    @Schema(description = "上下架状态：0-下架；1-上架")
    private Integer prodStatus;

    /**
     * 运营商
     */
    @Schema(description = "运营商 1-移动 2-联通 3-电信 4-广电")
    private Integer operator;

    /**
     * 归属地类型
     */
    @Schema(description = "归属地类型 0-全国 1-分省 2-分市")
    private Integer regionType;

    /**
     * 勾选的归属地
     */
    @Schema(description = "支持的归属地列表")
    private List<EccPostAreaResp> regionList;


    public static EccProdResp buildResp(EccProductDO productDO) {
        if (Objects.isNull(productDO)) {
            return null;
        }
        EccProdResp resp = new EccProdResp();
        resp.setProdId(productDO.getProdId());
        resp.setProdName(productDO.getProdName());
        resp.setProdCode(productDO.getProdCode());
        resp.setPrice(productDO.getPrice());
        resp.setDistributionUrl(productDO.getDistributionUrl());
        resp.setSpProdId(productDO.getSpProdId());
        SpProdEnum spProdEnum = SpProdEnum.of(productDO.getSpProdId());
        if (Objects.nonNull(spProdEnum)) {
            EccSpEnum eccSpEnum = spProdEnum.getSpEnum();
            SpEnum spEnum = eccSpEnum.getSpEnum();
            resp.setOperator(spEnum.getCode());
        }

        resp.setSpProdName(productDO.getSpProdName());
        resp.setSpGoodsId(productDO.getSpGoodsId());
        resp.setSpGoodsName(productDO.getSpGoodsName());
        resp.setProhibitedAreas(productDO.getProhibitedAreas());
        resp.setGoodsCount(productDO.getGoodsCount());
        resp.setProdStatus(productDO.getProdStatus());
        resp.setRegionType(productDO.getRegionType());
        if (productDO.getRegionList() != null) {
            resp.setRegionList(JSON.parseArray(productDO.getRegionList(), EccPostAreaResp.class));
        }
        return resp;
    }
}
