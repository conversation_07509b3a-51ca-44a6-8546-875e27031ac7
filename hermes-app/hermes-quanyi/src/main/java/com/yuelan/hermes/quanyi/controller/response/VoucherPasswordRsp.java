package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class VoucherPasswordRsp extends GoodsVirtualStockDetailRsp {

    @ColumnWidth(20)
    @ExcelProperty(value = "卡号")
    @JsonIgnore
    private String voucherCode;

    @ColumnWidth(20)
    @ExcelProperty(value = "卡密")
    @JsonIgnore
    private String voucherPassword;

    @ColumnWidth(20)
    @ExcelProperty(value = "短链接")
    @JsonIgnore
    private String qrCodeUrl;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "有效开始时间")
    @Schema(description = "有效开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "有效结束时间")
    @Schema(description = "有效结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    @ExcelIgnore
    @Schema(description = "0禁用1启用")
    private Integer enable;

}
