package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.handler.impl.BenefitMiGuProxy2PayChannel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Tag(name = "咪咕全国代理版本2")
@RequestMapping("/xcode/migu")
@SaIgnore
@RestController
@AllArgsConstructor
public class MiGuProxy2Controller {

    private final BenefitMiGuProxy2PayChannel benefitMiGuProxyPayChannel;

    @Operation(summary = "回调")
    @GetMapping(value = "/order/notify")
    public String notify(HttpServletRequest request) {
        return benefitMiGuProxyPayChannel.payNotify(request);
    }

}
