package com.yuelan.hermes.quanyi.controller.request;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/6/18 下午3:55
 */
@Data
public class BenefitDistributionUrlCopyReq {

    @NotNull(message = "权益N选1产品id不能为空")
    @Schema(description = "权益N选1产品id")
    private Long benefitProductId;

    @NotNull(message = "渠道Id不能为空")
    @Schema(description = "渠道Id")
    private String channelId;

    @Schema(title = "广告渠道Id", description = "可以传空")
    private Long adChannelId;

    @Schema(title = "占位符替换参数", description = "可以传空，传了会替换链接中的占位符")
    private JSONObject replaceParams;
}
