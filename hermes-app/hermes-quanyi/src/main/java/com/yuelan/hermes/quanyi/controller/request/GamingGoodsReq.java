package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.EnumLimit;
import com.yuelan.hermes.commons.enums.DeliveryTypeEnum;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 电竞卡商品管理
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GamingGoodsReq extends PageRequest {

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "上下架状态0下架1上架")
    private Integer status;

    @Schema(description = "供应商类型")
    private Integer supplierType;

    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;

    @Schema(description = "库存状态：0-全部状态 1-仅库存预警状态")
    private Integer stockAlertStatus;

    @Schema(description = "发放方式：1-直充，2-兑换码")
    @EnumLimit(message = "发放方式不合法", enumInterface = DeliveryTypeEnum.class)
    private Integer deliveryType;


}