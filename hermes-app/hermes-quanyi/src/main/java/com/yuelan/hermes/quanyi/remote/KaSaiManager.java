package com.yuelan.hermes.quanyi.remote;


import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.yuelan.hermes.quanyi.common.pojo.properties.KaiSaiProperties;
import com.yuelan.hermes.quanyi.remote.kassai.request.*;
import com.yuelan.hermes.quanyi.remote.kassai.response.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR> 2025/7/9
 * @since 2025/7/9
 */
@Service
@Slf4j
@AllArgsConstructor
public class KaSaiManager {

    private static final String BASE_URL = "https://10099.cscsearch.com/ocs_broadnet";
    private static final String HEADER_TOKEN = "token";

    private final KaiSaiProperties kaiSaiProperties;

    /**
     * 下单
     *
     * @param req 请求
     * @return {@link BroadnetOrderDto}
     */
    public KsBaseResponse<BroadnetOrderDto> tradeOrder(BroadnetTradeOrderReq req) {
        KaiSaiProperties.KsGoodsInfo ksGoodsInfo = kaiSaiProperties.getGoodsInfoByGoodsCode(req.getLinkNum());

        String url = BASE_URL + "/cbn/api/v/tradeOrder";
        log.info("卡赛下单请求地址:{}", url);
        log.info("卡赛下单请求参数:{}", JSON.toJSONString(req));
        try (HttpResponse response = HttpRequest.post(url)
                .header(HEADER_TOKEN, ksGoodsInfo.getToken())
                .body(JSON.toJSONString(req)).execute()) {
            String body = response.body();
            log.info("卡赛下单返回参数:{}", body);
            return JSON.parseObject(body, new TypeReference<KsBaseResponse<BroadnetOrderDto>>() {
            });
        }
    }


    /**
     * 取消订单
     *
     * @param req Req
     */
    public KsBaseResponse<Void> cancelOrder(CancelOrderReq req) {
        KaiSaiProperties.KsGoodsInfo ksGoodsInfo = kaiSaiProperties.getDefaultGoodsInfo();

        String url = BASE_URL + "/cbn/api/v/cancleOrder";
        log.info("卡赛取消订单请求地址:{}", url);
        log.info("卡赛取消订单请求参数:{}", JSON.toJSONString(req));
        try (HttpResponse response = HttpRequest.get(url)
                .header(HEADER_TOKEN, ksGoodsInfo.getToken())
                .form(JSON.parseObject(JSON.toJSONString(req))).execute()) {
            String body = response.body();
            log.info("卡赛取消订单返回参数:{}", body);
            return JSON.parseObject(body, new TypeReference<KsBaseResponse<Void>>() {
            });
        }
    }


    /**
     * 获取token
     * <p>
     * 只需要在接入新产品调用一次就行了
     * 产品 kLnzPm6N  固定=live+10522
     *
     * @param req Req
     * @return {@link GetTokenDto}
     */
    public KsBaseResponse<GetTokenDto> getToken(GetTokenReq req) {
        String url = BASE_URL + "/cbn/api/v/loadToken";
        log.info("卡赛获取token请求地址:{}", url);
        log.info("卡赛获取token请求参数:{}", JSON.toJSONString(req));
        try (HttpResponse response = HttpRequest.get(url).form(JSON.parseObject(JSON.toJSONString(req))).execute()) {
            String body = response.body();
            log.info("卡赛获取token返回参数:{}", body);
            KsBaseResponse<GetTokenDto> resp = JSON.parseObject(body, new TypeReference<KsBaseResponse<GetTokenDto>>() {
            });
            resp.getData().setToken(response.header("token"));
            return resp;
        }
    }




    /**
     * 选号
     *
     * @param req Req
     */
    public KsBaseResponse<List<BroadnetSelectDto>> selectNumber(BroadnetSelectNumberReq req) {
        KaiSaiProperties.KsGoodsInfo ksGoodsInfo = kaiSaiProperties.getGoodsInfoByGoodsCode(req.getLinkNum());

        String url = BASE_URL + "/cbn/api/v/numberSelect";
        log.info("卡赛选号请求地址:{}", url);
        log.info("卡赛选号请求参数:{}", JSON.toJSONString(req));
        try (HttpResponse response = HttpRequest.post(url)
                .header(HEADER_TOKEN, ksGoodsInfo.getToken())
                .body(JSON.toJSONString(req)).execute()) {
            String body = response.body();
            log.info("卡赛选号返回参数:{}", body);
            return JSON.parseObject(body, new TypeReference<KsBaseResponse<List<BroadnetSelectDto>>>() {
            });
        }
    }


    /**
     * 配送所属区
     *
     * @param req Req
     */
    public KsBaseResponse<List<BroadnetExpressArea>> findExpressArea(FindExpressAreaReq req) {
        KaiSaiProperties.KsGoodsInfo ksGoodsInfo = kaiSaiProperties.getDefaultGoodsInfo();

        String url = BASE_URL + "/cbn/api/v/findExpressArea";
        log.info("卡赛配送所属区请求地址:{}", url);
        log.info("卡赛配送所属区请求参数:{}", JSON.toJSONString(req));
        try (HttpResponse response = HttpRequest.get(url)
                .header(HEADER_TOKEN, ksGoodsInfo.getToken())
                .form(JSON.parseObject(JSON.toJSONString(req))).execute()) {
            String body = response.body();
            log.info("卡赛配送所属区返回参数:{}", body);
            return JSON.parseObject(body, new TypeReference<KsBaseResponse<List<BroadnetExpressArea>>>() {
            });
        }
    }


    /**
     * 配送所属省
     *
     * @param req Req
     */
    public KsBaseResponse<List<BroadnetExpressArea>> findExpressProvince(FindExpressProvinceReq req) {
        KaiSaiProperties.KsGoodsInfo ksGoodsInfo = kaiSaiProperties.getDefaultGoodsInfo();

        String url = BASE_URL + "/cbn/api/v/findExpressProvince";
        log.info("卡赛配送所属省请求地址:{}", url);
        log.info("卡赛配送所属省请求参数:{}", JSON.toJSONString(req));
        try (HttpResponse response = HttpRequest.get(url)
                .header(HEADER_TOKEN, ksGoodsInfo.getToken())
                .execute()) {
            String body = response.body();
            log.info("卡赛配送所属省返回参数:{}", body);
            return JSON.parseObject(body, new TypeReference<KsBaseResponse<List<BroadnetExpressArea>>>() {
            });
        }
    }


    /**
     * 配送所属市
     *
     * @param req Req
     */
    public KsBaseResponse<List<BroadnetExpressArea>> findExpressCity(FindExpressCityReq req) {
        KaiSaiProperties.KsGoodsInfo ksGoodsInfo = kaiSaiProperties.getDefaultGoodsInfo();
        String url = BASE_URL + "/cbn/api/v/findExpressCity";
        log.info("卡赛配送所属市请求地址:{}", url);
        log.info("卡赛配送所属市请求参数:{}", JSON.toJSONString(req));
        try (HttpResponse response = HttpRequest.get(url)
                .header(HEADER_TOKEN, ksGoodsInfo.getToken())
                .form(JSON.parseObject(JSON.toJSONString(req))).execute()) {
            String body = response.body();
            log.info("卡赛配送所属市返回参数:{}", body);
            return JSON.parseObject(body, new TypeReference<KsBaseResponse<List<BroadnetExpressArea>>>() {
            });
        }
    }
}
