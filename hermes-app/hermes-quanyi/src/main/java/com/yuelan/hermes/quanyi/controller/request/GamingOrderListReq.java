package com.yuelan.hermes.quanyi.controller.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuelan.core.validator.constraints.EnumLimit;
import com.yuelan.hermes.commons.constant.CommonConstants;
import com.yuelan.hermes.quanyi.common.enums.MgCollaborationModeEnum;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GamingOrderListReq extends PageRequest {

    @Schema(description = "订单号")
    private List<Long> orderIds;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "商户订单号")
    private String outOrderNo;

    @Schema(description = "供应商订单号")
    private String supplierOrderNo;

    @Schema(description = "商户ID")
    private Long merchantId;

    @Schema(description = "第三方用户手机号")
    private String phone;

    @Schema(description = "咪咕合作模式：1-杭州西米（未走咪咕平台） 2-禾辰（咪咕新平台）3-长沙西米（咪咕新平台）4-杭州西米（咪咕新平台）")
    @EnumLimit(message = "去选择合作模式", enumInterface = MgCollaborationModeEnum.class)
    private Integer mgModel;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "产品ID列表")
    private List<Long> productIds;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "通知状态")
    private Integer notifyStatus;

    @Schema(description = "包月类型 0-点播/首次包月 1-续订")
    private Integer monthlyType;

    @Schema(description = "产品类型：1-点播 2-权益（包月）")
    private Integer productType;

    @Schema(description = "下单开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderStartTime;

    @Schema(description = "下单结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderEndTime;

    @Schema(description = "更新开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateStartTime;

    @Schema(description = "更新结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateEndTime;

    private Long startId;



    public void check() {
        if (Objects.equals(CommonConstants.ALL_OPTION, orderStatus)) {
            this.orderStatus = null;
        }
        if (Objects.equals(CommonConstants.ALL_OPTION, notifyStatus)) {
            this.notifyStatus = null;
        }
    }
}
