package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> 2024/4/26 上午10:53
 */
@Data
public class EcommerceSimCardReceiveReq {

    /**
     * 身份证名字
     */
    @Schema(description = "身份证名字")
    private String idCardName;

    /**
     * 身份证号码
     */
    @Schema(description = "身份证号码")
    private String idCardNo;

    /**
     * 联系手机号码
     */
    @Schema(description = "联系手机号码")
    private String phone;

    /**
     * 收货区县
     */
    @Schema(description = "收货区县")
    private String area;

    /**
     * 收货地址
     */
    @Schema(description = "收货地址")
    private String address;

    /**
     * 选择的手机号码
     */
    @Schema(description = "选择的手机号码")
    private String simCardPhone;

}
