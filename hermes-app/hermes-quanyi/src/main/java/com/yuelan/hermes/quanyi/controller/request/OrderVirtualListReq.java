package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderVirtualListReq extends PageRequest {

    @Schema(description = "订单号列表")
    private List<String> orderNos;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "第三方订单号")
    private String outOrderNo;

    @Schema(description = "用户手机号")
    private String phone;

    @Schema(description = "充值账号")
    private String userAccount;

    @Schema(description = "商品类型")
    private Integer goodsType;

    @Schema(description = "虚拟商品ID")
    private Long goodsId;

    @Schema(description = "虚拟商品SKU编号")
    private String skuNo;

    @Schema(description = "供应商")
    private String supplier;

    @Schema(description = "集采客户")
    private String mchId;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "回调状态")
    private Integer notifyStatus;

    @Schema(description = "卡号")
    private String voucherCode;

    @Schema(description = "卡密")
    private String voucherPassword;

    @Schema(description = "下单开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderStartTime;

    @Schema(description = "下单结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderEndTime;


    public void check() {
        if (Objects.nonNull(orderEndTime)) {
            orderEndTime = DateUtil.endOfDay(orderEndTime);
        }
        if (CollectionUtil.isNotEmpty(orderNos)) {
            orderNos = orderNos.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        }
    }

}