package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.fastjson2.JSONArray;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/8 11:40
 */
@Data
public class UserBenefitProductResp {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long prodId;

    /**
     * 产品名字
     */
    @Schema(description = "产品名字")
    private String prodName;

    /**
     * 销售价格
     */
    @Schema(description = "销售价格")
    private BigDecimal price;

    /**
     * 背景色代码
     */
    @Schema(description = "背景色代码")
    private String bgColorCode;

    /**
     * 首页图
     */
    @Schema(description = "首页图")
    private String homepage;

    /**
     * 宣传组图
     */
    @Schema(description = "宣传组图")
    private List<String> imgs;

    /**
     * 协议文档
     */
    @Schema(description = "协议文档")
    private String agreementContent;

    /**
     * 兑换次数限制（多合一会员可以兑换几个）
     */
    @Schema(description = "兑换次数限制")
    private Integer redeemLimit;


    public static UserBenefitProductResp buildResp(BenefitProductDO productDO) {
        UserBenefitProductResp resp = new UserBenefitProductResp();
        resp.setProdId(productDO.getProdId());
        resp.setProdName(productDO.getProdName());
        resp.setPrice(productDO.getPrice());
        resp.setBgColorCode(productDO.getBgColorCode());
        resp.setHomepage(productDO.getHomepage());
        if (Objects.nonNull(productDO.getImgs())) {
            resp.setImgs(JSONArray.parseArray(productDO.getImgs(), String.class));
        }
        resp.setAgreementContent(productDO.getAgreementContent());
        resp.setRedeemLimit(productDO.getRedeemLimit());
        return resp;
    }
}
