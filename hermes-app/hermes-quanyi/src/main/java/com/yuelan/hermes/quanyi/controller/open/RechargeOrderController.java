package com.yuelan.hermes.quanyi.controller.open;


import com.yuelan.hermes.quanyi.biz.service.RechargeOrderService;
import com.yuelan.hermes.quanyi.common.pojo.bo.MchThirdConfigBO;
import com.yuelan.hermes.quanyi.controller.request.RechargerOrderDetailReq;
import com.yuelan.hermes.quanyi.controller.request.RechargerOrderUnifiedReq;
import com.yuelan.hermes.quanyi.controller.response.RechargerOrderDetailRsp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "充值API")
@RequestMapping("/recharge")
@RestController
public class RechargeOrderController extends MchBaseController {
    @Autowired
    private RechargeOrderService rechargeOrderService;

    /**
     * 支持三网
     */
    @Operation(summary = "话费快充充值下单")
    @PostMapping(value = "/order/fast")
    public BizResult<String> fastOrder(@RequestBody RechargerOrderUnifiedReq req) {
        MchThirdConfigBO mchThirdConfigBO = checkSign(req);
        return rechargeOrderService.createOrder(req, mchThirdConfigBO, false);
    }

    @Operation(summary = "话费慢充充值下单")
    @PostMapping(value = "/order/slow")
    public BizResult<String> slowOrder(@RequestBody RechargerOrderUnifiedReq req) {
        MchThirdConfigBO mchThirdConfigBO = checkSign(req);
        return rechargeOrderService.createOrder(req, mchThirdConfigBO, true);
    }

    @Operation(summary = "充值订单查询")
    @PostMapping(value = "/order/detail")
    public BizResult<RechargerOrderDetailRsp> orderDetail(@RequestBody RechargerOrderDetailReq req) {
        checkSign(req);
        RechargerOrderDetailRsp result = rechargeOrderService.orderDetail(req);
        return BizResult.create(result);
    }

}
