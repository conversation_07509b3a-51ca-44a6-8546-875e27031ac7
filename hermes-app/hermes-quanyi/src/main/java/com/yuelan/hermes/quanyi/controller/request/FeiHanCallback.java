package com.yuelan.hermes.quanyi.controller.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.00
 * @description: ret_code=1&sporder_id=test001234567&ordersuccesstime=20160817140214&err_msg=
 * @ClassName: FeiHanCallback
 * @Date 2024/3/29 10:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeiHanCallback {
    /** 充值后状态，1代表成功，9代表撤消 */
    private String retCode;
    /** sp订单号 */
    private String spOrderId;
    /** 处理时间 */
    private String orderSuccessTime;
    /** 失败原因(ret_code为1时，该值为空) */
    private String errMsg;
}
