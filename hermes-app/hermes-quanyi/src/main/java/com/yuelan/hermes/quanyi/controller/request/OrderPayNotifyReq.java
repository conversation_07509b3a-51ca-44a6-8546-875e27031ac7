package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class OrderPayNotifyReq {

    @Schema(description = "业务响应码")
    private String bizCode;

    @Schema(description = "业务响应信息")
    private String bizMsg;

    @Schema(description = "响应时间戳")
    private String timeStamp;

    @Schema(description = "业务数据签名结果")
    private String sign;

    @Schema(description = "商户编号")
    private String mno;

    @Schema(description = "商户订单号")
    private String ordNo;

    @Schema(description = "天阙平台订单号")
    private String uuid;

    @Schema(description = "交易支付完成时间，格式：YYYYMMDDHHmmss")
    private String payTime;

    @Schema(description = "订单总金额(元)，格式：#######.##")
    private String amt;
    /**
     * 枚举值
     * 取值范围：
     * WECHAT 微信
     * ALIPAY 支付宝
     * UNIONPAY 银联
     * DCEP 数字人民币
     */
    @Schema(description = "支付渠道")
    private String payType;
    /**
     * 枚举值
     * 取值范围：
     * 00 主扫
     * 01 被扫
     * 02 微信公众号/支付宝服务窗/银联js支付/支付宝小程序
     * 03 微信小程序
     */
    @Schema(description = "交易方式")
    private String payWay;
    /**
     * 支付宝渠道：买家支付宝用户号buyer_user_id
     * 微信渠道：微信平台的sub_openid
     */
    @Schema(description = "买家用户号")
    private String buyerId;

    @Schema(description = "微信/支付宝流水号")
    private String transactionId;
    /**
     * 枚举值
     * 取值范围：
     * 1 借记卡
     * 2 贷记卡
     * 3 其他
     */
    @Schema(description = "借贷标识")
    private String drType;

    @Schema(description = "消费者付款金额（单位：元）")
    private String totalOffstAmt;
    /**
     * 包含手续费、预充值、平台补贴（优惠），不含免充值代金券（商家补贴）
     */
    @Schema(description = "商家入账金额（单位：元）")
    private String settleAmt;

    @Schema(description = "付款银行")
    private String payBank;
    /**
     * 积分支付金额，优惠金额或折扣券的金额
     */
    @Schema(description = "代金券金额（单位：元）")
    private String pointAmount;

    @Schema(description = "交易手续费率（单位：%）")
    private String recFeeRate;

    @Schema(description = "交易手续费（单位：元）")
    private String recFeeAmt;

    @Schema(description = "商家出款金额")
    private String realRefundAmount;

    @Schema(description = "渠道商商户号")
    private String channelId;

    @Schema(description = "子商户号")
    private String subMechId;

    @Schema(description = "消费者到账金额")
    private String refBuyerAmt;

    @Schema(description = "微信或支付宝的身份ID,OPENID或USERID")
    private String openid;
    /**
     * 枚举值
     * 取值范围：
     * 00 线下标准
     * 01 线上标准
     * 02 蓝海
     * 03 绿洲
     * 04 线下缴费
     * 05 线下保险
     * 06 线下非赢利
     * 07 高校食堂
     * 08 私立院校
     * 09 其他
     * 10 教培机构
     */
    @Schema(description = "活动类型")
    private String activityNo;

    @Schema(description = "落单号")
    private String sxfUuid;

    @Schema(description = "扩展字段")
    private String extend;

    @Schema(description = "天阙门店编号")
    private String storeNum;

    @Schema(description = "清算日期")
    private String clearDt;

    @Schema(description = "交易完成时间")
    private String finishTime;
}
