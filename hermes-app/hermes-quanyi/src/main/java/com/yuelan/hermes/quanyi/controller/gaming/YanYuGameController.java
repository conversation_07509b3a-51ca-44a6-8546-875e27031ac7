package com.yuelan.hermes.quanyi.controller.gaming;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.session.SaSession;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.biz.manager.YanYuGamingManager;
import com.yuelan.hermes.quanyi.common.enums.OsTypeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.HshUserSessionUser;
import com.yuelan.hermes.quanyi.common.pojo.bo.YanYuRoleInfoBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO;
import com.yuelan.hermes.quanyi.config.satoken.StpHshUserUtil;
import com.yuelan.hermes.quanyi.controller.response.GamingBenefitDetailResp;
import com.yuelan.hermes.quanyi.controller.response.GamingOrderItemSimpleRsp;
import com.yuelan.hermes.quanyi.mapper.GamingOrderItemMapper;
import com.yuelan.hermes.quanyi.remote.gaming.LanJinManager;
import com.yuelan.hermes.quanyi.remote.response.LanJinUserInfoResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/7/11
 * @since 2025/7/11
 */
@Slf4j
@Validated
@RestController
@Tag(name = "电竞卡-烟雨江湖接口")
@RequiredArgsConstructor
@SaCheckLogin(type = StpHshUserUtil.TYPE)
@RequestMapping("/yanyu")
public class YanYuGameController {

    private final YanYuGamingManager yanYuGamingManager;
    private final GamingOrderItemMapper gamingOrderItemMapper;
    private final LanJinManager lanJinManager;

    @Operation(summary = "当月所有权益明细")
    @PostMapping("/benefitDetail")
    public BizResult<GamingBenefitDetailResp> benefitDetail() {
        GamingBenefitDetailResp detail = new GamingBenefitDetailResp();
        Long userId = StpHshUserUtil.getLoginIdAsLong();

        YanYuRoleInfoBO roleInfoBO = yanYuGamingManager.queryBindRoleId(userId);
        if (Objects.nonNull(roleInfoBO)) {
            detail.setRoleId(roleInfoBO.getRoleId());
            detail.setOsType(roleInfoBO.getOsType());
        }
        HshUserSessionUser sessionUser = StpHshUserUtil.getSession().getModel(SaSession.USER, HshUserSessionUser.class);
        String phone = sessionUser.getPhone();

        // 本月订单 - 只查询蓝鲸供应商的订单
        LocalDateTime start = LocalDate.now().withDayOfMonth(1).atStartOfDay();
        LocalDateTime end = LocalDateTime.now();
        List<GamingOrderItemDO> items = gamingOrderItemMapper.findByPhoneAndTimeAndSupplierType(phone, start, end, SupplierEnum.LAN_JIN.getCode());
        List<GamingOrderItemSimpleRsp> simpleRspList = items.stream().map(GamingOrderItemSimpleRsp::buildRsp).collect(Collectors.toList());
        detail.setItems(simpleRspList);

        return BizResult.create(detail);
    }

    /**
     * 登录后用户，查询游戏内角色信息
     */
    @Operation(summary = "查询游戏内角色信息")
    @PostMapping("/getRoleInfo/{roleId}/{osType}")
    @Parameters({
            @Parameter(name = "roleId", description = "角色id"),
            @Parameter(name = "osType", description = "操作系统类型：1-iOS，2-Android")
    })
    public BizResult<LanJinUserInfoResp> getRoleInfo(@PathVariable @NotEmpty(message = "角色id不能为空") String roleId,
                                                     @PathVariable @NotNull(message = "操作系统类型不能为空") Integer osType) {
        LanJinUserInfoResp resp = null;
        try {
            OsTypeEnum osTypeEnum = OsTypeEnum.of(osType);
            if (osTypeEnum == null) {
                throw BizException.create(BaseErrorCodeEnum.SYSTEM_BUSY, "请选择正确的操作系统类型");
            }
            resp = lanJinManager.getUserInfo(roleId, osTypeEnum.getName());
        } catch (Exception e) {
            log.error("查询角色信息失败", e);
        }
        if (resp != null) {
            if (resp.isSuccess()) {
                return BizResult.create(resp);
            } else {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, resp.getMsg());
            }
        }
        throw BizException.create(BaseErrorCodeEnum.SYSTEM_BUSY, "查询角色信息失败");
    }

    /**
     * 绑定并且，确认发放到角色（双向绑定 一个用户只能绑定一个烟雨江湖的角色   只兑换当月订单）
     */
    @Operation(summary = "绑定并且确认兑换到角色")
    @GetMapping("/exchange")
    @Parameters({
            @Parameter(name = "itemId", description = "订单id"),
            @Parameter(name = "roleId", description = "角色id，未绑定角色时候必传"),
            @Parameter(name = "osType", description = "操作系统类型：1-iOS，2-Android")
    })
    public BizResult<Void> exchange(@Param("itemId") String itemId,
                                    @Param("roleId") String roleId,
                                    @Param("osType") @NotNull(message = "操作系统类型不能为空") Integer osType) {

        OsTypeEnum osTypeEnum = OsTypeEnum.of(osType);
        if (osTypeEnum == null) {
            throw BizException.create(BaseErrorCodeEnum.SYSTEM_BUSY, "请选择正确的操作系统类型");
        }

        HshUserSessionUser sessionUser = StpHshUserUtil.getSession().getModel(SaSession.USER, HshUserSessionUser.class);
        yanYuGamingManager.exchangeBenefit(sessionUser, itemId, osType, roleId);
        return BizResult.ok();
    }


}
