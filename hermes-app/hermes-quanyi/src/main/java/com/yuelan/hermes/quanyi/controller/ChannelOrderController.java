package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.EccNcOrderService;
import com.yuelan.hermes.quanyi.controller.request.EccNcOrderListReq;
import com.yuelan.hermes.quanyi.controller.response.EccNcOrderResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 渠道用户订单管理控制器
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
@Tag(name = "渠道用户订单管理")
@RestController
@RequestMapping("/channel/order")
@RequiredArgsConstructor
public class ChannelOrderController extends ChannelBaseController {

    private final EccNcOrderService eccNcOrderService;

    /**
     * 分页查询订单列表（渠道用户专用）
     */
    @Operation(summary = "分页查询订单列表")
    @PostMapping("/pageList")
    public BizResult<PageData<EccNcOrderResp>> pageList(@Valid @RequestBody EccNcOrderListReq req) {
        Long adminId = getCurrentAdminId();
        PageData<EccNcOrderResp> result = eccNcOrderService.pageList(req, adminId);
        return BizResult.create(result);
    }

    /**
     * 导出订单数据（渠道用户专用）
     */
    @Operation(summary = "导出订单数据")
    @PostMapping("/export")
    public void export(@Valid @RequestBody EccNcOrderListReq req) {
        Long adminId = getCurrentAdminId();
        eccNcOrderService.export(req, adminId);
    }

    /**
     * 获取订单详情
     */
    @Operation(summary = "获取订单详情")
    @GetMapping("/detail/{orderId}")
    public BizResult<EccNcOrderResp> getOrderDetail(@PathVariable Long orderId) {
        Long channelId = getCurrentChannelId();

        // 这里需要检查订单是否对当前渠道可见
        // 可以通过订单服务的权限控制来实现
        EccNcOrderResp order = eccNcOrderService.getOrderDetail(orderId, channelId);

        return BizResult.create(order);
    }

    /**
     * 获取订单统计信息（渠道用户专用）
     */
    @Operation(summary = "获取订单统计信息")
    @PostMapping("/statistics")
    public BizResult<ChannelOrderStatistics> getOrderStatistics(@Valid @RequestBody EccNcOrderListReq req) {
        Long channelId = getCurrentChannelId();

        // 获取订单统计数据
        ChannelOrderStatistics statistics = eccNcOrderService.getChannelOrderStatistics(req, channelId);

        return BizResult.create(statistics);
    }

    /**
     * 渠道订单统计信息
     */
    public static class ChannelOrderStatistics {
        /**
         * 总订单数
         */
        private Long totalOrders;

        /**
         * 发展订单数（作为发展渠道的订单）
         */
        private Long developmentOrders;

        /**
         * 扣量订单数（作为扣量渠道的订单）
         */
        private Long deductionOrders;

        /**
         * 激活订单数
         */
        private Long activatedOrders;

        /**
         * 首充订单数
         */
        private Long firstChargeOrders;

        /**
         * 总首充金额（分）
         */
        private Long totalFirstChargeAmount;

        // Getters and Setters
        public Long getTotalOrders() {
            return totalOrders;
        }

        public void setTotalOrders(Long totalOrders) {
            this.totalOrders = totalOrders;
        }

        public Long getDevelopmentOrders() {
            return developmentOrders;
        }

        public void setDevelopmentOrders(Long developmentOrders) {
            this.developmentOrders = developmentOrders;
        }

        public Long getDeductionOrders() {
            return deductionOrders;
        }

        public void setDeductionOrders(Long deductionOrders) {
            this.deductionOrders = deductionOrders;
        }

        public Long getActivatedOrders() {
            return activatedOrders;
        }

        public void setActivatedOrders(Long activatedOrders) {
            this.activatedOrders = activatedOrders;
        }

        public Long getFirstChargeOrders() {
            return firstChargeOrders;
        }

        public void setFirstChargeOrders(Long firstChargeOrders) {
            this.firstChargeOrders = firstChargeOrders;
        }

        public Long getTotalFirstChargeAmount() {
            return totalFirstChargeAmount;
        }

        public void setTotalFirstChargeAmount(Long totalFirstChargeAmount) {
            this.totalFirstChargeAmount = totalFirstChargeAmount;
        }
    }
}
