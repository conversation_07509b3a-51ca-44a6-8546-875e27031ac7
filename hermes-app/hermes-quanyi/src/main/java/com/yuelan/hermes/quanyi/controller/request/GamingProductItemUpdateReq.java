package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.YearMonth;
import java.util.List;
import java.util.Objects;

@Data
public class GamingProductItemUpdateReq {

    @NotNull(message = "关联商品Ids不能为空")
    @Size(min = 1, message = "关联商品Ids不能为空")
    @Schema(description = "关联商品IDs")
    private List<Long> itemIds;

    @Schema(description = "生效月份yyyy-MM ，该月第一天")
    @NotNull(message = "请选择生效月份")
    private YearMonth effectiveMonth;

    @Schema(description = "过期月份yyyy-MM,含该月最后一天")
    private YearMonth expireMonth;

    public void checkReq() {
        if (Objects.isNull(effectiveMonth)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "生效时间必填");
        }
        if (Objects.nonNull(expireMonth) && expireMonth.isBefore(effectiveMonth)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "过期时间不能早于生效时间");
        }
    }

}
