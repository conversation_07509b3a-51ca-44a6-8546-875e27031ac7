package com.yuelan.hermes.quanyi.controller.open;

import com.alibaba.fastjson2.JSON;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> 2025/6/25
 * @since 2025/6/25
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/wx/jsapi/{appid}")
public class WxJsapiController {

    private final WxMpService wxMpService;

    @PostMapping("/createJsapiSignature")
    @Operation(summary = "生成jsapi签名", description = "根据url生成当前页面的jsapi签名")
    public BizResult<WxJsapiSignature> createJsapiSignature(@PathVariable String appid, @RequestBody String url) {
        WxJsapiSignature jsapiSignature;
        try {
            WxMpService mpService = wxMpService.switchoverTo(appid);
            if (mpService == null) {
                log.error("No WxMpService found for appid: {}", appid);
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "无效的appid");
            }
            jsapiSignature = mpService.createJsapiSignature(url);
        } catch (WxErrorException e) {
            log.error("Error creating jsapi signature for appid: {}, url: {}", appid, url, e);
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "出错啦");
        }
        log.info("url：{} jsapiSignature: {}", url, JSON.toJSONString(jsapiSignature));
        return BizResult.create(jsapiSignature);
    }
}