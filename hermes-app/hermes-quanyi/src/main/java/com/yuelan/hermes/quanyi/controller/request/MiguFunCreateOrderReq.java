package com.yuelan.hermes.quanyi.controller.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class MiguFunCreateOrderReq {

    @Schema(description = "签名")
    private String sign;

    @NotBlank(message = "orderId不能为空")
    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "快游用户ID")
    private String userId;

    @Schema(description = "发起时间(13 位时间戳) body")
    private Long createTime;

    @NotBlank(message = "phoneNum不能为空")
    @Schema(description = "用户手机号")
    private String phoneNum;

    @NotBlank(message = "productCode不能为空")
    @Schema(description = "道具标识")
    private String productCode;

    @Schema(description = "扩展字段(咪咕互娱透传用)")
    private String extrInfo;

    @Schema(description = "兑换账号，采用SM4对称加密方式")
    private String exchangeAccount;

    /**
     * 解密后的手机号
     */
    @JsonIgnore
    private String phone;

    /**
     * 解密后的account
     */
    @JsonIgnore
    private String account;
}
