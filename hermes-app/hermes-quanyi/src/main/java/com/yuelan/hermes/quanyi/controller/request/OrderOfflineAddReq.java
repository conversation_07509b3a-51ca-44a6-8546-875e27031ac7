package com.yuelan.hermes.quanyi.controller.request;

import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderOfflineAddReq {

    @NotBlank(message = "sku不能为空")
    @Schema(description = "SKU编号")
    private String skuNo;

    @NotBlank(message = "客户名称不能为空")
    @Schema(description = "集采客户名称")
    private String buyer;

    @NotNull(message = "销售单价不能为空")
    @Schema(description = "销售单价")
    private BigDecimal price;

    @NotNull(message = "订单模式不能为空")
    @Schema(description = "订单模式0卡密1模板充值（短信）2模板充值（无短信）")
    private Integer orderModel;

    @Schema(description = "充值账户")
    private List<UserAccountReq> userAccounts = Lists.newArrayList();

    @Schema(description = "商品数量")
    private Integer quantity;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "短信模板")
    private String smsTemplate;

}
