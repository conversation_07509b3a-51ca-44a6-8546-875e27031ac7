package com.yuelan.hermes.quanyi.controller.wx;

import com.yuelan.hermes.quanyi.config.WxConfig;
import com.yuelan.hermes.quanyi.config.WxMpMessageRouterConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/wx/portal")
@Tag(name = "微信portal接口")
@AllArgsConstructor
public class WxPortalController {
    private static final String ENC_TYPE_AES = "aes";
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private final WxConfig wxConfig;
    private final WxMpMessageRouterConfig wxMpMessageRouterConfig;

    @GetMapping(produces = "text/plain;charset=utf-8")
    @Operation(summary = "用来进行微信认证，也就是当在：" +
            "公众号官方管理后台->开发->基本配置->修改配置时，需要进行的校验")
    public String authGet(@RequestParam(name = "signature", required = false) String signature,
                          @RequestParam(name = "timestamp", required = false) String timestamp,
                          @RequestParam(name = "nonce", required = false) String nonce,
                          @RequestParam(name = "echostr", required = false) String echostr) {

        this.logger.info("\n接收到来自微信服务器的认证消息：[{}, {}, {}, {}]", signature,
                timestamp, nonce, echostr);
        if (StringUtils.isAnyBlank(signature, timestamp, nonce, echostr)) {
            // 请求参数非法，请核实
            throw new IllegalArgumentException("yami.sys.illegalArgument");
        }

        if (wxConfig.getWxMpService().checkSignature(timestamp, nonce, signature)) {
            return echostr;
        }
        return "非法请求";
    }

    @PostMapping(produces = "application/xml; charset=utf-8")
    @Operation(summary = "接收公众号的消息")
    public String post(@RequestBody String requestBody,
                       @RequestParam("signature") String signature,
                       @RequestParam("timestamp") String timestamp,
                       @RequestParam("nonce") String nonce,
                       @RequestParam("openid") String openid,
                       @RequestParam(name = "encrypt_type", required = false) String encType,
                       @RequestParam(name = "msg_signature", required = false) String msgSignature) {
        this.logger.info("\n接收微信请求：[openid=[{}], [signature=[{}], encType=[{}], msgSignature=[{}],"
                        + " timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
                openid, signature, encType, msgSignature, timestamp, nonce, requestBody);


        String out = null;
        if (encType == null) {
            // 明文传输的消息
            WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(requestBody);
            WxMpXmlOutMessage outMessage = this.route(inMessage);
            if (outMessage == null) {
                return "";
            }
            out = outMessage.toXml();
        } else if (ENC_TYPE_AES.equalsIgnoreCase(encType)) {
            // aes加密的消息
            WxMpXmlMessage inMessage = WxMpXmlMessage.fromEncryptedXml(requestBody, wxConfig.getWxMpService().getWxMpConfigStorage(),
                    timestamp, nonce, msgSignature);
            this.logger.debug("\n消息解密后内容为：\n{} ", inMessage.toString());
            WxMpXmlOutMessage outMessage = this.route(inMessage);
            if (outMessage == null) {
                return "";
            }

            out = outMessage.toEncryptedXml(wxConfig.getWxMpService().getWxMpConfigStorage());
        }
        this.logger.debug("\n组装回复信息：{}", out);
        return out;
    }

    private WxMpXmlOutMessage route(WxMpXmlMessage message) {
        try {
            return wxMpMessageRouterConfig.getMessageRouter().route(message);
        } catch (Exception e) {
            this.logger.error("路由消息时出现异常！", e);
        }
        return null;
    }
}
