package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.AdChannelDOService;
import com.yuelan.hermes.quanyi.controller.response.AdChannelOptionsResp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/6/18 下午3:36
 */
@Validated
@RestController
@Tag(name = "广告api/广告投放渠道")
@RequiredArgsConstructor
@RequestMapping("/a/adChannel")
public class AdChannelController {

    private final AdChannelDOService adChannelDOService;

    @Operation(summary = "广告投放渠道选择框选项集合")
    @GetMapping("/selectOptions/{moduleType}")
    public BizResult<AdChannelOptionsResp> selectOptions(@Parameter(description = "moduleType：1-电商卡相关 2-权益N选1")
                                                         @PathVariable Integer moduleType) {
        return BizResult.create(adChannelDOService.selectOptions(moduleType));
    }

}
