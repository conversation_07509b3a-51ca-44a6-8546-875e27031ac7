package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.RegexMatch;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/4/10 下午2:06
 */
@Data
public class BenefitUserLoginReq {

    @Schema(description = "手机号")
    @RegexMatch(regex = "^1[3-9]\\d{9}$", message = "手机号格式错误")
    @NotEmpty(message = "手机号不能为空")
    private String phone;

    @Schema(description = "验证码")
    @NotEmpty(message = "验证码不能为空")
    @Length(min = 6, max = 6, message = "验证码长度错误")
    private String smsCode;
}
