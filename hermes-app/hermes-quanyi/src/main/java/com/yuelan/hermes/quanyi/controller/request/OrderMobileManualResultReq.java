package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.EnumLimit;
import com.yuelan.hermes.commons.enums.OrderMobileManualResultEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class OrderMobileManualResultReq {

    @NotBlank(message = "订单号不能为空")
    @Schema(description = "订单号")
    private String orderNo;

    @EnumLimit(message = "请选择处理结果", enumInterface = OrderMobileManualResultEnum.class)
    @Schema(description = "处理结果 0充值失败,1充值成功,2重试")
    private Integer flag;

    @Schema(description = "供应商订单号")
    private String supplierOrderNo;

    @Schema(description = "备注")
    private String remark;
}