package com.yuelan.hermes.quanyi.controller.request.third;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuelan.hermes.quanyi.common.pojo.domain.ThirdChannelOrderDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ThirdChannelOrderReq extends ChannelBaseReq{

    @Schema(description = "同步订单号")
    @NotEmpty(message = "同步订单号不能为空")
    private String thirdOrderNo;

    @Schema(description = "渠道包唯一ID")
    @NotNull(message = "渠道包唯一ID不能为空")
    private Integer uniqueChannelPkgId;

    @Schema(description = "包名")
    private String packageName;

    @Schema(description = "用户号码")
    @NotEmpty(message = "用户号码不能为空")
    private String phone;

    @Schema(description = "支付状态，1支付成功2支付失败")
    @NotNull(message = "支付状态不能为空")
    private Integer payStatus;

    @Schema(description = "支付消息")
    private String payMessage;

    @Schema(description = "下单时间")
    @NotNull(message = "下单时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTime;

    public static ThirdChannelOrderDO convert(ThirdChannelOrderReq req) {
        ThirdChannelOrderDO orderDO = new ThirdChannelOrderDO();
        orderDO.setThirdOrderNo(req.getThirdOrderNo());
        orderDO.setUniqueChannelPkgId(req.getUniqueChannelPkgId());
        orderDO.setPackageName(req.getPackageName());
        orderDO.setPhone(req.getPhone());
        orderDO.setPayStatus(req.getPayStatus());
        orderDO.setPayMessage(req.getPayMessage());
        orderDO.setOrderTime(req.getOrderTime());
        return orderDO;
    }

}
