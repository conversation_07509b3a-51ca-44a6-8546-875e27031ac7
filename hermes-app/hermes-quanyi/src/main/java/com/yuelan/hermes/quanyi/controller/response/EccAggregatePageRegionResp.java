package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2025/5/25
 * @since 2025/5/25
 */
@Data
public class EccAggregatePageRegionResp {

    @Schema(description = "聚合页内所有产品的归属地配置")
    public List<ProdRegionResp> prodRegions;
    @Schema(description = "ip定位城市")
    private Ip2RegionResp ip2Region;
    @Schema(description = "省份配置")
    private List<Province2ProductResp> province2Products;

    @Data
    public static class Province2ProductResp {

        @Schema(description = "省份 行政编码")
        private String provinceAdCode;

        @Schema(description = "省份名字")
        private String provinceName;

        @Schema(description = "产品id")
        private Long productId;

        @Schema(description = "是否是默认省份")
        private boolean isDefault;
    }
}
