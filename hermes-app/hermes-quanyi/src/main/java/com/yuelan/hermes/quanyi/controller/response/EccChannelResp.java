package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccChannelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> 2024/5/3 下午2:53
 */
@Data
public class EccChannelResp {
    @Schema(description = "渠道id")
    private Long channelId;

    @Schema(description = "渠道名字")
    private String channelName;

    public static EccChannelResp buildResp(EccChannelDO eccChannelDO) {
        EccChannelResp resp = new EccChannelResp();
        resp.setChannelId(eccChannelDO.getChannelId());
        resp.setChannelName(eccChannelDO.getChannelName());
        return resp;
    }
}
