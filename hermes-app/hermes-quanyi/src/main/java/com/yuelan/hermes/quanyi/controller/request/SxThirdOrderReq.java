package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/10/16
 * @description:
 */

@Data
public class SxThirdOrderReq {

    @Schema(description = "商品名字")
    private String skuName;

    @Schema(description = "商品id")
    private String skuId;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "手机号不能为空")
    private String phone;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Schema(description = "渠道标识")
    private String appKey;

    @Schema(description = "订单状态 0-订单初始化， 1-订单待支付， 2-订单成功， 3-订单取消， 4-订单退款， 5-订单失败", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单状态不能为空")
    private Integer orderStatus;

    @Schema(description = "订单时间, 毫秒时间戳", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "订单时间不能为空")
    private Long orderTime;
}
