package com.yuelan.hermes.quanyi.controller.request;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2025/1/18
 * @description NxExchangeReq
 */

@NoArgsConstructor
@Data
public class NxExchangeReq {


    /**
     * 属地权益平台分配给业务平台的appid
     */
    private String appid;
    /**
     * 通过签名算法计算出的签名值
     */
    private String sign;
    /**
     * 时间戳	2020-02-01 00:00:00
     */
    private String timestamp;
    /**
     * 随机字符串
     */
    private String nonce_str;
    /**
     * 权益状态：
     * 1-已核销
     * 0-取消核销
     */
    private int status;
    /**
     * 调用下发权益返回的券码
     */
    private String coupon_code;
    /**
     * 属地权益平台请求第三方平台发权益时的订单号
     */
    private String trade_no;
    /**
     * 加密手机号
     */
    private String user_phone;
    /**
     * 核销流水号（如果有关联的订单号就传订单号，没有就传流水号）
     */
    private String verification_id;
}
