package com.yuelan.hermes.quanyi.controller.request.third;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Data
public class ChannelBaseReq {

    @Schema(description = "apiKey")
    @NotEmpty(message = "apiKey不能为空")
    private String apiKey;

    @Schema(description = "签名")
    @NotEmpty(message = "签名不能为空")
    private String sign;

}
