package com.yuelan.hermes.quanyi.controller.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> 2024/5/17 下午5:53
 */
@Data
public class EccOuterChannelResp {

    /**
     * 外部渠道id
     */
    @Schema(description = "外部渠道id")
    private Long outerChannelId;


    /**
     * 渠道名字
     */
    @Schema(description = "渠道名字")
    private String channelName;

    /**
     * 后台生成给渠道的参数
     */
    @Schema(description = "apiKey")
    private String apiKey;

    /**
     * 后台生成的密钥
     */
    @Schema(description = "apiSecret")
    private String apiSecret;

    /**
     * 是否禁用：0-表示正常，1-表示禁用
     */
    @Schema(description = "是否禁用：0-表示正常，1-表示禁用")
    private Integer isDisabled;

    /**
     * ip白名单
     */
    @Schema(description = "ip白名单")
    private String ipWhitelist;


    /**
     * 联通zop发展人编号
     */
    @Schema(description = "联通zop发展人编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String zopReferrerCode;

    /**
     * 上级渠道ID
     */
    @Schema(description = "上级渠道ID")
    private Long parentChannelId;

    /**
     * 上级渠道名称
     */
    @Schema(description = "上级渠道名称")
    private String parentChannelName;

    /**
     * 扣量比例（0-95，单位：%）
     */
    @Schema(description = "扣量比例（0-95，单位：%）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer deductionRate;


    /**
     * 渠道层级类型：1-一级渠道，2-二级渠道
     */
    @Schema(description = "渠道层级类型：1-一级渠道，2-二级渠道")
    private Integer channelLevel;

    public static EccOuterChannelResp buildResp(EccOuterChannelDO channelDO) {
        if (Objects.isNull(channelDO)) {
            return null;
        }
        EccOuterChannelResp resp = new EccOuterChannelResp();
        resp.setOuterChannelId(channelDO.getOuterChannelId());
        resp.setChannelName(channelDO.getChannelName());
        resp.setApiKey(channelDO.getApiKey());
        resp.setApiSecret(channelDO.getApiSecret());
        resp.setIsDisabled(channelDO.getIsDisabled());
        resp.setIpWhitelist(channelDO.getIpWhitelist());
        resp.setZopReferrerCode(channelDO.getZopReferrerCode());
        resp.setParentChannelId(channelDO.getParentChannelId());
        resp.setDeductionRate(channelDO.getDeductionRate());
        resp.setChannelLevel(channelDO.getChannelLevel());
        return resp;
    }
}
