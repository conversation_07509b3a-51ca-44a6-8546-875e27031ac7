package com.yuelan.hermes.quanyi.controller;


import com.yuelan.hermes.quanyi.biz.service.MerchantAccountService;
import com.yuelan.hermes.quanyi.controller.request.MchAccountListReq;
import com.yuelan.hermes.quanyi.controller.request.MchAccountRechargeReq;
import com.yuelan.hermes.quanyi.controller.request.MchAccountStatementReq;
import com.yuelan.hermes.quanyi.controller.response.MchAccountListRsp;
import com.yuelan.hermes.quanyi.controller.response.MchAccountStatementRsp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(name = "商家账户管理")
@RequestMapping("/a/account")
@RestController
public class MerchantAccountController {

    @Autowired
    private MerchantAccountService merchantAccountService;


    @Operation(summary = "账户列表")
    @PostMapping(value = "/list", consumes = MediaType.APPLICATION_JSON_VALUE)
    public BizResult<PageData<MchAccountListRsp>> list(@RequestBody MchAccountListReq req) {
        PageData<MchAccountListRsp> pageData = merchantAccountService.list(req);
        return BizResult.create(pageData);
    }

    @Operation(summary = "账户流水")
    @PostMapping(value = "/stream", consumes = MediaType.APPLICATION_JSON_VALUE)
    public BizResult<PageData<MchAccountStatementRsp>> stream(@RequestBody MchAccountStatementReq req) {
        PageData<MchAccountStatementRsp> pageData = merchantAccountService.stream(req);
        return BizResult.create(pageData);
    }

    @Operation(summary = "账户充值")
    @PostMapping(value = "/recharge", consumes = MediaType.APPLICATION_JSON_VALUE)
    public BizResult<Boolean> recharge(@Valid @RequestBody MchAccountRechargeReq req) {
        Boolean result = merchantAccountService.recharge(req);
        return BizResult.create(result);
    }
}
