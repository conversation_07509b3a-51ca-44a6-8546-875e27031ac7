package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 虚拟商品规格
 */
@Data
public class GoodsVirtualSkuRsp {

    @Schema(description = "SKU ID")
    private Long skuId;

    @Schema(description = "SKU编号")
    private String skuNo;

    @Schema(description = "规格名称")
    private String skuName;

    @Schema(description = "规格图片")
    private String skuImage;

    @Schema(description = "第三方商品编号")
    private String thirdNo;

    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "采购价")
    private BigDecimal purchasePrice;

    @Schema(description = "面值")
    private BigDecimal faceAmount;

    @Schema(description = "状态0下架1上架")
    private Integer status;

}