package com.yuelan.hermes.quanyi.biz.service;

import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.plugins.redisson.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 扣量计算服务
 * 负责订单创建时的扣量计算逻辑
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
@Service
public class DeductionCalculationService {

    @Autowired
    private ChannelHierarchyService channelHierarchyService;

    @Autowired
    private EccOuterChannelDOService eccOuterChannelDOService;

    // 扣量比例对应的扣量位置映射表
    private static final Map<Integer, List<Integer>> DEDUCTION_POSITION_MAP = new HashMap<>();

    static {
        // 初始化扣量位置映射表（基于 1-20 的位置）
        DEDUCTION_POSITION_MAP.put(5, Arrays.asList(20));
        DEDUCTION_POSITION_MAP.put(10, Arrays.asList(10, 20));
        DEDUCTION_POSITION_MAP.put(15, Arrays.asList(6, 13, 20));
        DEDUCTION_POSITION_MAP.put(20, Arrays.asList(5, 10, 15, 20));
        DEDUCTION_POSITION_MAP.put(25, Arrays.asList(4, 8, 12, 16, 20));
        DEDUCTION_POSITION_MAP.put(30, Arrays.asList(3, 7, 9, 13, 17, 19));
        DEDUCTION_POSITION_MAP.put(35, Arrays.asList(3, 7, 9, 11, 13, 17, 19));
        DEDUCTION_POSITION_MAP.put(40, Arrays.asList(2, 4, 6, 8, 12, 14, 16, 18));
        DEDUCTION_POSITION_MAP.put(45, Arrays.asList(2, 4, 6, 8, 10, 12, 14, 16, 18));
        DEDUCTION_POSITION_MAP.put(50, Arrays.asList(2, 4, 6, 8, 10, 12, 14, 16, 18, 20));
        DEDUCTION_POSITION_MAP.put(55, Arrays.asList(2, 4, 6, 8, 10, 11, 13, 15, 16, 17, 19));
        DEDUCTION_POSITION_MAP.put(60, Arrays.asList(2, 4, 5, 7, 8, 10, 12, 14, 15, 17, 18, 20));
        DEDUCTION_POSITION_MAP.put(65, Arrays.asList(2, 4, 5, 7, 8, 10, 11, 13, 14, 16, 17, 19, 20));
        DEDUCTION_POSITION_MAP.put(70, Arrays.asList(2, 3, 4, 6, 7, 8, 10, 12, 13, 14, 16, 17, 18, 20));
        DEDUCTION_POSITION_MAP.put(75, Arrays.asList(2, 3, 4, 6, 7, 8, 10, 11, 12, 14, 15, 16, 18, 19, 20));
        DEDUCTION_POSITION_MAP.put(80, Arrays.asList(2, 3, 4, 5, 7, 8, 9, 10, 12, 13, 14, 15, 17, 18, 19, 20));
        DEDUCTION_POSITION_MAP.put(85, Arrays.asList(2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20));
        DEDUCTION_POSITION_MAP.put(90, Arrays.asList(2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20));
        DEDUCTION_POSITION_MAP.put(95, Arrays.asList(2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20));
    }

    /**
     * 计算订单的扣量渠道
     * 按照漏斗式计算：从平台开始，逐级向下计算扣量比例，首次扣量成功的渠道即为扣量渠道
     *
     * @param order 订单对象
     * @return 扣量渠道ID，如果没有扣量则返回null
     */
    public EccOuterChannelDO calculateDeductionChannel(EccNcOrderDO order) {
        if (order == null || order.getChannelId() == null) {
            log.warn("订单或发展渠道ID为空，无法计算扣量");
            return null;
        }

        Long developmentChannelId = order.getChannelId();
        log.info("开始计算订单扣量，订单号: {}, 发展渠道ID: {}", order.getOrderNo(), developmentChannelId);

        // 获取发展渠道的完整上级链路（不包含发展渠道本身）
        // 例如：A2-1 → 返回 [A, A2]（从根级到直接父级，level从小到大）
        List<EccOuterChannelDO> parentChain = channelHierarchyService.getParentChannelChain(developmentChannelId);

        // 获取发展渠道
        EccOuterChannelDO developmentChannel = eccOuterChannelDOService.getById(developmentChannelId);
        if (developmentChannel == null) {
            log.warn("发展渠道不存在，渠道ID: {}", developmentChannelId);
            return null;
        }

        // 构建完整的扣量检查链条：[A, A2, A2-1]（从根级到发展渠道）
        // parentChain已经是正确顺序[A, A2]，直接使用并添加发展渠道
        List<EccOuterChannelDO> deductionChain = new ArrayList<>(parentChain);

        // 添加发展渠道到末尾：[A, A2, A2-1]
        deductionChain.add(developmentChannel);

        // 逐层检查扣量：从根级到发展渠道
        log.debug("开始扣量检查，链条: {}",
                deductionChain.stream().map(EccOuterChannelDO::getChannelName).collect(Collectors.toList()));

        for (int i = 0; i < deductionChain.size(); i++) {
            EccOuterChannelDO currentChannel = deductionChain.get(i);
            Integer deductionRate = currentChannel.getDeductionRate();


            log.debug("检查渠道[{}]的扣量，比例: {}%", currentChannel.getChannelName(), deductionRate);

            // 执行扣量计算（使用发展渠道+检查层级的计数器）
            if (performDeduction(currentChannel, developmentChannelId)) {
                // 扣量成功，归属到上级渠道
                // 逻辑归属/扣量渠道
                Long deductionChannelId = null;
                // 最终归属/扣量渠道 (逻辑归属/扣量渠道 关停了时候递归找上级)
                Long finalDeductionChannelId = null;
                if (i > 0) {
                    // 有上级渠道，归属到上级
                    EccOuterChannelDO deductionChannel =  deductionChain.get(i - 1);
                    deductionChannelId = deductionChannel.getOuterChannelId();
                    if (isChannelDisabled(deductionChannel)) {
                        finalDeductionChannelId = findFinalActiveChannel(deductionChain,deductionChannelId);
                    }
                } else {
                    // 没有上级渠道（当前是根级渠道），归属到平台（返回null或特殊标识）
                    deductionChannelId = null; // 表示归属到平台
                }

                log.info("订单扣量成功，订单号: {}, 被扣量渠道: {}, 逻辑归属渠道ID: {},最终归属渠道：{}",
                        order.getOrderNo(), currentChannel.getChannelName(),
                        deductionChannelId != null ? deductionChannelId : "平台",
                        finalDeductionChannelId != null ? finalDeductionChannelId : "平台");

                return finalDeductionChannelId
            }
        }

        // 所有层级都未扣量
        log.info("订单未被扣量，订单号: {}", order.getOrderNo());
        return null;
    }

    /**
     * 执行扣量计算 - 基于Redis计数器的精确扣量算法
     *
     * @param deductionConfigChannel 扣量配置渠道
     * @param sourceChannelId 订单的发展渠道ID
     * @return true表示扣量成功
     */
    private boolean performDeduction(EccOuterChannelDO deductionConfigChannel, Long sourceChannelId) {
        Integer channelLevel = deductionConfigChannel.getChannelLevel();
        String key = RedisKeys.getDeductionCounterByLevelKey(sourceChannelId, channelLevel);
        long orderCount = RedisUtils.incrAtomicValue(key);

        // 取模20得到余数（0-19），然后映射到位置（1-20）
        int remainder = (int) (orderCount % 20);
        int position = remainder == 0 ? 20 : remainder;

        Integer deductionRate = deductionConfigChannel.getDeductionRate();
        if (deductionRate == null || deductionRate <= 0) {
            log.debug("渠道扣量比例为0或未设置，不扣量，渠道ID: {}", deductionConfigChannel.getOuterChannelId());
            return false;
        }

        // 获取扣量位置列表
        List<Integer> deductionPositions = DEDUCTION_POSITION_MAP.get(deductionRate);
        if (deductionPositions == null || deductionPositions.isEmpty()) {
            log.warn("未找到扣量比例{}%对应的扣量位置配置，渠道ID: {}", deductionRate, deductionConfigChannel.getOuterChannelId());
            return false;
        }

        // 判断位置是否在扣量位置列表中
        boolean deducted = deductionPositions.contains(position);

        log.debug("扣量计算结果，发展渠道ID: {}, 扣量配置渠道ID: {}, 扣量比例: {}%, 计数器值: {}, 位置: {}, 扣量位置: {}, 是否扣量: {}",
                sourceChannelId, deductionConfigChannel.getOuterChannelId(), deductionRate, orderCount, position, deductionPositions, deducted);

        return deducted;
    }



    /**
     * 检查渠道是否停用
     *
     * @param channel 渠道信息
     * @return true表示停用
     */
    private boolean isChannelDisabled(EccOuterChannelDO channel) {
        return channel.getIsDisabled() != null && channel.getIsDisabled() == 1;
    }


    /**
     * 递归查找最终的没有关停的渠道
     *
     * @param deductionChain 扣量链条
     * @param startChannelId 开始的渠道 id
     * @return 最终没有关停的渠道ID，如果都关停了则返回null（归属到平台）
     */
    private Long findFinalActiveChannel(List<EccOuterChannelDO> deductionChain, Long startChannelId) {
        Long channelId = null;
        boolean startFound = false;
        for (int i = deductionChain.size() - 1; i >= 0; i--) {
            if (deductionChain.get(i).getOuterChannelId().equals(startChannelId)) {
                startFound = true;
            }
            if (!startFound) {
                continue;
            }
            if (!isChannelDisabled(deductionChain.get(i))) {
                channelId = deductionChain.get(i).getOuterChannelId();
            }
        }
        return channelId;
    }

    /**
     * 更新订单的扣量渠道信息
     * 
     * @param order 订单对象
     */
    public void updateOrderDeductionChannel(EccNcOrderDO order) {
        if (order == null) {
            return;
        }

        Long deductionChannelId = calculateDeductionChannel(order);
        order.setDeductionChannelId(deductionChannelId);
        
        log.info("更新订单扣量渠道，订单号: {}, 扣量渠道ID: {}", order.getOrderNo(), deductionChannelId);
    }

    /**
     * 批量更新订单的扣量渠道信息
     *
     * @param orders 订单列表
     */
    public void batchUpdateOrderDeductionChannel(List<EccNcOrderDO> orders) {
        if (orders == null || orders.isEmpty()) {
            return;
        }

        for (EccNcOrderDO order : orders) {
            try {
                updateOrderDeductionChannel(order);
            } catch (Exception e) {
                log.error("批量更新订单扣量渠道失败，订单号: {}", order.getOrderNo(), e);
            }
        }
    }

    /**
     * 获取渠道当前的扣量计数器值
     *
     * @param channelId 渠道ID
     * @return 当前计数器值，如果不存在则返回0
     */
    public Long getChannelDeductionCounter(Long channelId) {
        String redisKey = RedisKeys.getDeductionCounterKey(channelId);
        try {
            return RedisUtils.getAtomicValue(redisKey);
        } catch (Exception e) {
            log.error("获取渠道扣量计数器失败，渠道ID: {}", channelId, e);
            return 0L;
        }
    }

    /**
     * 重置渠道的扣量计数器
     *
     * @param channelId 渠道ID
     */
    public void resetChannelDeductionCounter(Long channelId) {
        String redisKey = RedisKeys.getDeductionCounterKey(channelId);
        try {
            RedisUtils.deleteObject(redisKey);
            log.info("重置渠道扣量计数器成功，渠道ID: {}", channelId);
        } catch (Exception e) {
            log.error("重置渠道扣量计数器失败，渠道ID: {}", channelId, e);
        }
    }

    /**
     * 获取扣量比例对应的扣量位置列表
     *
     * @param deductionRate 扣量比例
     * @return 扣量位置列表
     */
    public List<Integer> getDeductionPositions(Integer deductionRate) {
        return DEDUCTION_POSITION_MAP.getOrDefault(deductionRate, Collections.emptyList());
    }

    /**
     * 预测下一个订单是否会被扣量
     *
     * @param channelId 渠道ID
     * @param deductionRate 扣量比例
     * @return true表示下一个订单会被扣量
     */
    public boolean predictNextDeduction(Long channelId, Integer deductionRate) {
        if (deductionRate == null || deductionRate <= 0) {
            return false;
        }

        List<Integer> deductionPositions = DEDUCTION_POSITION_MAP.get(deductionRate);
        if (deductionPositions == null || deductionPositions.isEmpty()) {
            return false;
        }

        Long currentCount = getChannelDeductionCounter(channelId);
        int nextRemainder = (int) ((currentCount + 1) % 20);
        int nextPosition = nextRemainder == 0 ? 20 : nextRemainder;

        return deductionPositions.contains(nextPosition);
    }

    /**
     * 获取渠道扣量统计信息
     *
     * @param channelId 渠道ID
     * @param deductionRate 扣量比例
     * @return 扣量统计信息
     */
    public Map<String, Object> getChannelDeductionStats(Long channelId, Integer deductionRate) {
        Map<String, Object> stats = new HashMap<>();

        Long currentCount = getChannelDeductionCounter(channelId);
        int currentRemainder = (int) (currentCount % 20);
        int currentPosition = currentRemainder == 0 ? 20 : currentRemainder;

        List<Integer> deductionPositions = getDeductionPositions(deductionRate);
        boolean nextWillDeduct = predictNextDeduction(channelId, deductionRate);

        // 计算已完成的周期数
        long completedCycles = currentCount / 20;

        // 计算当前周期内的扣量次数
        int currentCycleDeductions = 0;
        int currentCycleRemainder = (int) (currentCount % 20);

        for (int i = 1; i <= (currentCycleRemainder == 0 ? 20 : currentCycleRemainder); i++) {
            if (deductionPositions.contains(i)) {
                currentCycleDeductions++;
            }
        }

        stats.put("channelId", channelId);
        stats.put("deductionRate", deductionRate);
        stats.put("currentCount", currentCount);
        stats.put("currentPosition", currentPosition);
        stats.put("completedCycles", completedCycles);
        stats.put("currentCycleDeductions", currentCycleDeductions);
        stats.put("deductionPositions", deductionPositions);
        stats.put("nextWillDeduct", nextWillDeduct);

        return stats;
    }
}
