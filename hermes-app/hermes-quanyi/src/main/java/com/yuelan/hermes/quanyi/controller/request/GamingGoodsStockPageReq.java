package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 电竞卡商品库存
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GamingGoodsStockPageReq extends PageRequest {

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "商品名称")
    private String goodsName;

}