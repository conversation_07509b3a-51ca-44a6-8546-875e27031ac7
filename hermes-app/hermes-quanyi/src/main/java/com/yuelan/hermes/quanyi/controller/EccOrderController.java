package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.EccOrderDOImportService;
import com.yuelan.hermes.quanyi.biz.service.EccOrderDOService;
import com.yuelan.hermes.quanyi.controller.request.EccOrderListReq;
import com.yuelan.hermes.quanyi.controller.response.EccOrderResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;

/**
 * <AUTHOR> 2024/5/3 下午11:10
 */
@Validated
@RestController
@Tag(name = "电商卡/后台api/权益包订单")
@RequiredArgsConstructor
@RequestMapping("/a/ecc/order")
public class EccOrderController {

    private final EccOrderDOImportService eccOrderDOImportService;
    private final EccOrderDOService eccOrderDOService;

    @Operation(summary = "权益包订单列表")
    @PostMapping("/list")
    public BizResult<PageData<EccOrderResp>> list(@RequestBody EccOrderListReq req) {
        return BizResult.create(eccOrderDOService.page(req));
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download/template", produces = "application/octet-stream")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        eccOrderDOImportService.downloadTemplate(response);
    }

    @Operation(summary = "导入每月电商卡用户", description = "必须使用官方模板导入（江苏联通权益清单数据）")
    @Parameters({
            @Parameter(name = "monthTime", description = "月份时间字符串，格式：yyyy-MM", required = true),
            @Parameter(name = "prodId", description = "电商卡产品id", required = true),
            @Parameter(name = "file", description = "文件", required = true)
    })
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BizResult<Void> importMonthOrder(@RequestParam("monthTime") @NotEmpty String monthTime,
                                            @RequestParam("prodId") Long prodId,
                                            @RequestParam("file") MultipartFile file) throws IOException {
        eccOrderDOImportService.importMonthOrder(prodId, monthTime, file);
        return BizResult.ok();
    }
}
