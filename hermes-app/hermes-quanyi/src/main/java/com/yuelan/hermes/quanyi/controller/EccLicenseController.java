package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.impl.EccLicenseServiceImpl;
import com.yuelan.hermes.quanyi.controller.request.EccLicenseDeleteReq;
import com.yuelan.hermes.quanyi.controller.request.EccLicensePageReq;
import com.yuelan.hermes.quanyi.controller.request.EccLicenseReq;
import com.yuelan.hermes.quanyi.controller.response.EccLicenseResp;
import com.yuelan.hermes.quanyi.controller.response.EccProdResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> 2025/5/21
 * @since 2025/5/21
 */
@Validated
@RestController
@Tag(name = "号卡/后台接口/授权牌管理")
@RequiredArgsConstructor
@RequestMapping("/a/nc/license")
public class EccLicenseController {

    private final EccLicenseServiceImpl eccLicenseService;

    @Operation(summary = "分页")
    @PostMapping("/page")
    public BizResult<PageData<EccLicenseResp>> page(@Validated @RequestBody EccLicensePageReq req) {
        return BizResult.create(eccLicenseService.page(req));
    }

    @Operation(summary = "新增/编辑")
    @Log(title = "授权牌新增/编辑", type = OperationType.OTHER)
    @PostMapping("/saveOrUpdate")
    public BizResult<Void> saveOrUpdate(@Validated @RequestBody EccLicenseReq req) {
        eccLicenseService.saveOrUpdate(req);
        return BizResult.ok();
    }

    @Operation(summary = "删除")
    @Log(title = "授权牌删除", type = OperationType.DELETE)
    @PostMapping("/delete")
    public BizResult<Void> delete(@Validated @RequestBody EccLicenseDeleteReq req) {
        eccLicenseService.delete(req.getLicenseIds());
        return BizResult.ok();
    }

    @Operation(summary = "授权牌关联产品列表")
    @PostMapping("/associateProductList/{licenseId}")
    public BizResult<List<EccProdResp>> productList(@Validated @NotNull @PathVariable("licenseId") Long licenseId) {
        return BizResult.create(eccLicenseService.associateProductList(licenseId));
    }

}
