package com.yuelan.hermes.quanyi.controller.open;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.service.XimiService;
import com.yuelan.hermes.quanyi.controller.request.GamingOrderNotifyReq;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "西米API")
@RequestMapping("/ximi")
@RestController
public class XimiController {
    @Autowired
    private XimiService ximiService;

    @Operation(summary = "道具下发通知")
    @PostMapping(value = "/vouchercp/notify")
    public BizResult<Boolean> notify(@RequestBody GamingOrderNotifyReq req) {
        if (log.isDebugEnabled()) {
            log.info("西米道具下发通知:{}", JSON.toJSONString(req));
        }
        Boolean result = ximiService.notify(req);
        return BizResult.create(result);
    }

}
