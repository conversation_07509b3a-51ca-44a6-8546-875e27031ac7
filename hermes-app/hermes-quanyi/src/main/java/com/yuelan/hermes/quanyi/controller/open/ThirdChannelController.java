package com.yuelan.hermes.quanyi.controller.open;

import com.yuelan.hermes.quanyi.biz.service.ThirdChannelOrderService;
import com.yuelan.hermes.quanyi.controller.request.third.ThirdChannelOrderReq;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "第三方渠道通知API")
@RequestMapping("/third/channel")
@RestController
public class ThirdChannelController {

    @Autowired
    private ThirdChannelOrderService thirdChannelOrderService;

    @Operation(summary = "订单通知")
    @PostMapping("/order/notice")
    public BizResult<String> orderNotice(@Validated @RequestBody ThirdChannelOrderReq req) {
        String result = thirdChannelOrderService.orderNotice(req);
        return BizResult.create(result);
    }

}
