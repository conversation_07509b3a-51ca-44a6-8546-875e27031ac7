package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR> 2024/6/24 上午11:43
 */
@SaIgnore
@Slf4j
@Tag(name = "快手API")
@RequestMapping("/kuaiShou")
@RestController
public class KuaiShouController {
    /**
     * 快手需要对接的-广告检测连接
     */
    @Schema(title = "快手需要对接的-广告检测连接")
    @GetMapping(value = "/ad/monitor")
    public String kuaiShouCallBack(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        log.info("快手广告检测回调参数:{}", JSONObject.toJSONString(parameterMap));
        return "OK";
    }


}
