package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuelan.hermes.commons.excel.NotifyStatusEnumConverter;
import com.yuelan.hermes.commons.excel.OrderStatusEnumConverter;
import com.yuelan.hermes.commons.excel.RechargeTypeEnumConverter;
import com.yuelan.hermes.commons.excel.SpEnumConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 话费订单导出
 */
@Data
@ContentRowHeight(15)
@HeadRowHeight(20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class OrderMobileExportRsp {
    /**
     * 订单ID
     */
    @ExcelIgnore
    private Long id;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "下单时间")
    @Schema(description = "下单时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ColumnWidth(28)
    @ExcelProperty(value = "订单号")
    @Schema(description = "订单号")
    private String orderNo;

    @ColumnWidth(28)
    @ExcelProperty(value = "平台订单号")
    @Schema(description = "平台订单号")
    private String outTradeNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "供应商订单号")
    @Schema(description = "供应商订单号")
    private String supplierOrderNo;

    @ExcelIgnore
    @Schema(description = "商户号")
    private String mchId;

    @ColumnWidth(20)
    @ExcelProperty(value = "商户名称")
    @Schema(description = "商户名称")
    private String merchantName;

    @ColumnWidth(15)
    @ExcelProperty(value = "运营商", converter = SpEnumConverter.class)
    @Schema(description = "运营商")
    private Integer sp;

    @ColumnWidth(20)
    @ExcelProperty(value = "手机号")
    @Schema(description = "手机号")
    private String mobile;

    @ColumnWidth(15)
    @ExcelProperty(value = "充值面额")
    @Schema(description = "充值面额")
    private BigDecimal rechargeAmount;

    @ColumnWidth(15)
    @ExcelProperty(value = "充值类型", converter = RechargeTypeEnumConverter.class)
    @Schema(description = "充值类型")
    private Integer hasSlow;

    @ColumnWidth(20)
    @ExcelProperty(value = "SKU名称")
    @Schema(description = "SKU名称")
    private String skuName;

    @ColumnWidth(20)
    @ExcelProperty(value = "SKU编号")
    @Schema(description = "SKU编号")
    private String skuNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "供应商")
    @Schema(description = "供应商")
    private String supplier;

    @ColumnWidth(20)
    @ExcelProperty(value = "销售价")
    @Schema(description = "销售价")
    private BigDecimal orderAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "采购价")
    @Schema(description = "采购价")
    private BigDecimal supplierPrice;

    @ColumnWidth(15)
    @ExcelProperty(value = "订单状态", converter = OrderStatusEnumConverter.class)
    @Schema(description = "订单状态")
    private Integer orderStatus;

    @ColumnWidth(15)
    @ExcelProperty(value = "回调状态", converter = NotifyStatusEnumConverter.class)
    @Schema(description = "回调状态")
    private Integer notifyStatus;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "到账时间")
    @Schema(description = "到账时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finishTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "备注")
    @Schema(description = "备注")
    private String remark;

}