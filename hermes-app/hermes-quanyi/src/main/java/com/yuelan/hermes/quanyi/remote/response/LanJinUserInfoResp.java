package com.yuelan.hermes.quanyi.remote.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 蓝鲸电竞卡 - 查看用户信息响应
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Data
public class LanJinUserInfoResp {

    /**
     * 响应码（0：成功，1：失败）
     */
    @Schema(description = "响应码（0：成功，1：失败）")
    private Integer code;

    /**
     * 用户等级
     */
    @Schema(description = "用户等级")
    private Integer lv;

    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称")
    private String nick;

    /**
     * 错误消息
     */
    @Schema(description = "错误消息")
    private String msg;

    /**
     * 判断是否成功
     *
     * @return true：成功，false：失败
     */
    public boolean isSuccess() {
        return Integer.valueOf(0).equals(code);
    }
}
