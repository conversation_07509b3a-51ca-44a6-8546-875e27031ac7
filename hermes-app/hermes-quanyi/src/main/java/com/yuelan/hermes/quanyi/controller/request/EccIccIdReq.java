package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.*;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ICCID查询请求对象
 *
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "ICCID查询请求")
public class EccIccIdReq extends PageRequest {

    @Schema(description = "ICCID号码")
    private String iccId;

    @Schema(description = "产品代码")
    private Long productId;

    @Schema(description = "渠道代码")
    private Long channelId;

    @Schema(description = "批次号")
    private Long batchId;

    @Schema(description = "有效期开始日期")
    private LocalDate validityPeriodStart;

    @Schema(description = "有效期结束日期")
    private LocalDate validityPeriodEnd;

    @Schema(description = "创建时间开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间结束")
    private LocalDateTime createTimeEnd;

    /**
     * 构建查询条件
     *
     * @return 查询条件
     */
    public Wrapper<EccIccIdDO> buildQueryWrapper() {
        return new MPJLambdaWrapper<EccIccIdDO>()
                .selectAll(EccIccIdDO.class)
                .select(EccProductDO::getProdName)
                .selectAs(EccOuterChannelDO::getChannelName, EccImeiDO::getOuterChannelName)
                .selectAs(EccChannelDO::getChannelName, EccImeiDO::getInnerChannelName)
                .leftJoin(EccProductDO.class, EccProductDO::getProdId, EccImeiDO::getProductId)
                .leftJoin(EccOuterChannelDO.class, EccOuterChannelDO::getOuterChannelId, EccImeiDO::getChannelId)
                .leftJoin(EccChannelDO.class, EccChannelDO::getChannelId, EccImeiDO::getChannelId)
                .like(StringUtils.isNotBlank(iccId), EccIccIdDO::getIccId, iccId)
                .eq(productId != null, EccIccIdDO::getProductId, productId)
                .eq(channelId != null, EccIccIdDO::getChannelId, channelId)
                .like(batchId != null, EccIccIdDO::getBatchId, batchId)
                .ge(validityPeriodStart != null, EccIccIdDO::getValidityPeriod, validityPeriodStart)
                .le(validityPeriodEnd != null, EccIccIdDO::getValidityPeriod, validityPeriodEnd)
                .ge(createTimeStart != null, EccIccIdDO::getCreateTime, createTimeStart)
                .le(createTimeEnd != null, EccIccIdDO::getCreateTime, createTimeEnd)
                .orderByDesc(EccIccIdDO::getId);
    }
}
