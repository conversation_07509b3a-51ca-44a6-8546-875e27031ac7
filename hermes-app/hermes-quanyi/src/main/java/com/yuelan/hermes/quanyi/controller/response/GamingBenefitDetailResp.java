package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2025/2/9
 * @since 2025/2/9
 * <p>
 * 电竞卡权益详情
 */
@Data
public class GamingBenefitDetailResp {
    /**
     * 已绑定角色的id
     */
    @Schema(description = "已绑定角色的id，拿到角色信息可调用查询接口查询最新角色信息")
    private String roleId;

    /**
     * 操作系统类型：1-iOS，2-Android
     */
    @Schema(description = "操作系统类型：1-iOS，2-Android")
    private Integer osType;

    @Schema(description = "权益明细")
    private List<GamingOrderItemSimpleRsp> items;

}
