package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR> 2024/4/2 19:41
 */
@Data
public class BenefitProdItemUpdateSortReq {

    @NotNull(message = "权益包产品id不能为空")
    private Long prodId;

    @NotNull(message = "排序后的关系ids不能为空")
    @Size(min = 1, message = "排序后的关系ids至少1个")
    @Schema(description = "排序后的关系ids")
    private List<Long> sortedItemIds;

}
