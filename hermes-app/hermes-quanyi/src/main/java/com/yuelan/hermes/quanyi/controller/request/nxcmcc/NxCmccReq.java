package com.yuelan.hermes.quanyi.controller.request.nxcmcc;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class NxCmccReq {

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "用户号码（加密）")
    private String mobile;

    @Schema(description = "业务编码")
    private String productId;

    @Schema(description = "下单时间，格式如：1744710407")
    private String createDate;

    @Schema(description = "订单状态：0办理中,1成功,2失败")
    private Integer status;

    @Schema(description = "办理业务结果信息")
    private String retMsg;

}
