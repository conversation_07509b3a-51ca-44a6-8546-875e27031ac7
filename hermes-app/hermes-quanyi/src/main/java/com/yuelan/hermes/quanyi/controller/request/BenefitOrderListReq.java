package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 2024/4/19 下午3:48
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class BenefitOrderListReq extends PageRequest {

    /**
     * 订单id
     */
    @Schema(description = "订单id")
    private Long orderId;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 三方订单号
     */
    @Schema(description = "三方订单号(支付通道订单号)")
    private String outOrderNo;

    /**
     * 投放渠道
     */
    @Schema(description = "支付通道id")
    private String distributionChannel;

    /**
     * 支付通道id
     */
    @Schema(description = "支付通道id")
    private Integer payChannelId;

    /**
     * 支付通道id
     */
    @Schema(description = "支付通道包id")
    private Integer payChannelPkgId;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;

    /**
     * 权益包id
     */
    @Schema(description = "权益包id")
    private Long prodId;

    /**
     * 预下单状态：0-默认状态，1-下单成功，2-下单失败
     */
    @Schema(description = "向支付通道预下单状态：0-默认状态，1-下单成功，2-下单失败")
    private Integer preorderStatus;

    /**
     * 支付状态，0等待用户支付 1支付成功 2支付失败
     */
    @Schema(description = "用户支付状态: 0-等待用户支付 1-支付成功 2-支付失败")
    private Integer payStatus;

    /**
     * 订单状态0处理中1交易成功2交易失败3订单异常
     */
    @Schema(description = "订单状态: 0-处理中 1-交易成功（支付成功且所有商品领取成功） 2-交易失败(预下单失败|用户支付失败) 3-订单异常（支付成功后，存在权益商品领取失败）")
    private Integer orderStatus;

    /**
     * 创建时间-开始
     */
    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间-结束
     */
    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    /**
     * 回调通知时间-开始
     */
    @Schema(description = "回调通知时间-开始")
    private LocalDateTime payNotifyTimeStart;

    /**
     * 回调通知时间-结束
     */
    @Schema(description = "回调通知时间-结束")
    private LocalDateTime payNotifyTimeEnd;

    /**
     * 外部渠道id
     */
    @Schema(description = "外部渠道id")
    private Long outChannelId;

    @Schema(description = "包名")
    private String packageName;

    @Schema(description = "城市地域编码")
    private List<Integer> cityAdcodeList;

    public Wrapper<BenefitOrderDO> buildLimitQueryWrapper(Long orderId, Integer limitValue) {
        return Wrappers.<BenefitOrderDO>lambdaQuery()
                .gt(BenefitOrderDO::getOrderId, orderId)
                .eq(StringUtils.isNotBlank(orderNo), BenefitOrderDO::getOrderNo, orderNo)
                .eq(StringUtils.isNotBlank(outOrderNo), BenefitOrderDO::getOutOrderNo, outOrderNo)
                .like(StringUtils.isNotBlank(distributionChannel), BenefitOrderDO::getDistributionChannel, distributionChannel)
                .eq(payChannelId != null, BenefitOrderDO::getPayChannelId, payChannelId)
                .eq(payChannelPkgId != null, BenefitOrderDO::getPayChannelPkgId, payChannelPkgId)
                .eq(StringUtils.isNotBlank(phone), BenefitOrderDO::getPhone, phone)
                .eq(prodId != null, BenefitOrderDO::getProdId, prodId)
                .eq(preorderStatus != null, BenefitOrderDO::getPreorderStatus, preorderStatus)
                .eq(payStatus != null, BenefitOrderDO::getPayStatus, payStatus)
                .eq(orderStatus != null, BenefitOrderDO::getOrderStatus, orderStatus)
                .ge(createTimeStart != null, BenefitOrderDO::getCreateTime, createTimeStart)
                .le(createTimeEnd != null, BenefitOrderDO::getCreateTime, createTimeEnd)
                .ge(payNotifyTimeStart != null, BenefitOrderDO::getPayNotifyTime, payNotifyTimeStart)
                .le(payNotifyTimeEnd != null, BenefitOrderDO::getPayNotifyTime, payNotifyTimeEnd)
                .eq(outChannelId != null, BenefitOrderDO::getOutChannelId, outChannelId)
                .eq(packageName != null, BenefitOrderDO::getPackageName, packageName)
                .in(CollectionUtil.isNotEmpty(cityAdcodeList), BenefitOrderDO::getCityAdcode, cityAdcodeList)
                .orderByAsc(BenefitOrderDO::getOrderId)
                .last("limit " + limitValue);
    }

    public Wrapper<BenefitOrderDO> buildBaseWrapper(Long startId, boolean isAsc) {
        return Wrappers.<BenefitOrderDO>lambdaQuery()
                .gt(startId != null, BenefitOrderDO::getOrderId, startId)
                .eq(orderId != null, BenefitOrderDO::getOrderId, orderId)
                .eq(StringUtils.isNotBlank(orderNo), BenefitOrderDO::getOrderNo, orderNo)
                .eq(StringUtils.isNotBlank(outOrderNo), BenefitOrderDO::getOutOrderNo, outOrderNo)
                .like(StringUtils.isNotBlank(distributionChannel), BenefitOrderDO::getDistributionChannel, distributionChannel)
                .eq(payChannelId != null, BenefitOrderDO::getPayChannelId, payChannelId)
                .eq(payChannelPkgId != null, BenefitOrderDO::getPayChannelPkgId, payChannelPkgId)
                .eq(StringUtils.isNotBlank(phone), BenefitOrderDO::getPhone, phone)
                .eq(prodId != null, BenefitOrderDO::getProdId, prodId)
                .eq(preorderStatus != null, BenefitOrderDO::getPreorderStatus, preorderStatus)
                .eq(payStatus != null, BenefitOrderDO::getPayStatus, payStatus)
                .eq(orderStatus != null, BenefitOrderDO::getOrderStatus, orderStatus)
                .ge(createTimeStart != null, BenefitOrderDO::getCreateTime, createTimeStart)
                .le(createTimeEnd != null, BenefitOrderDO::getCreateTime, createTimeEnd)
                .ge(payNotifyTimeStart != null, BenefitOrderDO::getPayNotifyTime, payNotifyTimeStart)
                .le(payNotifyTimeEnd != null, BenefitOrderDO::getPayNotifyTime, payNotifyTimeEnd)
                .eq(outChannelId != null, BenefitOrderDO::getOutChannelId, outChannelId)
                .eq(packageName != null, BenefitOrderDO::getPackageName, packageName)
                .in(CollectionUtil.isNotEmpty(cityAdcodeList), BenefitOrderDO::getCityAdcode, cityAdcodeList)
                .orderByDesc(!isAsc, BenefitOrderDO::getOrderId)
                .orderByAsc(isAsc, BenefitOrderDO::getOrderId);
    }


}
