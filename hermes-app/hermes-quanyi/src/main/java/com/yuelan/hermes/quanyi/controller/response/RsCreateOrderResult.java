package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class RsCreateOrderResult {

    @Schema(description = "第三方供应商订单号")
    private String thirdOrderNo;

    @Schema(description = "荣数信息订单号")
    private String cupdOrderNo;

    @Schema(description = "Json格式的list，具体参数见下，异步处理时为空，充值类供应商也为空")
    private List<RsVoucherResult> ordVoucherList;


}
