package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * <AUTHOR> 2024/5/5 下午12:59
 * <p>
 * 用户领取卡片请求  外部渠道
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EccOuterChannelGetCardReq extends EccBaseGetCardReq {

    @Schema(description = "渠道订单号")
    protected String channelOrderNo;

    @Schema(description = "渠道Id")
    protected Long channelId;

    @Schema(title = "回调URL，http:// 或者 https:// 开头", description = "传参不为空时候，接口返回成功后，该手机号码的激活/退单/首冲等状态回调通知")
    protected String callbackUrl;

    @Override
    public void reqCheck() {
        super.reqCheck();
        if (Objects.isNull(channelId)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "缺少渠道参数");
        }
        if (Objects.isNull(channelOrderNo)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "缺少渠道订单号");
        }
    }

}
