package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> 2025/4/25
 * @since 2025/4/25
 */
@Data
public class HshBenefitsPackageRedeemReq {

    @Schema(description = "类型：1-即时发放权益内的权益重新领取 2-选发权益包内权益领取")
    public Integer dispatchTiming;
    @Schema(description = "权益订单id")
    private Long packageOrderId;
    @Schema(description = "权益id")
    private Long benefitItemId;

}
