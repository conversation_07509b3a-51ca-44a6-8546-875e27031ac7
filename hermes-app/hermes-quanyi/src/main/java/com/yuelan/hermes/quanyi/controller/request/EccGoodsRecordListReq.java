package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsRecordDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR> 2024/5/4 上午12:58
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class EccGoodsRecordListReq extends PageRequest {

    @Schema(description = "ecc用户id")
    private Long eccUserId;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "子订单订单号")
    private String eccItemNo;

    @Schema(description = "电商卡权益包id")
    private Long prodId;

    @Schema(description = "电商卡商品id")
    private Long goodsId;

    @Schema(description = "记录开始时间")
    private String recordStartTime;

    @Schema(description = "记录结束时间")
    private String recordEndTime;


    public Wrapper<EccGoodsRecordDO> buildQueryWrapper() {
        return Wrappers.lambdaQuery(EccGoodsRecordDO.class)
                .eq(eccUserId != null, EccGoodsRecordDO::getEccUserId, eccUserId)
                .eq(phone != null, EccGoodsRecordDO::getPhone, phone)
                .eq(prodId != null, EccGoodsRecordDO::getProdId, prodId)
                .eq(goodsId != null, EccGoodsRecordDO::getGoodsId, goodsId)
                .eq(eccItemNo != null, EccGoodsRecordDO::getEccItemNo, eccItemNo)
                .ge(recordStartTime != null, EccGoodsRecordDO::getCreateTime, recordStartTime)
                .le(recordEndTime != null, EccGoodsRecordDO::getCreateTime, recordEndTime)
                .orderByDesc(EccGoodsRecordDO::getRecordId);
    }
}
