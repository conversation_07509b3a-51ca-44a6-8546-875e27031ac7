package com.yuelan.hermes.quanyi.controller;


import com.yuelan.hermes.quanyi.biz.service.OrderMobileService;
import com.yuelan.hermes.quanyi.controller.request.OrderMobileExportReq;
import com.yuelan.hermes.quanyi.controller.request.OrderMobileListReq;
import com.yuelan.hermes.quanyi.controller.request.OrderMobileManualResultReq;
import com.yuelan.hermes.quanyi.controller.request.OrderMobileRefundReq;
import com.yuelan.hermes.quanyi.controller.response.OrderMobileItemRsp;
import com.yuelan.hermes.quanyi.controller.response.OrderMobileRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@Tag(name = "话费订单")
@RequestMapping("/a/orderMobile")
@RestController
public class OrderMobileController extends AdminBaseController {
    @Autowired
    private OrderMobileService orderMobileService;

    @Operation(summary = "订单列表")
    @PostMapping("/list")
    public BizResult<PageData<OrderMobileRsp>> list(@RequestBody OrderMobileListReq req) {
        req.check();
        PageData<OrderMobileRsp> pageData = orderMobileService.list(req);
        return BizResult.create(pageData);
    }

    @Operation(summary = "订单详情")
    @GetMapping("/detail")
    public BizResult<OrderMobileRsp> detail(@RequestParam String orderNo) {
        OrderMobileRsp result = orderMobileService.detail(orderNo);
        return BizResult.create(result);
    }

    @Operation(summary = "订单明细")
    @GetMapping("/item")
    public BizResult<List<OrderMobileItemRsp>> item(@RequestParam String orderNo) {
        List<OrderMobileItemRsp> items = orderMobileService.item(orderNo);
        return BizResult.create(items);
    }

    @Log(title = "话费订单导出", type = OperationType.OTHER)
    @Operation(summary = "导出订单")
    @PostMapping("/export")
    public void export(@RequestBody OrderMobileExportReq req, HttpServletResponse response) throws IOException {
        req.check();
        orderMobileService.export(req, response);
    }

    @Log(title = "话费订单异常处理", type = OperationType.OTHER)
    @Operation(summary = "异常处理")
    @PostMapping("/manual/result")
    public BizResult<Boolean> manualResult(@Valid @RequestBody OrderMobileManualResultReq req) {
        Boolean result = orderMobileService.manualResult(req);
        return BizResult.create(result);
    }

    @Log(title = "话费订单退款", type = OperationType.OTHER)
    @Operation(summary = "退款")
    @PostMapping("/refund")
    public BizResult<Boolean> refund(@Valid @RequestBody OrderMobileRefundReq req) {
        Boolean result = orderMobileService.refund(req);
        return BizResult.create(result);
    }

    @Operation(summary = "回调重试")
    @GetMapping("/notify/retry")
    public BizResult<Boolean> notify(@RequestParam String orderNo) {
        boolean result = orderMobileService.notify(orderNo);
        return BizResult.create(result);
    }
}
