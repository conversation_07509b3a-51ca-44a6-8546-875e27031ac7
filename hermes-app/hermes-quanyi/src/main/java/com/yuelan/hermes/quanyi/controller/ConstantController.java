package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.AreaCodeService;
import com.yuelan.hermes.quanyi.controller.response.AreaCodeResp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "常量API")
@RequestMapping("/constants")
@RestController
public class ConstantController {

    @Autowired
    private AreaCodeService areaCodeService;

    @Operation(summary = "地域编码列表")
    @GetMapping("/areaList")
    public BizResult<List<AreaCodeResp>> areaList() {
        return BizResult.create(areaCodeService.getRespList());
    }

}
