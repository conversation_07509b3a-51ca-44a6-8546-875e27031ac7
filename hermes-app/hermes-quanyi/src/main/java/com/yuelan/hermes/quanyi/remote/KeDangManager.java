package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.enums.error.EccErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.properties.KeDangProperties;
import com.yuelan.hermes.quanyi.remote.request.KeDangSubmitOrderReq;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/12/23
 * @since 2024/12/23
 * <p>
 * 可当电信卡接口对接
 * <a href="https://www.yuque.com/yunmuzhouzhu/kiubni/xkbv7b8pwpq6phbk?singleDoc#">文档</a>
 */
@Slf4j
@Service
@AllArgsConstructor
public class KeDangManager {

    private static final String LOG_TAG = "可当电信卡";
    private static final String API_DO_MAIN = "https://api.kashichang.cn";
    private final KeDangProperties keDangProperties;

    /**
     * 获取签名
     * 每个订单都需要调用一次该接口，获取sign供后续接口使用，多个订单不可使用同一个sign
     *
     * @param pid 商品id
     */
    public String getSign(String pid) {
        String url = API_DO_MAIN + "/saas-api/realTime/getSign";
        HttpRequest form = HttpUtil.createPost(url)
                .form("eid", keDangProperties.getEid())
                .form("pid", pid)
                .form("syncCode", keDangProperties.getSyncCode());
        log.info("{}获取签名请求参数:{}", LOG_TAG, form);
        HttpResponse response = form.execute();
        log.info("{}获取签名响应:{}", LOG_TAG, response.body());
        //{"msg":"success","code":0,"sign":"000c91c7d50e9b94356bda9335b85c3129c"}
        if (response.isOk()) {
            JSONObject respBody = JSONObject.parseObject(response.body());
            String msg = respBody.getString("msg");
            Integer code = respBody.getInteger("code");
            if (Objects.equals(0, code)) {
                return respBody.getString("sign");
            }
            throw BizException.create(EccErrorCodeEnum.KE_DANG_SET_ASCRIPTION_FAIL, "获取签名失败:" + msg);
        }
        throw BizException.create(EccErrorCodeEnum.KE_DANG_GET_SIGN_FAIL, "获取签名失败");
    }

    /**
     * 设置归属地（兼选号）
     *
     * @param pid          商品id
     * @param provinceName 省份名称
     *                     例如：广东 不包含省字
     * @param cityName     城市名称 例如：深圳市
     */
    public List<String> setAscriptionOrGetPhone(String pid, String sign, String provinceName, String cityName) {
        if (StrUtil.isNotBlank(provinceName)) {
            provinceName = provinceName.replace("省", "");
        }
        String url = API_DO_MAIN + "/saas-api/realTime/setAscriptionOrGetPhone";
        HttpRequest form = HttpUtil.createPost(url)
                .form("eid", keDangProperties.getEid())
                .form("pid", pid)
                .form("syncCode", keDangProperties.getSyncCode())
                .form("provinceName", provinceName)
                .form("cityName", cityName)
                .form("sign", sign);

        log.info("{}设置归属地请求参数:{}", LOG_TAG, form);
        HttpResponse response = form.execute();
        log.info("{}设置归属地响应:{}", LOG_TAG, response.body());
        //{"msg":"success","phoneProvinceCode":"600302","code":0,"numbers":[{"number":"17377404837","indexArrStr":"3,4,5,6,7","law":"1","ranking":"69","lawCode":"AABCB","category":"连号","lawNumber":"7,7,4,0,4"},{"number":"17776231335","indexArrStr":"6,7,8,9","law":"1","ranking":"70","lawCode":"ABAA","category":"连号","lawNumber":"3,1,3,3"},{"number":"17776690342","indexArrStr":"4,5","law":"1","ranking":"72","lawCode":"AA","category":"连号","lawNumber":"6,6"},{"number":"17776239327","indexArrStr":"6,7,8","law":"1","ranking":"72","lawCode":"ABA","category":"连号","lawNumber":"3,9,3"},{"number":"17776234941","indexArrStr":"5,6,7","law":"1","ranking":"72","lawCode":"ABC","category":"顺子号","lawNumber":"2,3,4"},{"number":"17377135236","indexArrStr":"3,4","law":"1","ranking":"72","lawCode":"AA","category":"连号","lawNumber":"7,7"},{"number":"17377125695","indexArrStr":"3,4","law":"1","ranking":"72","lawCode":"AA","category":"连号","lawNumber":"7,7"},{"number":"17776237949","indexArrStr":"8,9,10","law":"1","ranking":"102","lawCode":"ABA","category":"连号","lawNumber":"9,4,9"},{"number":"17376025626","indexArrStr":"8,9,10","law":"1","ranking":"102","lawCode":"ABA","category":"连号","lawNumber":"6,2,6"},{"number":"17776235747","indexArrStr":"8,9,10","law":"1","ranking":"102","lawCode":"ABA","category":"连号","lawNumber":"7,4,7"}],"phoneCityCode":"8450100"}
        if (response.isOk()) {
            JSONObject respBody = JSONObject.parseObject(response.body());
            String msg = respBody.getString("msg");
            Integer code = respBody.getInteger("code");
            if (Objects.equals(0, code)) {
                JSONArray numbers = respBody.getJSONArray("numbers");
                List<String> phoneList = new ArrayList<>();
                for (int i = 0; i < numbers.size(); i++) {
                    JSONObject number = numbers.getJSONObject(i);
                    String phone = number.getString("number");
                    phoneList.add(phone);
                }
                return phoneList;
            } else {
                throw BizException.create(EccErrorCodeEnum.KE_DANG_SET_ASCRIPTION_FAIL, "获取号码失败:" + msg);
            }
        }
        throw BizException.create(EccErrorCodeEnum.KE_DANG_SET_ASCRIPTION_FAIL, "获取号码失败");
    }

    /**
     * 锁定号码
     */
    public void lockPhoneNumber(String pid, String sign, String phone) {
        String url = API_DO_MAIN + "/saas-api/realTime/lockPhoneNumber";
        HttpRequest form = HttpUtil.createPost(url)
                .form("eid", keDangProperties.getEid())
                .form("pid", pid)
                .form("sign", sign)
                .form("phoneNumber", phone)
                .form("syncCode", keDangProperties.getSyncCode());
        log.info("{}锁定号码请求参数:{}", LOG_TAG, form);
        HttpResponse response = form.execute();
        log.info("{}锁定号码响应:{}", LOG_TAG, response.body());
        if (response.isOk()) {
            JSONObject respBody = JSONObject.parseObject(response.body());
            String msg = respBody.getString("msg");
            Integer code = respBody.getInteger("code");
            if (Objects.equals(0, code)) {
                log.info("{}锁定号码成功", LOG_TAG);
                return;
            }
            throw BizException.create(EccErrorCodeEnum.KE_DANG_LOCK_PHONE_FAIL, "锁定号码失败:" + msg);
        }
        throw BizException.create(EccErrorCodeEnum.KE_DANG_LOCK_PHONE_FAIL, "锁定号码失败：内部网络异常");
    }

    /**
     * 发送短信sendMsg
     */
    public void sendMsg(String pid, String sign, String idCard) {
        String url = API_DO_MAIN + "/saas-api/realTime/sendMsg";
        HttpRequest form = HttpUtil.createPost(url)
                .form("eid", keDangProperties.getEid())
                .form("pid", pid)
                .form("sign", sign)
                .form("cardId", idCard)
                .form("syncCode", keDangProperties.getSyncCode());
        log.info("{}发送短信请求参数:{}", LOG_TAG, form);
        HttpResponse response = form.execute();
        log.info("{}发送短信响应:{}", LOG_TAG, response.body());
        if (response.isOk()) {
            JSONObject respBody = JSONObject.parseObject(response.body());
            String msg = respBody.getString("msg");
            Integer code = respBody.getInteger("code");
            if (Objects.equals(0, code)) {
                log.info("{}发送短信成功", LOG_TAG);
            } else {
                log.error("{}发送短信失败,code:{},msg:{}", LOG_TAG, code, msg);
            }
        } else {
            log.error("{}发送短信失败", LOG_TAG);
        }

    }

    /**
     * 提交订单
     */
    public String submitOrder(String pid, String sign, KeDangSubmitOrderReq orderReq) {
        String url = API_DO_MAIN + "/saas-api/realTime/upOrder";
        HttpRequest form = HttpUtil.createPost(url)
                .form("eid", keDangProperties.getEid())
                .form("pid", pid)
                .form("sign", sign)
                .form("syncCode", keDangProperties.getSyncCode())

                .form("name", orderReq.getName())
                .form("cardId", orderReq.getCardId())
                .form("phone", orderReq.getContactPhone())
                .form("sendProvinceName", StrUtil.replace(orderReq.getSendProvinceName(), "省", ""))
                .form("sendCityName", orderReq.getSendCityName())
                .form("sendDistrictName", orderReq.getSendDistrictName())
                .form("address", orderReq.getAddress())
                .form("source", 1);


        log.info("{}提交订单请求参数:{}", LOG_TAG, form);
        HttpResponse response = form.execute();
        log.info("{}提交订单响应:{}", LOG_TAG, response.body());
        //{"msg":"success","code":0,"orderNumber":"2023071321040866135613"}
        if (response.isOk()) {
            JSONObject respBody = JSONObject.parseObject(response.body());
            String msg = respBody.getString("msg");
            Integer code = respBody.getInteger("code");
            if (Objects.equals(0, code)) {
                return respBody.getString("orderNumber");
            } else {
                throw BizException.create(EccErrorCodeEnum.KE_DANG_SUBMIT_ORDER_FAIL, "提交订单失败:" + msg);
            }
        }
        log.error("{}提交订单失败", LOG_TAG);
        throw BizException.create(EccErrorCodeEnum.KE_DANG_SUBMIT_ORDER_FAIL, "提交订单失败：内部网络异常");
    }

    /**
     * 订单查询接口
     */
    public void queryOrder(String spOrderNo) {
        String url = API_DO_MAIN + "/saas-api/interface/phoneInfo/queryByOrderNumber";
        HttpRequest form = HttpUtil.createPost(url)
                .form("orderNo", spOrderNo);
        log.info("{}订单查询请求参数:{}", LOG_TAG, form);
        HttpResponse response = form.execute();
        log.info("{}订单查询响应:{}", LOG_TAG, response.body());
        if (response.isOk()) {
            JSONObject respBody = JSONObject.parseObject(response.body());
            String msg = respBody.getString("msg");
            Integer code = respBody.getInteger("code");
            if (Objects.equals(0, code)) {
                log.info("{}订单查询成功", LOG_TAG);
            } else {
                log.error("{}订单查询失败,code:{},msg:{}", LOG_TAG, code, msg);
            }
        } else {
            log.error("{}订单查询失败", LOG_TAG);
        }

    }
}
