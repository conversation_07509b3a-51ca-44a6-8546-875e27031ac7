package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/7/27 下午5:07
 */
@Data
public class WxCodeReq {

    @Schema(description = "微信授权scope")
    @NotEmpty(message = "scope不能为空")
    private String scope;

    @Schema(description = "微信授权code")
    @NotEmpty(message = "code不能为空")
    private String code;
}
