package com.yuelan.hermes.quanyi.controller;


import com.yuelan.hermes.quanyi.biz.service.OrderOnlineService;
import com.yuelan.hermes.quanyi.controller.request.OrderOnlineManualResultReq;
import com.yuelan.hermes.quanyi.controller.request.OrderVirtualListExportReq;
import com.yuelan.hermes.quanyi.controller.request.OrderVirtualListReq;
import com.yuelan.hermes.quanyi.controller.response.OrderVirtualItemRsp;
import com.yuelan.hermes.quanyi.controller.response.OrderVirtualRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Tag(name = "虚拟商品订单")
@RequestMapping("/a/orderVirtual")
@RestController
public class OrderOnlineController extends AdminBaseController {
    @Autowired
    OrderOnlineService orderOnlineService;

    @Operation(summary = "虚拟商品订单列表")
    @PostMapping("/list")
    public BizResult<PageData<OrderVirtualRsp>> orderVirtualList(@RequestBody OrderVirtualListReq req) {
        req.check();
        PageData<OrderVirtualRsp> resultData = orderOnlineService.orderVirtualList(req);
        return BizResult.create(resultData);
    }

    @Operation(summary = "订单详情")
    @GetMapping("/detail")
    public BizResult<OrderVirtualRsp> detail(@RequestParam String orderNo) {
        OrderVirtualRsp result = orderOnlineService.detail(orderNo);
        return BizResult.create(result);
    }

    @Operation(summary = "订单明细")
    @GetMapping("/item")
    public BizResult<List<OrderVirtualItemRsp>> item(@RequestParam String orderNo) {
        List<OrderVirtualItemRsp> items = orderOnlineService.item(orderNo);
        return BizResult.create(items);
    }


    @Log(title = "虚拟商品订单导出", type = OperationType.OTHER)
    @Operation(summary = "虚拟商品订单导出")
    @PostMapping("/export")
    public void detailExport(@RequestBody OrderVirtualListExportReq req, HttpServletResponse response) throws IOException {
        req.check();
        orderOnlineService.export(response, req);
    }

    @Operation(summary = "回调重试")
    @GetMapping("/notify/retry")
    public BizResult<Boolean> notify(@RequestParam String orderNo) {
        boolean result = orderOnlineService.notify(orderNo);
        return BizResult.create(result);
    }

    @Log(title = "订单异常处理", type = OperationType.OTHER)
    @Operation(summary = "异常处理")
    @PostMapping("/manual/result")
    public BizResult<Boolean> manualResult(@RequestBody OrderOnlineManualResultReq req) {
        boolean result = orderOnlineService.manualResult(req);
        return BizResult.create(result);
    }
}
