package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/5/3 下午6:16
 */
@Data
public class EccProdUpdateStatusReq {

    @NotNull(message = "权益产品包id不能为空")
    @Schema(description = "权益产品包")
    private Long productId;


    @NotNull(message = "上下架状态不能为空")
    @Max(value = 1, message = "状态值不合法:上下架状态：0-下架；1-上架")
    @Min(value = 0, message = "状态值不合法:上下架状态：0-下架；1-上架")
    @Schema(description = "上下架状态：0-下架；1-上架")
    private Integer prodStatus;

    public Wrapper<EccProductDO> buildUpdateWrapper() {
        return Wrappers.lambdaUpdate(EccProductDO.class)
                .eq(EccProductDO::getProdId, getProductId())
                .set(EccProductDO::getProdStatus, getProdStatus());

    }
}
