package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.chinaunicom.zop.api.client.ZopHttpClient;
import com.chinaunicom.zop.api.model.request.*;
import com.chinaunicom.zop.api.model.response.*;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.biz.service.EccAreaService;
import com.yuelan.hermes.quanyi.common.enums.SpProdEnum;
import com.yuelan.hermes.quanyi.common.enums.ZopMsgEnum;
import com.yuelan.hermes.quanyi.common.enums.ZopSelectPhoneNumRespCodeEnum;
import com.yuelan.hermes.quanyi.common.enums.error.EccErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.City2ReferrerCode;
import com.yuelan.hermes.quanyi.common.pojo.bo.EccZopSelectPhoneNumsResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.PhoneLocationBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.ZopApiSimpleRespBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.ZopProperties;
import com.yuelan.hermes.quanyi.controller.request.EcommerceSimCardSearchReq;
import com.yuelan.hermes.quanyi.controller.response.EccProdConfigResp;
import com.yuelan.hermes.quanyi.remote.request.zop.KingOrderQueryReq;
import com.yuelan.hermes.quanyi.remote.response.zop.KingOrderQueryResp;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/4/26 上午10:44
 * zop的接口 其实就是用户在我们的电商卡推广页面的收货信息，点击领取之后，我们把信息同步给联通的接口就叫zop
 * 用户选号接口
 * ->3.4-客户资料校验 和 身份证认证通用接口2.0版 (可省略 因为3.8会校验身份证信息是否正确)
 * -> 3.8-选号后置-意向单同步
 * ->3.33-选号后置-正式单同步(非提前预占版)
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ZOPManager {
    private final ApplicationContext applicationContext;
    private final ZopProperties zopProperties;
    private static final boolean isDebugZop = false;

    /***
     * 构建zop客户端
     */
    private ZopHttpClient buildZopHttpClient(ZopProperties.ZopConfig zopProperties) {
        String appCode = zopProperties.getAppCode();
        String aesKey = zopProperties.getAesKey();
        String hmcKey = zopProperties.getHmcKey();
        return new ZopHttpClient(zopProperties.getApiBaseUrl(), appCode, hmcKey, aesKey);
    }

    /**
     * 根据  电商卡权益包 返回zop的参数配置
     */
    private ZopProperties.ZopConfig getZopPropertiesByProductDO(EccProductDO eccProductDO) {
        // 默认返回 悦蓝大王卡  以后多配置扩展课在新增EccProductDO 选择ZopProdEnum
        SpProdEnum spProdEnum = getSpProdEnumByProductDO(eccProductDO);
        if (Objects.isNull(spProdEnum)) {
            spProdEnum = SpProdEnum.YL;
        }
        return zopProperties.getZopConfMap().get(spProdEnum);
    }

    private SpProdEnum getSpProdEnumByProductDO(EccProductDO eccProductDO) {
        Integer spProdId = eccProductDO.getSpProdId();
        return SpProdEnum.of(spProdId);
    }


    /**
     * 用户选号接口 --精简版本
     * 对接自：docs/文档/电商卡/联通/号码服务接口规范.docx
     */
    public EccZopSelectPhoneNumsResultBO selectPhoneNumV1(EcommerceSimCardSearchReq searchReq, EccProductDO eccProductDO) {
        EccZopSelectPhoneNumsResultBO resultBO = new EccZopSelectPhoneNumsResultBO();
        List<PhoneLocationBO> phoneList = new ArrayList<>();
        resultBO.setPhoneList(phoneList);
        if (isDebugZop) {
            resultBO.setReqSuccess(Boolean.TRUE);
            log.info("测试环境模拟[用户选号接口 --精简版本]成功");
            String phone = RandomUtil.randomEle(Arrays.asList("13", "14", "15", "16", "17", "18", "19")) + RandomUtil.randomNumbers(9);
            phoneList.add(new PhoneLocationBO(phone, "0", "测试省", "0", "测试市"));
            return resultBO;
        }
        ZopProperties.ZopConfig zopConfig = getZopPropertiesByProductDO(eccProductDO);
        SpProdEnum spProdEnum = getSpProdEnumByProductDO(eccProductDO);
        Map<String, List<City2ReferrerCode>> allowOrderLocationMap = zopProperties.getAllowOrderLocation(spProdEnum);

        setProvinceAndCityCodes(searchReq, allowOrderLocationMap);

        EccAreaDO numCode = applicationContext.getBean(EccAreaService.class).getOneAreaByNumCode(spProdEnum.getSpEnum().getSpEnum(), searchReq.getProvinceCode(), searchReq.getCityCode());
        if (Objects.isNull(numCode)) {
            throw BizException.create(EccErrorCodeEnum.ZOP_SELECT_CARD_FAIL, "系统错误：没有找到对应的归属地信息");
        }
        String province = numCode.getAreaName();
        String city = numCode.getSubAreaList().get(0).getAreaName();
        return selectPhoneNumV1(searchReq, province, city, zopConfig, eccProductDO.getSpGoodsId());
    }

    public String getReferrerCode(EccProductDO eccProductDO, String provinceCode, String cityCode) {
        SpProdEnum spProdEnum = getSpProdEnumByProductDO(eccProductDO);
        return getReferrerCode(spProdEnum, provinceCode, cityCode);
    }

    public String getReferrerCode(SpProdEnum spProdEnum, String provinceCode, String cityCode) {
        Map<String, List<City2ReferrerCode>> allowOrderLocationMap = zopProperties.getAllowOrderLocation(spProdEnum);
        List<City2ReferrerCode> city2ReferrerCodes = allowOrderLocationMap.get(provinceCode);
        return Optional.ofNullable(city2ReferrerCodes)
                .flatMap(codes -> codes.stream()
                        .filter(code -> code.getCityCode().equals(cityCode))
                        .map(City2ReferrerCode::getReferrerCode)
                        .findFirst())
                .orElse(null);
    }

    /**
     * 如果指定的省市归属地那么就校验是否在允许的范围内  否是就随机一个可用的归属地
     */
    private void setProvinceAndCityCodes(EcommerceSimCardSearchReq searchReq, Map<String, List<City2ReferrerCode>> allowOrderLocationMap) {
        if (Objects.nonNull(searchReq.getProvinceCode())) {
            if (!allowOrderLocationMap.containsKey(searchReq.getProvinceCode())) {
                throw BizException.create(EccErrorCodeEnum.ZOP_SELECT_CARD_FAIL, "系统错误：不支持该省份的选号");
            }
            List<City2ReferrerCode> city2ReferrerCodes = allowOrderLocationMap.get(searchReq.getProvinceCode());
            if (Objects.nonNull(searchReq.getCityCode())) {
                if (city2ReferrerCodes.stream().noneMatch(city2ReferrerCode -> city2ReferrerCode.getCityCode().equals(searchReq.getCityCode()))) {
                    throw BizException.create(EccErrorCodeEnum.ZOP_SELECT_CARD_FAIL, "系统错误：不支持该城市的选号");
                }
            } else {
                searchReq.setCityCode(city2ReferrerCodes.get(RandomUtil.randomInt(city2ReferrerCodes.size())).getCityCode());
            }
        } else {
            String provinceCode = RandomUtil.randomEle(new ArrayList<>(allowOrderLocationMap.keySet()));
            searchReq.setProvinceCode(provinceCode);
            List<City2ReferrerCode> city2ReferrerCodes = allowOrderLocationMap.get(provinceCode);
            searchReq.setCityCode(city2ReferrerCodes.get(RandomUtil.randomInt(city2ReferrerCodes.size())).getCityCode());
        }
    }

    public EccZopSelectPhoneNumsResultBO selectPhoneNumV1(EcommerceSimCardSearchReq searchReq, String province, String city, ZopProperties.ZopConfig zopConfig, String zopGoodsId) {
        EccZopSelectPhoneNumsResultBO resultBO = new EccZopSelectPhoneNumsResultBO();
        List<PhoneLocationBO> phoneList = new ArrayList<>();
        resultBO.setPhoneList(phoneList);
        // 查询类型：01:列表查询（未分组号码查询） 02：组内号码查询
        final String qryType = "02";
        // 查询类型 1、普通选号 2、靓号选号 3、全部（普通、靓号都包括）
        final String searchCategory = "1";
        String searchType = null;
        String searchValue = null;
        String phoneLast4 = searchReq.getPhoneLast4();
        if (Objects.nonNull(phoneLast4)
                && phoneLast4.length() >= 2 && phoneLast4.length() <= 4
                && NumberUtil.isNumber(phoneLast4)) {
            // 尾号查询
            searchType = "02";
            searchValue = phoneLast4;
        }
        // 查询数量 默认10个每次
        String amounts = Objects.equals(searchReq.getSelectNum(), 100) ? "100" : "10";
        KingNumSelectRequest req = new KingNumSelectRequest();
        req.setGoodsId(zopGoodsId);
        req.setProvinceCode(searchReq.getProvinceCode());
        req.setCityCode(searchReq.getCityCode());
        req.setQryType(qryType);
        req.setSearchCategory(searchCategory);
        req.setSearchType(searchType);
        req.setSearchValue(searchValue);
        req.setAmounts(amounts);
        KingNumSelectResponse response = buildZopHttpClient(zopConfig).execute(req);
        if (ZopSelectPhoneNumRespCodeEnum.M1.getCode().equals(response.getRspCode()) || ZopSelectPhoneNumRespCodeEnum.T1.getCode().equals(response.getRspCode())) {
            resultBO.setReqSuccess(Boolean.TRUE);
            // 成功但是没有号码
            return resultBO;
        }
        if (!ZopSelectPhoneNumRespCodeEnum.M0.getCode().equals(response.getRspCode())) {
            ZopSelectPhoneNumRespCodeEnum respCodeEnum = ZopSelectPhoneNumRespCodeEnum.of(response.getRspCode());
            log.error("请求选号接口响应错误{}", respCodeEnum);
            // 失败才记录resp
            resultBO.setReqSuccess(Boolean.FALSE);
            resultBO.setFailResponseSource(JSONObject.toJSONString(response));
            resultBO.setFailMsg(response.getRspDesc());
            return resultBO;
        }
        resultBO.setReqSuccess(Boolean.TRUE);
        KingNumSelectResponse.KingNumSelectRspBody body = response.getBody();
        List<String> apiPhones = body.getNumArray().stream().filter(num -> Objects.nonNull(num) && num.length() == 11).collect(Collectors.toList());
        for (String apiPhone : apiPhones) {
            phoneList.add(new PhoneLocationBO(apiPhone, req.getProvinceCode(), province, req.getCityCode(), city));
        }
        return resultBO;
    }




    /**
     * 选号后置-意向单同步
     * 对接自：docs/文档/电商卡/联通/2I业务接口规范v0411.docx-3.8选号后置-意向单同步
     */
    public ZopApiSimpleRespBO preOrderSync(EccZopOrderDO orderDO, EccProductDO eccProductDO) {
        if (!AppConstants.isReal() && !isDebugZop) {
            log.info("测试环境模拟[选号后置-意向单同步]成功");
            return new ZopApiSimpleRespBO(Boolean.TRUE, null);
        }
        ZopProperties.ZopConfig zopProperties = getZopPropertiesByProductDO(eccProductDO);
        ZopProperties.ZopGoodsInfo zopGoodsInfo = zopProperties.getZopGoodsInfoByZopGoodsId(orderDO.getZopGoodsId());
        KingPreOrderSyncRequest req = new KingPreOrderSyncRequest();
        // 首月资费方式 默认 首月按天
        req.setFirstMonthFee("A000011V000004");
        if (Objects.nonNull(zopGoodsInfo.getFirstMonthFee())) {
            req.setFirstMonthFee(zopGoodsInfo.getFirstMonthFee());
        }
        req.setOrderId(orderDO.getOrderNo());
        req.setGoodsId(orderDO.getZopGoodsId());
        req.setCertName(orderDO.getIdCardName());
        req.setContactNum(orderDO.getContactPhone());
        req.setCertNo(orderDO.getIdCard());
        req.setPostProvinceCode(orderDO.getPostProvinceCode());
        req.setPostCityCode(orderDO.getPostCityCode());
        req.setPostDistrictCode(orderDO.getPostDistrictCode());
        req.setPostAddr(orderDO.getAddress());
        req.setChannel(zopProperties.getChannel());
        req.setLaunchPlatform("sy-zjz-yuelan001");
        req.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
        req.setUpdateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
        req.setOrderTotalFee("0");
        String pageUrl = orderDO.getPageUrl() == null ? zopGoodsInfo.getPageUrl() : orderDO.getPageUrl();
        req.setPageUrl(pageUrl);
        req.setResourceId(zopGoodsInfo.getResourceId());
        req.setReferrerCode(orderDO.getZopReferrerCode());
        KingPreOrderSyncResponse response = buildZopHttpClient(zopProperties).execute(req);
        if (Objects.isNull(response)) {
            orderDO.setPreOrderResp("运营商接口无响应");
            return new ZopApiSimpleRespBO(Boolean.FALSE, "运营商接口无响应");
        }
        orderDO.setPreOrderResp(StrUtil.subPre(JSONObject.toJSONString(response), 200));
        boolean isSuccess = "0000".equals(response.getRspCode());
        if (isSuccess) {
            JSONObject bodyJson = JSONObject.parse(response.getBody());
            String orderNo = bodyJson.getString("orderNo");
            String token = bodyJson.getString("token");
            orderDO.setPreOrderNo(orderNo);
            orderDO.setPreOrderToken(token);
            return new ZopApiSimpleRespBO(Boolean.TRUE, null);
        } else {
            return new ZopApiSimpleRespBO(Boolean.FALSE, response.getRspDesc());
        }
    }

    /**
     * 3.4、客户资料校验 和 身份证认证通用接口2.0版
     * 对接自：docs/文档/电商卡/联通/2I业务接口规范v0411.docx-3.4、客户资料校验 和 身份证认证通用接口2.0版
     */
    public KingIdentityCustV2Response getIdCardCheck(EccZopOrderDO zopOrderDO, EccProductDO eccProductDO, EccAreaDO areaDO) {
        if (!AppConstants.isReal() && !isDebugZop) {
            log.info("测试环境模拟资料校验成功");
            KingIdentityCustV2Response response = new KingIdentityCustV2Response();
            response.setaCode("0000");
            response.setbCode("0000");
            return response;
        }
        ZopProperties.ZopConfig zopProperties = getZopPropertiesByProductDO(eccProductDO);
        KingIdentityCustV2Request request = new KingIdentityCustV2Request();
        request.setCertName(zopOrderDO.getIdCardName());
        request.setCertNum(zopOrderDO.getIdCard());
        // 联通技术说这个是 收货地址的  归属地代码 是2位和3位的不是 postProvinceCode postCityCode
        request.setProvince(areaDO.getNumCode());
        request.setCity(areaDO.getSubAreaList().get(0).getNumCode());
        return buildZopHttpClient(zopProperties).execute(request);
    }

    public boolean checkIdCard(String idCard, String name, EccProductDO eccProductDO) {
        ZopProperties.ZopConfig zopProperties = getZopPropertiesByProductDO(eccProductDO);
        KingIdentityCustV2Request request = new KingIdentityCustV2Request();
        request.setCertNum(idCard);
        request.setCertName(name);
        // 联通技术说这个是 【收货地址的归属地代码】 是2位和3位的不是 postProvinceCode postCityCode
        request.setProvince("30");
        request.setCity("303");
        request.setCertType("03");
        KingIdentityCustV2Response response = buildZopHttpClient(zopProperties).execute(request);
        System.out.println(JSONObject.toJSONString(response));
        return false;
    }

    /**
     * 风控校验
     * 对接自：docs/文档/电商卡/联通/2I业务接口规范v0411.docx-3.21、 风控校验
     */
    public KingRiskCheckResponse riskControlCheck(EccZopOrderDO zopOrderDO, EccProductDO eccProductDO) {
        if (!AppConstants.isReal() && !isDebugZop) {
            log.info("测试环境模拟风控校验成功");
            KingRiskCheckResponse response = new KingRiskCheckResponse();
            response.setRspCode("0000");
            return response;
        }
        ZopProperties.ZopConfig zopProperties = getZopPropertiesByProductDO(eccProductDO);
        SpProdEnum spProdEnum = getSpProdEnumByProductDO(eccProductDO);
        KingRiskCheckRequest req = new KingRiskCheckRequest();
        req.setCertId(zopOrderDO.getIdCard());
        req.setCertName(zopOrderDO.getIdCardName());
        req.setContactPhone(zopOrderDO.getContactPhone());
        req.setPostProvinceCode(zopOrderDO.getPostProvinceCode());
        req.setPostCityCode(zopOrderDO.getPostCityCode());
        req.setPostDistrictCode(zopOrderDO.getPostDistrictCode());
        req.setPostAddr(zopOrderDO.getAddress());
        req.setChannel(zopProperties.getChannel());
        req.setProvinceCode(zopOrderDO.getProvinceCode());
        req.setCityCode(zopOrderDO.getCityCode());
        // 业务类型字段：01 移网订单 02 宽融订单 ，该字段可不传，不传的情况下默认为移网订单
        req.setBusinessType("01");
        return buildZopHttpClient(zopProperties).execute(req);
    }

    /**
     * 同步订单给zop后订单的状态变更 获取和消费 类似MQ
     * <p>
     * 3.10、订单消息获取
     */
    public KingMessageGetResponse orderMessageGet(SpProdEnum spProdEnum, ZopMsgEnum zopMsgEnum) {
        ZopProperties.ZopConfig zopConfig = zopProperties.getZopConfMap().get(spProdEnum);
        KingMessageGetRequest req = new KingMessageGetRequest();
        req.setMsgChannel(zopConfig.getMsgChannel());
        // 消息类型：3-下单消息；4-订单状态变更消息   下单消息没必要吧我已经入库了
        req.setType(String.valueOf(zopMsgEnum.getType()));
        return buildZopHttpClient(zopConfig).execute(req);
    }

    /**
     * 3.10、消息删除服务
     */
    public boolean orderMessageConsume(SpProdEnum spProdEnum, String delMsgId, ZopMsgEnum zopMsgEnum) {
        ZopProperties.ZopConfig zopConfig = zopProperties.getZopConfMap().get(spProdEnum);
        KingMessageDelRequest req = new KingMessageDelRequest();
        req.setMsgId(delMsgId);
        req.setType(zopMsgEnum.getType().toString());
        KingMessageDelResponse resp = buildZopHttpClient(zopConfig).execute(req);
        return Objects.nonNull(resp) && "0000".equals(resp.getRspCode());
    }


    /**
     * 订单查询服务
     * 对接自：docs/文档/电商卡/联通/2I业务接口规范v04-11.docx-3.24、订单查询服务
     */
    public void orderQuery(EccProductDO eccProductDO, String contactNum) {
        ZopProperties.ZopConfig zopProperties = getZopPropertiesByProductDO(eccProductDO);
        KingOrderQueryReq req = new KingOrderQueryReq();
        req.setProductId(zopProperties.getZopProductId());
        req.setTimeRange("7");
        req.setContactNum(contactNum);
        KingOrderQueryResp resp = buildZopHttpClient(zopProperties).execute(req);
        System.out.println(JSONObject.toJSONString(resp));
    }

    /**
     * 省市区的代码查询
     * 对接自：docs/文档/电商卡/联通/2I业务接口规范v04
     */
    public void provinceInfo(EccProductDO eccProductDO) {
        ZopProperties.ZopConfig zopProperties = getZopPropertiesByProductDO(eccProductDO);
        KingPostInfoRequest req = new KingPostInfoRequest();
        req.setProvinceCode("36");
        KingPostInfoResponse execute = buildZopHttpClient(zopProperties).execute(req);
        log.info("provinceInfo:{}", JSONObject.toJSONString(execute.getBody()));
    }

    /**
     * 调用ZOP API查询省份数据
     *
     * @param    req    三方SDK请求对象"KingPostInfoRequest"
     */
    public KingPostInfoResponse queryAreaInfo(KingPostInfoRequest req) {
        return this.buildZopHttpClient(zopProperties.getZopConfMap().get(SpProdEnum.YL)).execute(req);
    }

    /**
     * zop正式订单同步
     */
    public ZopApiSimpleRespBO confirmOrder(EccZopOrderDO orderDO, EccProductDO eccProductDO) {
        if (!AppConstants.isReal() && !isDebugZop) {
            log.info("测试环境模拟[zop正式订单同步]成功");
            return new ZopApiSimpleRespBO(Boolean.TRUE, null);
        }
        ZopProperties.ZopConfig zopProperties = getZopPropertiesByProductDO(eccProductDO);
        KingOrderSyncV2Request req = new KingOrderSyncV2Request();
        req.setGoodsId(orderDO.getZopGoodsId());
        req.setProvinceCode(orderDO.getProvinceCode());
        req.setCityCode(orderDO.getCityCode());
        req.setPhoneNum(orderDO.getPhone());
        req.setToken(orderDO.getPreOrderToken());
        req.setCreateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN)));
        // 不开通 呼叫转移
        req.setIsOpenCF("0");
        KingOrderSyncResponse response = buildZopHttpClient(zopProperties).execute(req);
        if (Objects.isNull(response)) {
            orderDO.setOrderSyncResp("运营商接口无响应");
            return new ZopApiSimpleRespBO(Boolean.FALSE, "运营商接口无响应");
        }
        orderDO.setOrderSyncResp(StrUtil.subPre(JSONObject.toJSONString(response), 200));
        boolean isSuccess = "0000".equals(response.getRspCode());
        if (isSuccess) {
            JSONObject bodyJson = JSONObject.parse(response.getBody());
            String orderNo = bodyJson.getString("orderNo");
            String mallOrderId = bodyJson.getString("mallOrderId");
            orderDO.setSyncOrderId(mallOrderId);
            orderDO.setSyncOrderNo(orderNo);
            return new ZopApiSimpleRespBO(Boolean.TRUE, null);
        } else {
            return new ZopApiSimpleRespBO(Boolean.FALSE, response.getRspDesc());
        }
    }

    /**
     * 资源上传接口
     */
    public void uploadResource(EccProductDO eccProductDO, String imgBase64, String zopGoodsIdFilter) {
        ZopProperties.ZopConfig zopProperties = getZopPropertiesByProductDO(eccProductDO);
        for (ZopProperties.ZopGoodsInfo zopGoodsInfo : zopProperties.getZopGoodsInfos()) {
            String zopGoodsId = zopGoodsInfo.getZopGoodId();
            if (Objects.nonNull(zopGoodsIdFilter) && !zopGoodsIdFilter.equals(zopGoodsId)) {
                continue;
            }
            // 上传资源
            KingResourceUploadRequest request = new KingResourceUploadRequest();
            request.setGoodsId(zopGoodsId);
            request.setChannel(zopProperties.getChannel());
            List<KingResourceUploadRequest.ResourceContent> resourceContents = new ArrayList<>();
            KingResourceUploadRequest.ResourceContent resourceContent = new KingResourceUploadRequest.ResourceContent();
            resourceContent.setSort(1);
            resourceContent.setContent(imgBase64);
            resourceContents.add(resourceContent);
            request.setResourceContents(resourceContents);
            KingResourceUploadResponse response = buildZopHttpClient(zopProperties).execute(request);
            if (Objects.nonNull(response)) {
                log.info("goodsId={} 响应= {}", zopGoodsId, JSONObject.toJSONString(response));
            }
        }
    }


    /**
     * 校验ecc产品是否包含联通参数
     */
    public boolean checkZopGoodsId(String zopGoodsId, EccProductDO productDO) {
        ZopProperties.ZopConfig zopProperties = getZopPropertiesByProductDO(productDO);
        ZopProperties.ZopGoodsInfo zopGoodsInfo = zopProperties.getZopGoodsInfoByZopGoodsId(zopGoodsId);
        return zopGoodsInfo != null;
    }

    public EccProdConfigResp getProdConfig(EccProductDO eccProductDO) {
        SpProdEnum spProdEnum = getSpProdEnumByProductDO(eccProductDO);
        Map<String, List<City2ReferrerCode>> allowOrderLocation = zopProperties.getAllowOrderLocation(spProdEnum);
        EccProdConfigResp resp = new EccProdConfigResp();
        if (Objects.nonNull(allowOrderLocation)) {
            Map<String, List<String>> allowSelectArea = new HashMap<>();
            allowOrderLocation.forEach((k, v) -> {
                List<String> cityCodes = v.stream().map(City2ReferrerCode::getCityCode).collect(Collectors.toList());
                allowSelectArea.put(k, cityCodes);
            });
            resp.setAllowSelectArea(allowSelectArea);
        }
        return resp;
    }
}
