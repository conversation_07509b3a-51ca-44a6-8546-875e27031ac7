package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.StrUtil;
import com.yuelan.hermes.quanyi.biz.handler.impl.BenefitWoReadWxPayV2Channel;
import com.yuelan.hermes.quanyi.biz.manager.BenefitOrderManager;
import com.yuelan.hermes.quanyi.common.enums.UnicomWoNotifyStatus;
import com.yuelan.hermes.quanyi.controller.request.WoReadStatusChangeReq;
import com.yuelan.hermes.quanyi.remote.UnicomReadPayManager;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/7 17:08
 */
@Tag(name = "联通Wo阅读api")
@Slf4j
@SaIgnore
@RestController
@RequiredArgsConstructor
@RequestMapping("/unicom")
public class UnicomController {

    private final BenefitOrderManager benefitOrderManager;

    private final UnicomReadPayManager unicomReadPayManager;

    private final BenefitWoReadWxPayV2Channel wxPayV2Channel;



    /**
     * 沃阅读支付回调
     * 如果合作方提供了后台回调URL地址，沃阅读将后台通知合作方订单处理结果。该URL后面会增加成功失败标签，并通过get方法调度。标签值为instatus=&sign=;其中instatus为订单状态，与流程第9步保持一致；sign申请临时订单时获取的签名，与流程第4步保持一致。合作方需要验签
     */
    @Operation(summary = "沃阅读支付回调")
    @RequestMapping(value = "/order/notify/{orderNo}", method = {RequestMethod.GET, RequestMethod.POST})
    public String readNotify(@PathVariable String orderNo, HttpServletRequest request) {
        String inStatus = request.getParameter("instatus");
        String sign = request.getParameter("sign");
        log.info("沃阅读支付回调 orderNo:{} inStatus:{} sign:{}", orderNo, inStatus, sign);
        if (StrUtil.isBlank(orderNo) || StrUtil.isBlank(inStatus) || StrUtil.isBlank(sign)) {
            log.error("沃阅读支付回调参数错误 orderNo:{} inStatus:{} sign:{}", orderNo, inStatus, sign);
            return "0";
        }
        UnicomReadPayManager.UnicomReadPayNotify notify = new UnicomReadPayManager.UnicomReadPayNotify();
        notify.setStatus(inStatus);
        notify.setSign(sign);
        notify.setOrderNo(orderNo);
        return benefitOrderManager.unicomPayNotify(notify);
    }

    /**
     * 对于微信支付,能有回调,就说明是扣费成功
     * 沃阅读支付宝支付也走这个回调
     * 返回数据格式:
     * {
     *     "code": "0000",
     *     "innercode": "0000",
     *     "message": "success",
     *     "newCapOrderId": null,
     *     "orderid": "****************",
     *     "userid": "***********",
     *     "useraccount": "***********"
     * }
     */
    @Operation(summary = "沃阅读微信支付回调")
    @RequestMapping(value = "/order/wxNotify/{orderNo}", method = RequestMethod.POST)
    public String wxNotify(@PathVariable String orderNo, @RequestBody Map<String, String> map, HttpServletRequest request) {
        log.info("沃阅读支付回调 orderNo:{} , body:{}", orderNo, map);
        if (StrUtil.isBlank(orderNo)) {
            log.error("沃阅读支付回调参数错误 orderNo:{} , body:{}", orderNo, map);
            return "0";
        }
        try {
            String message = map.get("message");
            String code = map.get("code");
            if (StringUtils.isAnyBlank(message, code)) {
                return "0";
            }
            UnicomWoNotifyStatus status = message.equals("success") ? UnicomWoNotifyStatus.SUCCESS : UnicomWoNotifyStatus.FAIL;
            UnicomReadPayManager.UnicomReadPayNotify notify = new UnicomReadPayManager.UnicomReadPayNotify();
            notify.setStatus(status.getCode() + "");
            notify.setOrderNo(orderNo);
            return benefitOrderManager.unicomPayNotify(notify);
        } catch (Exception e) {
            log.error("沃阅读支付回调失败", e);
            return "0";
        }

    }

    @Operation(summary = "沃阅读微信支付订购状态状态变更回调")
    @RequestMapping(value = "/order/status/wxNotify", method = RequestMethod.POST)
    public BizResult wxStatusNotify(@RequestBody @Validated WoReadStatusChangeReq params, HttpServletRequest request) {
        // 先判断是不是微信 2.0的订单
        boolean isWxPayV2Order = wxPayV2Channel.isWxPayV2Order(params);
        if (isWxPayV2Order) {
            log.info("沃阅读微信支付V2订单状态变更回调: {}", params);
            BizResult bizResult = wxPayV2Channel.wxStatusNotify(params);
            if (Objects.equals(bizResult.getCode(), "0000")) {
                // 处理成功直接返回否则给其他业务逻辑处理 因为也有一样 productpkgId 的区分不开
                return bizResult;
            }
        }
        return unicomReadPayManager.wxStatusNotify(params);
    }
}
