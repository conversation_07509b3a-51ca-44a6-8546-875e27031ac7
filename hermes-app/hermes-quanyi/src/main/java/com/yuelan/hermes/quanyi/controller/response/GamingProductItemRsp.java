package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class GamingProductItemRsp extends GamingGoodsRsp {

    @Schema(description = "明细ID")
    private Long itemId;

    @Schema(description = "生效时间")
    private LocalDate effectiveDate;

    @Schema(description = "过期时间")
    private LocalDate expireDate;

}
