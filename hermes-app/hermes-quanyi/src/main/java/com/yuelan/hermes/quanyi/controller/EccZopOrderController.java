package com.yuelan.hermes.quanyi.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.service.EccZopOrderDOService;
import com.yuelan.hermes.quanyi.config.satoken.StpAdminUtil;
import com.yuelan.hermes.quanyi.controller.request.EccZopOrderListReq;
import com.yuelan.hermes.quanyi.controller.request.EccZopOrderTotalReq;
import com.yuelan.hermes.quanyi.controller.response.EccZopOrderDateTotalResp;
import com.yuelan.hermes.quanyi.controller.response.EccZopOrderResp;
import com.yuelan.hermes.quanyi.controller.response.EccZopOrderTotalResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.KeyValue;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> 2024/5/15 上午10:57
 */
@Validated
@Tag(name = "电商卡/后台api/电商卡领取记录")
@RestController
@RequestMapping("/a/ecc/zopOrder")
@AllArgsConstructor
public class EccZopOrderController {

    private final EccZopOrderDOService eccZopOrderDOService;

    @Operation(summary = "电商卡领取记录分页列表")
    @PostMapping("/list")
    public BizResult<PageData<EccZopOrderResp>> list(@RequestBody EccZopOrderListReq req) {
        return BizResult.create(eccZopOrderDOService.pageList(req));
    }

    @SaIgnore
    @Operation(summary = "查询联通状态选项")
    @PostMapping("/unicomStatusOptions")
    public BizResult<List<KeyValue<String, String>>> unicomStatusOptions() {
        return BizResult.create(eccZopOrderDOService.unicomStatusOptions());
    }

    /**
     * 导入联通领卡记录
     * 现有领卡记录都是请求接口，系统插入的
     * 只过滤 网上商城端订单 导入 否则会影响次月的权益领取
     */
    @Operation(summary = "导入联通领卡记录")
    @PostMapping(value = "/importUnicomOrder", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BizResult<Void> importUnicomOrder(@RequestParam("file") MultipartFile file) throws IOException {
        eccZopOrderDOService.importUnicomOrder(file);
        return BizResult.ok();
    }

    @Operation(summary = "电商卡领卡记录汇总")
    @PostMapping("/total")
    public BizResult<EccZopOrderTotalResp> total(@RequestBody EccZopOrderTotalReq req) {
        return BizResult.create(eccZopOrderDOService.total(req));
    }

    @Operation(summary = "电商卡领卡日期汇总分页")
    @PostMapping("/dateTotal")
    public BizResult<PageData<EccZopOrderDateTotalResp>> dateTotal(@RequestBody EccZopOrderTotalReq req) {
        return BizResult.create(eccZopOrderDOService.dateTotalPage(req));
    }

    @Operation(summary = "导出电商卡领取记录")
    @PostMapping("/export")
    public BizResult<FileExportTaskCreateResp> export(@RequestBody EccZopOrderListReq req) {
        long adminId = StpAdminUtil.getLoginIdAsLong();
        return BizResult.create(eccZopOrderDOService.export(req, adminId));
    }

    @Operation(summary = "商城订单重试")
    @PostMapping("/retry/{orderId}")
    public BizResult<Void> retryMallOrder(@PathVariable Long orderId) {
        eccZopOrderDOService.retryMallOrder(orderId);
        return BizResult.ok();
    }


}
