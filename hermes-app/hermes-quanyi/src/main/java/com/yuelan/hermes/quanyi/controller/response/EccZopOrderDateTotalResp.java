package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class EccZopOrderDateTotalResp extends EccZopOrderTotalResp {

    @Schema(description = "领卡日期")
    private String getDate;

    @Schema(description = "产品id")
    private Long prodId;

    @Schema(description = "产品名称")
    private String prodName;

    @Schema(description = "渠道id")
    private Long channelId;

    @Schema(description = "渠道名称")
    private String channelName;

    @Schema(description = "渠道类型")
    private Integer channelType;

}
