package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AreaCodeResp {

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "省份地域编码")
    private Integer provinceAdcode;

    @Schema(description = "城市编码列表")
    private List<CityCode> cityList;

    @Data
    public static class CityCode {

        @Schema(description = "城市名称")
        private String cityName;

        @Schema(description = "城市地域编码")
        private Integer cityAdcode;

    }

}
