package com.yuelan.hermes.quanyi.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class OrderOfflineItemListRsp {

    @Schema(description = "订单明细ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单明细单号")
    private String itemNo;

    @Schema(description = "供应商")
    private String supplier;

    @Schema(description = "供应商订单号")
    private String supplierOrderNo;

    @Schema(description = "采购价")
    private BigDecimal purchasePrice;

    @Schema(description = "用户手机号")
    private String phone;

    @Schema(description = "用户充值账号")
    private String userAccount;

    @Schema(description = "卡号")
    private String voucherCode;

    @Schema(description = "卡密")
    private String voucherPassword;

    @Schema(description = "券SN,线下核销使用")
    private String snCode;

    @Schema(description = "短链接，二维码短链接或者条码短链接")
    private String qrCodeUrl;

    @Schema(description = "可用门店列表url")
    private String shopUrl;

    @Schema(description = "有效起始日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @Schema(description = "有效结束日期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "短信状态")
    private Integer smsStatus;

    @Schema(description = "短信发送时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sendTime;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "是否发送短信")
    private Boolean isSendSms;
}
