package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.yuelan.core.util.MapstructUtils;
import com.yuelan.hermes.commons.excel.LocalDateTimeConverter;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderExtensionDO;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.*;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> 2025/4/28
 * @since 2025/4/28
 */
@ContentRowHeight(25)
@HeadRowHeight(35)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMapper(target = BenefitItemOrderDO.class)
public class BenefitItemOrderResp extends BenefitsOrderExtensionResp {

    @ColumnWidth(20)
    @ExcelProperty("权益订单id")
    @Schema(description = "权益订单id")
    private Long itemOrderId;

    @ColumnWidth(20)
    @ExcelProperty(value = "订单类型", converter = SendTypeConverter.class)
    @Schema(description = "订单类型：1-原始订单 2-补发订单")
    private Integer orderType;

    @ColumnWidth(20)
    @ExcelProperty("原始权益订单id")
    @Schema(description = "原始权益订单id(只有订单=补发订单才有该值)")
    private Long originalItemOrderId;

    @ColumnWidth(20)
    @ExcelProperty("权益订单号")
    @Schema(description = "向供应商下单订单号（一般用扩展信息里面的order_no填充，部分供应商可能存在特殊订单号格式要求）")
    private String supplierRequestOrder;

    @ColumnWidth(20)
    @ExcelProperty("权益id")
    @Schema(description = "权益id")
    private Long benefitItemId;

    @ColumnWidth(20)
    @ExcelProperty("权益名字")
    @Schema(description = "权益名字")
    private String benefitItemName;

    @ColumnWidth(20)
    @ExcelProperty(value = "发放时机", converter = DispatchTimingConverter.class)
    @Schema(description = "发放时机：1-即时发放  2-用户领取")
    private Integer dispatchTiming;

    @ColumnWidth(20)
    @ExcelProperty(value = "发货类型", converter = DeliveryTypeConverter.class)
    @Schema(description = "发货类型 1-直充 2-兑换码")
    private Integer deliveryType;

    @ColumnWidth(20)
    @ExcelProperty("手机号码")
    @Schema(description = "手机号码")
    private String mobile;

    @ColumnWidth(20)
    @ExcelProperty("供应商类型")
    @Schema(description = "供应商类型")
    private Integer supplierId;

    @ColumnWidth(20)
    @ExcelProperty("供应商名字")
    @Schema(description = "供应商名字")
    private String supplierName;

    @ColumnWidth(20)
    @ExcelProperty("供应商订单号")
    @Schema(description = "供应商响应的订单号")
    private String supplierOrderNo;

    @ColumnWidth(20)
    @ExcelProperty("兑换码id")
    @Schema(description = "兑换码id（兑换码存在我们这边才有）")
    private Long redeemCodeId;

    @ColumnWidth(20)
    @ExcelProperty("兑换码cdk")
    @Schema(description = "兑换码cdk")
    private String redeemCode;

    @ColumnWidth(20)
    @ExcelProperty("兑换码卡密")
    @Schema(description = "兑换码卡密")
    private String redeemCodePwd;

    @ColumnWidth(20)
    @ExcelProperty(value = "兑换码有效期", converter = LocalDateTimeConverter.class)
    @Schema(description = "兑换码有效期")
    private LocalDateTime redeemCodeExpireTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "订单状态", converter = BenefitItemOrderStatusConverter.class)
    @Schema(description = "订单状态：0-下发中 1-成功 2-失败")
    private Integer orderStatus;

    /**
     * 执行状态:0-待请求 1-请求成功（等待回调）2-请求失败 3-下发成功 4-下发失败 5-回调超时
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "执行状态", converter = ProcessStateConverter.class)
    @Schema(description = "执行状态:0-待请求 1-请求成功（等待回调）2-请求失败 3-下发成功 4-下发失败 5-回调超时")
    private Integer processState;

    @ColumnWidth(20)
    @ExcelProperty("执行状态描述")
    @Schema(description = "执行状态描述")
    private String processStateDesc;

    @ExcelIgnore
    @Schema(description = "供应商下单请求")
    private String request;

    @ColumnWidth(20)
    @ExcelProperty(value = "请求时间", converter = LocalDateTimeConverter.class)
    @Schema(description = "请求时间")
    private LocalDateTime requestTime;

    @ExcelIgnore
    @Schema(description = "供应商下单响应")
    private String response;

    @ColumnWidth(20)
    @ExcelProperty(value = "核销状态", converter = RedemptionStatusConverter.class)
    @Schema(description = "核销状态（仅兑换码类型）：0-无核销通知 1-待核销 2-核销成功")
    private Integer redemptionStatus;

    @ColumnWidth(20)
    @ExcelProperty(value = "核销时间", converter = LocalDateTimeConverter.class)
    @Schema(description = "核销时间")
    private LocalDateTime redemptionTime;

    @ExcelIgnore
    @Schema(description = "供应商回调请求内容")
    private String callbackReq;

    @ExcelIgnore
    @Schema(description = "回调时间")
    private LocalDateTime callbackTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "创建时间", converter = LocalDateTimeConverter.class)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @ExcelIgnore
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;


    public static BenefitItemOrderResp buildResp(BenefitItemOrderDO itemOrderDO) {
        if (Objects.isNull(itemOrderDO)) {
            return null;
        }
        BenefitItemOrderResp resp = MapstructUtils.convertNotNull(itemOrderDO, BenefitItemOrderResp.class);
        if (itemOrderDO.getOrderExtension() != null) {
            BenefitOrderExtensionDO orderExtension = itemOrderDO.getOrderExtension();
            resp.setExtensionInfo(orderExtension);
        }
        return resp;
    }
}
