package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.commons.enums.ZopOrderStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.ZopStateEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/5/15 下午7:29
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class EccZopOrderListReq extends PageRequest {

    @Schema(description = "电商卡权益包产品id")
    private Long prodId;

    @Schema(description = "我方订单号")
    private String orderNo;

    @Schema(description = "原始订单号")
    private String originalOrderNo;

    @Schema(description = "渠道订单号")
    private String channelOrderNo;

    @Schema(description = "订单创建类型：1-原始订单 2-补发订单")
    private Integer orderCreateType;

    @Schema(description = "渠道订单号")
    private String idCardName;

    @Schema(description = "联通状态")
    private String unicomStatus;

    @Schema(description = "联通状态列表")
    private List<String> unicomStatusList;
    /**
     * @see com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum
     */
    @Schema(description = "渠道类型:1-内部渠道,2-外部渠道")
    private Integer channelType;

    @Schema(description = "订单来源：1-API订单 2-商城订单（导入）")
    private Integer zopOrderSource;

    @Schema(description = "渠道id")
    private Long channelId;

    @Schema(description = "渠道名字")
    private String channelName;

    @Schema(description = "身份证")
    private String idCard;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "快递联系号码")
    private String contactPhone;

    @Schema(description = "领卡状态，0-未领卡成功，1-已领卡")
    private Integer status;

    @Schema(description = "领卡时间开始")
    private String startTime;

    @Schema(description = "领卡时间结束")
    private String endTime;

    @Schema(description = "风控检测状态：0-默认，1-风控检测通过，2-风控检测拦截")
    private String riskStatus;

    @Schema(description = "预下单状态：0-默认 1-预下单成功 2-预下单失败")
    private String preOrderStatus;


    @Schema(description = "首充金额查询范围，格式为二维数组[[0，10],[10,20]....[90,100],[100,-1]]")
    private List<String> amountLimit;

    /**
     * @param outChannelLimit   null 表示不限制 其他表示in
     * @param innerChannelLimit null 表示不限制 其他表示in
     * @return
     */
    public Wrapper<EccZopOrderDO> buildQueryWrapper(List<Long> outChannelLimit, List<Long> innerChannelLimit, Long startId, boolean isAsc) {
        List<Integer> orderStatusList = null;
        if (status != null) {
            if (status == 0) {
                orderStatusList = new ArrayList<>();
                orderStatusList.add(ZopOrderStatusEnum.DEFAULT.getCode());
                orderStatusList.add(ZopOrderStatusEnum.FAIL.getCode());
            } else if (status == 1) {
                orderStatusList = Collections.singletonList(ZopOrderStatusEnum.SUCCESS.getCode());
            }
        }
        List<Long> allChannelLimit = new ArrayList<>();
        if (outChannelLimit != null) {
            allChannelLimit.addAll(outChannelLimit);
        }
        if (innerChannelLimit != null) {
            allChannelLimit.addAll(innerChannelLimit);
        }
        List<String> zopStateEnumCodeList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(unicomStatusList)) {
            for (String status : unicomStatusList) {
                ZopStateEnum zopStateEnum = ZopStateEnum.getByCode(status);
                String zopStateEnumCode = zopStateEnum == null ? null : zopStateEnum.getCode();
                if (Objects.nonNull(zopStateEnumCode)) {
                    zopStateEnumCodeList.add(zopStateEnumCode);
                }
            }
        }

        return Wrappers.lambdaQuery(EccZopOrderDO.class)
                .gt(startId != null && startId > 0, EccZopOrderDO::getZopOrderId, startId)
                .eq(orderNo != null, EccZopOrderDO::getOrderNo, orderNo)
                .eq(originalOrderNo != null, EccZopOrderDO::getOriginalOrderNo, originalOrderNo)
                .eq(channelOrderNo != null, EccZopOrderDO::getChannelOrderNo, channelOrderNo)
                .eq(prodId != null, EccZopOrderDO::getProdId, prodId)
                .eq(channelId != null, EccZopOrderDO::getChannelId, channelId)
                .eq(channelType != null, EccZopOrderDO::getChannelType, channelType)
                .eq(idCardName != null, EccZopOrderDO::getIdCardName, idCardName)
                .eq(zopOrderSource != null, EccZopOrderDO::getZopOrderSource, zopOrderSource)
                .eq(riskStatus != null, EccZopOrderDO::getRiskCheckStatus, riskStatus)
                .eq(preOrderStatus != null, EccZopOrderDO::getPreOrderStatus, preOrderStatus)
                .in(!zopStateEnumCodeList.isEmpty(), EccZopOrderDO::getZopOrderState, zopStateEnumCodeList)
                .in(!allChannelLimit.isEmpty(), EccZopOrderDO::getChannelId, allChannelLimit)
                .like(channelName != null, EccZopOrderDO::getChannelName, channelName)
                .like(idCard != null, EccZopOrderDO::getIdCardName, idCard)
                .like(phone != null, EccZopOrderDO::getPhone, phone)
                .like(contactPhone != null, EccZopOrderDO::getContactPhone, contactPhone)
                .in(orderStatusList != null, EccZopOrderDO::getOrderSyncStatus, orderStatusList)
                .ge(startTime != null, EccZopOrderDO::getCreateTime, startTime)
                .le(endTime != null, EccZopOrderDO::getCreateTime, endTime)
                .isNotNull(Objects.equals(orderCreateType, 2), EccZopOrderDO::getOriginalOrderNo)
                .isNull(Objects.equals(orderCreateType, 1), EccZopOrderDO::getOriginalOrderNo)
                .and(amountLimit != null && !amountLimit.isEmpty(), (item) -> {
                    for (String scope : amountLimit) {
                        String[] integers = scope.split(",");
                        item.or((query) -> {
                            query.ge(EccZopOrderDO::getFirstRecharge, Integer.parseInt(integers[0]) * 100);
                            if (!Objects.equals(integers[1], "-1")) {
                                query.lt(EccZopOrderDO::getFirstRecharge, Integer.parseInt(integers[1]) * 100);
                            }
                        });
                    }
                })
                .orderBy(true, isAsc, EccZopOrderDO::getZopOrderId);
    }
}
