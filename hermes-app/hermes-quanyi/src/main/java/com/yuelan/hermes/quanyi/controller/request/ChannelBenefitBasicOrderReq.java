package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.RegexMatch;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2025/4/21
 * @since 2025/4/21
 * <p>
 * 权益下单基础参数
 */
@Data
public abstract class ChannelBenefitBasicOrderReq {

    @Schema(title = "apiKey", description = "我方分配分配的参数")
    @NotEmpty(message = "apiKey不能为空")
    @Length(max = 32, message = "apiKey长度不能超过32位")
    private String apiKey;

    @Schema(title = "渠道自定义订单号（32位长度以内）")
    @NotEmpty(message = "渠道自定义订单号不能为空")
    @Length(max = 32, message = "渠道自定义订单号长度不能超过32位")
    private String channelOrderNo;

    @Schema(title = "手机号码", description = "权益发放到的账户")
    @NotEmpty(message = "手机号码不能为空")
    @Length(max = 11, message = "手机号码长度不能超过11位")
    @RegexMatch(regex = "^1[3-9]\\d{9}$", message = "手机号格式错误")
    private String mobile;

    @Schema(title = "时间戳-单位秒")
    @NotEmpty(message = "时间戳不能为空")
    private Long timestamp;

    @Schema(title = "签名", description = "签名")
    @NotEmpty(message = "签名不能为空")
    private String sign;
}
