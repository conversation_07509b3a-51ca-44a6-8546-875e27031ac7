package com.yuelan.hermes.quanyi.controller.request.benefitplatform;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/7/19
 * @description:
 */

@Data
public class BenefitPlatformSmsCodeReq extends BenefitPlatformBaseReq {

    @Schema(description = "手机验证码")
    @NotBlank(message = "手机验证码不能为空")
    private String smsCode;

}
