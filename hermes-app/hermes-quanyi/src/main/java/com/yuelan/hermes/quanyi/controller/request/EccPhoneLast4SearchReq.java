package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/9/10 18:10
 */
@Data
public class EccPhoneLast4SearchReq {

    @NotEmpty(message = "[产品编号]不能为空")
    @Schema(description = "产品编号")
    private String eccProdCode;

    @NotEmpty(message = "[尾号搜索]不能为空")
    @Length(min = 2, max = 4, message = "[尾号搜索]长度只能在2-4位")
    @Schema(description = "尾号0-4位")
    private String phoneLast4;

    @Schema(title = "号码归属地-省份编码", description = "非必传，不传返回随机归属地", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String provinceCode;

    @Schema(title = "号码归属地-城市编码", description = "非必传，不传返回随机归属地", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String cityCode;
}
