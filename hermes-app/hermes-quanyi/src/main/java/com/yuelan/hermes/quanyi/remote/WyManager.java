package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.common.pojo.bo.DirectRechargeResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.WyProperties;
import com.yuelan.hermes.quanyi.common.util.WySignUtil;
import com.yuelan.hermes.quanyi.remote.request.WyCreateOrderReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 网易的全明星街球游戏，和其他游戏不通用
 */
@Slf4j
@Component
public class WyManager {
    @Autowired
    private WyProperties wyProperties;

    /**
     * 创建订单
     */
    public DirectRechargeResultBO createOrder(GamingOrderDO gamingOrderDO, GamingOrderItemDO gamingOrderItemDO) {
        DirectRechargeResultBO resultBO = new DirectRechargeResultBO();
        resultBO.setSuccess(false);
        if (!AppConstants.isReal()) {
            resultBO.setSuccess(true);
            resultBO.setSupplierOrderNo(IdUtil.getSnowflakeNextIdStr());
            return resultBO;
        }
        WyCreateOrderReq req = new WyCreateOrderReq();
        req.setGoodsNo(gamingOrderItemDO.getSupplierGoodsNo());
        req.setOrderNo(gamingOrderItemDO.getItemNo());
        req.setPhone(gamingOrderDO.getPhone());
        req.setTimestamp(System.currentTimeMillis());
        req.setSign("");

        String sign = WySignUtil.signMD5(wyProperties.getSecretKey(), BeanUtil.beanToMap(req), Lists.newArrayList("sign"));
        req.setSign(sign);

        String json = JSON.toJSONString(req);

        String result = HttpUtil.post(wyProperties.getOrderUrl(), json, 8000);
        resultBO.setSourceResp(result);
        if (log.isDebugEnabled()) {
            log.info("网易下单请求成功。body:{},result:{}", json, result);
        }
        // {"code": "0", "msg": "success", "data": "GE24081822881059660943361"}
        JSONObject resultJson = JSON.parseObject(result);
        String code = resultJson.getString("code");
        String orderNo = resultJson.getString("data");
        if (Objects.equals(code, "0")) {
            resultBO.setSuccess(true);
            resultBO.setSupplierOrderNo(orderNo);
        }
        return resultBO;
    }

}
