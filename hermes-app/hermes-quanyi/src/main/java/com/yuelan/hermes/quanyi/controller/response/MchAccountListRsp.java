package com.yuelan.hermes.quanyi.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MchAccountListRsp {

    @Schema(description = "商户ID")
    private Long merchantId;

    @Schema(description = "商户号")
    private String mchId;

    @Schema(description = "商户名称")
    private String mchName;

    @Schema(description = "账户余额")
    private BigDecimal amountBalance;

    @Schema(description = "充值总额")
    private BigDecimal totalIn;

    @Schema(description = "支出总额")
    private BigDecimal totalOut;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


}
