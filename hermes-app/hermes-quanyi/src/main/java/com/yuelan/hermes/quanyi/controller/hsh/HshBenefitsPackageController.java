package com.yuelan.hermes.quanyi.controller.hsh;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.session.SaSession;
import com.yuelan.hermes.quanyi.biz.manager.HshUserBenefitManager;
import com.yuelan.hermes.quanyi.common.pojo.bo.HshUserSessionUser;
import com.yuelan.hermes.quanyi.config.satoken.StpHshUserUtil;
import com.yuelan.hermes.quanyi.controller.request.HshBenefitsPackageCodeReq;
import com.yuelan.hermes.quanyi.controller.request.HshBenefitsPackageRedeemReq;
import com.yuelan.hermes.quanyi.controller.response.UserBenefitItemStatusResp;
import com.yuelan.hermes.quanyi.controller.response.UserBenefitPackageOrderResp;
import com.yuelan.hermes.quanyi.controller.response.UserBenefitPackagesResp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> 2025/4/24
 * @since 2025/4/24
 */
@Validated
@RestController
@Tag(name = "惠生活/权益包接口")
@RequiredArgsConstructor
@RequestMapping("/hshBenefitsPackage")
@SaCheckLogin(type = StpHshUserUtil.TYPE)
public class HshBenefitsPackageController {

    private final HshUserBenefitManager hshUserBenefitManager;


    @PostMapping("/userPackages")
    @Operation(summary = "用户订购的包列表查询")
    public BizResult<List<UserBenefitPackagesResp>> userPackages() {
        HshUserSessionUser sessionUser = StpHshUserUtil.getSession().getModel(SaSession.USER, HshUserSessionUser.class);
        return BizResult.create(hshUserBenefitManager.userPackages(sessionUser));
    }

    @Operation(summary = "权益包订单查询", description = "只返回用户最后一次该权益包的订单")
    @PostMapping("/queryOrder")
    public BizResult<UserBenefitPackageOrderResp> orderQuery(@RequestBody @Validated HshBenefitsPackageCodeReq req) {
        HshUserSessionUser sessionUser = StpHshUserUtil.getSession().getModel(SaSession.USER, HshUserSessionUser.class);
        return BizResult.create(hshUserBenefitManager.queryPackageOrder(sessionUser, req.getPackageCode()));
    }


    @PostMapping("/redeem")
    @Operation(summary = "权益包权益领取")
    public BizResult<UserBenefitItemStatusResp> redeem(@RequestBody HshBenefitsPackageRedeemReq req) {
        HshUserSessionUser sessionUser = StpHshUserUtil.getSession().getModel(SaSession.USER, HshUserSessionUser.class);
        return BizResult.create(hshUserBenefitManager.redeem(sessionUser, req));
    }


}
