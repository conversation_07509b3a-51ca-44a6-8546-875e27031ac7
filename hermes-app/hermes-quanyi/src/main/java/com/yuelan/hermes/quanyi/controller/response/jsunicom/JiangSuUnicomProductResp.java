package com.yuelan.hermes.quanyi.controller.response.jsunicom;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/6/25
 * @description:
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class JiangSuUnicomProductResp {


    @Schema(description = "上送产品推荐时的流水号（下单时回传）")
    private String BUSINESS_ID;

    @Schema(description = "商品信息")
    private List<CommInfoDTO> COMM_INFO;

    @Data
    public static class CommInfoDTO {

        @Schema(description = "商品归属的策略ID")
        private String STRATEGY_ID;

        @Schema(description = "策略名称")
        private String STRATEGY_NAME;

        @Schema(description = "商品排序")
        private Integer COMM_ORDER;

        @Schema(description = "归属省份")
        private String CREATE_PROVINCE_CODE;

        @Schema(description = "市场价")
        private Integer MARKET_PRICE;

        @Schema(description = "商品标识")
        private Long COMM_ID;

        @Schema(description = "商品名称")
        private String COMM_NAME;

        @Schema(description = "商品销售价")
        private Integer COMM_PRICE;

        @Schema(description = "归属商家：联通：1001、京东：2001、苏宁2002、天猫2003")
        private Integer BELONG_PLATFORM_CODE;

    }
}
