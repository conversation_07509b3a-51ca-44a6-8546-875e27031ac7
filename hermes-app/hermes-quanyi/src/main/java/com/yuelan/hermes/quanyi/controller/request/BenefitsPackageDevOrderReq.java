package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.RegexMatch;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2025/4/28
 * @since 2025/4/28
 */
@Data
public class BenefitsPackageDevOrderReq {

    @Schema(description = "权益包编码")
    @NotEmpty(message = "权益包编码不能为空")
    private String packageCode;

    @Schema(description = "手机号码")
    @Length(max = 11, message = "手机号码长度不能超过11位")
    @RegexMatch(regex = "^1[3-9]\\d{9}$", message = "手机号格式错误")
    private String mobile;


}
