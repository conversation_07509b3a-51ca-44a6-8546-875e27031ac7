package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2024/5/3 下午2:20
 */
@Data
public class EccProdSelectOptionsResp {

    private List<SelectOptionResp> options;


    @Data
    public static class SelectOptionResp {
        /**
         * 主键
         */
        @Schema(description = "主键")
        private Long prodId;

        /**
         * 产品名字
         */
        @Schema(description = "产品名字")
        private String prodName;

        public static SelectOptionResp buildResp(EccProductDO productDO) {
            SelectOptionResp resp = new SelectOptionResp();
            resp.setProdId(productDO.getProdId());
            resp.setProdName(productDO.getProdName());
            return resp;
        }
    }
}
