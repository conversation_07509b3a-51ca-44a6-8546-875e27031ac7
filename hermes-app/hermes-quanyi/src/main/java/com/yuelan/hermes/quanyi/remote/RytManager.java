package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.properties.RytProperties;
import com.yuelan.hermes.quanyi.common.util.RytUtil;
import com.yuelan.hermes.quanyi.remote.request.RytOrderDetailReq;
import com.yuelan.hermes.quanyi.remote.request.RytOrderListReq;
import com.yuelan.hermes.quanyi.remote.request.RytSendOrderReq;
import com.yuelan.hermes.quanyi.remote.response.RytRsp;
import com.yuelan.hermes.quanyi.remote.response.RytSendOrderRsp;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class RytManager {

    private static final String HOST = "http://openapi.rytjsj.com";
    private static final String SEND_ORDER = "/business-center/order/sendOrder";
    private static final String ORDER_DETIAL = "/business-center/order/orderDetial";
    private static final String ORDER_LIST = "/business-center/order/orderList";
    private static final String SIGN_KEY = "sign";

    @Autowired
    private RytProperties rytProperties;

    /**
     * 充值
     */
    public RytSendOrderRsp sendOrder(String orderNo, String chargeCode, String goodsCode, String callBackUrl, StringBuilder reqBuild, StringBuilder respBuild) {
        if (!AppConstants.isReal()) {
            RytSendOrderRsp rytSendOrderRsp = new RytSendOrderRsp();
            rytSendOrderRsp.setRecTime(0L);
            rytSendOrderRsp.setBusinessOrderId(orderNo);
            rytSendOrderRsp.setOrderId(RandomUtil.randomLong(10));
            return rytSendOrderRsp;
        }
        RytSendOrderReq req = new RytSendOrderReq();
        req.setBusinessCode(rytProperties.getBusinessCode());
        req.setBusinessOrderId(orderNo);
        req.setCallBackUrl(callBackUrl);
        req.setChargeCode(chargeCode);
        req.setGoodsCode(goodsCode);
        req.setNum(1);
        req.setTradeIp(rytProperties.getTradeIp());
        req.setTimeStamp(System.currentTimeMillis());

        Map<String, Object> stringObjectMap = BeanUtil.beanToMap(req);

        String sign = RytUtil.signMD5(stringObjectMap, rytProperties.getAppKey(), Lists.newArrayList(SIGN_KEY));
        req.setSign(sign);
        String url = HOST + SEND_ORDER;
        String body = JSON.toJSONString(req);
        reqBuild.append(body);
        RytRsp rytRsp = null;
        try {
            String result = HttpUtil.post(url, body, 10000);
            respBuild.append(result);
            log.info("软游通充值下单.body:{},result:{}", body, result);
            rytRsp = JSON.parseObject(result, RytRsp.class);
        } catch (Exception e) {
            log.error("软游通充值下单异常.body:{}", body, e);
            throw BizException.create(BizErrorCodeEnum.RYT_ERROR, "充值下单失败");
        }
        if (Objects.isNull(rytRsp) || !rytRsp.isSuccess()) {
            log.error("软游通充值下单失败.body:{},result:{}", body, rytRsp);
            throw BizException.create(BizErrorCodeEnum.RYT_ERROR, "充值下单失败");
        }
        RytSendOrderRsp resp = rytRsp.getObject(RytSendOrderRsp.class);
        resp.setRespBody(body);
        return resp;
    }

    /**
     * 单笔订单查询
     */
    public Integer orderDetial(String orderNo) {
        RytOrderDetailReq req = new RytOrderDetailReq();
        req.setBusinessCode(rytProperties.getBusinessCode());
        req.setBusinessOrderId(orderNo);
        req.setTimeStamp(System.currentTimeMillis());

        Map<String, Object> paramMap = BeanUtil.beanToMap(req);
        String sign = RytUtil.signMD5(paramMap, rytProperties.getAppKey(), Lists.newArrayList(SIGN_KEY));
        paramMap.put(SIGN_KEY, sign);

        String url = HOST + ORDER_DETIAL;
        RytRsp rytRsp = null;
        try {
            String result = HttpUtil.get(url, paramMap, 10000);
            log.info("软游通查询单笔订单.body:{},result:{}", paramMap, result);
            rytRsp = JSON.parseObject(result, RytRsp.class);

        } catch (Exception e) {
            log.error("软游通查询单笔订单异常.body:{}", paramMap, e);
        }
        if (Objects.isNull(rytRsp) || !rytRsp.isSuccess()) {
            return null;
        }
        return rytRsp.getObject(Integer.class);
    }

    /**
     * 获取订单列表
     * 接口文档不对，验签失败
     */
    @Deprecated
    public String orderList(String createTime, int page, int size) {
        RytOrderListReq req = new RytOrderListReq();
        req.setBusinessCode(rytProperties.getBusinessCode());
        req.setCreateTime(new String[]{"2022-12-22 00:00:00", "2022-12-30 00:00:00"});
        req.setPage(page);
        req.setPageSize(size);
        req.setTimeStamp(System.currentTimeMillis());

        Map<String, Object> mapRes = BeanUtil.beanToMap(req);

//        Map<String, Object> mapRes = Maps.newHashMap();
//        mapRes.put("businessCode", rytProperties.getBusinessCode());
//        mapRes.put("timeStamp", System.currentTimeMillis());

        String sign = RytUtil.signMD5(mapRes, rytProperties.getAppKey(), Lists.newArrayList(SIGN_KEY));
        mapRes.put(SIGN_KEY, sign);
//        mapRes.put("createTime", createTime);
//        mapRes.put("page", page);
//        mapRes.put("pageSzie", size);

        String url = HOST + ORDER_LIST;
//        String body = JSON.toJSONString(req);
        try {
            String result = HttpUtil.get(url, mapRes, 10000);
            log.info("软游通查询订单列表.body:{},result:{}", mapRes, result);
            return result;
        } catch (Exception e) {
            log.error("软游通查询订单列表异常.body:{}", mapRes, e);
        }
        return "";
    }

}
