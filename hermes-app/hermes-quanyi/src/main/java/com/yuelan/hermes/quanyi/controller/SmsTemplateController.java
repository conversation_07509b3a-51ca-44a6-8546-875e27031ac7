package com.yuelan.hermes.quanyi.controller;


import com.yuelan.hermes.quanyi.biz.service.SmsTemplateService;
import com.yuelan.hermes.quanyi.controller.response.SmsTemplateRsp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "短信")
@RequestMapping("/a/sms")
@RestController
public class SmsTemplateController extends AdminBaseController {

    @Autowired
    private SmsTemplateService smsTemplateService;

    @Operation(summary = "查询短信模板")
    @GetMapping("/template")
    public BizResult<List<SmsTemplateRsp>> queryTemplate(@RequestParam Integer bizType) {
        List<SmsTemplateRsp> list = smsTemplateService.queryTemplate(bizType);
        return BizResult.create(list);
    }

}
