package com.yuelan.hermes.quanyi.remote.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <p> 特祯 -余额查询</p>
 *
 * <AUTHOR>
 * @date 2024/2/21
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class VProductBalanceRsp extends TeZhenResult {
    /**
     * 代理账号
     */
    @JSONField(name = "Account")
    private String account;
    /**
     * 余额
     */
    @JSONField(name = "Balance")
    private BigDecimal balance;


}