package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGdOrderDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class EccGdOrderListReq extends PageRequest {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long gdOrderId;

    /**
     * 我方订单号
     */
    @Schema(description = "我方订单号")
    private String orderNo;

    /**
     * 原始订单号（补发类型订单特有）
     */
    @Schema(description = "原始订单号（补发类型订单特有）")
    private String originalOrderNo;
    /**
     * 三方订单号或者运营商订单号
     */
    @Schema(description = "三方订单号或者运营商订单号")
    private String spOrderNo;

    /**
     * 其他运营商失败转入订单
     */
    @Schema(description = "其他运营商失败转入订单")
    private Integer transferOperator;

    /**
     * 我方电商卡权益包id
     */
    @Schema(description = "我方电商卡权益包id")
    private Long prodId;

    /**
     * 推广渠道id
     */
    @Schema(description = "推广渠道id")
    private Long channelId;

    /**
     * 推广渠道类型:1-内部渠道，2-外部渠道
     */
    @Schema(description = "推广渠道类型:1-内部渠道，2-外部渠道")
    private Integer channelType;


    /**
     * 外部渠道订单号
     */
    @Schema(description = "外部渠道订单号")
    private String channelOrderNo;

    /**
     * 广告渠道id
     */
    @Schema(description = "广告渠道id")
    private Long adChannelId;

    /**
     * 身份证名字
     */
    @Schema(description = "身份证名字")
    private String idCardName;

    /**
     * 身份证号码
     */
    @Schema(description = "身份证号码")
    private String idCard;

    /**
     * 运营商或者三方分配的产品编码
     */
    @Schema(description = "运营商或者三方分配的产品编码")
    private String gdGoodsId;

    /**
     * 收货地址省份
     */
    @Schema(description = "收货地址省份")
    private String postProvinceCode;

    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    private String postCityCode;

    /**
     * 区县代码
     */
    @Schema(description = "区县代码")
    private String postDistrictCode;

    /**
     * 收货手机号码
     */
    @Schema(description = "收货手机号码")
    private String contactPhone;

    /**
     * 选择的手机号码
     */
    @Schema(description = "选择的手机号码")
    private String phone;

    /**
     * 归属地省code
     */
    @Schema(description = "归属地省code")
    private String provinceCode;

    /**
     * 归属地城市code
     */
    @Schema(description = "归属地城市code")
    private String cityCode;

    /**
     * 选号类型：0-随机选号，1-用户选号
     */
    @Schema(description = "选号类型：0-随机选号，1-用户选号")
    private Integer selectType;


    /**
     * 订单状态：0-提交失败 1-提交成功 2-下单失败 3-下单成功 4-退单
     */
    @Schema(description = "订单状态：0-提交失败 1-提交成功 2-下单失败 3-下单成功 4-退单")
    private Integer orderStatus;

    /**
     * sim卡状态：0-待激活 1-激活 2-停机 3-销户
     */
    @Schema(description = "sim卡状态：0-待激活 1-激活 2-停机 3-销户")
    private Integer cardStatus;

    /**
     * 首冲状态
     */
    @Schema(description = "首冲状态 0-未首冲 1-已首冲")
    private Integer firstChargeStatus;

    /**
     * 物流状态：0-待发货 1-发货 2-签收 3-拒收
     */
    @Schema(description = "物流状态：0-待发货 1-发货 2-签收 3-拒收")
    private Integer expressStatus;

    /**
     * 物流公司
     */
    @Schema(description = "物流公司")
    private String expressCompany;

    /**
     * 物流订单号
     */
    @Schema(description = "物流订单号")
    private String expressNo;

    /**
     * 激活时间
     */
    @Schema(description = "激活时间区间-开始")
    private LocalDateTime activateTimeStart;

    /**
     * 激活时间
     */
    @Schema(description = "激活时间区间-结束")
    private LocalDateTime activateTimeEnd;

    /**
     * 停机时间
     */
    @Schema(description = "停机时间-开始")
    private LocalDateTime stopTimeStart;

    /**
     * 销户时间
     */
    @Schema(description = "销户时间-结束")
    private LocalDateTime closeTimeEnd;

    /**
     * 首冲金额分
     */
    @Schema(description = "首冲金额分")
    private Integer firstChargeAmount;

    /**
     * 首冲时间
     */
    @Schema(description = "首冲时间区间-开始")
    private LocalDateTime firstChargeTimeStart;

    /**
     * 首冲时间
     */
    @Schema(description = "首冲时间区间-结束")
    private LocalDateTime firstChargeTimeEnd;


    /**
     * 订单时间
     */
    @Schema(description = "订单时间区间-开始")
    private LocalDateTime orderTimeStart;

    /**
     * 订单时间
     */
    @Schema(description = "订单时间区间-结束")
    private LocalDateTime orderTimeEnd;


    @Schema(description = "首充金额查询范围，格式为二维数组[[0，10],[10,20]....[90,100],[100,-1]]")
    private List<String> amountLimit;


    public Wrapper<EccGdOrderDO> buildQueryWrapper(List<Long> outChannelLimit, List<Long> innerChannelLimit) {
        List<Long> allChannelLimit = new ArrayList<>();
        if (outChannelLimit != null) {
            allChannelLimit.addAll(outChannelLimit);
        }
        if (innerChannelLimit != null) {
            allChannelLimit.addAll(innerChannelLimit);
        }
        return Wrappers.lambdaQuery(EccGdOrderDO.class)
                .eq(gdOrderId != null, EccGdOrderDO::getGdOrderId, gdOrderId)
                .eq(orderNo != null, EccGdOrderDO::getOrderNo, orderNo)
                .eq(originalOrderNo != null, EccGdOrderDO::getOriginalOrderNo, originalOrderNo)
                .eq(spOrderNo != null, EccGdOrderDO::getSpOrderNo, spOrderNo)
                .eq(transferOperator != null, EccGdOrderDO::getTransferOperator, transferOperator)
                .eq(prodId != null, EccGdOrderDO::getProdId, prodId)
                .eq(channelId != null, EccGdOrderDO::getChannelId, channelId)
                .eq(channelType != null, EccGdOrderDO::getChannelType, channelType)
                .eq(channelOrderNo != null, EccGdOrderDO::getChannelOrderNo, channelOrderNo)
                .eq(adChannelId != null, EccGdOrderDO::getAdChannelId, adChannelId)
                .eq(idCardName != null, EccGdOrderDO::getIdCardName, idCardName)
                .eq(idCard != null, EccGdOrderDO::getIdCard, idCard)
                .in(!allChannelLimit.isEmpty(), EccGdOrderDO::getChannelId, allChannelLimit)
                .eq(gdGoodsId != null, EccGdOrderDO::getGdGoodsId, gdGoodsId)
                .eq(postProvinceCode != null, EccGdOrderDO::getPostProvinceCode, postProvinceCode)
                .eq(postCityCode != null, EccGdOrderDO::getPostCityCode, postCityCode)
                .eq(postDistrictCode != null, EccGdOrderDO::getPostDistrictCode, postDistrictCode)
                .eq(contactPhone != null, EccGdOrderDO::getContactPhone, contactPhone)
                .eq(phone != null, EccGdOrderDO::getPhone, phone)
                .eq(provinceCode != null, EccGdOrderDO::getProvinceCode, provinceCode)
                .eq(cityCode != null, EccGdOrderDO::getCityCode, cityCode)
                .eq(selectType != null, EccGdOrderDO::getSelectType, selectType)
                .eq(orderStatus != null, EccGdOrderDO::getOrderStatus, orderStatus)
                .eq(cardStatus != null, EccGdOrderDO::getCardStatus, cardStatus)
                .isNotNull(Objects.equals(firstChargeStatus, 1), EccGdOrderDO::getFirstChargeAmount)
                .isNull(Objects.equals(firstChargeStatus, 0), EccGdOrderDO::getFirstChargeAmount)
                .eq(expressStatus != null, EccGdOrderDO::getExpressStatus, expressStatus)
                .like(expressCompany != null, EccGdOrderDO::getExpressCompany, expressCompany)
                .eq(expressNo != null, EccGdOrderDO::getExpressNo, expressNo)
                .ge(activateTimeStart != null, EccGdOrderDO::getActivateTime, activateTimeStart)
                .le(activateTimeEnd != null, EccGdOrderDO::getActivateTime, activateTimeEnd)
                .ge(stopTimeStart != null, EccGdOrderDO::getStopTime, stopTimeStart)
                .le(closeTimeEnd != null, EccGdOrderDO::getCloseTime, closeTimeEnd)
                .eq(firstChargeAmount != null, EccGdOrderDO::getFirstChargeAmount, firstChargeAmount)
                .ge(firstChargeTimeStart != null, EccGdOrderDO::getFirstChargeTime, firstChargeTimeStart)
                .le(firstChargeTimeEnd != null, EccGdOrderDO::getFirstChargeTime, firstChargeTimeEnd)
                .ge(orderTimeStart != null, EccGdOrderDO::getCreateTime, orderTimeStart)
                .le(orderTimeEnd != null, EccGdOrderDO::getCreateTime, orderTimeEnd)
                .and(amountLimit != null && !amountLimit.isEmpty(), (item) -> {
                    for (String scope : amountLimit) {
                        String[] integers = scope.split(",");
                        item.or((query) -> {
                            query.ge(EccGdOrderDO::getFirstChargeAmount, Integer.parseInt(integers[0]) * 100);
                            if (!Objects.equals(integers[1], "-1")) {
                                query.lt(EccGdOrderDO::getFirstChargeAmount, Integer.parseInt(integers[1]) * 100);
                            }
                        });
                    }
                })
                .orderByDesc(EccGdOrderDO::getGdOrderId);
    }
}
