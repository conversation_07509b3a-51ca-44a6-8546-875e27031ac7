package com.yuelan.hermes.quanyi.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class MchAccountStatementRsp {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "业务类型")
    private Integer bizType;

    @Schema(description = "业务单号")
    private String bizNo;

    @Schema(description = "收支类型")
    private Integer tradeType;

    @Schema(description = "交易金额")
    private BigDecimal amount;

    @Schema(description = "账户余额")
    private BigDecimal balance;

    @Schema(description = "交易时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "备注")
    private String remark;
}
