package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class EccZopOrderTotalReq extends PageRequest {

    @Schema(description = "电商卡权益包产品id")
    private Long prodId;

    /**
     * @see com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum
     */
    @Schema(description = "渠道类型:1-内部渠道,2-外部渠道")
    private Integer channelType;

    @Schema(description = "渠道id")
    private Long channelId;

    @Schema(description = "开始时间")
    private LocalDate startTime;

    @Schema(description = "结束时间")
    private LocalDate endTime;

}
