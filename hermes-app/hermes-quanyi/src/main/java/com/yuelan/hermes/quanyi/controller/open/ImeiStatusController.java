package com.yuelan.hermes.quanyi.controller.open;

import com.yuelan.hermes.quanyi.biz.manager.ImeiManager;
import com.yuelan.hermes.quanyi.controller.request.ImeiActivationStatusQueryReq;
import com.yuelan.hermes.quanyi.controller.response.ImeiActivationStatusResp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2025/6/13
 * @since 2025/6/13
 */
@Slf4j
@Validated
@Tag(name = "IMEI状态查询接口")
@RequestMapping("/imei")
@RestController
@RequiredArgsConstructor
public class ImeiStatusController {

    private final ImeiManager imeiManager;

    @Operation(summary = "查询imei激活状态", description = "code = 0表示查询成功，其他状态码请参考文档说明")
    @PostMapping("/activation-status")
    public BizResult<ImeiActivationStatusResp> activationStatus(@RequestBody @Validated ImeiActivationStatusQueryReq req) {
        return BizResult.create(imeiManager.activationStatus(req));
    }
}
