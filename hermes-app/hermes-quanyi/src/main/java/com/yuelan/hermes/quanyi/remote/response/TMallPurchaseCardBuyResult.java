package com.yuelan.hermes.quanyi.remote.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR> 2024/5/13 下午5:46
 */
@Data
public class TMallPurchaseCardBuyResult {

    private Response response;

    @JSO<PERSON>ield(name = "result_code")
    private String resultCode;

    private boolean success;

    @Data
    public static class Response {
        @JSONField(name = "out_pur_req_id")
        private OutPurchaseRequestId outPurchaseRequestId;
        @JSONField(name = "pur_req_id")
        private PurchaseRequestId purchaseRequestId;
        @JSONField(name = "pur_req_line_list")
        private PurchaseRequestLineList purchaseRequestLineList;
    }


    @Data
    public static class OutPurchaseRequestId {
        private String id;
    }

    @Data
    public static class PurchaseRequestId {
        private String id;
    }

    @Data
    public static class PurchaseRequestLineList {
        @JSONField(name = "pur_req_line_vo")
        private PurchaseRequestLineVO[] purchaseRequestLineVO;
    }

    @Data
    public static class PurchaseRequestLineVO {
        @JSONField(name = "pur_req_line_id")
        private PurchaseRequestLineId purchaseRequestLineId;

        @JSONField(name = "out_pur_req_line_id")
        private OutPurchaseRequestLineId outPurchaseRequestLineId;
    }

    @Data
    public static class PurchaseRequestLineId {
        private String id;
    }

    @Data
    public static class OutPurchaseRequestLineId {
        private String id;
    }

}
