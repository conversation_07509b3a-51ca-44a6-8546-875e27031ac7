package com.yuelan.hermes.quanyi.controller.request;

import com.alibaba.fastjson2.JSONArray;
import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/3 09:54
 */
@Data
public class BenefitProdSaveReq {

    /**
     * 主键
     */
    @NotNull(message = "权益包id不能为空", groups = {EditGroup.class})
    @Schema(description = "权益包id")
    private Long prodId;

    /**
     * 产品名字
     */
    @NotEmpty(message = "产品名字不能为空", groups = {AddGroup.class, EditGroup.class})
    @Schema(description = "产品名字")
    private String prodName;

    /**
     * 投放渠道
     */
    @NotEmpty(message = "投放渠道不能为空", groups = {AddGroup.class, EditGroup.class})
    @Schema(description = "投放渠道")
    private String distributionChannel;

    /**
     * 销售价格
     */
    @NotNull(message = "销售价格不能为空", groups = {EditGroup.class})
    @Schema(description = "销售价格")
    private BigDecimal price;

    /**
     * 支付通道id
     */
    @NotNull(message = "支付通道id不能为空", groups = {AddGroup.class})
    @Schema(description = "支付通道id")
    private Integer payChannelId;


    /**
     * 支付通道id
     */
    @NotNull(message = "支付通道包id不能为空", groups = {AddGroup.class})
    @Schema(description = "支付通道包id")
    private Integer payChannelPkgId;

    /**
     * 背景色代码
     */
    @NotEmpty(message = "背景色代码不能为空", groups = {EditGroup.class})
    @Schema(description = "背景色代码")
    private String bgColorCode;

    /**
     * 首页图
     */
    @NotEmpty(message = "首页图不能为空", groups = {EditGroup.class})
    @Schema(description = "首页图")
    private String homepage;

    /**
     * 宣传组图
     */
    @Schema(description = "宣传组图")
    private List<String> imgs;

    /**
     * 协议文档
     */
    @NotEmpty(message = "协议文案不能为空", groups = {EditGroup.class})
    @Schema(description = "协议文档")
    private String agreementContent;

    /**
     * 订阅按钮图
     */
    @NotEmpty(message = "订阅按钮图不能为空", groups = {EditGroup.class})
    @Schema(description = "订阅按钮图")
    private String subButton;

    /**
     * 订阅成功的手机号
     */
    @Schema(description = "订阅成功手机号,用英文逗号或空格分割")
    private String subSuccessTel;


    /**
     * 投放链接
     */
    @Deprecated
    @Schema(description = "投放链接")
    private String distributionUrl;

    /**
     * 兑换链接
     */
    @Deprecated
    @Schema(description = "推广链接")
    private String redeemUrl;


    /**
     * 兑换次数限制（多合一会员可以兑换几个）
     */
    @NotNull(message = "兑换次数限制不能为空", groups = {EditGroup.class})
    @Min(value = 1, message = "兑换次数限制不能小于1", groups = {EditGroup.class})
    @Schema(description = "兑换次数限制")
    private Integer redeemLimit;


    /**
     * 周期类型：0-终生 1-自然月
     */
    @Schema(description = "周期类型：0-终生 1-自然月")
    private Integer cycleType;

    /**
     * 每个周期可以兑换的次数
     */
    @Schema(description = "每个周期可以兑换的次数")
    private Integer cycleRedeemLimit;

    /**
     * 是否显示二确弹窗
     */
    @Schema(description = "是否显示二确弹窗")
    private Integer showPopover;

    /**
     * 二确介绍图
     */
    @Schema(description = "二确介绍图")
    private String popBgPath;

    /**
     * 二确确认键
     */
    @Schema(description = "二确确认键")
    private String popBtnPath;

    /**
     * 二确取消键
     */
    @Schema(description = "二确取消键")
    private String popCancelPath;

    /**
     * 是否显示客服电话
     */
    @Schema(description = "是否显示客服电话")
    private Integer showHelpTel;

    /**
     * 是否显示订购结果页
     */
    @Schema(description = "是否显示订购结果页")
    private Integer showOrderResult;

    public static BenefitProductDO convert(BenefitProdSaveReq req) {
        BenefitProductDO productDO = new BenefitProductDO();
        productDO.setProdId(req.getProdId());
        productDO.setProdName(req.getProdName());
        productDO.setDistributionChannel(req.getDistributionChannel());
        productDO.setPrice(req.getPrice());
        productDO.setPayChannelId(req.getPayChannelId());
        productDO.setPayChannelPkgId(req.getPayChannelPkgId());
        productDO.setBgColorCode(req.getBgColorCode());
        productDO.setHomepage(req.getHomepage());
        if (Objects.nonNull(req.getImgs())) {
            productDO.setImgs(JSONArray.toJSONString(req.getImgs()));
        }
        productDO.setSubButton(req.getSubButton());
        productDO.setSubSuccessTel(req.getSubSuccessTel());
        productDO.setAgreementContent(req.getAgreementContent());
        productDO.setRedeemLimit(req.getRedeemLimit());
        productDO.setCycleType(req.getCycleType());
        productDO.setCycleRedeemLimit(req.getCycleRedeemLimit());
        productDO.setShowPopover(req.getShowPopover());
        if (Objects.equals(req.getShowPopover(), 1)) {
            productDO.setPopBgPath(req.getPopBgPath());
            productDO.setPopBtnPath(req.getPopBtnPath());
            productDO.setPopCancelPath(req.getPopCancelPath());
        }
        productDO.setShowHelpTel(req.getShowHelpTel());
        productDO.setShowOrderResult(req.getShowOrderResult());
        return productDO;
    }


}
