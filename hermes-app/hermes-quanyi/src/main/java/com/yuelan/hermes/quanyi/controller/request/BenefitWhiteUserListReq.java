package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitWhiteUserDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = true)
public class BenefitWhiteUserListReq extends PageRequest {

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;
    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long prodId;

    public Wrapper<BenefitWhiteUserDO> buildQueryWrapper() {
        return Wrappers.<BenefitWhiteUserDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(phone), BenefitWhiteUserDO::getPhone, phone)
                .eq(prodId != null,BenefitWhiteUserDO::getProdId,prodId)
                .orderByDesc(BenefitWhiteUserDO::getId);
    }
}
