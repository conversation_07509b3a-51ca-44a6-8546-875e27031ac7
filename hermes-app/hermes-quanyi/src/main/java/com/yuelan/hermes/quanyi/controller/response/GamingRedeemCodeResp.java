package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingGoodsDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingRedeemCodeDO;
import com.yuelan.hermes.quanyi.common.util.RedeemCodeDesensitizedUtil;
import com.yuelan.result.enums.YesOrNoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR> 2024/7/11 下午7:40
 */
@Data
public class GamingRedeemCodeResp {

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "商品图片")
    private String goodsImg;

    @Schema(description = "供应商")
    private String supplier;

    @Schema(description = "兑换码")
    private String redeemCodeKey;

    @Schema(description = "兑换码对应密码")
    private String redeemCodePwd;

    @Schema(description = "过期日期")
    private LocalDate expireDate;

    @Schema(description = "权益订单子订单号")
    private String gamingItemNo;

    @Schema(description = "发货状态：1-有效/未发货（未使用且未过期），2-已经发货(已经使用)，3-已失效/已过期（未使用且已经过期）")
    private Integer status;

    public static GamingRedeemCodeResp buildResp(GamingGoodsDO goodsDO, GamingRedeemCodeDO redeemCodeDO) {
        if (redeemCodeDO == null) {
            return null;
        }
        GamingRedeemCodeResp resp = new GamingRedeemCodeResp();
        resp.setGoodsId(goodsDO.getGoodsId());
        resp.setGoodsName(goodsDO.getGoodsName());
        resp.setGoodsImg(goodsDO.getGoodsImg());
        SupplierEnum supplierEnum = SupplierEnum.of(redeemCodeDO.getSupplierId());
        if (supplierEnum != null) {
            resp.setSupplier(supplierEnum.getDesc());
        }
        resp.setGamingItemNo(redeemCodeDO.getItemNo());
        resp.setRedeemCodeKey(RedeemCodeDesensitizedUtil.desensitized(redeemCodeDO.getRedeemCodeKey()));
        resp.setRedeemCodePwd(RedeemCodeDesensitizedUtil.desensitized(redeemCodeDO.getRedeemCodePwd()));
        resp.setExpireDate(redeemCodeDO.getExpireDate());
        boolean used = YesOrNoEnum.isYes(redeemCodeDO.getUsed());
        boolean expired = LocalDate.now().isAfter(redeemCodeDO.getExpireDate());
        // 未使用且未过期
        if (!used && !expired) {
            resp.setStatus(1);
        }
        if (used) {
            resp.setStatus(2);
        }
        if (!used && expired) {
            resp.setStatus(3);
        }
        return resp;
    }
}
