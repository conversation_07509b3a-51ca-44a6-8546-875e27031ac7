package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.biz.service.BenefitWhiteUserDOService;
import com.yuelan.hermes.quanyi.controller.request.BenefitBlackUserDeleteReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitWhiteUserListReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitWhiteUserSaveReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitWhiteUserResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@Tag(name = "权益N选1/后台接口/白名单管理")
@RequiredArgsConstructor
@RequestMapping("/a/benefit/white")
public class BenefitWhiteUserController {

    private final BenefitWhiteUserDOService benefitWhiteUserDOService;

    @Operation(summary = "白名单用户列表")
    @PostMapping("/list")
    public BizResult<PageData<BenefitWhiteUserResp>> list(@RequestBody BenefitWhiteUserListReq req) {
        return BizResult.create(benefitWhiteUserDOService.pageList(req));
    }

    @Log(title = "删除白名单用户", type = OperationType.DELETE)
    @Operation(summary = "删除白名单用户")
    @PostMapping("/delete")
    public BizResult<Void> delete(@RequestBody BenefitBlackUserDeleteReq req) {
        benefitWhiteUserDOService.userDelete(req);
        return BizResult.ok();
    }

    @Log(title = "新增白名单用户", type = OperationType.INSERT)
    @Operation(summary = "新增白名单用户")
    @PostMapping("/save")
    public BizResult<Void> userSave(@Validated(AddGroup.class) @RequestBody BenefitWhiteUserSaveReq req) {
        benefitWhiteUserDOService.userSave(req);
        return BizResult.ok();
    }

    @Log(title = "编辑白名单用户", type = OperationType.UPDATE)
    @Operation(summary = "编辑白名单用户")
    @PostMapping("/update")
    public BizResult<Void> userUpdate(@Validated(EditGroup.class) @RequestBody BenefitWhiteUserSaveReq req) {
        benefitWhiteUserDOService.userUpdate(req);
        return BizResult.ok();
    }

}
