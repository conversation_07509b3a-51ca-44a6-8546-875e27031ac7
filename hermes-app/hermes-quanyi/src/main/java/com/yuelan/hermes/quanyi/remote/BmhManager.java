package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.util.ExecutorServiceUtils;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderItemDOService;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderItemDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.BmhProperties;
import com.yuelan.hermes.quanyi.controller.response.BmhCDKNotifyReq;
import com.yuelan.hermes.quanyi.remote.request.BmhCreateCDKReq;
import com.yuelan.hermes.quanyi.remote.response.BmhBaseResp;
import com.yuelan.hermes.quanyi.remote.response.BmhCreatCDKResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

@Slf4j
@Component
public class BmhManager {
    public static final String TAG = "[爆米花]";
    private static final String CREATE_CDK = "/app-api/business/store/sp/cdk/create";
    private static final String SIGN_KEY = "sign";
    public static final int TIME_OUT = 10000;

    @Resource
    private BmhProperties bmhProperties;
    @Resource
    private BenefitOrderItemDOService benefitOrderItemDOService;
    @Resource
    @Lazy
    private NXBenefitManager nxBenefitManager;

    /**
     * 爆米花创建一个cdk
     *
     * @param skuCod 爆米花方商品skuCode
     */
    public BmhCreatCDKResp createCDK(String skuCod, StringBuilder reqBuild, StringBuilder respBuild) {
        if (StringUtils.isBlank(skuCod)) {
            throw BizException.create(BizErrorCodeEnum.ORDER_UNIFIED_ERROR);
        }
        BmhCreateCDKReq req = new BmhCreateCDKReq();
        req.setType(1);
        req.setSkuCode(skuCod);
        req.setNum(1);
        req.setCallbackUrl(bmhProperties.getCallBackUrl());

        SortedMap<String, String> headerMap = buildBaseHeaderMap();
        String reqBody = JSON.toJSONString(req);
        String sign = this.buildSignatureString(null, headerMap, reqBody);
        headerMap.put(SIGN_KEY, sign);
        String url = bmhProperties.getHost() + CREATE_CDK;

        HttpRequest request = HttpRequest.post(url).timeout(TIME_OUT);
        request.body(reqBody);
        request.addHeaders(headerMap);
        reqBuild.append(reqBody);
        log.info(TAG + "获取CDK:{}", request);
        HttpResponse execute = request.execute();
        log.info(TAG + "获取CDK:{}", execute);
        respBuild.append(execute.body());
        if (!execute.isOk()) {
            throw BizException.create(BizErrorCodeEnum.BAO_MI_HUA_ERROR);
        }
        BmhBaseResp bmhBaseResp = JSON.parseObject(execute.body(), BmhBaseResp.class);
        if (0 == bmhBaseResp.getCode()) {
            List<BmhCreatCDKResp> dataList = JSON.parseArray(JSON.toJSONString(bmhBaseResp.getData()), BmhCreatCDKResp.class);
            return dataList.get(0);
        } else {
            throw BizException.create(BizResult.error(String.valueOf(bmhBaseResp.getCode()), bmhBaseResp.getMsg()));
        }
    }

    private SortedMap<String, String> buildBaseHeaderMap() {
        SortedMap<String, String> headerMap = new TreeMap<>();
        headerMap.put("appId", bmhProperties.getAppId());
        headerMap.put("timestamp", System.currentTimeMillis() + "");
        headerMap.put("nonce", IdUtil.fastSimpleUUID());
        return headerMap;
    }

    private String buildSignatureString(SortedMap<String, String> parameterMap, SortedMap<String, String> headerMap, String body) {
        String signStr = MapUtil.join(parameterMap, "&", "=")
                + body
                + MapUtil.join(headerMap, "&", "=")
                + bmhProperties.getAppSecret();
        return DigestUtil.sha256Hex(signStr);
    }

    @Deprecated// 写在这不合适已经迁移到了 BmhCdKeyUseManager
    public Boolean cdkExchangeNotify(BmhCDKNotifyReq params) {
        if ("true".equals(params.getSuccess())) {
            Long cdkId = params.getData().getCdkId();
            LambdaQueryWrapper<BenefitOrderItemDO> queryWrapper = Wrappers.<BenefitOrderItemDO>lambdaQuery()
                    .eq(BenefitOrderItemDO::getSupplierOrderNo, cdkId + "");
            BenefitOrderItemDO orderItemDO = benefitOrderItemDOService.getOne(queryWrapper);
            if (orderItemDO == null) {
                log.info(TAG + "未查找到有效商品订单");
                return false;
            } else {
                orderItemDO.setOrderStatus(OrderStatusEnum.SUCCESS.getCode());
                orderItemDO.setCallBackTime(DateUtil.date().toLocalDateTime());
                orderItemDO.setCallBackContent(JSON.toJSONString(params));
                benefitOrderItemDOService.updateById(orderItemDO);
                // 通知给宁夏
                this.asyncNotifyNX(orderItemDO);
                return true;
            }
        }
        log.info(TAG + "核销回调通知失败");
        return false;
    }

    /**
     * 异步通知宁夏权益核销
     *
     * @param orderItemDO 商品订单
     */
    private void asyncNotifyNX(BenefitOrderItemDO orderItemDO) {
        ExecutorServiceUtils.execute(() -> nxBenefitManager.notifyCDKExchange(orderItemDO));
    }

    public static void main(String[] args) {
        SortedMap<String, String> headerMap = new TreeMap<>();
        headerMap.put("appId", "1881159302699294721");
        headerMap.put("timestamp", System.currentTimeMillis() + "");
        headerMap.put("nonce", IdUtil.fastSimpleUUID());

        Map<String, String> bodyMap = new TreeMap<>();
        bodyMap.put("spCode", "1881159302699294721");

        //String body = JSON.toJSONString(bodyMap);

        String signStr = MapUtil.join(bodyMap, "&", "=")
                //+ body
                + MapUtil.join(headerMap, "&", "=")
                + "b7662d3ac27b4b51a384027791b13d5b";
        String sign = DigestUtil.sha256Hex(signStr);
        headerMap.put(SIGN_KEY, sign);

        HttpRequest request = HttpRequest.post("https://dev.pomoho.com/app-api/business/store/sp/cdk/list-all?spCode=1881159302699294721")
                .timeout(10000)
                //.body(body)
                .addHeaders(headerMap);
        String result = request.execute().body();
        System.out.println(result);

    }
}
