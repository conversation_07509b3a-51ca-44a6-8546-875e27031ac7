package com.yuelan.hermes.quanyi.controller;


import com.yuelan.hermes.quanyi.biz.service.GamingProductService;
import com.yuelan.hermes.quanyi.controller.request.*;
import com.yuelan.hermes.quanyi.controller.response.GamingGoodsRsp;
import com.yuelan.hermes.quanyi.controller.response.GamingProductItemRsp;
import com.yuelan.hermes.quanyi.controller.response.GamingProductRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Tag(name = "电竞卡产品API")
@RequestMapping("/a/gaming/product")
@RestController
public class GamingProductController extends AdminBaseController {

    @Autowired
    private GamingProductService gamingProductService;

    @Operation(summary = "产品列表")
    @PostMapping("/list")
    public BizResult<PageData<GamingProductRsp>> productList(@RequestBody GamingProductListReq req) {
        PageData<GamingProductRsp> result = gamingProductService.productList(req);
        return BizResult.create(result);
    }

    @Operation(summary = "产品详情")
    @GetMapping("/detail")
    public BizResult<GamingProductRsp> productDetail(@RequestParam Long productId) {
        GamingProductRsp result = gamingProductService.productDetail(productId);
        return BizResult.create(result);
    }

    @Operation(summary = "生成产品编号")
    @GetMapping("/code/generate")
    public BizResult<String> productCode() {
        String result = gamingProductService.productCode();
        return BizResult.create(result);
    }

    @Log(title = "新增/编辑产品", type = OperationType.INSERT)
    @Operation(summary = "新增/编辑产品")
    @PostMapping("/save")
    public BizResult<Long> saveProduct(@Valid @RequestBody GamingProductReq req) {
        Long result = gamingProductService.saveProduct(req);
        return BizResult.create(result);
    }

    @Log(title = "产品上下架", type = OperationType.UPDATE)
    @Operation(summary = "产品上下架")
    @PostMapping("/status")
    public BizResult<Boolean> updateStatus(@Valid @RequestBody GamingProductStatusOptReq req) {
        Boolean result = gamingProductService.updateStatus(req);
        return BizResult.create(result);
    }

    @Log(title = "删除产品", type = OperationType.DELETE)
    @Operation(summary = "删除产品")
    @PostMapping("/remove")
    public BizResult<Boolean> delProduct(@Valid @RequestBody GamingProductOptReq req) {
        Boolean result = gamingProductService.delProduct(req.getProductId());
        return BizResult.create(result);
    }

    @Operation(summary = "商品列表")
    @GetMapping("/goods")
    public BizResult<List<GamingProductItemRsp>> goodsList(@RequestParam Long productId) {
        List<GamingProductItemRsp> result = gamingProductService.goodsList(productId);
        return BizResult.create(result);
    }

    @Operation(summary = "测试某个时间订单会下发什么权益")
    @PostMapping("/testProdOrderItem")
    public BizResult<List<GamingGoodsRsp>> testProdOrderItem(@Validated @RequestBody TestProdOrderItemsReq req) {
        return BizResult.create(gamingProductService.testProdOrderItem(req));
    }

    @Log(title = "新增商品", type = OperationType.INSERT)
    @Operation(summary = "新增商品")
    @PostMapping("/goods/add")
    public BizResult<Long> addGoods(@Valid @RequestBody GamingProductItemReq req) {
        Long result = gamingProductService.addGoods(req);
        return BizResult.create(result);
    }

    @Log(title = "更新商品", type = OperationType.UPDATE)
    @Operation(summary = "更新商品")
    @PostMapping("/goods/update")
    public BizResult<Boolean> updateGoods(@Valid @RequestBody GamingProductItemUpdateReq req) {
        return BizResult.create(gamingProductService.updateGoods(req));
    }

    @Log(title = "删除商品", type = OperationType.UPDATE)
    @Operation(summary = "删除商品")
    @PostMapping("/goods/remove")
    public BizResult<Boolean> delGoods(@Valid @RequestBody GamingProductGoodsRemoveReq req) {
        Boolean result = gamingProductService.delGoods(req);
        return BizResult.create(result);
    }
}
