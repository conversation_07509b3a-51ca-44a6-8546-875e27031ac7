package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Lists;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.common.pojo.properties.KnhqProperties;
import com.yuelan.hermes.quanyi.common.util.KnhqUtil;
import com.yuelan.hermes.quanyi.remote.request.KnhqBaseReq;
import com.yuelan.hermes.quanyi.remote.request.KnhqQueryOrderReq;
import com.yuelan.hermes.quanyi.remote.request.KnhqSubmitOrderReq;
import com.yuelan.hermes.quanyi.remote.response.KnhqBaseRsp;
import com.yuelan.hermes.quanyi.remote.response.KnhqOrderQueryRsp;
import com.yuelan.hermes.quanyi.remote.response.KnhqQueryBalanceRsp;
import com.yuelan.hermes.quanyi.remote.response.KnhqSubmitOrderRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

@Slf4j
@Component
public class KnhqManager {
    //充值下单
    private static final String SUBMIT_ORDER = "/api/v1/merchant/submitorder";
    //查询订单
    private static final String QUERY_ORDER = "/api/v1/merchant/queryorder";
    //账户余额查询
    private static final String QUERY_BALANCE = "/api/v1/merchant/querybalance";

    @Autowired
    private KnhqProperties knhqProperties;


    /**
     * 充值下单
     */
    public KnhqSubmitOrderRsp submitOrder(String orderNo, String mobile, BigDecimal amount) {
        if (!AppConstants.isReal()) {
            KnhqSubmitOrderRsp qquRechargeRsp = new KnhqSubmitOrderRsp();
            qquRechargeRsp.setMerchantAppId(knhqProperties.getAppid());
            qquRechargeRsp.setMerchantOrderId(orderNo);
            qquRechargeRsp.setMobile(mobile);
            qquRechargeRsp.setOrderNo(IdUtil.getSnowflakeNextIdStr());
            qquRechargeRsp.setOrderStatus(1);
            qquRechargeRsp.setOrderContent("");
            qquRechargeRsp.setPrice(amount);
            qquRechargeRsp.setDesc("充值中");
            return qquRechargeRsp;
        }
        KnhqSubmitOrderReq orderReq = new KnhqSubmitOrderReq();
        orderReq.setMobile(mobile);
        orderReq.setMerchantOrderId(orderNo);
        orderReq.setFaceAmount(amount.intValue());
        orderReq.setCallbackUrl(knhqProperties.getCallBackUrl());
        return doGet(SUBMIT_ORDER, orderReq, KnhqSubmitOrderRsp.class);
    }

    /**
     * 充值订单查询
     */
    public KnhqOrderQueryRsp queryOrder(String supplierOrderNo) {
        KnhqQueryOrderReq req = new KnhqQueryOrderReq();
        req.setMerchantOrderId(supplierOrderNo);
        return doGet(QUERY_ORDER, req, KnhqOrderQueryRsp.class);
    }

    /**
     * 账户余额查询
     */
    public KnhqQueryBalanceRsp accountBalance() {
        return doGet(QUERY_BALANCE, new KnhqBaseReq(), KnhqQueryBalanceRsp.class);
    }

    /**
     * 发送请求
     */
    private <T> T doGet(String url, KnhqBaseReq req, Class<T> clazz) {
        req.setMerchantAppId(knhqProperties.getAppid());
        req.setTimestamp(System.currentTimeMillis() / 1000 + "");

        Map<String, Object> beanToMap = BeanUtil.beanToMap(req);
        String sign = KnhqUtil.signMD5(knhqProperties.getAppsecret(), beanToMap, Lists.newArrayList("sign"));

        beanToMap.put("sign", sign);

        String uri = knhqProperties.getHost() + url;
        String result = HttpUtil.get(uri, beanToMap, 10000);
        log.info("科能恒启请求成功,url:{},params:{},result:{}", uri, beanToMap, result);
        KnhqBaseRsp<T> knhqBaseRsp = JSON.parseObject(result, new TypeReference<KnhqBaseRsp<T>>(clazz) {
        });
        knhqBaseRsp.checkResult();
        return knhqBaseRsp.getData();
    }
}
