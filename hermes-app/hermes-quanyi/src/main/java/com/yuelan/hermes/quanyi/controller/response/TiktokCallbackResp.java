package com.yuelan.hermes.quanyi.controller.response;

import lombok.Data;

/**
 * <AUTHOR> 2024/9/9 14:35
 */
@Data
public class TiktokCallbackResp {

    private Integer err_no;

    private String err_tips;

    public static TiktokCallbackResp success() {
        TiktokCallbackResp resp = new TiktokCallbackResp();
        resp.setErr_no(0);
        resp.setErr_tips("success");
        return resp;
    }

    public static TiktokCallbackResp fail(String err_tips) {
        TiktokCallbackResp resp = new TiktokCallbackResp();
        resp.setErr_no(1);
        resp.setErr_tips(err_tips);
        return resp;
    }
}
