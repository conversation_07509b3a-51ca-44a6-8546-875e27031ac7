package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.util.PhoneUtil;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * <AUTHOR> 2024/5/5 下午12:59
 * <p>
 * 用户领取卡片请求-内部渠道选号版
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EccInnerChannelGetCardSelectPhoneReq extends EccInnerChannelGetCardReq {

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "手机号码归属地-省份代码")
    private String provinceCode;

    @Schema(description = "手机号码归属地-城市代码")
    private String cityCode;


    @Override
    public void reqCheck() {
        super.reqCheck();
        if (Objects.isNull(phone)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "[手机号码]必传参数缺失");
        }
        if (!PhoneUtil.isMobile(phone)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "[手机号码]格式不合法");
        }
        if (Objects.isNull(provinceCode)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "[省份代码]必传参数缺失");
        }
        if (Objects.isNull(cityCode)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "[城市代码]必传参数缺失");
        }
    }

}
