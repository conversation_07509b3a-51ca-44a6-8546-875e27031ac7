package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.BenefitsPackageOrderService;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageDevOrderReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageOrderRefundReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageOrderReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitsPackageOrderResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2025/4/28
 * @since 2025/4/28
 */
@Validated
@RestController
@Tag(name = "权益组合包/后台接口/权益包订单")
@RequiredArgsConstructor
@RequestMapping("/a/benefits/packageOrder")
public class BenefitPackageOrderController {

    private final BenefitsPackageOrderService benefitsPackageOrderService;

    @Operation(summary = "分页")
    @PostMapping("/page")
    public BizResult<PageData<BenefitsPackageOrderResp>> page(@RequestBody BenefitsPackageOrderReq req) {
        return BizResult.create(benefitsPackageOrderService.pageList(req));
    }

    @Operation(summary = "导出")
    @PostMapping("/export")
    public BizResult<FileExportTaskCreateResp> export(@RequestBody BenefitsPackageOrderReq req) {
        return BizResult.create(benefitsPackageOrderService.export(req));
    }

    @Operation(summary = "测试环境-模拟下单")
    @PostMapping("/devOrder")
    public BizResult<Void> devOrder(@RequestBody @Validated BenefitsPackageDevOrderReq req) {
        benefitsPackageOrderService.devOrder(req);
        return BizResult.ok();
    }


    @Operation(summary = "权益包订单-退款")
    @Log(title = "权益包订单-退款", type = OperationType.UPDATE)
    @PostMapping("/refund")
    public BizResult<Void> refund(@RequestBody BenefitsPackageOrderRefundReq req) {
        benefitsPackageOrderService.refund(req);
        return BizResult.ok();
    }

}
