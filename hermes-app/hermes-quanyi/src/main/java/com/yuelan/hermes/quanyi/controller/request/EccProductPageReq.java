package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductPageDO;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> 2025/5/22
 * @since 2025/5/22
 */
@Data
@AutoMapper(target = EccProductPageDO.class, reverseConvertGenerate = false)
public class EccProductPageReq {

    @Schema(description = "落地页id")
    private Long pageId;


    @Schema(description = "落地页名称")
    private String pageName;

    /**
     * 号卡产品 id
     */
    @Schema(description = "号卡产品 id")
    @NotNull(message = "号卡关联产品 id 不能为空")
    private Long productId;

    /**
     * 首屏
     */
    @Schema(description = "首屏")
    private String indexImg;

    /**
     * 下单按键图
     */
    @Schema(description = "下单按键图")
    private String orderButtonImg;

    /**
     * 详情图
     */
    @Schema(description = "详情图")
    private String pricingDetailImg;

    /**
     * 产品介绍图
     */
    @Schema(description = "产品介绍图")
    private String productDetailImg;

    /**
     * 授权牌 id
     */
    @Schema(description = "授权牌 id")
    private Long licenseId;

    /**
     * 附加扩张图
     */
    @Schema(description = "附加扩张图")
    @AutoMapping(ignore = true)
    @ReverseAutoMapping(ignore = true)
    private List<String> extraImg;

    /**
     * 可折叠图
     */
    @Schema(description = "可折叠图")
    @AutoMapping(ignore = true)
    @ReverseAutoMapping(ignore = true)
    private List<String> collapseImg;

    @Schema(description = "背景颜色")
    private String bgColor;


    @Schema(description = "是否启用")
    private Boolean enabled;
}
