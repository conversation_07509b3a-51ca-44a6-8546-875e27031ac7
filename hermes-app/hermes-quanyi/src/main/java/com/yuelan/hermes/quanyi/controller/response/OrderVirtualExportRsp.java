package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yuelan.hermes.commons.excel.GoodsVirtualTypeConverter;
import com.yuelan.hermes.commons.excel.OrderStatusEnumConverter;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
@ContentRowHeight(15)
@HeadRowHeight(20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class OrderVirtualExportRsp {

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "订单时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderCreateTime;

    @ColumnWidth(28)
    @ExcelProperty(value = "订单编号")
    private String orderNo;

    @ColumnWidth(28)
    @ExcelProperty(value = "第三方订单号")
    private String outOrderNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "用户手机号")
    private String phone;

    @ColumnWidth(20)
    @ExcelProperty(value = "充值账号")
    private String userAccount;

    @ColumnWidth(20)
    @ExcelProperty(value = "商品名称")
    private String goodsName;

    @ColumnWidth(20)
    @ExcelProperty(value = "SKU名称")
    private String skuName;

    @ColumnWidth(15)
    @ExcelProperty(value = "SKU编号")
    private String skuNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "供应商")
    private String supplier;

    @ColumnWidth(20)
    @ExcelProperty(value = "集采客户")
    private String buyer;

    @ColumnWidth(15)
    @ExcelProperty(value = "充值类型", converter = GoodsVirtualTypeConverter.class)
    private Integer goodsType;

    @ColumnWidth(15)
    @ExcelProperty(value = "采购价")
    private BigDecimal purchasePrice;

    @ColumnWidth(15)
    @ExcelProperty(value = "销售价")
    private BigDecimal salePrice;

    @ColumnWidth(10)
    @ExcelProperty(value = "数量")
    private Integer quantity;

    @ColumnWidth(15)
    @ExcelProperty(value = "订单状态", converter = OrderStatusEnumConverter.class)
    private Integer orderStatus;

    @ColumnWidth(20)
    @ExcelProperty(value = "卡号")
    @JsonIgnore
    private String voucherCode;

    @ColumnWidth(20)
    @ExcelProperty(value = "卡密")
    @JsonIgnore
    private String voucherPassword;

    @ColumnWidth(20)
    @ExcelProperty(value = "短链接")
    @JsonIgnore
    private String qrCodeUrl;

    @ColumnWidth(20)
    @ExcelProperty(value = "券SN")
    @JsonIgnore
    private String snCode;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "有效开始时间")
    @JsonIgnore
    private Date startDate;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "有效结束时间")
    @JsonIgnore
    private Date endDate;


}
