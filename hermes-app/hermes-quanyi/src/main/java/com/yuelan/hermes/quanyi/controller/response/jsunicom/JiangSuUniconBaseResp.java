package com.yuelan.hermes.quanyi.controller.response.jsunicom;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/6/25
 * @description:
 */

@Data
public class JiangSuUniconBaseResp<T> {

    @Schema(description = "应答数据")
    private T data;

    @Schema(description = "应答编码 0000-处理成功 4444-接口异常 8888-无升级信息 9999-其他业务错误")
    private String respCode;

    @Schema(defaultValue = "应答描述")
    private String respDesc;


}
