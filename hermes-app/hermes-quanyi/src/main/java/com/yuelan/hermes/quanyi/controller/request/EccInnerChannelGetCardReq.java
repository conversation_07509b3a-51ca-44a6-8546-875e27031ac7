package com.yuelan.hermes.quanyi.controller.request;

import com.alibaba.fastjson2.JSONObject;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

/**
 * <AUTHOR> 2024/5/5 下午12:59
 * <p>
 * 用户领取卡片请求
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EccInnerChannelGetCardReq extends EccBaseGetCardReq {

    @Schema(description = "渠道Id")
    private Long channelId;

    @Schema(description = "广告渠道代码")
    private String adChannelCode;

    @Schema(description = "广告扩展参数")
    private JSONObject adExt;

    @Override
    public void reqCheck() {
        super.reqCheck();
        if (Objects.isNull(channelId)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "缺少渠道参数");
        }
    }

}
