package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.fastjson2.JSONArray;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/2 19:12
 */
@Data
public class BenefitProdResp {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long prodId;

    /**
     * 产品名字
     */
    @Schema(description = "产品名字")
    private String prodName;

    /**
     * 产品编码
     */
    @Schema(description = "产品编码")
    private String prodCode;

    /**
     * 投放渠道
     */
    @Schema(description = "投放渠道")
    private String distributionChannel;

    /**
     * 销售价格
     */
    @Schema(description = "销售价格")
    private BigDecimal price;

    /**
     * 支付通道id
     */
    @Schema(description = "支付通道id")
    private Integer payChannelId;

    /**
     * 支付通道id
     */
    @Schema(description = "支付通道名字")
    private String payChannelName;

    /**
     * 支付通道包id
     */
    @Schema(description = "支付通道包id")
    private Integer payChannelPkgId;

    /**
     * 支付通道包名字
     */
    @Schema(description = "支付通道包名字")
    private String payChannelPkgName;

    /**
     * 背景色代码
     */
    @Schema(description = "背景色代码")
    private String bgColorCode;

    /**
     * 首页图
     */
    @Schema(description = "首页图")
    private String homepage;

    /**
     * 宣传组图
     */
    @Schema(description = "宣传组图")
    private List<String> imgs;

    /**
     * 订阅按钮图
     */
    @Schema(description = "订阅按钮图")
    private String subButton;

    /**
     * 订阅按钮图
     */
    @Schema(description = "订阅成功手机号,用英文逗号或空格分割")
    private String subSuccessTel;
    /**
     * 协议文档
     */
    @Schema(description = "协议文档")
    private String agreementContent;

    /**
     * 投放链接
     */
    @Schema(description = "投放链接")
    private String distributionUrl;

    /**
     * 兑换链接
     */
    @Schema(description = "兑换链接")
    private String redeemUrl;

    /**
     * 兑换次数限制（多合一会员可以兑换几个）
     */
    @Schema(description = "兑换次数限制")
    private Integer redeemLimit;

    /**
     * 周期类型：0-终生 1-自然月
     */
    @Schema(description = "周期类型：0-终生 1-自然月")
    private Integer cycleType;

    /**
     * 每个周期可以兑换的次数
     */
    @Schema(description = "每个周期可以兑换的次数")
    private Integer cycleRedeemLimit;

    /**
     * 上下架状态：0-下架；1-上架
     */
    @Schema(description = "上下架状态：0-下架；1-上架")
    private Integer prodStatus;

    /**
     * 是否显示二确弹窗
     */
    @Schema(description = "是否显示二确弹窗")
    private Integer showPopover;

    /**
     * 二确介绍图
     */
    @Schema(description = "二确介绍图")
    private String popBgPath;

    /**
     * 二确确认键
     */
    @Schema(description = "二确确认键")
    private String popBtnPath;

    /**
     * 二确取消键
     */
    @Schema(description = "二确取消键")
    private String popCancelPath;

    /**
     * 是否显示客服电话
     */
    @Schema(description = "是否显示客服电话")
    private Integer showHelpTel;

    /**
     * 是否显示订购结果页
     */
    @Schema(description = "是否显示订购结果页")
    private Integer showOrderResult;

    public static BenefitProdResp buildResp(BenefitProductDO productDO) {
        if (productDO == null) {
            return null;
        }
        BenefitProdResp resp = new BenefitProdResp();
        resp.setProdId(productDO.getProdId());
        resp.setProdName(productDO.getProdName());
        resp.setProdCode(productDO.getProdCode());
        resp.setDistributionChannel(productDO.getDistributionChannel());
        resp.setPrice(productDO.getPrice());
        resp.setPayChannelId(productDO.getPayChannelId());
        BenefitPayChannelEnum payChannelEnum = null;
        if (productDO.getPayChannelId() != null) {
            payChannelEnum = BenefitPayChannelEnum.of(productDO.getPayChannelId());
            String payChannelName = payChannelEnum == null ? "未知渠道" + productDO.getPayChannelId() : payChannelEnum.getName();
            resp.setPayChannelName(payChannelName);
        }
        resp.setPayChannelPkgId(productDO.getPayChannelPkgId());
        if (productDO.getPayChannelPkgId() != null) {
            if (payChannelEnum != null) {
                String pkgName = payChannelEnum.findPkgName(productDO.getPayChannelPkgId());
                resp.setPayChannelPkgName(pkgName);
            }
        }
        resp.setBgColorCode(productDO.getBgColorCode());
        resp.setHomepage(productDO.getHomepage());
        if (Objects.nonNull(productDO.getImgs())) {
            resp.setImgs(JSONArray.parseArray(productDO.getImgs(), String.class));
        }
        resp.setSubButton(productDO.getSubButton());
        resp.setSubSuccessTel(productDO.getSubSuccessTel());
        resp.setAgreementContent(productDO.getAgreementContent());
        resp.setDistributionUrl(productDO.getDistributionUrl());
        resp.setRedeemUrl(productDO.getRedeemUrl());
        resp.setRedeemLimit(productDO.getRedeemLimit());
        resp.setCycleType(productDO.getCycleType());
        resp.setCycleRedeemLimit(productDO.getCycleRedeemLimit());
        resp.setProdStatus(productDO.getProdStatus());
        resp.setShowPopover(productDO.getShowPopover());
        resp.setPopBgPath(productDO.getPopBgPath());
        resp.setPopBtnPath(productDO.getPopBtnPath());
        resp.setPopCancelPath(productDO.getPopCancelPath());
        resp.setShowHelpTel(productDO.getShowHelpTel());
        resp.setShowOrderResult(productDO.getShowOrderResult());
        return resp;
    }
}
