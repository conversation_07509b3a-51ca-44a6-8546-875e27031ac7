package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.boot.utils.HttpServletRequestUtil;
import com.yuelan.hermes.quanyi.biz.handler.impl.UnicomServerBy52NumberImpl;
import com.yuelan.hermes.quanyi.biz.manager.GdCardByWoAiNumManager;
import com.yuelan.hermes.quanyi.remote.WoAiNumManager;
import com.yuelan.hermes.quanyi.remote.request.WaOrderNotify;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/11/21
 * @since 2024/11/21
 */
@SaIgnore
@Slf4j
@Tag(name = "我爱号码网API")
@RequestMapping("/52haoma")
@RestController
@RequiredArgsConstructor
public class WaNumController {

    private final GdCardByWoAiNumManager gdCardByWoAiNumManager;
    private final WoAiNumManager woAiNumManager;
    private final UnicomServerBy52NumberImpl woAiNumCardService;

    /**
     * 广电和联通都对接我爱号码网 因为存在不同的数据库表中 所以要分开调用
     */
    @PostMapping(value = "/notify")
    public JSONObject orderNotify(@RequestBody String req) {
        WaOrderNotify notifyReq;
        try {
             notifyReq = JSONObject.parseObject(req, WaOrderNotify.class);
        }catch (Exception e){
            log.error("orderNotify error", e);
            return createResponse(500, "系统异常");
        }
        //广电早期的订单 都是入gdOrder数据表
        if (gdCardByWoAiNumManager.isGdOrder(notifyReq.getOrderNo())) {
            return gdCardByWoAiNumManager.orderNotifyHandler(req);
        }else {
            return woAiNumCardService.callBack(notifyReq);
        }
    }

    @Deprecated // 有时候我爱号码网会公司不固定 ip 回调 所以这个只能去掉
    private boolean isWhiteIp() {
        String ip = ServletUtil.getClientIP(HttpServletRequestUtil.getCurrentRequest());
        if (AppConstants.isReal()) {
            return woAiNumManager.isWhiteIp(ip);
        }
        return true;
    }

    /**
     * 创建返回结果
     * 我爱号码网络实际回调是是不需要返回 r
     * response的 他们也没有重试回调机制
     */
    private JSONObject createResponse(int code, String msg) {
        JSONObject result = new JSONObject();
        result.put("code", code);
        result.put("msg", msg);
        return result;
    }


}
