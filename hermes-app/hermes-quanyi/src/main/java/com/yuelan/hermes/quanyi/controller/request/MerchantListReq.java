package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.hermes.commons.constant.CommonConstants;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = true)
public class MerchantListReq extends PageRequest {

    @Schema(description = "商户ID")
    private Long id;

    @Schema(description = "商户名称")
    private String name;

    @Schema(description = "商户号")
    private String mchId;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "状态 0启用，1停用，2注销")
    private Integer status;


    public boolean check() {
        if (Objects.equals(CommonConstants.ALL_OPTION, status)) {
            this.status = null;
        }
        return true;
    }
}
