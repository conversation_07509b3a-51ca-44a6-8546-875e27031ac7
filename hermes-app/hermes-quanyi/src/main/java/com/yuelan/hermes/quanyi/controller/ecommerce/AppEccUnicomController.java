package com.yuelan.hermes.quanyi.controller.ecommerce;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.biz.manager.AdManager;
import com.yuelan.hermes.quanyi.biz.manager.ZopOrderManager;
import com.yuelan.hermes.quanyi.biz.service.EccAreaService;
import com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.EccGetCardResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.PhonePrettyTagBO;
import com.yuelan.hermes.quanyi.common.util.NumCardPromptPolishingUtil;
import com.yuelan.hermes.quanyi.controller.request.*;
import com.yuelan.hermes.quanyi.controller.response.EccPostAreaResp;
import com.yuelan.hermes.quanyi.controller.response.EccProdConfigResp;
import com.yuelan.hermes.quanyi.controller.response.EccTagsResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> 2024/4/26 上午10:48
 */
@Tag(name = "电商卡/app/领取电商卡相关接口")
@Slf4j
@Validated
@RestController
@RequestMapping("/ecc")
@RequiredArgsConstructor
public class AppEccUnicomController {

    private final ZopOrderManager zopOrderManager;
    private final EccAreaService eccAreaService;
    private final AdManager adManager;

    /**
     * 用户通过推广页领取电商卡
     */
    @SaIgnore
    @Operation(summary = "用户通过推广页领取电商卡")
    @PostMapping("/receiveSimCard")
    public BizResult<Void> receiveSimCard(@RequestBody @Validated EccInnerChannelGetCardSelectPhoneReq req) {
        try {
            EccGetCardResultBO eccGetCardResultBO = zopOrderManager.receiveSimCard(req, EccChannelTypeEnum.INNER);
            if (!AppConstants.isReal()) {
                log.info("非正式环境模拟选号完成，手机号码：{}", eccGetCardResultBO.getPhone());
            }
        } catch (BizException e) {
            e.setMsg(NumCardPromptPolishingUtil.getPrompt(e.getMsg()));
            throw e;
        }
        return BizResult.ok();
    }

    @SaIgnore
    @Operation(summary = "号卡详情信息")
    @GetMapping("/prodConfig")
    public BizResult<EccProdConfigResp> prodConfig(@RequestParam("eccProdCode") String eccProdCode) {
        return BizResult.create(zopOrderManager.getProdConfig(eccProdCode));
    }

    /**
     * 获取靓号标签组
     */
    @SaIgnore
    @Operation(summary = "获取靓号标签组", description = "该接口已废弃默认搜索的是29元畅游卡（权益版）的号码池")
    @GetMapping("/getTags")
    @Deprecated
    public BizResult<EccTagsResp> getTagGroup() {
        PhoneTagGroupReq req = new PhoneTagGroupReq();
        req.setEccProdCode("VYzB7T5h");
        return BizResult.create(zopOrderManager.getTagGroup(req));
    }

    /**
     * 获取靓号标签组
     */
    @SaIgnore
    @Operation(summary = "获取靓号标签组V2")
    @PostMapping("/getTags/v2")
    public BizResult<EccTagsResp> getTagGroupV2(@RequestBody @Validated PhoneTagGroupReq req) {
        return BizResult.create(zopOrderManager.getTagGroup(req));
    }

    /**
     * 选号搜索
     */
    @SaIgnore
    @Operation(summary = "选号搜索")
    @PostMapping("/selectPhone")
    public BizResult<PageData<PhonePrettyTagBO>> selectPhone(@RequestBody @Validated SelectPhoneReq req) {
        return BizResult.create(zopOrderManager.selectPrettyPhoneListInCachePool(req));
    }

    /**
     * 尾号搜索
     */
    @SaIgnore
    @Operation(summary = "尾号搜索")
    @PostMapping("/las4Search")
    public BizResult<PageData<PhonePrettyTagBO>> searchPhone(@Validated @RequestBody EccPhoneLast4SearchReq req) {
        return BizResult.create(zopOrderManager.searchPhone(req));
    }

    /**
     * 获取省份信息列表，只有省份这一层
     */
    @Operation(summary = "获取省份信息列表")
    @GetMapping("/listProvince")
    public BizResult<List<EccPostAreaResp>> listProvince() {
        return BizResult.create(eccAreaService.listProvince(SpEnum.UNICOM));
    }

    /**
     * 获取某一个省份下的地区信息列表
     *
     * @param postProvinceCode 省份编码
     */
    @Operation(summary = "获取某一个省的，地区信息列表")
    @Parameters({
            @Parameter(name = "postProvinceCode", description = "省份编码", required = true)
    })
    @GetMapping("/listArea")
    public BizResult<List<EccPostAreaResp>> listArea(@RequestParam("postProvinceCode") String postProvinceCode) {
        return BizResult.create(eccAreaService.listArea(SpEnum.UNICOM, postProvinceCode));
    }

    @Operation(summary = "页面事件回传", description = "页面事件回传")
    @PostMapping("/ad/eventUpload")
    public BizResult<Void> eventUpload(@Validated @RequestBody PageEventUploadReq req) {
        adManager.adEventUploadByEcc(req);
        return BizResult.ok();
    }

}
