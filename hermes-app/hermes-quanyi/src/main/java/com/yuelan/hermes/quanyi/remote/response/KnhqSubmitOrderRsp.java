package com.yuelan.hermes.quanyi.remote.response;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class KnhqSubmitOrderRsp {

    /**
     * 商户应用 ID
     */
    private int merchantAppId;
    /**
     * 商户的订单号
     */
    private String merchantOrderId;
    /**
     * 充值号码
     */
    private String mobile;
    /**
     * 平台订单号
     */
    private String orderNo;
    /**
     * 订单状态
     */
    private int orderStatus;
    /**
     * 订单充值内容
     */
    private String orderContent;
    /**
     * 订单价格
     */
    private BigDecimal price;
    /**
     * 充值描述
     */
    private String desc;

}
