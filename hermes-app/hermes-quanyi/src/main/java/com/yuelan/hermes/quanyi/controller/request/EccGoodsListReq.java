package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR> 2024/5/2 上午10:44
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EccGoodsListReq extends PageRequest {

    /**
     * id
     */
    @Schema(description = "商品id")
    private Long goodsId;

    /**
     * 权益商品名字
     */
    @Schema(description = "权益商品名字")
    private String goodsName;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Integer supplierId;

    /**
     * 供应商名字
     */
    @Schema(description = "供应商名字")
    private String supplierName;

    /**
     * 兑换方式：1-直充无库存，2-cdKey(兑换码)
     */
    @Schema(description = "兑换方式：1-直充无库存，2-兑换码")
    private Integer redeemType;


    /**
     * 商品上下架状态：0-下架，1-上架
     */
    @Schema(description = "商品上下架状态：0-下架，1-上架")
    private Integer goodsStatus;


    public Wrapper<EccGoodsDO> buildQueryWrapper() {
        return Wrappers.lambdaQuery(EccGoodsDO.class)
                .eq(goodsId != null, EccGoodsDO::getGoodsId, goodsId)
                .like(goodsName != null, EccGoodsDO::getGoodsName, goodsName)
                .eq(supplierId != null, EccGoodsDO::getSupplierId, supplierId)
                .like(supplierName != null, EccGoodsDO::getSupplierName, supplierName)
                .eq(redeemType != null, EccGoodsDO::getRedeemType, redeemType)
                .eq(goodsStatus != null, EccGoodsDO::getGoodsStatus, goodsStatus)
                .orderByDesc(EccGoodsDO::getGoodsId);
    }
}
