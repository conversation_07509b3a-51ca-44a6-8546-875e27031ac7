package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PreorderStatusEnum;
import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.biz.service.BenefitAppUserService;
import com.yuelan.hermes.quanyi.biz.service.BenefitGoodsService;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderItemDOService;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderItemDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.NXQYProperties;
import com.yuelan.hermes.quanyi.common.util.NXDataEncryptUtil;
import com.yuelan.hermes.quanyi.common.util.NXSignUtil;
import com.yuelan.hermes.quanyi.controller.request.NXCreateCDKReq;
import com.yuelan.hermes.quanyi.controller.request.NxExchangeReq;
import com.yuelan.hermes.quanyi.controller.response.NxCreateCDKRes;
import com.yuelan.hermes.quanyi.remote.response.BmhCreatCDKResp;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2025/1/18
 * @description NXBenefitManager
 */
@Slf4j
@Component
public class NXBenefitManager {

    public static final String TAG = "[宁夏爆米花]";
    // 核销通知接口
    public static final String NOTIFY_URL = "/equity-openapi/v1/equity/status/notify";

    public static final int TIME_OUT = 10000;

    @Resource
    private BenefitGoodsService benefitGoodsService;
    @Resource
    private BenefitAppUserService benefitAppUserService;
    @Resource
    private BenefitOrderItemDOService benefitOrderItemDOService;
    @Resource
    private BmhManager bmhManager;
    @Resource
    private NXQYProperties nxqyProperties;
    @Resource
    private CacheHandler cacheHandler;

    /**
     * 宁夏获取cdk
     *
     * @param params 参数
     */
    @Transactional(rollbackFor = Exception.class)
    public NxCreateCDKRes createCDK(NXCreateCDKReq params) {
        String appid = params.getAppid();
        if (!appid.equals(nxqyProperties.getAppId())) {
            log.info(TAG + "传入appId错误");
            NxCreateCDKRes res = new NxCreateCDKRes();
            res.setCode("10004");
            res.setSuccess(false);
        }
        String productId = params.getProduct_id();
        String tradeNo = params.getTrade_no();
        String userPhone = NXDataEncryptUtil.decode(params.getUser_phone());
        RLock lock = cacheHandler.getBenefitPlatformOrderLock("nx:" + userPhone);
        if (Objects.isNull(lock)) {
            throw BizException.create(BizErrorCodeEnum.SMS_SEND_BUSY);
        }
        try {
            LambdaQueryWrapper<BenefitGoodsDO> queryWrapper = Wrappers.<BenefitGoodsDO>lambdaQuery()
                    .eq(BenefitGoodsDO::getGoodsId, Long.valueOf(productId))
                    .eq(BenefitGoodsDO::getGoodsStatus, 1);
            BenefitGoodsDO goodsDO = benefitGoodsService.getOne(queryWrapper);
            if (goodsDO == null) {
                throw BizException.create(BizErrorCodeEnum.GOODS_NOT_EXIST);
            }
            String skuCode = goodsDO.getSupplierGoodsNo();
            if (StringUtils.isBlank(skuCode)) {
                throw BizException.create(BizErrorCodeEnum.SKU_NOT_EXIST);
            }
            BmhCreatCDKResp cdk = bmhManager.createCDK(skuCode, new StringBuilder(), new StringBuilder());
            // 入库
            BenefitOrderDO benefitOrderDO = new BenefitOrderDO();
            benefitOrderDO.setPhone(userPhone);
            BenefitOrderItemDO orderItemDO = benefitAppUserService.createNewOrRetry(null, benefitOrderDO, goodsDO);
            orderItemDO.setSupplierGoodsNo(cdk.getCode());
            orderItemDO.setSupplierOrderNo(cdk.getId());

            // 将上游订单号也塞进去
            Map<String, Object> cdkMap = BeanUtil.beanToMap(cdk);
            cdkMap.put("tradeNo", tradeNo);
            orderItemDO.setPreorderContent(JSON.toJSONString(cdkMap));

            orderItemDO.setPreorderStatus(PreorderStatusEnum.SUCCESS.getCode());
            orderItemDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
            orderItemDO.setPayTime(DateUtil.date().toLocalDateTime());
            benefitOrderItemDOService.updateById(orderItemDO);
            // 组装返回数据
            NxCreateCDKRes.DataDTO.CouponListDTO couponListDTO = new NxCreateCDKRes.DataDTO.CouponListDTO();
            couponListDTO.setCoupon_code(cdk.getCode());
            couponListDTO.setCoupon_name(goodsDO.getGoodsName());
            couponListDTO.setCreate_time(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
            couponListDTO.setEnd_time(DateUtil.date(cdk.getExpirationDate()).toString(DatePattern.PURE_DATETIME_PATTERN));

            NxCreateCDKRes.DataDTO dataDTO = new NxCreateCDKRes.DataDTO();
            dataDTO.setAppid(nxqyProperties.getAppId());
            dataDTO.setNonce_str(IdUtil.fastSimpleUUID());
            dataDTO.setTrade_no(tradeNo);
            dataDTO.setOut_trade_no(orderItemDO.getItemNo());
            dataDTO.setCoupon_list(Collections.singletonList(couponListDTO));
            String sign = NXSignUtil.sign(BeanUtil.beanToMap(dataDTO, false, true), nxqyProperties.getSecretKey());
            dataDTO.setSign(sign);

            NxCreateCDKRes res = new NxCreateCDKRes();
            res.setCode("10000");
            res.setSuccess(true);
            res.setData(dataDTO);
            return res;
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * cdk核销通知
     */
    public void notifyCDKExchange(BenefitOrderItemDO orderItemDO) {
        if (orderItemDO == null) {
            return;
        }
        try {
            NxExchangeReq nxExchangeReq = new NxExchangeReq();
            nxExchangeReq.setAppid(nxqyProperties.getAppId());
            nxExchangeReq.setNonce_str(IdUtil.fastSimpleUUID());
            nxExchangeReq.setTimestamp(DateUtil.now());
            String phoneEncodeStr = NXDataEncryptUtil.encode(orderItemDO.getPhone());
            nxExchangeReq.setUser_phone(phoneEncodeStr);
            nxExchangeReq.setCoupon_code(orderItemDO.getSupplierGoodsNo());
            nxExchangeReq.setVerification_id(orderItemDO.getItemNo());
            nxExchangeReq.setStatus(1);
            JSONObject jsonObject = JSON.parseObject(orderItemDO.getPreorderContent(), JSONObject.class);
            nxExchangeReq.setTrade_no(jsonObject.getString("tradeNo"));
            String sign = NXSignUtil.sign(BeanUtil.beanToMap(nxExchangeReq, false, true), nxqyProperties.getSecretKey());
            nxExchangeReq.setSign(sign);

            String url = nxqyProperties.getHost() + NOTIFY_URL;
            HttpRequest request = HttpRequest.post(url).timeout(TIME_OUT);
            request.body(JSON.toJSONString(nxExchangeReq));
            log.info(TAG + "请求前:{}", request);
            HttpResponse response = request.execute();
            log.info(TAG + "请求后:{}", response);
        } catch (Exception e) {
            log.error(TAG + "核销通知宁夏失败", e);
        }
    }
}
