package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2025/4/21
 * @since 2025/4/21
 * <p>
 * 合作方下单权益单商品
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ChannelBenefitItemOrderReq extends ChannelBenefitBasicOrderReq {

    @Schema(title = "权益编号", description = "我方分配权益唯一标识")
    @NotEmpty(message = "权益编号不能为空")
    private String prodCode;

}
