package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.biz.service.EccGoodsDOService;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsDO;
import com.yuelan.hermes.quanyi.controller.request.EccGoodsListReq;
import com.yuelan.hermes.quanyi.controller.request.EccGoodsSaveReq;
import com.yuelan.hermes.quanyi.controller.request.EccGoodsUpdateStatusReq;
import com.yuelan.hermes.quanyi.controller.response.EccGoodsResp;
import com.yuelan.hermes.quanyi.controller.response.SupplierResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR> 2024/5/1 下午9:45
 * 电商卡权益商品
 */
@Validated
@RestController
@Tag(name = "电商卡/后台api/权益商品")
@RequiredArgsConstructor
@RequestMapping("/a/ecc/goods")
public class EccGoodsController {

    private final EccGoodsDOService eccGoodsDOService;

    @Operation(summary = "电商卡权益商品详情")
    @GetMapping("/detail/{goodsId}")
    public BizResult<EccGoodsResp> detail(@PathVariable Long goodsId) {
        EccGoodsDO goodsDO = eccGoodsDOService.getDetailById(goodsId);
        return BizResult.create(EccGoodsResp.buildResp(goodsDO));
    }

    @Operation(summary = "电商卡权益商品列表")
    @PostMapping("/list")
    public BizResult<PageData<EccGoodsResp>> list(@RequestBody EccGoodsListReq req) {
        return BizResult.create(eccGoodsDOService.page(req));
    }

    @Log(title = "新增电商卡权益商品", type = OperationType.INSERT)
    @Operation(summary = "新增权益商品")
    @PostMapping("/save")
    public BizResult<Void> save(@Validated(AddGroup.class) @RequestBody EccGoodsSaveReq req) {
        eccGoodsDOService.save(req);
        return BizResult.ok();
    }

    @Log(title = "编辑电商卡权益商品", type = OperationType.UPDATE)
    @Operation(summary = "编辑电商卡权益商品")
    @PostMapping("/edit")
    public BizResult<Void> edit(@Validated(EditGroup.class) @RequestBody EccGoodsSaveReq req) {
        eccGoodsDOService.updateById(req);
        return BizResult.ok();
    }

    @Log(title = "权益商品上架/下架", type = OperationType.UPDATE)
    @Operation(summary = "电商卡权益商品上架/下架")
    @PostMapping("/updateStatus")
    public BizResult<Void> updateStatus(@RequestBody @Validated EccGoodsUpdateStatusReq req) {
        eccGoodsDOService.updateStatusById(req);
        return BizResult.ok();
    }

    @Operation(summary = "获取电商卡权益商品支持的供应商")
    @GetMapping("/getSuppliers")
    public BizResult<List<SupplierResp>> getSuppliers() {
        return BizResult.create(eccGoodsDOService.getSuppliers());
    }
}
