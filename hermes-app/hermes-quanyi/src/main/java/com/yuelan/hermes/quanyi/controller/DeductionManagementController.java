package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.DeductionCalculationService;
import com.yuelan.hermes.quanyi.biz.service.EccOuterChannelDOService;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.result.entity.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 扣量管理控制器
 * 提供扣量计数器的监控和管理功能
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Slf4j
@RestController
@RequestMapping("/api/deduction")
@RequiredArgsConstructor
@Tag(name = "扣量管理", description = "扣量计数器监控和管理接口")
public class DeductionManagementController {

    private final DeductionCalculationService deductionCalculationService;
    private final EccOuterChannelDOService eccOuterChannelDOService;

    @GetMapping("/counter/{channelId}")
    @Operation(summary = "获取渠道扣量计数器", description = "获取指定渠道的当前扣量计数器值")
    public Result<Long> getChannelCounter(
            @Parameter(description = "渠道ID") @PathVariable Long channelId) {
        try {
            Long counter = deductionCalculationService.getChannelDeductionCounter(channelId);
            return Result.success(counter);
        } catch (Exception e) {
            log.error("获取渠道扣量计数器失败，渠道ID: {}", channelId, e);
            return Result.error("获取计数器失败");
        }
    }

    @PostMapping("/counter/{channelId}/reset")
    @Operation(summary = "重置渠道扣量计数器", description = "将指定渠道的扣量计数器重置为0")
    public Result<Void> resetChannelCounter(
            @Parameter(description = "渠道ID") @PathVariable Long channelId) {
        try {
            deductionCalculationService.resetChannelDeductionCounter(channelId);
            log.info("重置渠道扣量计数器，渠道ID: {}", channelId);
            return Result.success();
        } catch (Exception e) {
            log.error("重置渠道扣量计数器失败，渠道ID: {}", channelId, e);
            return Result.error("重置计数器失败");
        }
    }

    @GetMapping("/stats/{channelId}")
    @Operation(summary = "获取渠道扣量统计", description = "获取指定渠道的详细扣量统计信息")
    public Result<Map<String, Object>> getChannelStats(
            @Parameter(description = "渠道ID") @PathVariable Long channelId) {
        try {
            EccOuterChannelDO channel = eccOuterChannelDOService.getById(channelId);
            if (channel == null) {
                return Result.error("渠道不存在");
            }

            Map<String, Object> stats = deductionCalculationService.getChannelDeductionStats(
                channelId, channel.getDeductionRate());
            
            // 添加渠道基本信息
            stats.put("channelName", channel.getChannelName());
            stats.put("isDisabled", channel.getIsDisabled());
            
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取渠道扣量统计失败，渠道ID: {}", channelId, e);
            return Result.error("获取统计信息失败");
        }
    }

    @GetMapping("/predict/{channelId}")
    @Operation(summary = "预测下一个订单扣量", description = "预测指定渠道的下一个订单是否会被扣量")
    public Result<Map<String, Object>> predictNextDeduction(
            @Parameter(description = "渠道ID") @PathVariable Long channelId) {
        try {
            EccOuterChannelDO channel = eccOuterChannelDOService.getById(channelId);
            if (channel == null) {
                return Result.error("渠道不存在");
            }

            boolean willDeduct = deductionCalculationService.predictNextDeduction(
                channelId, channel.getDeductionRate());
            
            Long currentCount = deductionCalculationService.getChannelDeductionCounter(channelId);
            int nextRemainder = (int) ((currentCount + 1) % 20);
            int nextPosition = nextRemainder == 0 ? 20 : nextRemainder;

            Map<String, Object> result = new HashMap<>();
            result.put("channelId", channelId);
            result.put("channelName", channel.getChannelName());
            result.put("deductionRate", channel.getDeductionRate());
            result.put("currentCount", currentCount);
            result.put("nextPosition", nextPosition);
            result.put("willDeduct", willDeduct);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("预测下一个订单扣量失败，渠道ID: {}", channelId, e);
            return Result.error("预测失败");
        }
    }

    @GetMapping("/positions/{deductionRate}")
    @Operation(summary = "获取扣量位置配置", description = "获取指定扣量比例对应的扣量位置列表")
    public Result<Map<String, Object>> getDeductionPositions(
            @Parameter(description = "扣量比例") @PathVariable Integer deductionRate) {
        try {
            List<Integer> positions = deductionCalculationService.getDeductionPositions(deductionRate);
            
            Map<String, Object> result = new HashMap<>();
            result.put("deductionRate", deductionRate);
            result.put("positions", positions);
            result.put("positionCount", positions.size());
            result.put("expectedCount", deductionRate * 20 / 100);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取扣量位置配置失败，扣量比例: {}", deductionRate, e);
            return Result.error("获取配置失败");
        }
    }

    @GetMapping("/positions/all")
    @Operation(summary = "获取所有扣量位置配置", description = "获取所有支持的扣量比例及其对应的扣量位置配置")
    public Result<Map<Integer, List<Integer>>> getAllDeductionPositions() {
        try {
            Map<Integer, List<Integer>> allPositions = new HashMap<>();
            
            // 获取所有支持的扣量比例
            int[] supportedRates = {5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95};
            
            for (int rate : supportedRates) {
                List<Integer> positions = deductionCalculationService.getDeductionPositions(rate);
                allPositions.put(rate, positions);
            }
            
            return Result.success(allPositions);
        } catch (Exception e) {
            log.error("获取所有扣量位置配置失败", e);
            return Result.error("获取配置失败");
        }
    }

    @PostMapping("/test/{channelId}")
    @Operation(summary = "测试扣量算法", description = "对指定渠道执行扣量算法测试")
    public Result<Map<String, Object>> testDeductionAlgorithm(
            @Parameter(description = "渠道ID") @PathVariable Long channelId,
            @Parameter(description = "测试次数") @RequestParam(defaultValue = "20") Integer testCount) {
        try {
            EccOuterChannelDO channel = eccOuterChannelDOService.getById(channelId);
            if (channel == null) {
                return Result.error("渠道不存在");
            }

            if (testCount <= 0 || testCount > 100) {
                return Result.error("测试次数必须在1-100之间");
            }

            // 记录测试前的计数器值
            Long beforeCount = deductionCalculationService.getChannelDeductionCounter(channelId);
            
            // 执行测试
            int deductedCount = 0;
            for (int i = 0; i < testCount; i++) {
                try {
                    // 使用反射调用私有方法
                    java.lang.reflect.Method method = DeductionCalculationService.class
                        .getDeclaredMethod("performDeduction", EccOuterChannelDO.class);
                    method.setAccessible(true);
                    boolean deducted = (Boolean) method.invoke(deductionCalculationService, channel);
                    
                    if (deducted) {
                        deductedCount++;
                    }
                } catch (Exception e) {
                    log.error("执行扣量测试失败", e);
                }
            }
            
            // 记录测试后的计数器值
            Long afterCount = deductionCalculationService.getChannelDeductionCounter(channelId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("channelId", channelId);
            result.put("channelName", channel.getChannelName());
            result.put("deductionRate", channel.getDeductionRate());
            result.put("testCount", testCount);
            result.put("deductedCount", deductedCount);
            result.put("actualRate", (double) deductedCount / testCount * 100);
            result.put("beforeCount", beforeCount);
            result.put("afterCount", afterCount);
            result.put("counterIncrement", afterCount - beforeCount);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("测试扣量算法失败，渠道ID: {}", channelId, e);
            return Result.error("测试失败");
        }
    }
}
