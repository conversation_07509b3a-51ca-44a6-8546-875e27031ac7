package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.validate.EditGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class BenefitsPackageLimitReq {

    @NotNull(message = "权益包id不能为空", groups = {EditGroup.class})
    @Schema(description = "主键id")
    private Long packageId;

    @NotNull(message = "最大兑换次数不能为空", groups = {EditGroup.class})
    @Schema(description = "最大兑换次数（不包含即时发放的权益数量）")
    private Integer redemptionLimit;

}
