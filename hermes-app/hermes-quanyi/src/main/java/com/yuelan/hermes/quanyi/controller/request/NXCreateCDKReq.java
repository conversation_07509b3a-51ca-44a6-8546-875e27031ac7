package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2025/1/18
 * @description NXCreateCDKReq
 */

@NoArgsConstructor
@Data
public class NXCreateCDKReq {

    /**
     * 属地权益平台分配给业务平台的appid
     */
    @Schema(description = "属地权益平台分配给业务平台的appid")
    private String appid;
    /**
     * 通过签名算法计算出的签名值
     */
    @Schema(description = "通过签名算法计算出的签名值")
    private String sign;
    /**
     * 随机字符串
     */
    @Schema(description = "随机字符串")
    private String nonce_str;
    /**
     * 时间戳，不得和属地权益平台时间差超过3分钟
     */
    @Schema(description = "时间戳，不得和属地权益平台时间差超过3分钟")
    private String timestamp;
    /**
     * 属地权益平台订单号
     */
    @Schema(description = "属地权益平台订单号")
    private String trade_no;
    /**
     * 用户手机号加密串，解密此号码为主账户
     */
    @Schema(description = "用户手机号加密串，解密此号码为主账户")
    @NotBlank(message = "手机号不能为空")
    private String user_phone;
    /**
     * 权益id，由第三方权益平台提供
     */
    @Schema(description = "权益id，由第三方权益平台提供")
    @NotBlank(message = "产品id不能为空")
    private String product_id;
}
