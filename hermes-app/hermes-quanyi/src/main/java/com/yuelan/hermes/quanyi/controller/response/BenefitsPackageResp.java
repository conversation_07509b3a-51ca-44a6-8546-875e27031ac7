package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class BenefitsPackageResp {

    @Schema(description = "主键id")
    private Long packageId;

    @Schema(description = "权益包名字")
    private String packageName;

    @Schema(description = "唯一编码")
    private String packageCode;

    @Schema(description = "兑换有效期天数")
    private Integer redemptionPeriod;

    @Schema(description = "单位元")
    private BigDecimal sellingPrice;

    @Schema(description = "最大兑换次数")
    private Integer redemptionLimit;

    @Schema(description = "直发商品数量")
    private Integer rechargeCount;

    @Schema(description = "选发商品数量")
    private Integer chooseCount;

    @Schema(description = "状态：0-下架，1-上架")
    private Integer status;

    public static BenefitsPackageResp buildResp(BenefitsPackageDO packageDO, Long rechargeCount, Long chooseCount) {
        BenefitsPackageResp resp = new BenefitsPackageResp();
        resp.setPackageId(packageDO.getPackageId());
        resp.setPackageName(packageDO.getPackageName());
        resp.setPackageCode(packageDO.getPackageCode());
        resp.setRedemptionPeriod(packageDO.getRedemptionPeriod());
        resp.setSellingPrice(BigDecimal.valueOf(packageDO.getSellingPrice() / 100));
        resp.setRedemptionLimit(packageDO.getRedemptionLimit());
        resp.setRechargeCount(rechargeCount.intValue());
        resp.setChooseCount(chooseCount.intValue());
        resp.setStatus(packageDO.getStatus());
        return resp;
    }


    public static BenefitsPackageResp buildResp(BenefitsPackageDO packageDO) {
        BenefitsPackageResp resp = new BenefitsPackageResp();
        resp.setPackageId(packageDO.getPackageId());
        resp.setPackageName(packageDO.getPackageName());
        resp.setPackageCode(packageDO.getPackageCode());
        resp.setRedemptionPeriod(packageDO.getRedemptionPeriod());
        resp.setSellingPrice(BigDecimal.valueOf(packageDO.getSellingPrice() / 100));
        resp.setRedemptionLimit(packageDO.getRedemptionLimit());
        resp.setStatus(packageDO.getStatus());
        return resp;
    }

}
