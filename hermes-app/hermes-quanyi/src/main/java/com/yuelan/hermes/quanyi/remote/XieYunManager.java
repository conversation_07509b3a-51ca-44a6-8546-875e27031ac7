package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.error.EccErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.properties.XieYunProperties;
import com.yuelan.hermes.quanyi.remote.request.XieYunGdSearchPhoneReq;
import com.yuelan.hermes.quanyi.remote.request.XieYunSubmitOrderReq;
import com.yuelan.hermes.quanyi.remote.request.XieYunSubmitPreCheckReq;
import com.yuelan.hermes.quanyi.remote.response.XieYunExpressDetail;
import com.yuelan.hermes.quanyi.remote.response.XieYunOrderDetailRsp;
import com.yuelan.hermes.quanyi.remote.response.XieYunSubmitResult;
import com.yuelan.plugins.redisson.util.RedisUtils;
import com.yuelan.result.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/12/4
 * @since 2024/12/4
 * <p>
 * 携云广电卡
 */
@Slf4j
@Service
@AllArgsConstructor
public class XieYunManager {
    private static final String LOG_TAG = "携云广电卡";
    private static final String SUCCESS_CODE = "000000";
    // 违规收货地址关键字
    private static final List<String> ILLEGAL_ADDRESS_KEYWORDS = Arrays.asList(
            "旅馆", "网吧", "酒店", "便利店", "车站", "地铁", "公交", "村委会", "值班室", "超市", "便利店", "4S店",
            "美团买菜", "酒店", "申通", "圆通", "中通", "KTV", "保安亭", "菜鸟驿站", "餐馆", "代收点", "丰巢", "公寓",
            "会所", "火车站", "加油站", "快递柜", "快递", "驿站", "门口", "门卫", "中学", "学校", "小学", "幼儿园", "学院",
            "汽车站", "前台", "网咖", "宾馆", "民宿", "派出所", "公安局", "生活超市", "停车场", "小卖部", "韵达", "站点"
    );

    private final XieYunProperties xieYunProperties;
    private final CacheHandler cacheHandler;

    /**
     * 获取 token
     * <p>
     * 根据请求参数生成 token，token 有效时间 30 分钟。
     */
    private String getToken() {
        String url = xieYunProperties.getDomain() + "/common/getToken";
        JSONObject body = new JSONObject();
        body.put("channelCode", xieYunProperties.getChannelCode());
        body.put("channelKey", xieYunProperties.getChannelKey());
        if (!AppConstants.isReal()) {
            throw BizException.create(EccErrorCodeEnum.XIE_YUN_GET_CARD_TOKEN_FAIL, "测试环境不允许请求token");
        }
        HttpRequest req = HttpUtil.createPost(url)
                .form(body);
        log.info("{}获取token请求：{}", LOG_TAG, req);
        try {
            HttpResponse response = req.execute();
            log.info("{}获取token响应：{}", LOG_TAG, response);
            if (response.isOk()) {
                JSONObject resp = JSONObject.parseObject(response.body());
                String code = resp.getString("msgCode");
                String message = resp.getString("message");
                if (SUCCESS_CODE.equals(code)) {
                    return resp.getJSONObject("result").getString("token");
                } else {
                    log.error("{}获取token失败，msgCode：{} message：{}", LOG_TAG, code, message);
                }
            } else {
                log.error("{}获取token失败，非200状态码，响应码：{}", LOG_TAG, response.getStatus());
            }
        } catch (Exception e) {
            log.error("{}获取token异常", LOG_TAG, e);
        }
        return null;
    }

    /**
     * 获取 token 缓存 如果缓存不存在则重新获取
     */
    public String getTokenCache() {
        String cacheKey = RedisKeys.XY_TOKEN;
        Object cacheVal = RedisUtils.getCacheObject(cacheKey);
        if (cacheVal != null) {
            return cacheVal.toString();
        }
        RLock lock = cacheHandler.getXieYunGetTokenLock();
        try {
            cacheVal = RedisUtils.getCacheObject(cacheKey);
            if (cacheVal != null) {
                return cacheVal.toString();
            }
            String token = getToken();
            if (token != null) {
                RedisUtils.setCacheObject(cacheKey, token, Duration.ofMinutes(25));
            }
            return token;
        } finally {
            if (Objects.nonNull(lock) && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 获取appCode
     * <p>
     * appCode= channelKey经 rsa 加密 后加密串
     */
    public String appCode() {
        String channelKey = xieYunProperties.getChannelKey();
        String channelPubKey = xieYunProperties.getChannelPubKey();
        RSA rsa = new RSA(null, channelPubKey);
        byte[] encrypt = rsa.encrypt(channelKey.getBytes(), KeyType.PublicKey);
        return Base64.getEncoder().encodeToString(encrypt);
    }

    public Map<String, String> getCommonHeader() {
        Map<String, String> header = new HashMap<>();
        header.put("X-Access-Token", getTokenCache());
        header.put("appCode", appCode());
        header.put("pubkey", xieYunProperties.getChannelPubKey());
        return header;
    }

    public Map<String, Object> getEncryptForm(String reqJson) {
        String s = AESUtil.enAES(reqJson, xieYunProperties.getChannelKey());
        Map<String, Object> map = new HashMap<>();
        map.put("requestParams", s);
        return map;
    }

    public List<String> selectPhonePool(XieYunGdSearchPhoneReq req) {
        String url = xieYunProperties.getDomain() + "/common/qryNumberNew";
        JSONObject body = JSONObject.from(req);

        HttpRequest request = HttpUtil.createPost(url)
                .form(getEncryptForm(body.toJSONString()));
        request.addHeaders(getCommonHeader());
        log.info("{}查询号码池请求原始参数：{}", LOG_TAG, body);
        log.info("{}查询号码池请求：{}", LOG_TAG, request);
        HttpResponse response = request.execute();
        log.info("{}查询号码池响应：{}", LOG_TAG, response);
        if (response.isOk()) {
            JSONObject resp = JSONObject.parseObject(response.body());
            String code = resp.getString("msgCode");
            if (SUCCESS_CODE.equals(code)) {
                return Optional.ofNullable(resp.getJSONObject("result"))
                        .map(result -> result.getJSONObject("result"))
                        .map(result1 -> result1.getJSONArray("numList"))
                        .map(numList -> numList.stream()
                                .map(num -> ((JSONObject) num).getString("accessNum"))
                                .collect(Collectors.toList()))
                        .orElse(Collections.emptyList());
            }
            log.error("{}查询号码池失败，msgCode：{}", LOG_TAG, code);
            throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_PHONE_POOL_FAIL, "查询号码池失败");
        }
        log.error("{}查询号码池失败，非200状态码，响应码：{}", LOG_TAG, response.getStatus());
        throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_PHONE_POOL_FAIL, "接口请求失败");
    }

    /**
     * 选占号码
     *
     * @param regionId 市级编码
     */
    public void occupyPhone(String regionId, String phoneNum, String idCard) {
        String url = xieYunProperties.getDomain() + "/common/numSelOccupy";
        JSONObject body = new JSONObject();
        body.put("regionId", regionId);
        body.put("accessNum", phoneNum);
        body.put("custCardNo", idCard);
        HttpRequest request = HttpUtil.createPost(url)
                .form(getEncryptForm(body.toJSONString()));
        request.addHeaders(getCommonHeader());
        log.info("{}选占号码请求原始参数：{}", LOG_TAG, body);
        log.info("{}选占号码请求：{}", LOG_TAG, request);
        HttpResponse response = request.execute();
        log.info("{}选占号码响应：{}", LOG_TAG, response);
        if (response.isOk()) {
            JSONObject resp = JSONObject.parseObject(response.body());
            String code = resp.getString("msgCode");
            String desc = resp.getString("desc");
            if (SUCCESS_CODE.equals(code)) {
                String respCode = resp.getJSONObject("result").getString("respCode");
                if (SUCCESS_CODE.equals(respCode)) {
                    //    {"msgCode":"000000","success":true,"message":"请求成功","result":{"data":"false","respCode":"000000","desc":"RES_NUMBER_INTF_00068`号码选占接口：传入号码【19275704875】已被当前证件号码选占！`"}}
                    String resultDesc = resp.getJSONObject("result").getString("desc");
                    Optional.ofNullable(resp.getJSONObject("result"))
                            .map(result -> result.getString("data"))
                            .filter(data -> "true".equals(data) || StrUtil.contains(resultDesc, "已被当前证件号码选占"))
                            .orElseThrow(() -> {
                                log.error("{}选占号码失败={}", LOG_TAG, resultDesc);
                                return BizException.create(EccErrorCodeEnum.XIE_YUN_OCCUPY_PHONE_FAIL, resultDesc);
                            });
                    return;
                }
            }
            log.error("{}选占号码失败，msgCode：{}", LOG_TAG, code);
            throw BizException.create(EccErrorCodeEnum.XIE_YUN_OCCUPY_PHONE_FAIL, desc);
        }
        log.error("{}选占号码失败，非200状态码，响应码：{}", LOG_TAG, response.getStatus());
        throw BizException.create(EccErrorCodeEnum.XIE_YUN_OCCUPY_PHONE_FAIL, "接口请求失败");
    }

    /**
     * 开户下单预校验接口
     * <p>
     * 通过该接口进行开户下单预校验，提前检查是否能下单成功，包含一证五号校
     * 验接口
     * 接口校验规则：
     * 1","一证 5 号校验
     * 2","黑名单校验
     * 3","未满 16 周岁校验。
     */
    public void submitPreCheck(XieYunSubmitPreCheckReq req) {
        String url = xieYunProperties.getDomain() + "/order/submitPreCheck";
        String reqBody = JSONObject.toJSONString(req);
        HttpRequest request = HttpUtil.createPost(url)
                .form(getEncryptForm(reqBody));
        request.addHeaders(getCommonHeader());
        log.info("{}开户下单预校验请求原始参数：{}", LOG_TAG, req);
        log.info("{}开户下单预校验请求：{}", LOG_TAG, request);
        HttpResponse response = request.execute();
        log.info("{}开户下单预校验响应：{}", LOG_TAG, response);
        if (response.isOk()) {
            JSONObject resp = JSONObject.parseObject(response.body());
            String code = resp.getString("msgCode");
            String message = resp.getString("message");
            if (SUCCESS_CODE.equals(code)) {
                JSONObject result = resp.getJSONObject("result");
                String respCode = result.getString("respCode");
                String respDesc = result.getString("国网接口返回respDesc");
                String result1 = result.getString("result");
                if (SUCCESS_CODE.equals(respCode) && "success".equals(result1)) {
                    log.info("{}开户下单预校验成功", LOG_TAG);
                    return;
                }
                log.error("{}开户下单预校验失败，result1:{} respCode：{} respDesc:{}", LOG_TAG, result1, respCode, respDesc);
                throw BizException.create(EccErrorCodeEnum.XIE_YUN_PRE_CHECK_FAIL, respDesc);
            }
            log.error("{}开户下单预校验失败，msgCode：{}", LOG_TAG, code);
            throw BizException.create(EccErrorCodeEnum.XIE_YUN_PRE_CHECK_FAIL, message);
        }
        log.error("{}开户下单预校验失败，非200状态码，响应码：{}", LOG_TAG, response.getStatus());
        throw BizException.create(EccErrorCodeEnum.XIE_YUN_PRE_CHECK_FAIL, "接口请求失败");
    }

    /**
     * 收货地址预校验
     */
    public boolean addressPreCheck(String receiveAddress) {
        for (String illegalAddressKeyword : ILLEGAL_ADDRESS_KEYWORDS) {
            if (StrUtil.contains(receiveAddress, illegalAddressKeyword)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 订单提交接口（开户，自主选号）
     */
    public XieYunSubmitResult submitOrder(XieYunSubmitOrderReq req) {
        String url = xieYunProperties.getDomain() + "/order/openAccount";
        String reqBody = JSONObject.toJSONString(req);
        HttpRequest request = HttpUtil.createPost(url)
                .form(getEncryptForm(reqBody));
        request.addHeaders(getCommonHeader());
        log.info("{}订单提交请求原始参数：{}", LOG_TAG, reqBody);
        log.info("{}订单提交请求：{}", LOG_TAG, request);
        HttpResponse response = request.execute();
        log.info("{}订单提交响应：{}", LOG_TAG, response);
        if (response.isOk()) {
            JSONObject resp = JSONObject.parseObject(response.body());
            String code = resp.getString("msgCode");
            String message = resp.getString("message");
            JSONObject result = resp.getJSONObject("result");
            if (SUCCESS_CODE.equals(code) && result != null && result.containsKey("orderId")) {
                return result.toJavaObject(XieYunSubmitResult.class);
            }
            log.error("{}订单提交失败，msgCode：{}", LOG_TAG, code);
            throw BizException.create(EccErrorCodeEnum.XIE_YUN_SUBMIT_ORDER_FAIL, message);
        }
        log.error("{}订单提交失败，非200状态码，响应码：{}", LOG_TAG, response.getStatus());
        throw BizException.create(EccErrorCodeEnum.XIE_YUN_OCCUPY_PHONE_FAIL, "接口请求失败");
    }

    /**
     * 订单状态同步获取接口
     *
     * @param orderId 订单号 提交订单返回的
     * @param phone   手机号
     * @param userId  用户id
     */
    public XieYunOrderDetailRsp queryOrderDetail(String orderId, String phone, String userId) {
        String url = xieYunProperties.getDomain() + "/order/queryOrder";
        JSONObject body = new JSONObject();
        body.put("orderId", orderId);
        body.put("phone", phone);
        body.put("userId", userId);

        HttpRequest request = HttpUtil.createPost(url)
                .form(getEncryptForm(body.toJSONString()));
        request.addHeaders(getCommonHeader());
        log.info("{}订单状态同步获取请求原始参数：{}", LOG_TAG, body);
        log.info("{}订单状态同步获取请求：{}", LOG_TAG, request);

        HttpResponse response = request.execute();
        log.info("{}订单状态同步获取响应：{}", LOG_TAG, response);

        if (response.isOk()) {
            JSONObject resp = JSONObject.parseObject(response.body());
            String code = resp.getString("msgCode");
            String message = resp.getString("message");
            if (SUCCESS_CODE.equals(code)) {
                JSONObject result = resp.getJSONObject("result");
                return result.toJavaObject(XieYunOrderDetailRsp.class);
            }
            log.error("{}订单状态同步获取失败，msgCode：{}", LOG_TAG, code);
            throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_ORDER_FAIL, message);
        }
        log.error("{}订单状态同步获取失败，非200状态码，响应码：{}", LOG_TAG, response.getStatus());
        throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_ORDER_FAIL, "接口请求失败");
    }

    /**
     * 物流详情查询接口
     */
    public XieYunExpressDetail queryLogisticsDetail(String orderId) {
        String url = xieYunProperties.getDomain() + "/common/qryorderLogistics";
        JSONObject body = new JSONObject();
        body.put("orderId", orderId);

        HttpRequest request = HttpUtil.createPost(url)
                .form(getEncryptForm(body.toJSONString()));
        request.addHeaders(getCommonHeader());
        log.info("{}物流详情查询请求原始参数：{}", LOG_TAG, body);
        log.info("{}物流详情查询请求：{}", LOG_TAG, request);

        HttpResponse response = request.execute();
        log.info("{}物流详情查询响应：{}", LOG_TAG, response);

        if (response.isOk()) {
            JSONObject resp = JSONObject.parseObject(response.body());
            String code = resp.getString("msgCode");
            String message = resp.getString("message");
            if (SUCCESS_CODE.equals(code)) {
                JSONObject result = resp.getJSONObject("result");
                return result.toJavaObject(XieYunExpressDetail.class);
            }
            log.error("{}物流详情查询失败，msgCode：{}", LOG_TAG, code);
            throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_ORDER_FAIL, message);
        }
        log.error("{}物流详情查询失败，非200状态码，响应码：{}", LOG_TAG, response.getStatus());
        throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_ORDER_FAIL, "接口请求失败");
    }

    /**
     * 首冲状态查询
     */
    public Integer queryFirstRechargeStatus(String phoneNumber) {
        String url = xieYunProperties.getDomain() + "/common/queryFirstRecharge";
        JSONObject body = new JSONObject();
        body.put("phoneNumber", phoneNumber);

        HttpRequest request = HttpUtil.createPost(url)
                .form(getEncryptForm(body.toJSONString()));
        request.addHeaders(getCommonHeader());
        log.info("{}首冲状态查询请求原始参数：{}", LOG_TAG, body);
        log.info("{}首冲状态查询请求：{}", LOG_TAG, request);

        HttpResponse response = request.execute();
        log.info("{}首冲状态查询响应：{}", LOG_TAG, response);

        if (response.isOk()) {
            JSONObject resp = JSONObject.parseObject(response.body());
            String code = resp.getString("msgCode");
            String message = resp.getString("message");
            if (SUCCESS_CODE.equals(code)) {
                String result = resp.getString("result");
                // 接口返回结果：
                // 未首充：尚未参加首充
                // 未开户：未查询到开户信息
                // 无法查询其他渠道首充信息：开户号码 渠道商非当前渠道商
                // 首充中：已提交首充订单，但尚未支付
                // 首充 50 元：已参加50 元首充
                // 首充 100 元：已参加 100 元首充
                if (StrUtil.containsIgnoreCase(result, "100")) {
                    return 100;
                } else if (StrUtil.containsIgnoreCase(result, "50")) {
                    return 50;
                }
                return null;
            }
            log.error("{}首冲状态查询失败，msgCode：{}", LOG_TAG, code);
            throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_ORDER_FAIL, message);
        }
        log.error("{}首冲状态查询失败，非200状态码，响应码：{}", LOG_TAG, response.getStatus());
        throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_ORDER_FAIL, "接口请求失败");
    }

    /**
     * 套餐权限验证接口
     *
     * @param offerId 套餐ID 套餐id
     */
    public boolean checkGoods(String offerId) {
        String url = xieYunProperties.getDomain() + "/common/checkGoods";
        JSONObject body = new JSONObject();
        body.put("offerId", offerId);

        HttpRequest request = HttpUtil.createPost(url)
                .form(getEncryptForm(body.toJSONString()));
        request.addHeaders(getCommonHeader());
        log.info("{}套餐权限验证请求原始参数：{}", LOG_TAG, body);
        log.info("{}套餐权限验证请求：{}", LOG_TAG, request);

        HttpResponse response = request.execute();
        log.info("{}套餐权限验证响应：{}", LOG_TAG, response);

        if (response.isOk()) {
            JSONObject resp = JSONObject.parseObject(response.body());
            String code = resp.getString("msgCode");
            String message = resp.getString("message");
            if (SUCCESS_CODE.equals(code)) {
                Boolean result = resp.getBoolean("result");
                return Boolean.TRUE.equals(result);
            }
            log.error("{}套餐权限验证失败，msgCode：{}", LOG_TAG, code);
            throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_ORDER_FAIL, message);
        }
        log.error("{}套餐权限验证失败，非200状态码，响应码：{}", LOG_TAG, response.getStatus());
        throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_ORDER_FAIL, "接口请求失败");

    }

    /**
     * 查询省市区信息  查询区域下一级区域信息
     *
     * @param areaCode 区域编码(可以是省，市，区)
     */
    public void getAreaList(String areaCode) {
        String url = xieYunProperties.getDomain() + "/common/getAreaList";
        JSONObject body = new JSONObject();
        if (StrUtil.isNotBlank(areaCode)) {
            body.put("areaCode", areaCode);
        }
        log.info("{}查询省市区信息请求原始参数：{}", LOG_TAG, body);
        HttpRequest request = HttpUtil.createPost(url)
                .form(getEncryptForm(body.toJSONString()));
        request.addHeaders(getCommonHeader());
        log.info("{}查询省市区信息请求：{}", LOG_TAG, request);

        HttpResponse response = request.execute();
        log.info("{}查询省市区信息响应：{}", LOG_TAG, response);

        if (response.isOk()) {
            JSONObject resp = JSONObject.parseObject(response.body());
            String code = resp.getString("msgCode");
            String message = resp.getString("message");
            if (SUCCESS_CODE.equals(code)) {
                JSONArray regionalList = Optional.ofNullable(resp.getJSONObject("result"))
                        .map(result -> result.getJSONArray("regionalList"))
                        .orElseThrow(() -> {
                            log.error("{}查询省市区信息失败 regionalList 为空", LOG_TAG);
                            return BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_ORDER_FAIL, "查询省市区信息失败");
                        });
                log.info("{}查询省市区信息成功，regionalList：{}", LOG_TAG, regionalList);
                return;
            }
            log.error("{}查询省市区信息失败，msgCode：{}", LOG_TAG, code);
            throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_ORDER_FAIL, message);
        }
        log.error("{}查询省市区信息失败，非200状态码，响应码：{}", LOG_TAG, response.getStatus());
        throw BizException.create(EccErrorCodeEnum.XIE_YUN_QUERY_ORDER_FAIL, "接口请求失败");
    }

    /**
     * 取消订单
     */
    public void cancelOrder(String orderId, String chosePhone, String returnCause, String returnCauseType, String userId) {

        String url = xieYunProperties.getDomain() + "/order/returnOrder";
        JSONObject body = new JSONObject();
        body.put("orderId", orderId);
        body.put("applyPerson", chosePhone);
        body.put("returnCause", returnCause);
        body.put("returnCauseType", returnCauseType);
        body.put("userId", userId);

        HttpRequest request = HttpUtil.createPost(url)
                .form(getEncryptForm(body.toJSONString()));
        request.addHeaders(getCommonHeader());
        log.info("{}取消订单请求原始参数：{}", LOG_TAG, body);
        log.info("{}取消订单请求：{}", LOG_TAG, request);
        HttpResponse response = request.execute();
        log.info("{}取消订单响应：{}", LOG_TAG, response);
        if (response.isOk()) {
            JSONObject resp = JSONObject.parseObject(response.body());
            String code = resp.getString("msgCode");
            String message = resp.getString("message");
            if (SUCCESS_CODE.equals(code)) {
                String respCode = resp.getJSONObject("result").getString("respCode");
                if (SUCCESS_CODE.equals(respCode)) {
                    return;
                }
            }
            log.error("{}取消订单失败，msgCode：{}", LOG_TAG, code);
            throw BizException.create(EccErrorCodeEnum.XIE_YUN_CANCEL_ORDER_FAIL, message);
        }

    }


    // public static void main(String[] args) throws Exception {
    //     // 1. 利用空构造器的RSA获取Base64位的publicKey, privateKey
    //     RSA rsa = new RSA();
    //     String privateKeyBase64 = rsa.getPrivateKeyBase64();
    //     String publicKeyBase64 = rsa.getPublicKeyBase64();
    //     System.out.println(privateKeyBase64);
    //     // 2. 根据公钥生成密文
    //     RSA rsa1 = new RSA(null, publicKeyBase64);
    //     byte[] encrypt = rsa1.encrypt(StrUtil.bytes("我是一段测试aaaa", CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);
    //     String hexStr = HexUtil.encodeHexStr(encrypt);
    //
    //     // 3. 根据私钥解密密文
    //     RSA rsa2 = new RSA(privateKeyBase64, null);
    //     byte[] decodeHex = HexUtil.decodeHex(hexStr);
    //     byte[] decrypt1 = rsa2.decrypt(decodeHex, KeyType.PrivateKey);
    //     System.out.println(StrUtil.str(decrypt1, CharsetUtil.CHARSET_UTF_8));
    //
    //     String encrypt1 = RsaSecretUtils.encrypt("我是一段测试aaaa", publicKeyBase64);
    //     System.out.println(StrUtil.str(rsa2.decrypt(Base64.getDecoder().decode(encrypt1), KeyType.PrivateKey), CharsetUtil.CHARSET_UTF_8));
    //
    // }
    //
    static class RsaSecretUtils {
        public static final String KEY_ALGORITHM = "RSA";

        // 1024 bits 的 RSA 密钥对，最大加密明文大小
        private static final int MAX_ENCRYPT_BLOCK = 117;

        // 1024 bits 的 RSA 密钥对，最大解密密文大小
        private static final int MAX_DECRYPT_BLOCK = 128;

        // 获取公钥
        public static PublicKey getPublicKey(String publicKeyString) throws NoSuchAlgorithmException, InvalidKeySpecException {
            byte[] publicKeyByte = Base64.getDecoder().decode(publicKeyString);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyByte);
            KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
            return keyFactory.generatePublic(keySpec);
        }

        /**
         * 公钥加密
         *
         * @param text         待加密的明文字符串
         * @param publicKeyStr 公钥
         * @return 加密后的密文
         */
        public static String encrypt(String text, String publicKeyStr) {
            try {
                Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
                cipher.init(Cipher.ENCRYPT_MODE, getPublicKey(publicKeyStr));
                byte[] tempBytes = cipher.doFinal(text.getBytes("UTF-8"));
                return Base64.getEncoder().encodeToString(tempBytes);
            } catch (Exception e) {
                throw new RuntimeException("加密字符串[" + text + "]时遇到异常", e);
            }
        }

    }

    static class AESUtil {

        public final static String CHART_DEFAULT = "UTF-8";
        private static String IVRANDOM = "0123456789ABEDEF";

        public static byte[] decodeAES(String security, String key) {
            try {
                Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
                IvParameterSpec sp = new IvParameterSpec(IVRANDOM.getBytes(CHART_DEFAULT));
                byte[] content = org.apache.commons.codec.binary.Base64.decodeBase64(security);
                SecureRandom random = new SecureRandom(key.getBytes());
                SecretKeySpec spec = new SecretKeySpec(key.getBytes(CHART_DEFAULT), "AES");
                cipher.init(Cipher.DECRYPT_MODE, spec, sp);
                return cipher.doFinal(content);

            } catch (Exception e) {
                e.printStackTrace();
            }
            return null;
        }


        public static String enAES(String security, String key) {
            try {
                byte[] content = security.getBytes(CHART_DEFAULT);
                byte[] aeskeys = key.getBytes(CHART_DEFAULT);
                Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
                IvParameterSpec sp = new IvParameterSpec(IVRANDOM.getBytes(CHART_DEFAULT));
                SecretKeySpec spec = new SecretKeySpec(aeskeys, "AES");
                cipher.init(Cipher.ENCRYPT_MODE, spec, sp);
                byte[] context = cipher.doFinal(content);
                return org.apache.commons.codec.binary.Base64.encodeBase64String(context);

            } catch (Exception e) {
            }
            return null;
        }

    }

}
