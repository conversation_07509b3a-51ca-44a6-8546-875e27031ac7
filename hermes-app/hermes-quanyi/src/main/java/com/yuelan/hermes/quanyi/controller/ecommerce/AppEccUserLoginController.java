package com.yuelan.hermes.quanyi.controller.ecommerce;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.manager.EccUserManager;
import com.yuelan.hermes.quanyi.controller.request.EccAlibabaCodeLoginReq;
import com.yuelan.hermes.quanyi.controller.request.EccBindPhoneReq;
import com.yuelan.hermes.quanyi.controller.request.EccGetTaoBaoOauthUrlReq;
import com.yuelan.hermes.quanyi.controller.request.EccSendBindSmsReq;
import com.yuelan.hermes.quanyi.controller.response.EccUserTokenResp;
import com.yuelan.hermes.quanyi.remote.TaoBaoTopManager;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/5/8 下午2:01
 */
@Tag(name = "电商卡/app/淘宝授权登录相关api")
@Validated
@RestController
@RequestMapping("/e/ecc")
@RequiredArgsConstructor
public class AppEccUserLoginController {

    private final TaoBaoTopManager taoBaoTopManager;
    private final EccUserManager eccUserManager;

    @SaIgnore
    @Operation(summary = "获取淘宝授权地址", description = "跳转到淘宝授权页面，授权成功后，淘宝会重定向到redirectUri，并携带code参数")
    @PostMapping("/getOauthUrl")
    public BizResult<String> getOauthUrl(@RequestBody EccGetTaoBaoOauthUrlReq req) {
        return BizResult.create(taoBaoTopManager.getOauthUrl(req.getRedirectUri()));
    }

    @SaIgnore
    @Operation(summary = "淘宝授权登录", description = "前端拿到code，传到后端，完成登录")
    @PostMapping("/login/taoBao")
    public BizResult<EccUserTokenResp> login(@RequestBody EccAlibabaCodeLoginReq req) {
        return eccUserManager.loginByTaoBao(req.getCode());
    }

    @Operation(summary = "token登录", description = "淘宝授权登录成功之后，token存在前端本地，每次打开网页把token值放在header=X-Ecc-Token传到后端，后端完成登录。如果该接口返回错误，前端需要重新授权登录")
    @PostMapping("/login/token")
    public BizResult<EccUserTokenResp> tokenLogin() {
        return eccUserManager.loginByToken();
    }

    @Operation(summary = "发送绑定手机号验证码", description = "绑定手机号时，发送验证码")
    @PostMapping("/sendBindSms")
    public BizResult<Void> sendBindSms(@RequestBody EccSendBindSmsReq req) {
        return eccUserManager.sendBindSms(req.getPhone());
    }

    @Operation(summary = "绑定手机号到电商卡用户", description = "绑定手机号到电商卡用户")
    @PostMapping("/bindPhone")
    public BizResult<Void> bindPhone(@RequestBody EccBindPhoneReq req) {
        return eccUserManager.bindPhone(req);
    }
}
