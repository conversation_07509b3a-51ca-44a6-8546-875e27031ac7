package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.biz.manager.NcOutChannelManager;
import com.yuelan.hermes.quanyi.biz.manager.NumberCardManager;
import com.yuelan.hermes.quanyi.biz.manager.ZopOrderManager;
import com.yuelan.hermes.quanyi.biz.service.EccProductDOService;
import com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum;
import com.yuelan.hermes.quanyi.common.enums.EccSpEnum;
import com.yuelan.hermes.quanyi.common.enums.SpProdEnum;
import com.yuelan.hermes.quanyi.common.enums.error.EccErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.EccGetCardResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.controller.request.EccBaseGetCardReq;
import com.yuelan.hermes.quanyi.controller.request.EccGetCardApiReq;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <AUTHOR> 2025/3/14
 * @since 2025/3/14
 */
@SaIgnore
@Slf4j
@Validated
@Tag(name = "号卡OpenApi")
@RequestMapping("/ecc")
@RestController
@RequiredArgsConstructor
public class NumberCardOpenApiController {
    private static final String API_KEY = "apiKey";
    private static final String SIGN = "sign";

    private final NcOutChannelManager ncOutChannelManager;
    private final CacheHandler cacheHandler;
    private final NumberCardManager numberCardManager;
    private final EccProductDOService eccProductDOService;
    private final ZopOrderManager zopOrderManager;

    @Operation(
            summary = "随机选号版本-领取号卡商品接口",
            description = "1.该接口只可以服务端调用。"
                    + "<br>2.测试环境为模拟下单接口响应较快，正式环境要请求多次运营商接口，实际接口响应时间在5S左右，请设置合理的超时时间。"
    )
    @Parameters({
            @Parameter(name = API_KEY, in = ParameterIn.HEADER, description = "分配给对接方的ApiKey", required = true),
            @Parameter(name = SIGN, in = ParameterIn.HEADER, description = "签名", required = true)
    })
    @ApiResponse(responseCode = "200", description = "code=0,成功返回数据，其他失败,msg为错误信息")
    @PostMapping("/numberCardOrder")
    public BizResult<EccGetCardResultBO> reqFreeCard(
            @RequestHeader(API_KEY) String apiKey
            , @RequestHeader(SIGN) String apiSign
            , @RequestBody @Validated EccGetCardApiReq req) {
        log.info("领取号卡接口, apiKey={}, sign={}", apiKey, apiSign);
        req.reqCheck();
        EccOuterChannelDO dbChannelDO = ncOutChannelManager.checkIpAndSign(apiKey, apiSign, req);
        EccBaseGetCardReq getCardReq = req.convert(dbChannelDO);
        Long outerChannelId = dbChannelDO.getOuterChannelId();
        String channelOrderNo = req.getChannelOrderNo();
        RLock createLock = cacheHandler.getEccApiOrderCreateLock(outerChannelId, channelOrderNo);
        try {
            if (Objects.isNull(createLock)) {
                throw BizException.create(EccErrorCodeEnum.CHANNEL_ORDER_CREATING);
            }
            if (numberCardManager.getOuterChannelOrderCount(outerChannelId, channelOrderNo) != 0) {
                throw BizException.create(EccErrorCodeEnum.CHANNEL_ORDER_EXIST);
            }
            EccProductDO product = eccProductDOService.getByProdCode(req.getEccProdCode());
            if (Objects.isNull(product)) {
                throw BizException.create(EccErrorCodeEnum.PRODUCT_NOT_EXIST);
            }
            EccGetCardResultBO resp;
            if (isZopOrder(product)) {
                resp = zopOrderManager.receiveSimCard(getCardReq, EccChannelTypeEnum.OUTER);
            } else {
                resp = numberCardManager.receiveSimCard(getCardReq, EccChannelTypeEnum.OUTER);
            }
            return BizResult.create(resp);
        } finally {
            if (Objects.nonNull(createLock) && createLock.isLocked() && createLock.isHeldByCurrentThread()) {
                createLock.unlock();
            }
        }
    }

    /**
     * 是否是联通官方订单
     */
    private boolean isZopOrder(EccProductDO productDO) {
        SpProdEnum spProdEnum = SpProdEnum.of(productDO.getSpProdId());
        try {
            EccSpEnum spEnum = spProdEnum.getSpEnum();
            return EccSpEnum.UNICOM_ZOP.equals(spEnum);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw BizException.create(EccErrorCodeEnum.PRODUCT_NOT_EXIST);
        }
    }


}
