package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/8/9 下午12:00
 */
@Data
public class TestProdOrderItemsReq {

    @NotNull(message = "测试产品id不能为空")
    @Schema(description = "产品ID")
    private Long productId;

    @NotNull(message = "测试订单时间不能为空")
    @Schema(description = "测试订单时间")
    private LocalDateTime testOrderTime;
}
