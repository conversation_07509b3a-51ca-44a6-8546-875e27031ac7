package com.yuelan.hermes.quanyi.remote.response;

import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.Data;

import java.util.Objects;

@Data
public class QiquBaseRsp<T> {
    /**
     * 接口错误码
     */
    private Integer return_code;
    /**
     * 接口返回错误信息
     */
    private String return_msg;
    /**
     * SUCCESS/FAIL
     * 业务状态码
     */
    private String result_code;
    /**
     * 接口返回数据实体
     */
    private T data;


    public boolean checkResult() {
        if (Objects.equals(200, return_code)) {
            return Objects.equals("SUCCESS", result_code);
        } else if (Objects.equals(208, return_code)) {
            //商家平台账户余额不能低于 200 元
            throw BizException.create(BizErrorCodeEnum.QQU_MCH_ACCOUNT_BALANCE_LIMIT_200, return_msg);
        }
        throw BizException.create(BizErrorCodeEnum.QQU_ERROR, return_msg);
    }

}
