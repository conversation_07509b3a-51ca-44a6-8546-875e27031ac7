package com.yuelan.hermes.quanyi.controller.request;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccRedeemCodeDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR> 2024/5/4 上午12:58
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class EccRedeemCodeListReq extends PageRequest {

    /**
     * 商品id
     */
    @Schema(description = "商品id")
    private Long goodsId;

    /**
     * 兑换码唯一标识
     */
    @Schema(description = "兑换码")
    private String redeemCodeKey;

    /**
     * 兑换是否已经使用：0-未使用；1-已使用
     */
    @Schema(description = "兑换是否已经使用：0-未使用；1-已使用")
    private Integer redeemCodeStatus;


    //
    // public Wrapper<EccRedeemCodeDO> buildQueryWrapper() {
    //     return Wrappers.lambdaQuery(EccRedeemCodeDO.class)
    //             .eq(goodsId != null, EccRedeemCodeDO::getGoodsId, goodsId)
    //             .eq(redeemCodeKey != null, EccRedeemCodeDO::getRedeemCodeKey, redeemCodeKey)
    //             .eq(redeemCodeStatus != null, EccRedeemCodeDO::getRedeemCodeStatus, redeemCodeStatus)
    //             .orderByDesc(EccRedeemCodeDO::getCodeId);
    // }

    public MPJLambdaWrapper<EccRedeemCodeDO> buildQueryWrapper() {
        MPJLambdaWrapper<EccRedeemCodeDO> wrapper = new MPJLambdaWrapper<EccRedeemCodeDO>()
                .eq(goodsId != null, EccRedeemCodeDO::getGoodsId, goodsId)
                .eq(redeemCodeKey != null, EccRedeemCodeDO::getRedeemCodeKey, redeemCodeKey)
                .eq(redeemCodeStatus != null, EccRedeemCodeDO::getRedeemCodeStatus, redeemCodeStatus)
                .selectAll(EccRedeemCodeDO.class)
                .select(EccGoodsDO::getGoodsName, EccGoodsDO::getSupplierName)
                .leftJoin(EccGoodsDO.class, EccGoodsDO::getGoodsId, EccRedeemCodeDO::getGoodsId)
                .orderByDesc(EccRedeemCodeDO::getCodeId);
        return wrapper;
    }
}
