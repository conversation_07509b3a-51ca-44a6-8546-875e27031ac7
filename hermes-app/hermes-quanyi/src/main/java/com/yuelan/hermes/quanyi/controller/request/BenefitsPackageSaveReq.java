package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class BenefitsPackageSaveReq {

    @NotNull(message = "权益包id不能为空", groups = {EditGroup.class})
    @Schema(description = "主键id")
    private Long packageId;

    @NotBlank(message = "权益包不能为空", groups = {AddGroup.class, EditGroup.class})
    @Schema(description = "权益包名字")
    private String packageName;

    @NotNull(message = "兑换有效期天数不能为空", groups = {AddGroup.class, EditGroup.class})
    @Schema(description = "兑换有效期天数")
    private Integer redemptionPeriod;

    @NotNull(message = "销售价/单位元不能为空", groups = {AddGroup.class, EditGroup.class})
    @Schema(description = "销售价/单位元")
    private BigDecimal sellingPrice;

    public static BenefitsPackageDO convert(BenefitsPackageSaveReq req) {
        BenefitsPackageDO packageDO = new BenefitsPackageDO();
        packageDO.setPackageId(req.getPackageId());
        packageDO.setPackageName(req.getPackageName());
        packageDO.setRedemptionPeriod(req.getRedemptionPeriod());
        packageDO.setSellingPrice(req.getSellingPrice().multiply(new BigDecimal("100")).intValue());
        return packageDO;
    }

}
