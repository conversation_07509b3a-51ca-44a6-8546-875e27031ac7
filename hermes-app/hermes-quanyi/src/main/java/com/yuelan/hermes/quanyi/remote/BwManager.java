package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.common.pojo.properties.BwProperties;
import com.yuelan.hermes.quanyi.common.util.BianWaUtil;
import com.yuelan.hermes.quanyi.remote.request.BwCreateOrderV2Req;
import com.yuelan.hermes.quanyi.remote.response.BwCreateOrderV2Rsp;
import com.yuelan.hermes.quanyi.remote.response.BwResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

@Slf4j
@Component
public class BwManager {

    private static final String CREATE_ORDER_V2 = "/vbankprod-webapp/InterfaceOrder/CreateOrderV2";
    private static final String QUERY_ORDER = "/vbankprod-webapp/InterfaceOrder/QueryOrder";

    @Autowired
    private BwProperties bwProperties;

    /**
     * 充值
     */
    public BwCreateOrderV2Rsp createOrderV2(String orderNo, String thirdGoodsNo, String thirdSkuNo, String account, Integer channel, String callBackUrl) {
        if (!NumberUtil.isNumber(thirdGoodsNo) && !NumberUtil.isNumber(thirdSkuNo)) {
            log.error("变蛙产品编号或面值编号不正确，thirdGoodsNo:{},thirdSkuNo:{}", thirdGoodsNo, thirdSkuNo);
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "变蛙产品编号或面值编号不正确");
        }
        Long productid = Long.valueOf(thirdGoodsNo);
        Long classid = Long.valueOf(thirdSkuNo);
        long timestamp = System.currentTimeMillis() / 1000;

        if (!AppConstants.isReal()) {
            BwCreateOrderV2Rsp bwCreateOrderV2Rsp = new BwCreateOrderV2Rsp();
            bwCreateOrderV2Rsp.setBankId(bwProperties.getBankId());
            bwCreateOrderV2Rsp.setProductId(productid);
            bwCreateOrderV2Rsp.setProductName("");
            bwCreateOrderV2Rsp.setGoodsId(0L);
            bwCreateOrderV2Rsp.setGoodsName("");
            bwCreateOrderV2Rsp.setOrderNo(RandomUtil.randomNumbers(10));
            bwCreateOrderV2Rsp.setOuterTradeNo(orderNo);
            bwCreateOrderV2Rsp.setCpAccount(account);
            bwCreateOrderV2Rsp.setTotalPayAmount(new BigDecimal("0"));
            return bwCreateOrderV2Rsp;
        }

        BwCreateOrderV2Req orderV2Req = new BwCreateOrderV2Req();
        orderV2Req.setBankid(bwProperties.getBankId());
        orderV2Req.setProductid(productid);
        orderV2Req.setClassid(classid);
        orderV2Req.setAccount(account);
        orderV2Req.setOut_trade_no(orderNo);
        orderV2Req.setChannel(channel.toString());
        orderV2Req.setTimestamp(timestamp);
        orderV2Req.setNotify_url(callBackUrl);
        //计算签名
        Map<String, Object> paramMap = BianWaUtil.getSignParam(orderV2Req, bwProperties.getSecurityCode(), null);
        //发送请求
        String url = bwProperties.getHost() + CREATE_ORDER_V2;
        String result = HttpUtil.post(url, paramMap, 10000);
        log.info("变蛙下单请求成功，body：{}，result:{}", paramMap, result);

        BwResult<BwCreateOrderV2Rsp> bwResult = JSON.parseObject(result, new TypeReference<BwResult<BwCreateOrderV2Rsp>>() {
        });
        if (!bwResult.isSuccess()) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "变蛙下单失败," + bwResult.getErrorDesc());
        }
        return bwResult.getContent();
    }

}
