package com.yuelan.hermes.quanyi.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.manager.GameOrderManager;
import com.yuelan.hermes.quanyi.biz.manager.HshUserLoginManager;
import com.yuelan.hermes.quanyi.biz.manager.NewBenefitsOrderManager;
import com.yuelan.hermes.quanyi.config.satoken.StpHshUserUtil;
import com.yuelan.hermes.quanyi.controller.request.HshSendBindSmsReq;
import com.yuelan.hermes.quanyi.controller.request.HshSmsLoginReq;
import com.yuelan.hermes.quanyi.controller.request.HshUserBindReq;
import com.yuelan.hermes.quanyi.controller.request.WxCodeReq;
import com.yuelan.hermes.quanyi.controller.response.UserAuthResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/7/29 下午7:21
 */
@SaIgnore
@Validated
@RestController
@Tag(name = "惠生活/用户接口")
@RequiredArgsConstructor
@RequestMapping("/hshUser")
public class HshUserLoginController {

    private final HshUserLoginManager userLoginManager;
    private final GameOrderManager gameOrderManager;
    private final NewBenefitsOrderManager newBenefitsOrderManager;

    /**
     * 公众号用户登录
     * 用的 电竞卡权益平台 公众号，不利于整个平台规划，这个是西米下的账号，应该权益都在悦蓝惠生活里面才对
     */
    @Operation(summary = "电竞卡权益平台-公众号用户登录", description = "用的 电竞卡权益平台 公众号")
    @PostMapping("/mpLogin")
    public BizResult<Object> gamingUserLogin(@Validated @RequestBody WxCodeReq req) throws WxErrorException {
        return BizResult.create(userLoginManager.mpLogin(req));
    }


    @Operation(summary = "获取绑定/登录的验证码", description = "支持电竞卡，新权益组合包的订单用户；测试环境后 9 位数字一样的手机号码可任意验证码登录")
    @PostMapping("/sendSmsCode")
    public BizResult<Void> sendSmsCode(@Validated @RequestBody HshSendBindSmsReq req) {
        String phone = req.getPhone();
        if (!gameOrderManager.haveGamingOrder(phone)
                && !newBenefitsOrderManager.isOrderUser(phone)
        ) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "未查询到权益，新用户请5分钟后重试");
        }
        userLoginManager.sendHshSms(phone);
        return BizResult.ok();
    }

    /**
     * 电竞卡绑定手机
     */
    @Operation(summary = "绑定手机号", description = "短信验证码验证通过即绑定")
    @PostMapping("/bindPhone")
    public BizResult<UserAuthResp> bindPhone(@Validated @RequestBody HshUserBindReq req) {
        return BizResult.create(userLoginManager.bindAndLogin(req));
    }

    /**
     * 用户短信验证码登录
     */
    @Operation(summary = "短信验证码登录")
    @PostMapping("/smsLogin")
    public BizResult<UserAuthResp> smsLogin(@Validated @RequestBody HshSmsLoginReq req) {
        return BizResult.create(userLoginManager.smsLogin(req));
    }

    /**
     * 解绑/退出
     */
    @SaCheckLogin(type = StpHshUserUtil.TYPE)
    @Operation(summary = "解绑当前手机号码")
    @PostMapping("/unbindPhone")
    public BizResult<Void> unbindPhone() {
        userLoginManager.unbindPhone();
        return BizResult.ok();
    }

    /**
     * token登录
     */
    @SaCheckLogin(type = StpHshUserUtil.TYPE)
    @Operation(summary = "jwt-token登录", description = "请在头里面携带x-hsh-token")
    @PostMapping("/tokenLogin")
    public BizResult<UserAuthResp> tokenLogin() {
        return BizResult.create(userLoginManager.tokenLogin());
    }

    /**
     * 退出登录
     */
    @SaCheckLogin(type = StpHshUserUtil.TYPE)
    @Operation(summary = "退出登录")
    @PostMapping("/logout")
    public BizResult<Void> logout() {
        userLoginManager.logOut();
        return BizResult.ok();
    }


}
