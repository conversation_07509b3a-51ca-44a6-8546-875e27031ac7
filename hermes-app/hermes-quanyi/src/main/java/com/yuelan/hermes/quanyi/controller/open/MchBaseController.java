package com.yuelan.hermes.quanyi.controller.open;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.commons.util.YlSignUtil;
import com.yuelan.hermes.quanyi.biz.manager.MerchantAccessManager;
import com.yuelan.hermes.quanyi.common.enums.error.RechargeErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.MchThirdConfigBO;
import com.yuelan.hermes.quanyi.controller.request.SignReq;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Objects;

public abstract class MchBaseController {

    @Autowired
    private MerchantAccessManager merchantAccessManager;

    public MchThirdConfigBO checkSign(SignReq signReq) {
        if (Objects.isNull(signReq)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        if (StrUtil.isEmpty(signReq.getMchId())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "商户号不能为空");
        }
        if (Objects.isNull(signReq.getTimestamp())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "时间戳不能为空");
        }
        if (System.currentTimeMillis() - signReq.getTimestamp() > 300000) {
            throw BizException.create(RechargeErrorCodeEnum.API_EXPIRES_LIMIT_5);
        }
        if (StrUtil.isEmpty(signReq.getSign())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "签名不能为空");
        }
        MchThirdConfigBO mchThirdConfigBO = merchantAccessManager.findOne(signReq.getMchId());
        if (Objects.isNull(mchThirdConfigBO)) {
            throw BizException.create(RechargeErrorCodeEnum.MCHID_ERROR);
        }
        if (AppConstants.isReal()) {
            Map<String, Object> beanToMap = BeanUtil.beanToMap(signReq);
            boolean result = YlSignUtil.check(mchThirdConfigBO.getSecretKey(), beanToMap, Lists.newArrayList("sign"), signReq.getSign());
            if (!result) {
                throw BizException.create(RechargeErrorCodeEnum.SIGN_ERROR);
            }
        }
        return mchThirdConfigBO;
    }
}
