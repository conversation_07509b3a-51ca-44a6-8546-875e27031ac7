package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.HMac;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.pojo.bo.DirectRechargeResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.NetEaseProperties;
import com.yuelan.hermes.quanyi.remote.request.NetEaseGrantBenefitsReq;
import com.yuelan.hermes.quanyi.remote.response.NetEaseGrantBenefitsResp;
import com.yuelan.hermes.quanyi.remote.response.zop.NetEaseGameRoleInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2025/1/23
 * @since 2025/1/23
 * 网易计费组开发的接口：蛋仔-权益发放解决方案(1).pdf
 * <p>
 * 网易计费组主要职责：
 * 开发及运维游戏计费系统，处理海量账号及计费数据，为各游戏产品及相关的上下游部门提供业务支撑、数据服务等，从而满足公司各产品业务的快速发展。作为各产品上线不过或缺的一部分，计费组从2004年起为梦幻、大话等拳头产品服务，一直得到各产品的高度认可，至今已经为500多个项目提供高质量服务。
 * <p>
 * 网易蛋仔派对逻辑
 * 移动->西米下单 西米同步返回成功。
 * 引导用户去兑换页，移动下发我们的兑换页链接。
 * 用户在兑换页->登录 登录成功=是电竞卡用户
 * 用户绑定游戏内角色（复制游戏内id，通过网易接口查询角色信息）
 * 用户确认兑换->同步向网易下单->网易同步返回成功->展示给用户
 * 网易异步通知我们发放成功
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NetEaseManager {

    private final NetEaseProperties netEaseProperties;

    /**
     * 网易权益发放接口
     */
    public NetEaseGrantBenefitsResp grantBenefits(NetEaseGrantBenefitsReq req) {
        String path = "/notify/china_mobile_reward2/be172fe77198655e_dzpd/web/1/hzlh";
        String url = netEaseProperties.getHost() + path;
        String reqJson = JSON.toJSONString(req);
        String sign = genSign(HttpMethod.POST.name(), path, reqJson);

        HttpRequest request = HttpUtil.createPost(url)
                .header("X-Server-Source", netEaseProperties.getSource())
                .header("X-Server-Sign", sign)
                .contentType(ContentType.JSON.toString())
                .body(reqJson);
        log.info("网易权益发放请求发起。body:{}", request);
        String result = request.execute().body();
        log.info("网易权益发放请求成功。body:{},result:{}", reqJson, result);
        return JSONObject.parseObject(result, NetEaseGrantBenefitsResp.class);
    }

    /**
     * 通过⻆⾊id，urs查询⻆⾊信息(新增服务端接⼝)
     */
    public NetEaseGameRoleInfoResp getRoleInfo(String gameId, String roleId) {
        String path = "/server/games/" + gameId + "/get_role?id=" + roleId;
        String url = netEaseProperties.getHost() + path;
        String sign = genSign(HttpMethod.GET.name(), path, "");
        HttpRequest formReq = HttpUtil.createGet(url)
                .header("X-Server-Source", netEaseProperties.getSource())
                .header("X-Server-Sign", sign);
        log.info("网易查询角色信息，请求发起:formReq:{}", formReq);
        String result = formReq.execute().body();
        log.info("网易查询角色信息，请求成功。result:{}", result);
        return JSONObject.parseObject(result, NetEaseGameRoleInfoResp.class);
    }

    /**
     * 通过计费中心的接口逻辑
     * 如果未绑定角色，待绑定之后再调用该接口
     */
    public DirectRechargeResultBO createOrder(GamingOrderDO gamingOrderDO, GamingOrderItemDO gamingOrderItemDO) {
        // 预下单无需逻辑判断直接返回成功
        DirectRechargeResultBO resultBO = new DirectRechargeResultBO();
        resultBO.setSuccess(true);
        return resultBO;
    }

    public String genSign(String method, String pathQs, String body) {
        String key = netEaseProperties.getSecretKey();
        // 拼接待签名字符串
        String strToSign = method.toUpperCase() + pathQs + body;
        // 创建HMAC-SHA256实例
        HMac hmac = SecureUtil.hmacSha256(key.getBytes(CharsetUtil.CHARSET_UTF_8));
        // 生成十六进制签名
        return hmac.digestHex(strToSign, CharsetUtil.CHARSET_UTF_8);
    }
}
