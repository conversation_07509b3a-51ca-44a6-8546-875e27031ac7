package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccLicenseDO;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2025/5/21
 * @since 2025/5/21
 */
@Data
@AutoMapper(target = EccLicenseDO.class)
public class EccLicenseResp {

    /**
     * 资源id
     */
    @Schema(description = "授权牌id")
    private Long licenseId;

    /**
     * 授权牌名称
     */
    @Schema(description = "授权牌名称")
    private String licenseName;

    /**
     * 运营商
     */
    @Schema(description = "运营商：1-移动 2-联通 3-电信 4-广电")
    private Integer operator;

    /**
     * 资源 uri （不包含域名）
     */
    @Schema(description = "资源 uri （不包含域名）")
    private String uri;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
