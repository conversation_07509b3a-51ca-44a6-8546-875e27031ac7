package com.yuelan.hermes.quanyi.remote.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

@Data
public class MiguFunCommonNotifyRsp {

    @Schema(description = "响应状态,1 响应成功0 响应失败")
    private String returnCode;

    @Schema(description = "响应描述")
    private String message;

    @Schema(description = "返回数据")
    private String resultData;

    public boolean check() {
        return Objects.equals("000000", returnCode);
    }


}
