package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class RsRequest {

    @Schema(description = "唯一标识")
    @NotBlank(message = "唯一标识不能为空")
    private String appId;

    @Schema(description = "时间戳")
    @NotBlank(message = "时间戳不能为空")
    private String timeStamp;

    @Schema(description = "下单参数")
    @NotBlank(message = "下单参数不能为空")
    private String data;

    @Schema(description = "签名")
    @NotBlank(message = "签名不能为空")
    private String sign;
}
