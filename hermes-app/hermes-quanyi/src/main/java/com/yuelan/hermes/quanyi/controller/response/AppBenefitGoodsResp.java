package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> 2024/4/2 19:12
 */
@Data
public class AppBenefitGoodsResp {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long goodsId;

    /**
     * 商品名
     */
    @Schema(description = "商品名")
    private String goodsName;


    /**
     * 产品图
     */
    @Schema(description = "产品图")
    private String goodsImg;


    public static AppBenefitGoodsResp buildResp(BenefitGoodsDO benefitGoodsDO) {
        if (Objects.isNull(benefitGoodsDO)) {
            return null;
        }
        AppBenefitGoodsResp resp = new AppBenefitGoodsResp();
        resp.setGoodsId(benefitGoodsDO.getGoodsId());
        resp.setGoodsName(benefitGoodsDO.getGoodsName());
        resp.setGoodsImg(benefitGoodsDO.getGoodsImg());
        return resp;
    }


}
