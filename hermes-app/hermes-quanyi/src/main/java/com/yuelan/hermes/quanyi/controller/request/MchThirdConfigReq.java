package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MchThirdConfigReq {

    @Schema(description = "商户名称")
    private String mchName;

    @Schema(description = "加密算法类型1RSA")
    private Integer algorithm;

    @Schema(description = "公钥地址")
    private String publicKeyPath;

    @Schema(description = "秘钥")
    private String secretKey;

    @Schema(description = "私钥地址")
    private String privateKeyPath;

    @Schema(description = "第三方公钥地址")
    private String thirdPublicKeyPath;
}
