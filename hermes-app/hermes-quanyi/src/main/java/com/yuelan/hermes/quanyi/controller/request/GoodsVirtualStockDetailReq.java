package com.yuelan.hermes.quanyi.controller.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.SortEnum;
import com.yuelan.hermes.quanyi.common.enums.GoodsVirtualStockItemSortFiledEnum;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.util.Objects;


@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsVirtualStockDetailReq extends PageRequest {

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "批次号")
    private String batchNo;

    @NotBlank(message = "SKU不能为空")
    @Schema(description = "SKU编号")
    private String skuNo;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "SKU名称")
    private String skuName;

    @Schema(description = "供应商名称")
    private String supplier;

    @Schema(description = "卡号")
    private String voucherCode;

    @Schema(description = "卡密")
    private String voucherPassword;

    @Schema(description = "短链接")
    private String qrCodeUrl;

    @Schema(description = "1入库2出库")
    private Integer status;

    @Schema(description = "0禁用1启用")
    private Integer enable;

    @Schema(description = "排序字段")
    private Integer filed;

    @Schema(description = "排序方式")
    private Integer direction;

    /**
     * 排序
     */
    @JsonIgnore
    private String orderBy;

    public void check() {
        GoodsVirtualStockItemSortFiledEnum sortFiledEnum = LocalEnumUtils.findByCode(GoodsVirtualStockItemSortFiledEnum.class, filed);
        if (Objects.isNull(sortFiledEnum)) {
            sortFiledEnum = GoodsVirtualStockItemSortFiledEnum.ITEM_ID;
        }
        SortEnum sortEnum = LocalEnumUtils.findByCode(SortEnum.class, direction);
        if (Objects.isNull(sortEnum)) {
            sortEnum = SortEnum.DESC;
        }
        orderBy = sortFiledEnum.getFiled() + " " + sortEnum.getDirection();
    }
}

