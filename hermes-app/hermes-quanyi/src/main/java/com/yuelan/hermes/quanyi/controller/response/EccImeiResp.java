package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiDO;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 2025/6/19
 * @since 2025/6/19
 */
@Data
@AutoMapper(target = EccImeiDO.class)
public class EccImeiResp {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long imeiId;

    /**
     * imei
     */
    @Schema(description = "imei")
    private String imei;

    /**
     * 导入的批次 id
     */
    @Schema(description = "导入的批次 id")
    private Long batchId;

    /**
     * 设备信息
     */
    @Schema(description = "设备信息")
    private String device;


    @Schema(description = "渠道类型:1-内部渠道,2-外部渠道")
    private Integer channelType;


    /**
     * 渠道id
     */
    @Schema(description = "渠道id")
    private Long channelId;

    /**
     * 渠道id
     */
    @Schema(description = "渠道name")
    private String channelName;

    /**
     * 产品Id
     */
    @Schema(description = "产品Id")
    private Long productId;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称")
    private String prodName;


    @Schema(description = "发货时间")
    private LocalDateTime deliveryTime;

    /**
     * 导入时间
     */
    @Schema(description = "导入时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "imei 绑定 iccid 的记录")
    private List<EccImeiBindResp> imeiBindList;

}
