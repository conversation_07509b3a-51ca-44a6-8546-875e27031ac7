package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
public class GoodsVirtualStockRsp {

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "SKU ID")
    private Long skuId;

    @Schema(description = "SKU编号")
    private String skuNo;

    @Schema(description = "SKU名称")
    private String skuName;

    @Schema(description = "实际库存")
    private Integer realAmount;

    @Schema(description = "已售数量")
    private Integer sellAmount;

    @Schema(description = "有效库存")
    private Integer effectiveAmount;

    @Schema(description = "即将过期库存")
    private Integer waitExpiresAmount;

    @Schema(description = "已过期库存")
    private Integer expiresAmount;

    @Schema(description = "已禁用库存")
    private Integer disableAmount;

    @Schema(description = "预警库存")
    private Integer warnAmount;
}
