package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderExtensionDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> 2025/4/29
 * @since 2025/4/29
 */
@Data
public class BenefitsOrderExtensionResp {

    @ColumnWidth(20)
    @ExcelProperty("订单扩展id")
    @Schema(description = "订单扩展id")
    private Long extensionId;

    @ColumnWidth(20)
    @ExcelProperty("渠道订单号")
    @Schema(description = "渠道订单号")
    private String channelOrderNo;

    @ColumnWidth(20)
    @ExcelProperty("我方生成的订单号")
    @Schema(description = "我方生成的订单号")
    private String orderNo;

    @ColumnWidth(20)
    @ExcelProperty("渠道id")
    @Schema(description = "渠道id")
    private Integer channelId;

    @ColumnWidth(20)
    @ExcelProperty("渠道名字")
    @Schema(description = "渠道名字")
    private String channelName;

    @ColumnWidth(20)
    @ExcelProperty("支付渠道id")
    @Schema(description = "支付渠道id")
    private Integer payChannelId;

    @ColumnWidth(20)
    @ExcelProperty("支付渠道名字")
    @Schema(description = "支付渠道名字")
    private String payChannelName;

    @ColumnWidth(20)
    @ExcelProperty("支付产品包id")
    @Schema(description = "支付产品包id")
    private Integer payPkgId;

    @ColumnWidth(20)
    @ExcelProperty("支付产品包名")
    @Schema(description = "支付产品包名")
    private String payPkgName;


    public void setExtensionInfo(BenefitOrderExtensionDO orderExtension) {
        this.setExtensionId(orderExtension.getExtensionId());
        this.setChannelOrderNo(orderExtension.getChannelOrderNo());
        this.setOrderNo(orderExtension.getOrderNo());
        this.setChannelId(orderExtension.getChannelId());
        this.setChannelName(orderExtension.getChannelName());
        this.setPayChannelId(orderExtension.getPayChannelId());
        this.setPayChannelName(orderExtension.getPayChannelName());
        this.setPayPkgId(orderExtension.getPayPkgId());
        this.setPayPkgName(orderExtension.getPayPkgName());
    }
}
