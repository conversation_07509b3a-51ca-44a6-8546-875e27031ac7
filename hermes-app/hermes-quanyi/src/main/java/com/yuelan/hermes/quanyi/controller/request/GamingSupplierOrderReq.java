package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class GamingSupplierOrderReq {

    @NotNull(message = "请选择订单")
    @Schema(description = "订单ID")
    private Long orderId;

    @NotBlank(message = "请选择明细单号")
    @Schema(description = "订单明细单号")
    private String itemNo;

    public void check() {
        itemNo = StrUtil.cleanBlank(itemNo);
    }
}