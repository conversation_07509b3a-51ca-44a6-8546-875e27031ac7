package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2025/5/25
 * @since 2025/5/25
 */
@Data
public class ProdRegionResp {
    @Schema(description = "产品id")
    private Long productId;

    @Schema(description = "产品编码")
    private String prodCode;

    /**
     * 归属地类型
     */
    @Schema(description = "归属地类型 0-全国 1-分省 2-分市")
    private Integer regionType;

    /**
     * 归属地类型
     */
    @Schema(description = "支持的归属地列表")
    private List<EccPostAreaResp> regionList;
}
