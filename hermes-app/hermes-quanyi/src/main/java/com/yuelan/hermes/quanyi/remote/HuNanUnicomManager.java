package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.yuelan.hermes.commons.enums.SmsCodeType;
import com.yuelan.hermes.quanyi.biz.handler.UnicomJobHandler;
import com.yuelan.hermes.quanyi.biz.handler.factory.UnicomJobFactory;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.enums.HNUnicomWoPayPkgEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.event.HuNanUnicomOrderNotifyEvent;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitUnicomPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.HNUnicomProperties;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.controller.response.jsunicom.JiangSuUnicomOrderDetailResp;
import com.yuelan.hermes.quanyi.controller.response.jsunicom.JiangSuUnicomOrderResp;
import com.yuelan.hermes.quanyi.controller.response.jsunicom.JiangSuUnicomProductResp;
import com.yuelan.hermes.quanyi.controller.response.jsunicom.ProductUnsubscribeResp;
import com.yuelan.plugins.redisson.util.RedisUtils;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HuNanUnicomManager implements UnicomJobHandler {


    public static final String TAG = "[湖南联通]";
    // 产品推荐接口
    public static final String PRODUCT_RECOMMEND = "/api/customerOperation/support/productRecommend/v1";
    // 下发验证码
    public static final String GET_SMS_CODE = "/api/customerOperation/support/createCaptcha/v1";
    // 校验验证码
    public static final String VERIFYCAPTCHA = "/api/customerOperation/support/verifyCaptcha/v1";
    // 下单接口
    public static final String PRODUCT_ORDER = "/api/customerOperation/support/productOrder/v1";
    // 订单详情
    public static final String PRODUCT_ORDER_DETAIL = "/api/customerOperation/support/productOrderDetail/v1";
    // 新产品退订查询
    public static final String PRODUCT_UNSUBSCRIBE = "/api/chinaUnicom/hn74/dataOpenPlatform/innovateProductUnsubscribe/v1";

    private static final String SUCCESS_CODE = "0000";
    private static final int TIME_OUT = 20000;

    @Resource
    private HNUnicomProperties hnUnicomProperties;
    @Resource
    private BenefitOrderLogService benefitOrderLogService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 点击发送验证码
     */
    public boolean orderSendSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        String mobile = reqParams.getMobile();
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }

        HNUnicomWoPayPkgEnum pkgEnum = HNUnicomWoPayPkgEnum.of(productDO.getPayChannelPkgId());

        String commId = hnUnicomProperties.getProperties(pkgEnum).getCommonId();
        String templateId = hnUnicomProperties.getProperties(pkgEnum).getTemplateId();
        String commPrice = hnUnicomProperties.getProperties(pkgEnum).getCommPrice();
        String remarkCode = hnUnicomProperties.getProperties(pkgEnum).getRemarkCode();
        // 获取运营商推荐的商品
        JiangSuUnicomProductResp remoteProductResp = this.productRecommend(mobile);
        List<JiangSuUnicomProductResp.CommInfoDTO> commInfo = remoteProductResp.getCOMM_INFO();
        JiangSuUnicomProductResp.CommInfoDTO commInfoDTO = commInfo.stream().filter(x -> x.getCOMM_ID().equals(Long.valueOf(commId))).findFirst().orElse(null);
//        JiangSuUnicomProductResp.CommInfoDTO commInfoDTO = commInfo.stream().findFirst().orElse(null);
        if (commInfoDTO == null) {
            log.info(TAG + "运营商未推荐该产品。手机号:{},权益包产品code:{},运营商产品id:{}", reqParams.getMobile(), productDO.getProdCode(), commId);
            throw BizException.create(BizErrorCodeEnum.PROD_NOT_EXIST, "未找到相符推荐产品");
        }
        remoteProductResp.setCOMM_INFO(Lists.newArrayList(commInfoDTO));

        String businessId = remoteProductResp.getBUSINESS_ID();
        String commName = commInfoDTO.getCOMM_NAME();
        // 运营商下发验证码
        boolean sendSmsCode = this.sendSmsCode(mobile, templateId, businessId, commInfoDTO.getCOMM_ID().toString(), commName, commPrice, remarkCode);
        if (!sendSmsCode) {
            return false;
        }
        // 放入Redis中
        String key = RedisKeys.getJiangSuUnicomProductKey(mobile);
        RedisUtils.setCacheObject(key, remoteProductResp, Duration.ofMinutes(15));

        return true;
    }

    /**
     * 申请订单
     */
    public BenefitPayResultBO createOrder(UserBenefitOrderReq req, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        BenefitUnicomPayResultBO payResultBO = new BenefitUnicomPayResultBO();
        payResultBO.setSuccess(false);
        String mobile = req.getMobile();

        HNUnicomWoPayPkgEnum pkgEnum = HNUnicomWoPayPkgEnum.of(productDO.getPayChannelPkgId());

        // 获取运营商推荐的商品
        String key = RedisKeys.getJiangSuUnicomProductKey(mobile);
        JiangSuUnicomProductResp productResp = RedisUtils.getCacheObject(key);
        if (productResp == null) {
            // 缓存中没有产品信息,说明缓存过期,中间间隔的时间过久
            payResultBO.setMessage("请重新发送验证码");
            return payResultBO;
        }
        String businessId = productResp.getBUSINESS_ID();
        JiangSuUnicomProductResp.CommInfoDTO commInfoDTO = productResp.getCOMM_INFO().stream().findFirst().orElse(null);
        if (commInfoDTO == null) {
            log.info(TAG + "运营商商品缓存没有数据.手机号:{},redisKey:{}", req.getMobile(), key);
            payResultBO.setMessage("订阅失败,请稍后重试");
            return payResultBO;
        }
        String strategyId = commInfoDTO.getSTRATEGY_ID();
        Long commId = commInfoDTO.getCOMM_ID();
        // 远程校验验证码
        boolean smsCodeFlag = this.checkSmsCode(mobile, businessId, commId, req.getSmsCode());
        if (!smsCodeFlag) {
            log.info(TAG + "运营商验证码校验失败.手机号:{}", req.getMobile());
            payResultBO.setMessage("订阅失败,请稍后重试");
            return payResultBO;
        }

        // 开始下单
        JiangSuUnicomOrderResp orderResp = this.productOrder(mobile, orderDO.getOrderNo(), businessId, commId, strategyId, pkgEnum);
        if (orderResp == null) {
            log.info(TAG + "运营商下单失败.手机号:{}", req.getMobile());
            payResultBO.setMessage("订阅失败,请稍后重试");
            return payResultBO;
        }
        orderDO.setPreorderContent(JSON.toJSONString(orderResp));
        String respCode = orderResp.getRSP_CODE();
        if (SUCCESS_CODE.equals(respCode)) {
            payResultBO.setSuccess(true);
            orderDO.setOutOrderNo(orderResp.getRSP_DATA().getORDER_ID());
        }
        return payResultBO;
    }

    /**
     * 运营商获取产品推荐
     */
    private JiangSuUnicomProductResp productRecommend(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }

        Map<String, Object> UNI_BSS_HEAD = buildBssHeader();

        Map<String, Object> UNI_BSS_BODY = new HashMap<>();
        Map<String, Object> PRODUCT_RECOMMEND_REQ = new HashMap<>();
        PRODUCT_RECOMMEND_REQ.put("CHANNEL_CODE", hnUnicomProperties.getChannelCode());
        PRODUCT_RECOMMEND_REQ.put("QUERY_TYPE", "1");
        PRODUCT_RECOMMEND_REQ.put("SERIAL_NUMBER", mobile);
        PRODUCT_RECOMMEND_REQ.put("TRADE_ID", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_FORMAT) + RandomUtil.randomNumbers(3));
        UNI_BSS_BODY.put("PRODUCT_RECOMMEND_REQ", PRODUCT_RECOMMEND_REQ);

        Map<String, Object> UNI_BSS_ATTACHED = new HashMap<>();
        UNI_BSS_ATTACHED.put("MEDIA_INFO", "");

        Map<String, Object> params = new HashMap<>();
        params.put("UNI_BSS_HEAD", UNI_BSS_HEAD);
        params.put("UNI_BSS_BODY", UNI_BSS_BODY);
        params.put("UNI_BSS_ATTACHED", UNI_BSS_ATTACHED);

        JSONObject object = this.sendRemoteRequest(PRODUCT_RECOMMEND, params, null);
        JSONObject recommendRsp = object.getJSONObject("PRODUCT_RECOMMEND_RSP");
        String code = recommendRsp.getString("RESP_CODE");
        if (SUCCESS_CODE.equals(code)) {
            JSONObject respData = recommendRsp.getJSONObject("RESP_DATA");
            JiangSuUnicomProductResp productResp = JSON.parseObject(JSON.toJSONString(respData), JiangSuUnicomProductResp.class);
            productResp.setBUSINESS_ID(recommendRsp.getString("TRADE_ID"));
            return productResp;
        } else {
            throw BizException.create(BizResult.error(code, recommendRsp.getString("RESP_DESC")));
        }
    }

    private Map<String, Object> buildBssHeader() {
        Map<String, Object> params = new HashMap<>();
        params.put("APP_ID", hnUnicomProperties.getClientId());
        DateTime date = DateUtil.date();
        params.put("TIMESTAMP", DateUtil.format(date, "yyyy-MM-dd HH:mm:ss SSS"));
        params.put("TRANS_ID", DateUtil.format(date, DatePattern.PURE_DATETIME_MS_PATTERN) + RandomUtil.randomNumbers(6));
        String tokenStr = MapUtil.sortJoin(params, "", "", true) + hnUnicomProperties.getClientSecret();
        String token = SecureUtil.md5(tokenStr);
        params.put("TOKEN", token);
        return params;
    }

    /**
     * 运营商下发短信验证码
     */
    private boolean sendSmsCode(String mobile, String templateId, String bussinessId, String commId, String commName, String commPrice, String remarkCode) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        Map<String, Object> UNI_BSS_HEAD = buildBssHeader();

        Map<String, Object> UNI_BSS_ATTACHED = new HashMap<>();
        UNI_BSS_ATTACHED.put("MEDIA_INFO", "");

        Map<String, Object> UNI_BSS_BODY = new HashMap<>();
        Map<String, Object> CREATE_CAPTCHA_REQ = new HashMap<>();
        CREATE_CAPTCHA_REQ.put("CHANNEL_CODE", hnUnicomProperties.getChannelCode());
        CREATE_CAPTCHA_REQ.put("COMM_ID", commId);
        CREATE_CAPTCHA_REQ.put("BUSINESS_ID", bussinessId);
        CREATE_CAPTCHA_REQ.put("SERIAL_NUMBER", mobile);
        CREATE_CAPTCHA_REQ.put("TEMPLATE_ID", templateId);
        CREATE_CAPTCHA_REQ.put("TRADE_ID", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_FORMAT) + RandomUtil.randomNumbers(6));

        Map<String, Object> PARA = new HashMap<>();
        PARA.put("PARA_ID", "COMM_NAME");
        PARA.put("PARA_VALUE", commName);
        Map<String, Object> PARA2 = new HashMap<>();
        PARA2.put("PARA_ID", "COMM_PRICE");
        PARA2.put("PARA_VALUE", commPrice);
        Map<String, Object> PARA3 = new HashMap<>();
        PARA3.put("PARA_ID", "SERIAL_NUMBER");
        PARA3.put("PARA_VALUE", mobile);
        Map<String, Object> PARA4 = new HashMap<>();
        PARA4.put("PARA_ID", "remark");
        PARA4.put("PARA_VALUE", remarkCode);
        CREATE_CAPTCHA_REQ.put("PARA", Lists.newArrayList(PARA, PARA2, PARA3, PARA4));

        UNI_BSS_BODY.put("CREATE_CAPTCHA_REQ", CREATE_CAPTCHA_REQ);

        Map<String, Object> params = new HashMap<>();
        params.put("UNI_BSS_HEAD", UNI_BSS_HEAD);
        params.put("UNI_BSS_BODY", UNI_BSS_BODY);
        params.put("UNI_BSS_ATTACHED", UNI_BSS_ATTACHED);

        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_SEND, mobile);
        JSONObject object = this.sendRemoteRequest(GET_SMS_CODE, params, args);
        JSONObject createCaptchaRsp = object.getJSONObject("CREATE_CAPTCHA_RSP");
        String code = createCaptchaRsp.getString("RESP_CODE");
        if (SUCCESS_CODE.equals(code)) {
            String key = createCaptchaRsp.getJSONObject("IDENTIFY_CODE").getString("KEY");
            // 存入缓存,
            String smsCodeKey = RedisKeys.getSmsCodeKey(mobile, SmsCodeType.BENEFIT_USER_ORDER);
            RedisUtils.setCacheObject(smsCodeKey, key, Duration.ofMinutes(10));
            return true;
        } else {
            throw BizException.create(BizResult.error(code, createCaptchaRsp.getString("RESP_DESC")));
        }
    }

    /**
     * 运营商校验验证码
     */
    private boolean checkSmsCode(String mobile, String bussinessId, Long commId, String smsCode) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        String key = RedisKeys.getSmsCodeKey(mobile, SmsCodeType.BENEFIT_USER_ORDER);
        String smsCodeKey = RedisUtils.getCacheObject(key);
        if (StringUtils.isBlank(smsCodeKey) || StringUtils.isBlank(smsCode)) {
            throw BizException.create(BizErrorCodeEnum.SMS_CODE_EXPIRE);
        }
        Map<String, Object> UNI_BSS_HEAD = buildBssHeader();

        Map<String, Object> UNI_BSS_ATTACHED = new HashMap<>();
        UNI_BSS_ATTACHED.put("MEDIA_INFO", "");

        Map<String, Object> UNI_BSS_BODY = new HashMap<>();
        Map<String, Object> VERIFY_CAPTCHA_REQ = new HashMap<>();
        VERIFY_CAPTCHA_REQ.put("CHANNEL_CODE", hnUnicomProperties.getChannelCode());
        VERIFY_CAPTCHA_REQ.put("CODE", smsCode);
        VERIFY_CAPTCHA_REQ.put("KEY", smsCodeKey);
        VERIFY_CAPTCHA_REQ.put("COMM_ID", commId + "");
        VERIFY_CAPTCHA_REQ.put("BUSINESS_ID", bussinessId);
        VERIFY_CAPTCHA_REQ.put("SERIAL_NUMBER", mobile);
        VERIFY_CAPTCHA_REQ.put("TRADE_ID", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_FORMAT) + RandomUtil.randomNumbers(6));
        VERIFY_CAPTCHA_REQ.put("TIMESTAMP", DateUtil.current() + "");
        UNI_BSS_BODY.put("VERIFY_CAPTCHA_REQ", VERIFY_CAPTCHA_REQ);

        Map<String, Object> params = new HashMap<>();
        params.put("UNI_BSS_HEAD", UNI_BSS_HEAD);
        params.put("UNI_BSS_BODY", UNI_BSS_BODY);
        params.put("UNI_BSS_ATTACHED", UNI_BSS_ATTACHED);

        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_CHECK, mobile);
        JSONObject object = this.sendRemoteRequest(VERIFYCAPTCHA, params, args);
        JSONObject verifyCaptchaRsp = object.getJSONObject("VERIFY_CAPTCHA_RSP");
        String code = verifyCaptchaRsp.getString("RESP_CODE");
        if (SUCCESS_CODE.equals(code)) {
            return true;
        } else {
            throw BizException.create(BizResult.error(code, verifyCaptchaRsp.getString("RESP_DESC")));
        }
    }

    /**
     * 下单
     */
    private JiangSuUnicomOrderResp productOrder(String mobile, String orderNo, String bussinessId, Long commId, String strategyId, HNUnicomWoPayPkgEnum pkgEnum) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        Map<String, Object> UNI_BSS_HEAD = buildBssHeader();

        Map<String, Object> UNI_BSS_ATTACHED = new HashMap<>();
        UNI_BSS_ATTACHED.put("MEDIA_INFO", "");

        Map<String, Object> UNI_BSS_BODY = new HashMap<>();
        Map<String, Object> PRODUCT_ORDER_REQ = new HashMap<>();
        PRODUCT_ORDER_REQ.put("SERIAL_NUMBER", mobile);
        PRODUCT_ORDER_REQ.put("BUSINESS_ID", bussinessId);
        PRODUCT_ORDER_REQ.put("PRICE_SUM", 0);
        PRODUCT_ORDER_REQ.put("PAY_TAG", "9");
        PRODUCT_ORDER_REQ.put("IS_PRINT_PAPER_LESS", "0");
        PRODUCT_ORDER_REQ.put("ORDER_TIME", DateUtil.now());
        PRODUCT_ORDER_REQ.put("TRADE_ID", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_FORMAT) + RandomUtil.randomNumbers(3));
        PRODUCT_ORDER_REQ.put("CHANNEL_CODE", hnUnicomProperties.getChannelCode());
        PRODUCT_ORDER_REQ.put("ORDER_TYPE", "2113");

        Map<String, Object> COMM_OBJECT = new HashMap<>();
        COMM_OBJECT.put("STRATEGY_ID", strategyId);
        COMM_OBJECT.put("MODIFY_TAG", "0");
        COMM_OBJECT.put("COMM_ID", commId + "");
        PRODUCT_ORDER_REQ.put("COMM_OBJECT", Lists.newArrayList(COMM_OBJECT));

        Map<String, Object> DEVELOPER_INFO = new HashMap<>();
        DEVELOPER_INFO.put("DEVELOP_DEPART_ID", "74b6k5f");
        DEVELOPER_INFO.put("DEVELOP_TYPE", "3");
        DEVELOPER_INFO.put("DEVELOP_STAFF_NAME", "长沙-线上杭州道盟互联网代理渠道");
        DEVELOPER_INFO.put("DEVELOP_DEPART_NAME", "长沙-线上杭州道盟互联网代理渠道");
        DEVELOPER_INFO.put("DEVELOP_DATE", DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss"));
        DEVELOPER_INFO.put("DEVELOP_EPARCHY_CODE", "0731");
        DEVELOPER_INFO.put("DEVELOP_STAFF_ID", "7416517785");
        PRODUCT_ORDER_REQ.put("DEVELOPER_INFO", DEVELOPER_INFO);

        UNI_BSS_BODY.put("PRODUCT_ORDER_REQ", PRODUCT_ORDER_REQ);

        Map<String, Object> params = new HashMap<>();
        params.put("UNI_BSS_HEAD", UNI_BSS_HEAD);
        params.put("UNI_BSS_BODY", UNI_BSS_BODY);
        params.put("UNI_BSS_ATTACHED", UNI_BSS_ATTACHED);

        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER, mobile, orderNo);
        JSONObject object = this.sendRemoteRequest(PRODUCT_ORDER, params, args);
        JSONObject productOrderRsp = object.getJSONObject("PRODUCT_ORDER_RSP");
        String code = productOrderRsp.getString("RESP_CODE");
        if (SUCCESS_CODE.equals(code)) {
            JSONObject respData = productOrderRsp.getJSONObject("RESP_DATA");
            return JSON.parseObject(JSON.toJSONString(respData), JiangSuUnicomOrderResp.class);
        } else {
            throw BizException.create(BizResult.error(code, productOrderRsp.getString("RESP_DESC")));
        }
    }

    /**
     * 订单详情查询
     *
     * @param orderId    订单id
     * @param outOrderId 外部订单id(如果为预下单的订单,传这个)
     */
    public JiangSuUnicomOrderDetailResp productOrderDetail(String mobile, String orderId, String outOrderId) {

        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        Map<String, Object> UNI_BSS_HEAD = buildBssHeader();

        Map<String, Object> UNI_BSS_ATTACHED = new HashMap<>();
        UNI_BSS_ATTACHED.put("MEDIA_INFO", "");

        Map<String, Object> UNI_BSS_BODY = new HashMap<>();
        Map<String, Object> PRODUCT_ORDER_DETAIL_REQ = new HashMap<>();
        if (StringUtils.isNotBlank(orderId)) {
            PRODUCT_ORDER_DETAIL_REQ.put("ORDER_ID", orderId);
        } else {
            PRODUCT_ORDER_DETAIL_REQ.put("TOUCH_ORDER_ID", outOrderId);
        }
        PRODUCT_ORDER_DETAIL_REQ.put("TRADE_ID", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_FORMAT) + RandomUtil.randomNumbers(3));
        PRODUCT_ORDER_DETAIL_REQ.put("CHANNEL_CODE", hnUnicomProperties.getChannelCode());

        UNI_BSS_BODY.put("PRODUCT_ORDER_DETAIL_REQ", PRODUCT_ORDER_DETAIL_REQ);
        Map<String, Object> params = new HashMap<>();
        params.put("UNI_BSS_HEAD", UNI_BSS_HEAD);
        params.put("UNI_BSS_BODY", UNI_BSS_BODY);
        params.put("UNI_BSS_ATTACHED", UNI_BSS_ATTACHED);

        JSONObject object = this.sendRemoteRequest(PRODUCT_ORDER_DETAIL, params, null);
        JSONObject productOrderDetailRsp = object.getJSONObject("PRODUCT_ORDER_DETAIL_RSP");
        return JSON.parseObject(JSON.toJSONString(productOrderDetailRsp), JiangSuUnicomOrderDetailResp.class);
    }

    private JSONObject sendRemoteRequest(String path, Map<String, Object> params, BenefitOrderLog.Args args) {
        String url = hnUnicomProperties.getHost() + path;

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url)
                .timeout(TIME_OUT)
                .contentType(ContentType.JSON.getValue())
                .body(JSON.toJSONString(params))
                .log(BenefitPayChannelEnum.HN_UNICOM, args);

        log.info(TAG + "请求前:{}", requestWrapper.getHttpRequest());
        HttpResponse response = benefitOrderLogService.http(requestWrapper);
        log.info(TAG + "请求后:{}", response);
        if (response.isOk()) {
            JSONObject jsonObject = JSON.parseObject(response.body(), JSONObject.class);
            return jsonObject.getJSONObject("UNI_BSS_BODY");
        } else {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
    }

    private JSONObject sendRemoteRequest2(String path, Map<String, Object> params, BenefitOrderLog.Args args) {
        String url = hnUnicomProperties.getHost() + path;

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url)
                .timeout(TIME_OUT)
                .contentType(ContentType.JSON.getValue())
                .body(JSON.toJSONString(params))
                .log(BenefitPayChannelEnum.HN_UNICOM, args);

        log.info(TAG + "请求前:{}", requestWrapper.getHttpRequest());
        HttpResponse response = benefitOrderLogService.http(requestWrapper);
        log.info(TAG + "请求后:{}", response);
        if (response.isOk()) {
            return JSON.parseObject(response.body(), JSONObject.class);
        } else {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
    }

    public List<ProductUnsubscribeResp> getProductUnsubscribeList(String dateStr, Integer current) {
        Map<String, Object> UNI_BSS_HEAD = buildBssHeader();

        Map<String, Object> INNOVATE_PRODUCT_UNSUBSCRIBE_REQ = new HashMap<>();
        INNOVATE_PRODUCT_UNSUBSCRIBE_REQ.put("current", current);
        INNOVATE_PRODUCT_UNSUBSCRIBE_REQ.put("size", 100);
        INNOVATE_PRODUCT_UNSUBSCRIBE_REQ.put("CANC_DT", dateStr);
        INNOVATE_PRODUCT_UNSUBSCRIBE_REQ.put("CNTC_CD", hnUnicomProperties.getChannelCode());
        INNOVATE_PRODUCT_UNSUBSCRIBE_REQ.put("Encrypt_CNTC_CD", "1F37C2D4734AC06F5AA4025CC51E44EF");
        Map<String, Object> UNI_BSS_BODY = new HashMap<>();
        UNI_BSS_BODY.put("INNOVATE_PRODUCT_UNSUBSCRIBE_REQ", INNOVATE_PRODUCT_UNSUBSCRIBE_REQ);

        Map<String, Object> UNI_BSS_ATTACHED = new HashMap<>();
        UNI_BSS_ATTACHED.put("MEDIA_INFO", "");

        Map<String, Object> params = new HashMap<>();
        params.put("UNI_BSS_HEAD", UNI_BSS_HEAD);
        params.put("UNI_BSS_BODY", UNI_BSS_BODY);
        params.put("UNI_BSS_ATTACHED", UNI_BSS_ATTACHED);
        JSONObject object = this.sendRemoteRequest2(PRODUCT_UNSUBSCRIBE, params, null);
        JSONObject headRsp = object.getJSONObject("UNI_BSS_HEAD");
        String code = headRsp.getString("RESP_CODE");
        if ("00000".equals(code)) {
            JSONObject unsubscribeRsp = object.getJSONObject("UNI_BSS_BODY").getJSONObject("INNOVATE_PRODUCT_UNSUBSCRIBE_RSP");
            JSONArray respData = unsubscribeRsp.getJSONObject("data").getJSONArray("list");
            return JSON.parseArray(JSON.toJSONString(respData), ProductUnsubscribeResp.class);
        } else {
            throw BizException.create(BizResult.error(code, headRsp.getString("RESP_DESC")));
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        UnicomJobFactory.registerStrategy(BenefitPayChannelEnum.HN_UNICOM.getCode(), this);
    }

    @Override
    public <T> T getOrderDetail(BenefitOrderDO benefitOrderDO, String orderId, String outOrderId, Class<T> cls) {
        if (benefitOrderDO == null) {
            return null;
        }
        JiangSuUnicomOrderDetailResp detailResp = this.productOrderDetail(benefitOrderDO.getPhone(), orderId, outOrderId);
        return BeanUtil.toBean(detailResp, cls);
    }

    @Override
    public <T> List<T> getUnsubscribeOrder(String dateStr, Class<T> cls) {
        int current = 1;
        boolean flag = true;
        List<ProductUnsubscribeResp> allList = new ArrayList<>();
        while (flag) {
            List<ProductUnsubscribeResp> list = this.getProductUnsubscribeList(dateStr, current);
            if (list.isEmpty()) {
                flag = false;
            } else {
                allList.addAll(list);
                current++;
            }
        }
        // 存在重复数据，需要去重
        List<ProductUnsubscribeResp> uniqueList = allList.stream().distinct().collect(Collectors.toList());
        return uniqueList.stream()
                .map(item -> BeanUtil.toBean(item, cls))
                .collect(Collectors.toList());
    }

}
