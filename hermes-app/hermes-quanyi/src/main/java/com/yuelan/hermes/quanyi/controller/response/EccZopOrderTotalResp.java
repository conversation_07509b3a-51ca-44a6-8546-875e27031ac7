package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class EccZopOrderTotalResp {

    @Schema(description = "订单数量")
    private Integer orderCount;

    @Schema(description = "领卡成功数量")
    private Integer successTotal;

    @Schema(description = "领卡成功转化率")
    private BigDecimal successRate;

    @Schema(description = "领卡失败数量")
    private Integer failTotal;

    @Schema(description = "领卡失败比例")
    private BigDecimal failRate;

    @Schema(description = "发货成功数量")
    private Integer sendTotal;

    @Schema(description = "激活成功数量")
    private Integer activateTotal;

    @Schema(description = "激活转化率")
    private BigDecimal activateRate;

    @Schema(description = "首冲数据同步数量")
    private Integer firstTotal;

    @Schema(description = "首充转化率")
    private BigDecimal firstRate;

}
