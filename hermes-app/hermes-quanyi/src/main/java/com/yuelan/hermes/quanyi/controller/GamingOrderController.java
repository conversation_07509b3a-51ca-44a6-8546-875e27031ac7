package com.yuelan.hermes.quanyi.controller;


import com.yuelan.hermes.quanyi.biz.service.GamingOrderItemService;
import com.yuelan.hermes.quanyi.biz.service.GamingOrderNotifyService;
import com.yuelan.hermes.quanyi.biz.service.GamingOrderService;
import com.yuelan.hermes.quanyi.controller.request.*;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.hermes.quanyi.controller.response.GamingOrderItemRsp;
import com.yuelan.hermes.quanyi.controller.response.GamingOrderListRsp;
import com.yuelan.hermes.quanyi.controller.response.GamingOrderRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@Tag(name = "电竞卡订单API")
@RequestMapping("/a/gaming/order")
@RestController
public class GamingOrderController extends AdminBaseController {

    @Autowired
    private GamingOrderService gamingOrderService;
    @Autowired
    private GamingOrderItemService gamingOrderItemService;
    @Autowired
    private GamingOrderNotifyService gamingOrderNotifyService;

    @Operation(summary = "订单列表")
    @PostMapping("/list")
    public BizResult<PageData<GamingOrderListRsp>> list(@RequestBody GamingOrderListReq req) {
        req.check();
        PageData<GamingOrderListRsp> pageData = gamingOrderService.list(req);
        return BizResult.create(pageData);
    }

    @Operation(summary = "订单详情")
    @GetMapping("/detail")
    public BizResult<GamingOrderRsp> detail(@RequestParam Long orderId) {
        GamingOrderRsp result = gamingOrderService.detail(orderId);
        return BizResult.create(result);
    }

    @Operation(summary = "订单明细")
    @GetMapping("/item")
    public BizResult<List<GamingOrderItemRsp>> item(@RequestParam Long orderId) {
        List<GamingOrderItemRsp> list = gamingOrderService.item(orderId);
        return BizResult.create(list);
    }


    @Operation(summary = "订单明细列表")
    @PostMapping("/itemList")
    public BizResult<PageData<GamingOrderItemRsp>> item(@RequestBody GamingOrderItemListReq req) {
        PageData<GamingOrderItemRsp> list = gamingOrderService.itemList(req);
        return BizResult.create(list);
    }

    @Log(title = "订单导出", type = OperationType.OTHER)
    @Operation(summary = "导出订单")
    @PostMapping("/export")
    public void export(@RequestBody GamingOrderExportReq req, HttpServletResponse response) throws IOException {
        req.check();
        gamingOrderService.export(req, response);
    }

    @Log(title = "订单导出到下载中心", type = OperationType.OTHER)
    @Operation(summary = "订单导出到下载中心")
    @PostMapping("/exportV2")
    public BizResult<FileExportTaskCreateResp> exportV2(@RequestBody GamingOrderExportReq req, HttpServletResponse response) throws IOException {
        req.check();
        return BizResult.create(gamingOrderService.exportV2(req, response));
    }


    @Log(title = "订单明细导出到下载中心", type = OperationType.OTHER)
    @Operation(summary = "订单明细导出到下载中心")
    @PostMapping("/itemExport")
    public BizResult<FileExportTaskCreateResp> itemExport(@RequestBody GamingOrderItemListReq req, HttpServletResponse response) throws IOException {
        return BizResult.create(gamingOrderItemService.itemExport(req, response));
    }



    @Log(title = "异常处理", type = OperationType.OTHER)
    @Operation(summary = "异常处理")
    @PostMapping("/manual/result")
    public BizResult<Boolean> manualResult(@Valid @RequestBody GamingOrderManualResultReq req) {
        req.check();
        Boolean result = gamingOrderService.manualResult(req);
        return BizResult.create(result);
    }

    @Log(title = "订单重试", type = OperationType.OTHER)
    @Operation(summary = "订单重试")
    @PostMapping("/supplier/retry")
    public BizResult<Boolean> supplierOrderRetry(@Valid @RequestBody GamingSupplierOrderReq req) {
        req.check();
        Boolean result = gamingOrderService.supplierOrderRetry(req);
        return BizResult.create(result);
    }

    @Operation(summary = "回调重试")
    @GetMapping("/notify/retry")
    public BizResult<Boolean> notify(@RequestParam String orderNo) {
        Boolean result = gamingOrderNotifyService.notify(orderNo);
        return BizResult.create(result);
    }
}
