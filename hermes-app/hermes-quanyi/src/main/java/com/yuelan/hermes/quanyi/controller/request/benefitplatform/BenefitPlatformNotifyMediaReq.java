package com.yuelan.hermes.quanyi.controller.request.benefitplatform;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/7/19
 * @description:
 */

@Data
public class BenefitPlatformNotifyMediaReq {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单支付是否成功")
    private boolean success;

    @Schema(description = "错误信息")
    private String msg;
}
