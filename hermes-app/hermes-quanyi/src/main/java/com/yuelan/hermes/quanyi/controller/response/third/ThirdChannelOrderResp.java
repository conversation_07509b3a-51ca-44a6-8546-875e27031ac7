package com.yuelan.hermes.quanyi.controller.response.third;

import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.interfaces.PayChannelPkgInter;
import com.yuelan.hermes.quanyi.common.pojo.domain.ThirdChannelOrderDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class ThirdChannelOrderResp {

    @Schema(description = "主键ID")
    private Long thirdOrderId;

    @Schema(description = "产品订单号")
    private String orderNo;

    @Schema(description = "第三方订单号")
    private String thirdOrderNo;

    @Schema(description = "渠道包唯一ID")
    private Integer uniqueChannelPkgId;

    @Schema(description = "渠道包名称")
    private String channelPkgName;

    @Schema(description = "渠道ID")
    private Long channelId;

    @Schema(description = "渠道名称")
    private String channelName;

    @Schema(description = "包名")
    private String packageName;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "支付状态，1支付成功2支付失败")
    private Integer payStatus;

    @Schema(description = "失败原因")
    private String payMessage;

    @Schema(description = "下单时间")
    private Date orderTime;

    @Schema(description = "同步时间")
    private Date createTime;

    public static ThirdChannelOrderResp buildResp(ThirdChannelOrderDO orderDO) {
        if (Objects.isNull(orderDO)) {
            return null;
        }
        String channelPkgName = null;
        for (BenefitPayChannelEnum channelEnum : BenefitPayChannelEnum.values()) {
            for (PayChannelPkgInter pkgInter : channelEnum.getPkgInterList()) {
                if (Objects.nonNull(pkgInter.getUniquePkgId())) {
                    if (pkgInter.getUniquePkgId().equals(orderDO.getUniqueChannelPkgId())) {
                        channelPkgName = pkgInter.getChannelPkgName();
                    }
                }
            }
        }
        ThirdChannelOrderResp resp = new ThirdChannelOrderResp();
        resp.setThirdOrderId(orderDO.getThirdOrderId());
        resp.setOrderNo(orderDO.getOrderNo());
        resp.setThirdOrderNo(orderDO.getThirdOrderNo());
        resp.setUniqueChannelPkgId(orderDO.getUniqueChannelPkgId());
        resp.setChannelPkgName(channelPkgName);
        resp.setChannelId(orderDO.getChannelId());
        resp.setChannelName(orderDO.getChannelName());
        resp.setPackageName(orderDO.getPackageName());
        resp.setPhone(orderDO.getPhone());
        resp.setPayStatus(orderDO.getPayStatus());
        resp.setPayMessage(orderDO.getPayMessage());
        resp.setOrderTime(orderDO.getOrderTime());
        resp.setCreateTime(orderDO.getCreateTime());
        return resp;
    }

}
