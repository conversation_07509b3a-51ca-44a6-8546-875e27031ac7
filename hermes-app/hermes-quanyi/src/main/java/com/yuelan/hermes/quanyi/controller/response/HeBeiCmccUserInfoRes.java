package com.yuelan.hermes.quanyi.controller.response;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2025/2/21
 * @description HeBeiUserInfoRes
 */

@NoArgsConstructor
@Data
public class HeBeiCmccUserInfoRes extends HeBeiCmccBaseRes{

    /**
     * 1代表室河北移动号码，如果返回0，则不能进行业务办理。
     */
    private String IsYD;
    /**
     * 省名字
     */
    private String ProvName;
    /**
     * 地域编号
     */
    private String Region;
    /**
     * 所在地
     */
    private String RegionName;
    /**
     * US10代表号码状态为正常在用，返回其他值时不能进行业务办理
     */
    private String UserSatus;
}
