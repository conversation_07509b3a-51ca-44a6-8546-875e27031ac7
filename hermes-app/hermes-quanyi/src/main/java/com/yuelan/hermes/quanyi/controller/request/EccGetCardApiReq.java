package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.PhoneUtil;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.util.Objects;

/**
 * <AUTHOR> 2024/5/5 下午12:59
 * <p>
 * 用户领取卡片请求
 */
@Data
public class EccGetCardApiReq {
    @NotEmpty(message = "[渠道订单号]不能为空")
    @Length(max = 64, message = "[渠道订单号]长度不能超过64位")
    @Schema(title = "渠道订单号", description = "接入方自定义订单号,需自行保持唯一", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 64)
    protected String channelOrderNo;

    @NotEmpty(message = "[身份证姓名]不能为空")
    @Schema(title = "身份证姓名", description = "身份证姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    protected String idCardName;

    @NotEmpty(message = "[身份证号码]不能为空")
    @Schema(title = "身份证号码", description = "请先进行身份证校验码算法校验身份证，再提交请求", requiredMode = Schema.RequiredMode.REQUIRED)
    protected String idCardNo;

    @NotEmpty(message = "[联系手机号码]不能为空")
    @Length(min = 11, max = 11, message = "请输入11位联系手机号码")
    @Schema(title = "联系手机号码", description = "请先进行基础手机号码验证，再提交请求", requiredMode = Schema.RequiredMode.REQUIRED)
    protected String contactPhone;

    @Schema(title = "收货地址-省份名(和省份编码二选一传)", description = "省份名-请参考提供的excel", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected String postProvince;

    @Schema(title = "收货地址-省份编码", description = "省份编码-请参考提供的excel", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected String postProvinceCode;

    @Schema(title = "收货地址-城市名(和城市编码二选一传)", description = "城市名-请参考提供的excel", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected String postCity;

    @Schema(title = "收货地址-城市编码", description = "城市编码-请参考提供的excel", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected String postCityCode;

    @Schema(title = "收货地址-区县名(和区县编码二选一传)", description = "区县名字-请参考提供的excel", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected String postDistrict;

    @Schema(title = "收货地址-区县编码", description = "区县编码-请参考提供的excel", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected String postDistrictCode;

    @NotEmpty(message = "[详细收货地址]不能为空")
    @Length(min = 4, message = "详细收货地址太短，请完善收货地址")
    @Schema(title = "详细收货地址", description = "最少4位长度，详细收货地址（不包括省市区县）", requiredMode = Schema.RequiredMode.REQUIRED)
    protected String address;

    @NotEmpty(message = "[号卡卡种唯一标识]不能为空")
    @Schema(title = "号卡卡种标识", description = "固定参数我方提供", requiredMode = Schema.RequiredMode.REQUIRED)
    protected String eccProdCode;

    @Schema(title = "订单类型", description = "订单类型：0-普通订单 1-15天下单限制订单", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected Integer orderType;

    @Schema(title = "推广页URL", description = "推广页URL", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected String pageUrl;

    @Schema(title = "回调URL，http:// 或者 https:// 开头", description = "传参不为空时候，接口返回成功后，该手机号码的激活/退单/首冲等状态回调通知", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    protected String callbackUrl;


    public void reqCheck() {
        // 名字里面允许出现·   穆罕默德·买买提·买买提
        String idCardNameCopy = idCardName.replaceAll("·", "");
        if (!Validator.isChineseName(idCardNameCopy)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "[身份证姓名]识别错误,不合法");
        }
        if (!IdcardUtil.isValidCard(getIdCardNo())) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "[身份证号码]格式错误");
        }
        if (!PhoneUtil.isMobile(contactPhone)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "[联系手机号码]格式错误");
        }
        boolean fullChinese = StringUtils.isNotBlank(postProvince) && StringUtils.isNotBlank(postCity) && StringUtils.isNotBlank(postDistrict);
        boolean fullCode = StringUtils.isNotBlank(postProvinceCode) && StringUtils.isNotBlank(postCityCode) && StringUtils.isNotBlank(postDistrictCode);
        if (!fullChinese && !fullCode) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "省市区信息不完整，请检查");
        }
        if (address.length() < 4) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "[详细收货地址太短]，请完善收货地址");
        }
        if (Objects.nonNull(callbackUrl)) {
            if (!callbackUrl.startsWith("http://") && !callbackUrl.startsWith("https://")) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "回调URL,地址不合法");
            }
        }

    }

    /**
     * 转成 EccGetCardReq 缺少渠道号
     */
    public EccBaseGetCardReq convert(EccOuterChannelDO dbChannelDO) {
        EccOuterChannelGetCardReq req = new EccOuterChannelGetCardReq();
        req.setIdCardName(idCardName);
        req.setIdCardNo(idCardNo);
        req.setContactPhone(contactPhone);
        req.setPostProvinceCode(postProvinceCode);
        req.setPostProvince(postProvince);
        req.setPostCityCode(postCityCode);
        req.setPostCity(postCity);
        req.setPostDistrictCode(postDistrictCode);
        req.setPostDistrict(postDistrict);
        req.setAddress(address);
        req.setEccProdCode(eccProdCode);
        req.setPageUrl(pageUrl);
        req.setOrderType(orderType);
        req.setChannelId(dbChannelDO.getOuterChannelId());
        req.setChannelOrderNo(channelOrderNo);
        req.setCallbackUrl(callbackUrl);
        return req;
    }


}
