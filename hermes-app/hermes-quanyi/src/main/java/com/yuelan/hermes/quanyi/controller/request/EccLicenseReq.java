package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.EnumLimit;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccLicenseDO;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2025/5/21
 * @since 2025/5/21
 */
@Data
@AutoMapper(target = EccLicenseDO.class)
public class EccLicenseReq {
    /**
     * 资源id
     */
    @Schema(description = "授权牌id")
    private Long licenseId;

    /**
     * 资源名字
     */
    @NotEmpty(message = "授权牌名称不能为空")
    @Schema(description = "授权牌名称")
    private String licenseName;

    /**
     * 运营商
     */
    @EnumLimit(message = "运营商错误", enumInterface = SpEnum.class)
    @Schema(description = "运营商：1-移动 2-联通 3-电信 4-广电")
    private Integer operator;

    /**
     * 资源 uri （不包含域名）
     */
    @NotEmpty(message = "资源 uri 不能为空")
    @Schema(description = "资源 uri （不包含域名）")
    private String uri;


}
