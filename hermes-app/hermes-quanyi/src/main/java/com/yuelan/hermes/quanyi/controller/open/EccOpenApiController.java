package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.biz.manager.NcOutChannelManager;
import com.yuelan.hermes.quanyi.biz.manager.ZopOrderManager;
import com.yuelan.hermes.quanyi.biz.service.EccAreaService;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum;
import com.yuelan.hermes.quanyi.common.enums.error.EccErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.EccGetCardResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.PhoneLocationBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import com.yuelan.hermes.quanyi.controller.request.*;
import com.yuelan.hermes.quanyi.controller.response.EccPostAreaResp;
import com.yuelan.hermes.quanyi.controller.response.EccQueryOrderApiV2Resp;
import com.yuelan.hermes.quanyi.controller.response.PhoneNumsResp;
import com.yuelan.plugins.redisson.util.RedisUtils;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/5/17 下午3:10
 */
@SaIgnore
@Slf4j
@Validated
@Tag(name = "联通号卡OpenApi")
@RequestMapping("/ecc")
@RestController
@RequiredArgsConstructor
public class EccOpenApiController {
    private static final String API_KEY = "apiKey";
    private static final String SIGN = "sign";

    private final ZopOrderManager zopOrderManager;
    private final EccAreaService eccAreaService;
    private final NcOutChannelManager ncOutChannelManager;
    private final CacheHandler cacheHandler;

    @Operation(
            summary = "随机选号版本-联通号卡领取接口",
            description = "1.该接口只可以服务端调用，领取联通号卡。"
                    + "<br>2.测试环境为模拟下单接口响应较快，正式环境要请求多次运营商接口，实际接口响应时间在5S左右，请设置合理的超时时间。"
    )
    @Parameters({
            @Parameter(name = API_KEY, in = ParameterIn.HEADER, description = "分配给对接方的ApiKey", required = true),
            @Parameter(name = SIGN, in = ParameterIn.HEADER, description = "签名", required = true)
    })
    @ApiResponse(responseCode = "200", description = "code=0,成功返回数据，其他失败,msg为错误信息")
    @PostMapping("/reqFreeCard")
    public BizResult<EccGetCardResultBO> reqFreeCard(
            @RequestHeader(API_KEY) String apiKey
            , @RequestHeader(SIGN) String apiSign
            , @RequestBody @Validated EccGetCardApiReq req) {
        log.info("领取联通号卡卡接口, apiKey={}, sign={}", apiKey, apiSign);
        req.reqCheck();
        // 校验签名
        EccOuterChannelDO dbChannelDO = ncOutChannelManager.checkIpAndSign(apiKey, apiSign, req);
        EccBaseGetCardReq getCardReq = req.convert(dbChannelDO);
        Long outerChannelId = dbChannelDO.getOuterChannelId();
        String channelOrderNo = req.getChannelOrderNo();
        RLock createLock = cacheHandler.getEccApiOrderCreateLock(outerChannelId, channelOrderNo);
        try {
            if (Objects.isNull(createLock)) {
                throw BizException.create(EccErrorCodeEnum.CHANNEL_ORDER_CREATING);
            }
            if (zopOrderManager.getOuterChannelOrderCount(outerChannelId, channelOrderNo) != 0) {
                throw BizException.create(EccErrorCodeEnum.CHANNEL_ORDER_EXIST);
            }
            EccGetCardResultBO resp = zopOrderManager.receiveSimCard(getCardReq, EccChannelTypeEnum.OUTER);
            return BizResult.create(resp);
        } finally {
            if (Objects.nonNull(createLock) && createLock.isLocked() && createLock.isHeldByCurrentThread()) {
                createLock.unlock();
            }
        }
    }


    @Operation(
            summary = "自主选号版本-选号接口",
            description = "1.该接口只可以服务端调用，返回可以选择的号码列表"
    )
    @Parameters({
            @Parameter(name = API_KEY, in = ParameterIn.HEADER, description = "分配给对接方的ApiKey", required = true),
            @Parameter(name = SIGN, in = ParameterIn.HEADER, description = "签名", required = true)
    })
    @ApiResponse(responseCode = "200", description = "code=0,成功返回数据，其他失败,msg为错误信息")
    @PostMapping("/queryPhonePool")
    public BizResult<PhoneNumsResp> selectNumber(@RequestHeader(API_KEY) String apiKey
            , @RequestHeader(SIGN) String apiSign
            , @RequestBody @Validated EccApiSelectPhoneListReq req) {
        log.info("渠道选号, apiKey={}, sign={}", apiKey, apiSign);
        String phoneLast4 = req.getPhoneLast4();
        // 校验签名
        EccOuterChannelDO dbChannelDO = ncOutChannelManager.checkIpAndSign(apiKey, apiSign, req);
        List<PhoneLocationBO> phoneNums;
        if (Objects.nonNull(phoneLast4)) {
            phoneLast4SearchCheck(phoneLast4);
            //直接调用运营商接口
            EccSelectPhoneListByPhoneLast4Req last4Req = req.convertPhoneLast4SearchReq();
            phoneNums= zopOrderManager.phoneLast4Search(last4Req);
        }else {
            // 我方虚拟号码池逻辑
            EccSelectPoolPhoneListReq selectPoolPhoneListReq = req.convertPoolReq();
            phoneNums = zopOrderManager.selectPhoneListInCachePool(selectPoolPhoneListReq);
        }
        return BizResult.create(new PhoneNumsResp(phoneNums));
    }

    @Operation(
            summary = "自主选号版本-确认下单",
            description = "1.该接口只可以服务端调用，带着选择的号码确认下单"
                    + "<br>2.比【随机选号版本-领取号卡卡接口】多了选择的手机号码相关参数"
    )
    @Parameters({
            @Parameter(name = API_KEY, in = ParameterIn.HEADER, description = "分配给对接方的ApiKey", required = true),
            @Parameter(name = SIGN, in = ParameterIn.HEADER, description = "签名", required = true)
    })
    @ApiResponse(responseCode = "200", description = "code=0,成功返回数据，其他失败,msg为错误信息")
    @PostMapping("/confirmOrder")
    public BizResult<EccGetCardResultBO> confirmOrder(@RequestHeader(API_KEY) String apiKey
            , @RequestHeader(SIGN) String apiSign
            , @RequestBody @Validated EccApiGetCardSelectPhoneReq req) {
        log.info("确认下单, apiKey={}, sign={}", apiKey, apiSign);
        req.reqCheck();
        // 校验签名
        EccOuterChannelDO dbChannelDO = ncOutChannelManager.checkIpAndSign(apiKey, apiSign, req);
        EccBaseGetCardReq confirmOrderReq = req.convert(dbChannelDO);
        Long outerChannelId = dbChannelDO.getOuterChannelId();
        String channelOrderNo = req.getChannelOrderNo();
        RLock createLock = cacheHandler.getEccApiOrderCreateLock(outerChannelId, channelOrderNo);
        try {
            if (Objects.isNull(createLock)) {
                throw BizException.create(EccErrorCodeEnum.CHANNEL_ORDER_CREATING);
            }
            if (zopOrderManager.getOuterChannelOrderCount(outerChannelId, channelOrderNo) != 0) {
                throw BizException.create(EccErrorCodeEnum.CHANNEL_ORDER_EXIST);
            }
            EccGetCardResultBO resp = zopOrderManager.receiveSimCard(confirmOrderReq, EccChannelTypeEnum.OUTER);
            return BizResult.create(resp);
        } finally {
            if (Objects.nonNull(createLock) && createLock.isLocked() && createLock.isHeldByCurrentThread()) {
                createLock.unlock();
            }
        }

    }


    @Operation(summary = "获取省份信息列表、获取省份下省市区信息", description =
            "1,该接口只可以服务端调用,接口不可频繁调用（一样参数10s只能请求一次），接入方需实现缓存或者数据入库，定期从我方接口更新" +
                    "<br>2,先请求省份列表，再按省份code遍历获取所有省市区信息" +
                    "<br>3,没有请求参数接口请传入空json`{}`")
    @Parameters({
            @Parameter(name = API_KEY, in = ParameterIn.HEADER, description = "分配给对接方的ApiKey", required = true),
            @Parameter(name = SIGN, in = ParameterIn.HEADER, description = "签名", required = true)
    })
    @ApiResponse(responseCode = "200", description = "code=0,成功返回数据，其他失败,msg为错误信息")
    @PostMapping("/areas")
    public BizResult<List<EccPostAreaResp>> areas(
            @RequestHeader(API_KEY) String apiKey
            , @RequestHeader(SIGN) String apiSign
            , @RequestBody @Validated EccPostAreasReq req) {
        EccOuterChannelDO channelDO = ncOutChannelManager.checkIpAndSign(apiKey, apiSign, req);
        reqAreaLimit(channelDO.getOuterChannelId(), req);
        if (Objects.isNull(req.getProvinceCode())) {
            return BizResult.create(eccAreaService.listProvince(SpEnum.UNICOM));
        } else {
            return BizResult.create(eccAreaService.listArea(SpEnum.UNICOM, req.getProvinceCode()));
        }

    }

    @Operation(summary = "查询订单信息", description = "订单号不存在，data节点是空json")
    @Parameters({
            @Parameter(name = API_KEY, in = ParameterIn.HEADER, description = "分配给对接方的ApiKey", required = true),
            @Parameter(name = SIGN, in = ParameterIn.HEADER, description = "签名", required = true)
    })
    @ApiResponse(responseCode = "200", description = "code=0,成功返回数据，其他失败,msg为错误信息")
    @PostMapping("/queryOrder")
    public BizResult<EccQueryOrderApiV2Resp> reqOrder(
            @RequestHeader(API_KEY) String apiKey
            , @RequestHeader(SIGN) String apiSign
            , @RequestBody @Validated EccQueryOrderApiReq req) {
        EccOuterChannelDO channelDO = ncOutChannelManager.checkIpAndSign(apiKey, apiSign, req);
        EccZopOrderDO outerChannelOrder = zopOrderManager.getOuterChannelOrder(channelDO.getOuterChannelId(), req.getChannelOrderNo());
        return BizResult.create(EccQueryOrderApiV2Resp.buildResp(outerChannelOrder));
    }


    /**
     * 请求限制 判断 一样参数10s只能请求异常
     */
    private void reqAreaLimit(Long channelId, Object req) {
        String reqBodyMd5 = SecureUtil.md5(JSONObject.toJSONString(req));
        String cacheKey = RedisKeys.getEccChannelRequestLimitKey(channelId, reqBodyMd5);
        // 10s只能请求一次
        boolean setSuccess = RedisUtils.setObjectIfAbsent(cacheKey, "", Duration.ofSeconds(10));
        if (!setSuccess) {
            throw BizException.create(EccErrorCodeEnum.CHANNEL_REQ_PROVINCE_LIMIT);
        }
    }


    private void phoneLast4SearchCheck(String phoneLast4) {
        if (Objects.isNull(phoneLast4)) {
            return;
        }
        if (!NumberUtil.isInteger(phoneLast4)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "尾号搜索参数不合法");
        }
        if (phoneLast4.length() < 2 || phoneLast4.length() > 4) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "尾号搜索参数不合法");
        }
    }


}
