package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RsCreateOrderRequest {

    @Schema(description = "荣数信息订单号")
    private String cupdOrderNo;

    @Schema(description = "数量，默认为1")
    private String num;

    @Schema(description = "第三方供应商商品编号")
    private String thirdProductNo;

    @Schema(description = "用户手机号，用于接收券码，券码类供应商必需")
    private String mobileNo;

    @Schema(description = "用户充值账号，用于充值，充值类供应商必需，包含手机号")
    private String userAccount;
}
