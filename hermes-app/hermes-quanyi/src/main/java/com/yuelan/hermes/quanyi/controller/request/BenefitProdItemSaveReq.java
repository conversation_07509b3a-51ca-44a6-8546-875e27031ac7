package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.validate.AddGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR> 2024/4/2 16:24
 */
@Data
public class BenefitProdItemSaveReq {

    @NotNull(message = "关联产品id不能为空", groups = {AddGroup.class})
    @Schema(description = "关联产品id")
    private Long prodId;


    @NotNull(message = "关联商品ids不能为空", groups = {AddGroup.class})
    @Size(min = 1, message = "关联商品ids至少1个", groups = {AddGroup.class})
    @Schema(description = "关联商品ids")
    private List<Long> goodsIds;

}
