package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.EnumLimit;
import com.yuelan.hermes.quanyi.common.enums.MgCollaborationModeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 电竞卡产品管理
 */
@Data
public class GamingProductReq {

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "产品主图")
    private String productImg;

    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = " 产品类型1点播2权益")
    private Integer type;

    @Schema(description = "咪咕合作模式：1-杭州西米（未走咪咕平台） 2-禾辰（咪咕新平台）3-长沙西米（咪咕新平台）4-杭州西米（咪咕新平台）")
    @NotNull(message = "请选择合作模式")
    @EnumLimit(message = "请选择处理结果", enumInterface = MgCollaborationModeEnum.class)
    private Integer mgModel;
}