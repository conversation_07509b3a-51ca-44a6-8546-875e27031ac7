package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GoodsVirtualDetailRsp extends GoodsVirtualRsp {

    @Schema(description = "内容")
    private String content;

    @Schema(description = "虚拟商品图片")
    private List<String> images;

}