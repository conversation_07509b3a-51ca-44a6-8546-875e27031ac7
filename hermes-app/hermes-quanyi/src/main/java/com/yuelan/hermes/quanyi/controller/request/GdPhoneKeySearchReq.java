package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/9/10 18:10
 */
@Data
public class GdPhoneKeySearchReq {
    @Schema(description = "产品编码")
    private String eccProdCode;

    @NotEmpty(message = "[归属地省份代码]不能为空")
    @Schema(description = "归属地省份代码")
    private String provinceCode;

    @NotEmpty(message = "[归属地城市代码]不能为空")
    @Schema(description = "归属地城市代码")
    private String cityCode;

    @Schema(description = "查询关键字（任意位置）最少2个字符")
    private String phoneKey;
}
