package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.core.collection.CollUtil;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/5/8 下午2:18
 */
@Data
public class EccPostAreaResp {

    @Schema(description = "省市区标识：1-省、2-市、3-区")
    private Integer level;

    @Schema(description = "地区名称")
    private String areaName;

    @Schema(description = "地区-收货编码")
    private String postCode;

    @Schema(description = "地区-号码归属地编码")
    private String numCode;

    @Schema(description = "子地区列表")
    private List<EccPostAreaResp> childrenAreaList;

    public static EccPostAreaResp buildResp(EccAreaDO eccAreaDO) {
        if (Objects.isNull(eccAreaDO)) {
            return null;
        }
        EccPostAreaResp resp = new EccPostAreaResp();
        resp.setLevel(eccAreaDO.getLevel());
        resp.setAreaName(eccAreaDO.getAreaName());
        resp.setNumCode(eccAreaDO.getNumCode());
        resp.setPostCode(eccAreaDO.getPostCode());
        // 递归
        if (!CollUtil.isEmpty(eccAreaDO.getSubAreaList())) {
            List<EccPostAreaResp> childrenAreaList = new ArrayList<>();
            for (EccAreaDO subAreaDO : eccAreaDO.getSubAreaList()) {
                EccPostAreaResp childResp = buildResp(subAreaDO);
                if (childResp != null) {
                    childrenAreaList.add(childResp);
                }
            }
            resp.setChildrenAreaList(childrenAreaList);
        }
        return resp;
    }

}
