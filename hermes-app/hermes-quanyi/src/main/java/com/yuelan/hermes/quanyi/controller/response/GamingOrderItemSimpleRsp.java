package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.ObtainStatusEnum;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PreorderStatusEnum;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO;
import com.yuelan.hermes.quanyi.common.util.RedeemCodeDesensitizedUtil;
import com.yuelan.result.entity.KeyValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 电竞卡订单明细
 *
 * <AUTHOR>
 */
@Data
public class GamingOrderItemSimpleRsp {

    @Schema(description = "订单明细ID")
    private Long itemId;

    @Schema(description = "产品id")
    private Long productId;

    @Schema(description = "产品名字")
    private String productName;

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "供应商类型")
    private KeyValue<Integer, String> supplierType;

    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;

    @Schema(description = "订单状态")
    private KeyValue<Integer, String> orderStatus;

    @Schema(description = "下发供应商状态")
    private KeyValue<Integer, String> preorderStatus;

    @Schema(description = "权益领取状态")
    private KeyValue<Integer, String> obtainStatus;


    @Schema(description = "权益领取时间")
    private LocalDateTime obtainTime;


    @Schema(description = "领取的兑换码")
    private String redeemCode;


    public static GamingOrderItemSimpleRsp buildRsp(GamingOrderItemDO obj) {
        if (Objects.isNull(obj)) {
            return null;
        }
        GamingOrderItemSimpleRsp gamingOrderItemRsp = new GamingOrderItemSimpleRsp();
        gamingOrderItemRsp.setItemId(obj.getId());
        gamingOrderItemRsp.setProductId(obj.getProductId());
        gamingOrderItemRsp.setProductName(obj.getProductName());
        gamingOrderItemRsp.setGoodsId(obj.getGoodsId());
        gamingOrderItemRsp.setGoodsName(obj.getGoodsName());
        gamingOrderItemRsp.setSupplierType(LocalEnumUtils.getEnumKeyValue(SupplierEnum.class, obj.getSupplierType()));
        gamingOrderItemRsp.setSupplierGoodsNo(obj.getSupplierGoodsNo());
        gamingOrderItemRsp.setOrderStatus(LocalEnumUtils.getEnumKeyValue(OrderStatusEnum.class, obj.getOrderStatus()));
        gamingOrderItemRsp.setPreorderStatus(LocalEnumUtils.getEnumKeyValue(PreorderStatusEnum.class, obj.getPreorderStatus()));
        gamingOrderItemRsp.setObtainStatus(LocalEnumUtils.getEnumKeyValue(ObtainStatusEnum.class, obj.getObtainStatus()));
        gamingOrderItemRsp.setObtainTime(obj.getObtainTime());
        gamingOrderItemRsp.setRedeemCode(RedeemCodeDesensitizedUtil.desensitized(obj.getRedeemCode()));
        return gamingOrderItemRsp;
    }

}