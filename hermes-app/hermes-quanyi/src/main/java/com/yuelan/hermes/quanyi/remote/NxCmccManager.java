package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.commons.util.ExecutorServiceUtils;
import com.yuelan.hermes.quanyi.biz.manager.AdManager;
import com.yuelan.hermes.quanyi.biz.service.AdChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.biz.service.BenefitPlatformService;
import com.yuelan.hermes.quanyi.common.enums.*;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitUnicomPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.AdChannelDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.NxCmccProperties;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.controller.response.nxcmcc.NxCmccRes;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @version V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NxCmccManager {

    public static final String TAG = "[宁夏移动]";
    private static final String TRUE = "true";
    // 发送二次确认短信
    private static final String GET_SMS_CODE = "/nxcmcc_ttp/api/sms/sendByProd";
    // 业务办理
    private static final String ORDER = "/nxcmcc_ttp/api/comm/trade";
    private static final int TIME_OUT = 10000;
    private static final String RSA_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCa+9u+YJcOYjpvn0vSNYYcDlkTz4aJdlvEYVeY0WAyNDtWnyDlirSCV1Y2lDmZW1B+RnGiF7XCTJf6VjLRX56BEyvo95hl2FQj5xBJbcGu5RMr2T764KdVnRRkhY/0UvJDFhqO6lumARRgG4GeeqJRGToyiBUD78cVVY4AvSX4JwIDAQAB";

    @Autowired
    private NxCmccProperties nxCmccProperties;
    @Resource
    @Lazy
    private BenefitPlatformService benefitPlatformService;

    @Resource
    private AdChannelDOService adChannelDOService;
    @Resource
    private AdManager adManager;
    @Resource
    private BenefitOrderLogService benefitOrderLogService;

    public static void main(String[] args) throws Exception {

        HttpRequest request = HttpRequest.post("https://www.nx.10086.cn/nxcmcc_ttp/api/sms/sendByProd");

        String appId = "100320";
        String appKey = "7c4df9a9361b4ee4861adefe4a06db87";
        String stamp = java.lang.String.valueOf(Instant.now().getEpochSecond());

        SortedMap<String, Object> params = new TreeMap<>();
        params.put("telnum", "14709671404");
        params.put("prodId", "********");

        request.header("appId", appId);
        request.header("timestamp", stamp);

        String jsonBody = JSON.toJSONString(params);
        JSONObject body = JSONObject.parseObject(jsonBody);
        String sign = DigestUtils.md5Hex(body + appId + appKey + stamp);
        request.header("signature", sign);
        request.contentType("application/json");
        request.body(jsonBody);
        HttpResponse execute = request.execute();
        System.out.println("返回数据:" + execute.body());

    }

    /**
     * 订阅下单
     *
     * @param req       请求参数
     * @param productDO 商品信息
     * @param orderDO   下单信息
     */
    public BenefitPayResultBO createOrder(UserBenefitOrderReq req, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        String mobile = req.getMobile();
        String smsCode = req.getSmsCode();

        BenefitUnicomPayResultBO payResultBO = new BenefitUnicomPayResultBO();
        payResultBO.setSuccess(false);
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(smsCode)) {
            log.info(TAG + "手机号或者验证码为空");
            payResultBO.setMessage("暂时无法订阅");
        }
        String prodId = this.getProdId(productDO);
        if (StringUtils.isEmpty(prodId)) {
            log.info(TAG + "业务产品编码为空");
            payResultBO.setMessage("暂时无法订阅");
        }
        NxCmccRes res = this.productOrder(mobile, smsCode, orderDO.getOrderNo(), prodId);
        if (TRUE.equals(res.getSuccess())) {
            payResultBO.setSuccess(true);
            // 更新订单状态
            orderDO.setPayStatus(PayStatusEnum.SUCCESS.getCode());
            orderDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
            // 用户支付成功事件
            sendPaySuccessEvent(orderDO);
        } else {
            payResultBO.setMessage(res.getMessage());
            orderDO.setPayStatus(PayStatusEnum.FAIL.getCode());
        }
        orderDO.setPayNotifyContent(JSON.toJSONString(res));
        orderDO.setPayNotifyTime(LocalDateTime.now());
        ExecutorServiceUtils.execute(() -> {
            try {
                // 先睡5秒,这里是为了让订单号先返回给媒体,等媒体处理好自己的流程后,再通知给媒体
                Thread.sleep(5000);
                // 通知媒体
                benefitPlatformService.notifyMediaOrderResult(orderDO, res.getMessage());
            } catch (Exception e) {
                log.info(TAG + "通知媒体失败.订单id:{}", orderDO.getOrderId(), e);
            }
        });
        return payResultBO;
    }

    /**
     * 业务受理
     *
     * @param mobile   手机号
     * @param randCode 验证码
     * @param orderNo  订单号
     */
    private NxCmccRes productOrder(String mobile, String randCode, String orderNo, String prodId) {

        TreeMap<String, Object> params = new TreeMap<>();
        params.put("telnum", mobile);
        params.put("ncode", prodId);
        params.put("type", "1");
        params.put("orderId", orderNo); // 注意是orderNo
        params.put("randCode", randCode);
        NxCmccRes res = this.sendRemoteRequest(ORDER, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER, mobile, orderNo));
        if (res == null) {
            throw BizException.create(BizErrorCodeEnum.ORDER_UNIFIED_ERROR);
        }
        return res;
    }


    /**
     * 运营商下发短信验证码
     */
    private boolean sendSmsCode(String mobile, String prodId) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("telnum", mobile);
        params.put("prodId", prodId);
        NxCmccRes res = this.sendRemoteRequest(GET_SMS_CODE, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_SEND, mobile));
        if (res == null) {
            return false;
        }
        if (TRUE.equals(res.getSuccess())) {
            return true;
        } else {
            log.info(TAG + "发送二次短信验证码失败.手机号:{} 失败原因:{}", mobile, JSON.toJSONString(res));
            throw BizException.create(BizErrorCodeEnum.SMS_SEND_ERROR, res.getMessage());
        }
    }

    /**
     * 获取业务id
     *
     * @param productDO 产品对象
     * @return 业务id
     */
    private String getProdId(BenefitProductDO productDO) {
        NxWoPayPkgEnum payPkgEnum = NxWoPayPkgEnum.of(productDO.getPayChannelPkgId());
        if (payPkgEnum == null) {
            return null;
        }
        return payPkgEnum.getProdId();
    }

    /**
     * 发送验证码短信
     *
     * @param reqParams 请求参数
     * @param productDO 商品信息
     */
    public boolean orderSendSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        String mobile = reqParams.getMobile();
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        String prodId = this.getProdId(productDO);
        NxWoPayPkgEnum payPkgEnum = NxWoPayPkgEnum.of(productDO.getPayChannelPkgId());
        if (payPkgEnum != null && payPkgEnum.isCrack()) {
            if (!saveData(payPkgEnum, mobile)) {
                return false;
            }
        }
        if (StringUtils.isEmpty(prodId)) {
            return false;
        }
        return this.sendSmsCode(mobile, prodId);
    }

    /**
     * 模拟js上报埋点数据-发送验证码前
     *
     * @param pkgEnum 产品枚举
     * @param mobile  手机号
     */
    public boolean saveData(NxWoPayPkgEnum pkgEnum, String mobile) {
        JSONObject data = new JSONObject() {{
            put("appId", nxCmccProperties.getAppId());
            put("applicationName", pkgEnum.getPkgId());
            put("title", pkgEnum.getProdId());
            // 下单页面
            put("url", "https://h5.hzyuelan.com/benefit/landingPage/iyiTss3S?channelId=22");
            // 为什么是空不知道
            put("phoneNo", "");
            // 真正手机号码
            put("params", mobile);
        }};
        String dataStr = data.toJSONString();
        String encryptedData = encryption(dataStr, 70, "_CMCC_");
        HttpRequest get = HttpRequest.get("https://www.nx.10086.cn/nxcmcc_ttp/api/pv/save?data=" + encryptedData);
        // 不确认如下header是否被校验 模拟了再说
        get.header("Accept", "*/*");
        get.header("Accept-Language", "zh-CN,zh;q=0.9");
        get.header("Origin", "https://h5.hzyuelan.com");
        get.header("Referer", "https://h5.hzyuelan.com/");
        get.header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36");
        log.info("模拟埋点数据上报:{}", get);

        HttpResponse response = get.execute();
        log.info("埋点数据上报结果:{}", response);
        //{
        //   "success": true,
        //   "code": 0,
        //   "message": "",
        //   "now": 1744016983
        // }
        JSONObject respJson = JSON.parseObject(response.body());
        Boolean success = respJson.getBoolean("success");
        String code = respJson.getString("code");

        if (!Boolean.TRUE.equals(success) || !"0".equals(code)) {
            log.error(TAG + "{}埋点数据上传失败:{}", mobile, respJson);
            return false;
        }
        return true;
    }

    public String encryption(String data, int length, String connect) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < data.length(); i += length) {
            String chunk = myRsa(data.substring(i, Math.min(i + length, data.length())));
            if (result.length() != 0) {
                result.append(connect);
            }
            result.append(chunk);
        }
        return URLEncodeUtil.encodeAll(result.toString());
    }


    /**
     * 发送远程接口
     *
     * @param path   接口路径
     * @param params 参数
     * @return 发送结果
     */
    private NxCmccRes sendRemoteRequest(String path, TreeMap<String, Object> params, BenefitOrderLog.Args args) {
        String url = nxCmccProperties.getHost() + path;
        String stamp = String.valueOf(Instant.now().getEpochSecond());
        String jsonBody = JSON.toJSONString(params);
        JSONObject body = new JSONObject(params);
        String sign = DigestUtils.md5Hex(body + nxCmccProperties.getAppId() + nxCmccProperties.getAppKey() + stamp);

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url)
                .header("appId", nxCmccProperties.getAppId())
                .header("timestamp", stamp)
                .header("signature", sign)
                .contentType("application/json")
                .body(jsonBody)
                .timeout(TIME_OUT)
                .log(BenefitPayChannelEnum.NX_CMCC, args);

        try {
            log.info(TAG + "平台请求运营商之前:{}", requestWrapper.getHttpRequest());
            HttpResponse response = benefitOrderLogService.http(requestWrapper);
            log.info(TAG + "平台请求运营商之后:{}", response);
            if (response.isOk()) {
                return JSON.parseObject(response.body(), NxCmccRes.class);
            }
            return null;
        } catch (Exception e) {
            log.info(TAG + "接口:{} 请求参数:{}", url, JSON.toJSONString(params), e);
            return null;
        }
    }

    /**
     * 支付成功广告追踪转化事件上报
     */
    private void sendPaySuccessEvent(BenefitOrderDO orderDO) {
        if (orderDO == null || orderDO.getAdChannelId() == null || StringUtils.isEmpty(orderDO.getAdExt())) {
            return;
        }
        com.alibaba.fastjson2.JSONObject adExt = com.alibaba.fastjson2.JSONObject.parseObject(orderDO.getAdExt());
        AdChannelDO adChannel = adChannelDOService.getByModuleAndAdChannelId(SysModuleEnum.BENEFIT, orderDO.getAdChannelId());
        AdChannelCodeEnum adChannelCodeEnum = AdChannelCodeEnum.of(adChannel.getAdChannelCode());
        BenefitTrackingEventEnum paySuccessTrackingEventEnum = BenefitTrackingEventEnum.PAY_SUCCESS;
        adManager.adCallBackHandlerCall(adChannelCodeEnum, adExt, paySuccessTrackingEventEnum, Boolean.FALSE);
    }

    public String myRsa(String content) {
        RSA encryptor = new RSA(null, RSA_KEY);
        return Base64.getEncoder().encodeToString(encryptor.encrypt(content.getBytes(StandardCharsets.UTF_8), KeyType.PublicKey));
    }


}
