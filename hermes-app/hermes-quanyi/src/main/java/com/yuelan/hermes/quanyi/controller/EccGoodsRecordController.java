package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.EccGoodsRecordDOService;
import com.yuelan.hermes.quanyi.controller.request.EccGoodsRecordListReq;
import com.yuelan.hermes.quanyi.controller.response.EccGoodsRecordResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR> 2024/5/1 下午9:45
 * 电商卡权益商品
 */
@Validated
@RestController
@Tag(name = "电商卡/后台api/权益商品发放记录")
@RequiredArgsConstructor
@RequestMapping("/a/ecc/goodsRecord")
public class EccGoodsRecordController {

    private final EccGoodsRecordDOService eccGoodsRecordDOService;

    @Operation(summary = "领取记录分页")
    @PostMapping("/page")
    public BizResult<PageData<EccGoodsRecordResp>> detail(@RequestBody EccGoodsRecordListReq req) {
        return BizResult.create(eccGoodsRecordDOService.pageList(req));
    }

}
