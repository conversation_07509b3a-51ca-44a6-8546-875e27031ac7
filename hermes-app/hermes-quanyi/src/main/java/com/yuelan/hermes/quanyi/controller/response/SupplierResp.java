package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.commons.enums.SupplierEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> 2024/4/12 上午11:45
 */
@Data
public class SupplierResp {
    @Schema(description = "供应商类型")
    private Integer supplierType;

    @Schema(description = "供应商名字")
    private String supplierName;


    public static SupplierResp buildResp(SupplierEnum supplierEnum) {
        if (Objects.isNull(supplierEnum)) {
            return null;
        }
        SupplierResp resp = new SupplierResp();
        resp.setSupplierType(supplierEnum.getCode());
        resp.setSupplierName(supplierEnum.getDesc());
        return resp;
    }
}
