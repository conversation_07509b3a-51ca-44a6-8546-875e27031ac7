package com.yuelan.hermes.quanyi.controller.ecommerce;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.extra.servlet.ServletUtil;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.boot.utils.HttpServletRequestUtil;
import com.yuelan.hermes.quanyi.biz.manager.Ip2RegionManager;
import com.yuelan.hermes.quanyi.biz.manager.NumberCardManager;
import com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.EccGetCardResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.PhonePrettyTagBO;
import com.yuelan.hermes.quanyi.common.util.NumCardPromptPolishingUtil;
import com.yuelan.hermes.quanyi.controller.AppEccProdConfigResp;
import com.yuelan.hermes.quanyi.controller.request.EccInnerChannelGetCardSelectPhoneReq;
import com.yuelan.hermes.quanyi.controller.request.NcPhoneKeySearchReq;
import com.yuelan.hermes.quanyi.controller.response.EccAggregatePageRegionResp;
import com.yuelan.hermes.quanyi.controller.response.EccPostAreaResp;
import com.yuelan.hermes.quanyi.controller.response.Ip2RegionResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> 2024/12/25
 * @since 2024/12/25
 */
@Tag(name = "电商卡/app/通用领卡接口")
@Slf4j
@Validated
@RestController
@RequestMapping("/ecc/numberCard")
@RequiredArgsConstructor
@SaIgnore
public class GetCardController {

    private final NumberCardManager numberCardManager;
    private final Ip2RegionManager ip2RegionManager;

    @Operation(summary = "ip归属地查询")
    @GetMapping("/ipLocation")
    public BizResult<Ip2RegionResp> ipLocation() {
        String ip = ServletUtil.getClientIP(HttpServletRequestUtil.getCurrentRequest());
        return BizResult.create(ip2RegionManager.ip2Region(ip));
    }

    @Operation(summary = "产品详情配置")
    @GetMapping("/productConfig/{productCode}")
    public BizResult<AppEccProdConfigResp> productPageResp(@PathVariable String productCode, @RequestParam(value = "pageId", required = false) Long pageId) {
        AppEccProdConfigResp productConfig = numberCardManager.productConfig(productCode, pageId);
        return BizResult.create(productConfig);
    }

    @Operation(summary = "号码搜索")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "成功", headers = {
                    @Header(name = "ext", description = "自定义的header，有概率会返回", schema = @Schema(type = "string"))
            })
    })
    @PostMapping("/phoneSearch")
    public BizResult<PageData<PhonePrettyTagBO>> phoneSearch(@Validated @RequestBody NcPhoneKeySearchReq req) {
        return BizResult.create(numberCardManager.searchPhone(req));
    }

    @Operation(summary = "领取号码接口")
    @Parameters({
            @Parameter(name = "ext", description = "如果号码搜索接口返回的header包含ext，请带入改请求中", schema = @Schema(type = "string"))
    })
    @PostMapping("/receiveSimCard")
    public BizResult<Void> receiveSimCard(@RequestBody @Validated EccInnerChannelGetCardSelectPhoneReq req) {
        try {
            EccGetCardResultBO eccGetCardResultBO = numberCardManager.receiveSimCard(req, EccChannelTypeEnum.INNER);
            if (!AppConstants.isReal()) {
                log.info("非正式环境模拟选号完成，手机号码：{}", eccGetCardResultBO.getPhone());
            }
            if (eccGetCardResultBO.isFailOrder()) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, eccGetCardResultBO.getDesc());
            }
        } catch (BizException e) {
            e.setMsg(NumCardPromptPolishingUtil.getPrompt(e.getMsg()));
            throw e;
        }
        return BizResult.ok();
    }

    @Operation(summary = "聚合页配置")
    @GetMapping("/aggregatePage")
    @Parameter(name = "aggregatePageId", description = "聚合页 id", required = false)
    public BizResult<EccAggregatePageRegionResp> getProductConfig(@RequestParam Long aggregatePageId) {
        return BizResult.create(numberCardManager.aggregatePage(aggregatePageId));
    }


    /**
     * 获取省份信息列表，只有省份这一层
     */
    @Operation(summary = "获取收货地省份信息列表")
    @GetMapping("/listProvince/{eccProdCode}")
    public BizResult<List<EccPostAreaResp>> listProvince(@PathVariable String eccProdCode) {
        return BizResult.create(numberCardManager.listProvince(eccProdCode));
    }

    /**
     * 获取某一个省份下的地区信息列表
     *
     * @param postProvinceCode 省份编码
     */
    @Operation(summary = "获取某一个省的，收货地区信息列表")
    @Parameters({
            @Parameter(name = "postProvinceCode", description = "省份编码", required = true)
    })
    @GetMapping("/listArea/{eccProdCode}")
    public BizResult<List<EccPostAreaResp>> listArea(@PathVariable String eccProdCode, @RequestParam("postProvinceCode") String postProvinceCode) {
        return BizResult.create(numberCardManager.listArea(eccProdCode, postProvinceCode));
    }


}
