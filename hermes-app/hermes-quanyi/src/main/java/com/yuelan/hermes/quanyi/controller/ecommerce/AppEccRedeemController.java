package com.yuelan.hermes.quanyi.controller.ecommerce;

import cn.dev33.satoken.session.SaSession;
import com.yuelan.hermes.commons.enums.ZopOrderStatusEnum;
import com.yuelan.hermes.quanyi.biz.manager.EccGoodsRedeemManager;
import com.yuelan.hermes.quanyi.biz.service.EccProductDOService;
import com.yuelan.hermes.quanyi.common.pojo.bo.EccUserSessionUser;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import com.yuelan.hermes.quanyi.config.satoken.StpEccUserUtil;
import com.yuelan.hermes.quanyi.controller.request.EccRedeemReq;
import com.yuelan.hermes.quanyi.controller.request.ProductCodeResp;
import com.yuelan.hermes.quanyi.controller.response.EccRedeemResp;
import com.yuelan.hermes.quanyi.controller.response.RedeemableDetailsResp;
import com.yuelan.hermes.quanyi.mapper.EccZopOrderDOMapper;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.util.Objects;

/**
 * <AUTHOR> 2024/5/8 下午2:01
 */
@Tag(name = "电商卡/app/权益兑换相关api")
@Validated
@RestController
@RequestMapping("/e/ecc")
@RequiredArgsConstructor
public class AppEccRedeemController {

    private final EccGoodsRedeemManager goodsRedeemManager;
    private final EccZopOrderDOMapper zopOrderDOMapper;
    private final EccProductDOService eccProductDOService;

    @Operation(summary = "查询最近一次用户手机号码下单成功的prodCode")
    @PostMapping("/detail/lastZopOrder")
    public BizResult<ProductCodeResp> order(){
        EccUserSessionUser sessionUser = StpEccUserUtil.getSession().getModel(SaSession.USER, EccUserSessionUser.class);
        // 查询最后一次 成功给运营商下单的订单
        int orderSyncStatus = ZopOrderStatusEnum.SUCCESS.getCode();
        EccZopOrderDO zopOrderDO = zopOrderDOMapper.selectLastZopOrder(sessionUser.getPhone(), orderSyncStatus);
        ProductCodeResp resp = new ProductCodeResp();
        if (Objects.nonNull(zopOrderDO) && Objects.nonNull(zopOrderDO.getProdId())) {
            EccProductDO productDO = eccProductDOService.getById(zopOrderDO.getProdId());
            if (Objects.nonNull(productDO)) {
                resp.setProdCode(productDO.getProdCode());
            }
        }
        return BizResult.create(resp);
    }

    @Operation(summary = "权益包详情", description = "权益包详情，包括用户领取情况")
    @PostMapping("/detail/{prodCode}")
    public BizResult<RedeemableDetailsResp> detail(@PathVariable @NotEmpty(message = "缺少权益包code") String prodCode) {
        EccUserSessionUser sessionUser = StpEccUserUtil.getSession().getModel(SaSession.USER, EccUserSessionUser.class);
        return BizResult.create(goodsRedeemManager.getRedeemableDetails(prodCode, sessionUser.getPhone()));
    }


    @Operation(summary = "权益兑换", description = "权益兑换")
    @PostMapping("/redeem")
    public BizResult<EccRedeemResp> redeem(@Validated @RequestBody EccRedeemReq eccRedeemReq) {
        EccUserSessionUser sessionUser = StpEccUserUtil.getSession().getModel(SaSession.USER, EccUserSessionUser.class);
        return BizResult.create(goodsRedeemManager.redeem(sessionUser, eccRedeemReq.getProdCode(), eccRedeemReq.getGoodsId()));
    }


}
