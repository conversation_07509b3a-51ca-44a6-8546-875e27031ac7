package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


@Data
public class GoodsVirtualSkuReq {

    @Schema(description = "SKU ID")
    private Long skuId;

    @NotBlank(message = "SKU编号不能为空")
    @Schema(description = "SKU编号")
    private String skuNo;

    @NotBlank(message = "规格名称不能为空")
    @Schema(description = "规格名称")
    private String skuName;

    @NotBlank(message = "规格图片不能为空")
    @Schema(description = "规格图片")
    private String skuImage;

    @Schema(description = "第三方商品编号")
    private String thirdNo;

    @NotNull(message = "销售价不能为空")
    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "采购价")
    private BigDecimal purchasePrice;

    @Schema(description = "面值")
    private BigDecimal faceAmount;

    @NotNull(message = "规格状态不能为空")
    @Schema(description = "状态0下架1上架")
    private Integer status;

}