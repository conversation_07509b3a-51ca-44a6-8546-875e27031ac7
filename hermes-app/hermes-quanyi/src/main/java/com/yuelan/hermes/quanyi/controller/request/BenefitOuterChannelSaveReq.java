package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class BenefitOuterChannelSaveReq {

    @NotNull(message = "外部渠道ID不能为空", groups = {EditGroup.class})
    @Schema(description = "外部渠道id")
    private Long outerChannelId;


    @NotEmpty(message = "渠道名字不能为空", groups = {AddGroup.class})
    @Schema(description = "渠道名字")
    private String platformName;

    @Schema(title = "是否禁用", description = "是否禁用：0-无效 1-正常")
    private Integer status;


    @Schema(title = "服务器白名单", description = "多个ip用英文逗号隔开")
    private String ipWhite;

    /**
     * 回调url
     */
    @Schema(description = "回调url")
    private String callBackUrl;

}
