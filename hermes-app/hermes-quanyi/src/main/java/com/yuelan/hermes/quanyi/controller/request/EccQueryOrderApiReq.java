package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/7/18 下午2:58
 * <p>
 * 对接方
 */
@Data
public class EccQueryOrderApiReq {

    @NotEmpty(message = "[渠道订单号]不能为空")
    @Length(max = 64, message = "[渠道订单号]长度不能超过64位")
    @Schema(title = "渠道订单号", description = "接入方下单时候订单号", requiredMode = Schema.RequiredMode.REQUIRED, maxLength = 64)
    private String channelOrderNo;
}
