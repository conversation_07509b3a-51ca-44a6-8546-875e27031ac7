package com.yuelan.hermes.quanyi.remote.response;

import lombok.Data;

/**
 * <AUTHOR> 2025/6/23
 * @since 2025/6/23
 */
@Data
public class HuNanDxQueryOrderApiResp {

    /**
     * 响应状态码，0表示正常，其他表示失败
     */
    private String code;

    /**
     * 响应数据主体
     */
    private BizData data;


    /**
     * 数据主体内部类，包含业务相关字段
     */
    @lombok.Data
    public static class BizData {

        /**
         * 生产前外呼单信息
         */
        private BusOppOrder busOppOrder;

        /**
         * 业务订单信息
         */
        private ServiceOrder serviceOrder;

        /**
         * 物流公司代码：1-EMS, 2-顺丰, 3-京东面对面, 4-顺丰面对面,5-集团补卡（卡速达）
         */
        private String serviceOrderCarrier;

        /**
         * 换号后业务订单信息
         */
        private HhServiceOrder hhserviceOrder;

        /**
         * 激活前充值标识，1表示激活前充值
         */
        private String isInvest;

        // Getters and Setters 省略

        /**
         * 生产前外呼单详细结构
         */
        @lombok.Data
        public static class BusOppOrder {

            /**
             * 外呼单状态：0-待处理，1-处理中，2-处理完成，3-待分配
             */
            private String tbBusOppOrderState;

            /**
             * 生产状态：0-待确认，1-生产，2-作废，3-同步失败，4-待下单，
             * 5-待转业务单，8-业务单挂起状态
             */
            private String tbBusOppOrderThState;

            /**
             * 外呼描述信息
             */
            private String tbBusOppRemark;

            /**
             * 外呼执行次数
             */
            private String tbBusOppDoCount;

            /**
             * 最新外呼时间
             */
            private String shoutTime;

            /**
             * 外呼历史备注信息
             */
            private String tbBusOppHistory;


        }

        /**
         * 业务订单详细结构
         */
        @lombok.Data
        public static class ServiceOrder {

            /**
             * 订单状态：pending-待处理,processing-处理中,completed-处理完成,ORDER_CANCEL-作废
             */
            private String serviceOrderState;

            /**
             * 订单创建时间（格式如：yyyy-MM-dd HH:mm:ss）
             */
            private String serviceOrderCreateTime;

            /**
             * 订单描述信息
             */
            private String serviceOrderRemark;

            /**
             * 订单作废原因
             */
            private String serviceOrderReason;

            /**
             * 物流单号
             */
            private String serviceOrderLogisCode;

            /**
             * 充值档次标识：
             * 0元，0-10元，10-30元，30-50元，50-70元，70-100元，100元及以上
             * 对应值：0, 010, 1030, 3050, 5070, 70100, 100
             */
            private String rechargeLevel;

            /**
             * 存费送费标识：1-有，0-无，2-曾办理但已失效
             */
            private String paySku;

            /**
             * 用户状态：1-正常，0-异常，-1-预拆机，-2-实拆机
             */
            private String prodStatusNormal;

            /**
             * CRM状态：
             * pending-待同步,CHARGE_SUCCESS-收费成功,IS_COMPLETED-已竣工,
             * WRITE_EXCEPTION-写卡异常,CHARGE_EXCEPTION-收费异常,
             * WRITE_SUCCESS-写卡成功,WRITE_FAIL-写卡失败,
             * NO_COMPLETED-未竣工,PIPEI SUCCESS-集团配卡成功
             */
            private String serviceOrderCrmStatus;

            /**
             * 物流状态代码：00-揽件，10-签收，20-拒收退回，30-派送中，40-签收中
             */
            private String serviceOrderLogisState;

            /**
             * 号码激活时间（格式如：yyyy-MM-dd HH:mm:ss）
             */
            private String serviceOrderActivationTime;

            /**
             * 激活状态：
             * 0-待激活,2-促激活，AC002-激活成功，AC001-激活失败，
             * IT001-激活异常，4-小程序/人审外呼
             */
            private String serviceOrderActivationStatus;

            /**
             * 业务订单唯一编号
             */
            private String serviceOrderOutOrderId;

            /**
             * 预占用的客户号码
             */
            private String serviceOrderCusAccPhone;

            /**
             * CPS1标识
             */
            private String cps1;

            /**
             * 生产前外呼结果记录
             */
            private String tbBusOppRgRemark;

            /**
             * 客户主动取消订单的原因记录
             */
            private String tbBusOppHistory;

            /**
             * 激活时关联的CRM单号
             */
            private String serviceOrderCrmNo;


        }

        /**
         * 换号后业务订单结构（结构字段与业务订单部分复用）
         */
        @lombok.Data
        public static class HhServiceOrder {

            /**
             * 订单状态：同业务订单状态定义
             */
            private String serviceOrderState;

            /**
             * 订单创建时间（格式如：yyyy-MM-dd HH:mm:ss）
             */
            private String serviceOrderCreateTime;

            /**
             * 物流单号
             */
            private String serviceOrderLogisCode;

            /**
             * 物流公司代码：同serviceOrderCarrier定义
             */
            private String serviceOrderCarrier;

            /**
             * CRM状态：同业务订单CRM状态定义
             */
            private String serviceOrderCrmStatus;

            /**
             * 激活状态：同业务订单激活状态定义
             */
            private String serviceOrderActivationStatus;

            /**
             * 换号后业务订单唯一编号
             */
            private String serviceOrderOutOrderId;

            /**
             * 物流状态代码：同业务订单物流状态定义
             */
            private String serviceOrderLogisState;

            /**
             * 换号后的预占用号码
             */
            private String serviceOrderCusAccPhone;
        }
    }
}