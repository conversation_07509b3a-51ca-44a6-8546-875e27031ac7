package com.yuelan.hermes.quanyi.controller.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR> 2025/3/14
 * @since 2025/3/14
 * 比赛报名
 * <p>
 * 1. 姓名
 * 2. 手机号码（格式基础判断）
 * 3. 学校
 * 4. 游戏昵称
 * 5. 角色编号
 * 6. 段位：
 * 1. 下拉选择（单选）
 * 2. 选项顺序为：凤凰蛋、恐龙蛋、鸵鸟蛋、鹅蛋、鸡蛋、鸽子蛋、鹌鹑蛋
 */
@Data
public class TournamentsSignup {

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String userName;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String phoneNumber;

    /**
     * 学校
     */
    @NotBlank(message = "学校不能为空")
    @Size(max = 50, message = "学校名称长度不能超过50个字符")
    private String school;

    /**
     * 游戏昵称
     */
    @NotBlank(message = "游戏昵称不能为空")
    @Size(max = 50, message = "游戏昵称长度不能超过50个字符")
    private String roleName;

    /**
     * 角色编号
     */
    @NotBlank(message = "角色编号不能为空")
    private String roleId;

    /**
     * 段位
     * 选项：凤凰蛋、恐龙蛋、鸵鸟蛋、鹅蛋、鸡蛋、鸽子蛋、鹌鹑蛋
     */
    @NotBlank(message = "请选择段位")
    private Integer rank;


}
