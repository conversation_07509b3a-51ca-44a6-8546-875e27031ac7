package com.yuelan.hermes.quanyi.controller;


import com.yuelan.hermes.quanyi.biz.service.OrderVirtualMQService;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "内部API")
@Slf4j
@RequestMapping("/inner")
@RestController
public class InnerController {

    @Autowired
    private OrderVirtualMQService orderVirtualMQService;

    @Operation(summary = "荣数异步通知")
    @GetMapping(value = "/rs/order/notify")
    public BizResult<Boolean> rsOrderNotify(String orderNo) {
        boolean result = orderVirtualMQService.orderComplete(orderNo);
        return BizResult.create(result);
    }


//    @Operation(summary = "初始化运营商缓存")
//    @GetMapping(value = "/isp/cache")
//    public BizResult<Integer> ispCache() {
//        int size = phoneAffiliationManager.initCache();
//        return BizResult.create(size);
//    }
}
    