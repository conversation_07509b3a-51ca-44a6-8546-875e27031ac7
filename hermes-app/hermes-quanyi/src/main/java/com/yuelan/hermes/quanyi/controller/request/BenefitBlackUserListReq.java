package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitBlackUserDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class BenefitBlackUserListReq extends PageRequest {

    /**
     * 支付通道id
     */
    @Schema(description = "支付通道id")
    private Integer payChannelId;

    /**
     * 支付通道id
     */
    @Schema(description = "支付通道包id")
    private Integer payChannelPkgId;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;
    /**
     * 权益包id
     */
    @Schema(description = "包名")
    private String packageName;

    @Schema(description = "查询业务类型 1-手机号查询 2-包名查询 默认查询全部")
    private Integer bizType;

    public Wrapper<BenefitBlackUserDO> buildQueryWrapper() {
        LambdaQueryWrapper<BenefitBlackUserDO> wrapper = Wrappers.<BenefitBlackUserDO>lambdaQuery()
                .eq(payChannelId != null, BenefitBlackUserDO::getPayChannelId, payChannelId)
                .eq(payChannelPkgId != null, BenefitBlackUserDO::getPayChannelPkgId, payChannelPkgId)
                .eq(bizType != null, BenefitBlackUserDO::getBizType, bizType)
                .orderByDesc(BenefitBlackUserDO::getId);
        List<String> bizValues = Lists.newArrayList();
        if (StringUtils.isNotBlank(phone)){
            bizValues.add(phone);
        }
        if (StringUtils.isNotBlank(packageName)){
            bizValues.add(packageName);
        }
        wrapper.in(CollectionUtils.isNotEmpty(bizValues),BenefitBlackUserDO::getBizValue,bizValues);
        return wrapper;
    }
}
