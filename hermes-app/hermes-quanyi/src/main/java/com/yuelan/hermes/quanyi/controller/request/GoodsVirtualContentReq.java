package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class GoodsVirtualContentReq {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 商品ID
     */
    @Schema(description = "商品ID")
    private Long goodsId;

    /**
     * 内容
     */
    @Schema(description = "商品详情")
    private String content;

}