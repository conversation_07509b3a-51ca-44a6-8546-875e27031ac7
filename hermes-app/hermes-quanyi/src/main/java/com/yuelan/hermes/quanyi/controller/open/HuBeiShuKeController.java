package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.handler.impl.BenefitHuBeiShuKePayChannel;
import com.yuelan.hermes.quanyi.remote.hbsk.response.HuBeiShiKeCallBackResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> 2025/7/18
 * @since 2025/7/18
 */
@SaIgnore
@Slf4j
@Tag(name = "湖北数科API")
@RequestMapping("/hbsk")
@RestController
@RequiredArgsConstructor
public class HuBeiShuKeController {

    private final BenefitHuBeiShuKePayChannel huBeiShuKePayChannel;

    @Operation(summary = "湖北数科回调")
    @PostMapping("/order/notify")
    public HuBeiShiKeCallBackResp payNotify(HttpServletRequest request) {
        try {
            return huBeiShuKePayChannel.dealCallBack(request);
        } catch (Exception e) {
            log.error("湖北数科回调异常", e);
            return HuBeiShiKeCallBackResp.error("处理异常");
        }


    }
}
