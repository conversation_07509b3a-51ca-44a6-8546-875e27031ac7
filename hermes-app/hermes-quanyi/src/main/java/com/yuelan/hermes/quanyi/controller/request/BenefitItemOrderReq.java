package com.yuelan.hermes.quanyi.controller.request;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yuelan.core.util.MapstructUtils;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderExtensionDO;
import com.yuelan.result.entity.PageRequest;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> 2025/4/28
 * @since 2025/4/28
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMapper(target = BenefitItemOrderDO.class)
public class BenefitItemOrderReq extends PageRequest {

    @Schema(description = "权益订单id")
    private Long itemOrderId;

    @Schema(description = "关联订单扩展id")
    private Long extensionId;

    @Schema(description = "订单类型：1-原始订单 2-补发订单")
    private Integer orderType;

    @Schema(description = "原始权益订单id(只有订单=补发订单才有该值)")
    private Long originalItemOrderId;

    @Schema(description = "向供应商下单订单号（一般用扩展信息里面的order_no填充，部分供应商可能存在特殊订单号格式要求）")
    private String supplierRequestOrder;

    @Schema(description = "权益id")
    private Long benefitItemId;

    @Schema(description = "权益名字")
    private String benefitItemName;

    @Schema(description = "发放时机：1-即时发放  2-用户领取")
    private Integer dispatchTiming;

    @Schema(description = "发货类型 1-直充 2-兑换码")
    private Integer deliveryType;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "供应商类型")
    private Integer supplierId;

    @Schema(description = "供应商名字")
    private String supplierName;

    @Schema(description = "供应商响应的订单号")
    private String supplierOrderNo;

    @Schema(description = "兑换码id（兑换码存在我们这边才有）")
    private Long redeemCodeId;

    @Schema(description = "兑换码cdk")
    private String redeemCode;

    @Schema(description = "兑换码卡密")
    private String redeemCodePwd;

    @Schema(description = "兑换码有效期")
    private LocalDateTime redeemCodeExpireTime;

    @Schema(description = "订单状态：0-下发中 1-成功 2-失败")
    private Integer orderStatus;

    @Schema(description = "执行状态:0-待请求 1-请求成功（等待回调）2-请求失败 3-下发成功 4-下发失败 5-回调超时")
    private Integer processState;

    @Schema(description = "请求时间-开始")
    private LocalDateTime requestTimeStart;

    @Schema(description = "请求时间-结束")
    private LocalDateTime requestTimeEnd;

    @Schema(description = "核销状态（仅兑换码类型）：0-无核销通知 1-待核销 2-核销成功")
    private Integer redemptionStatus;

    @Schema(title = "回调时间-开始")
    private LocalDateTime callbackTimeStart;

    @Schema(description = "回调时间-结束")
    private LocalDateTime callbackTimeEnd;

    @Schema(description = "创建时间")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    // 以下字段是join表的字段
    @Schema(description = "渠道订单号")
    private String channelOrderNo;

    @Schema(description = "我方生成的订单号")
    private String orderNo;

    @Schema(description = "渠道id")
    private Integer channelId;

    @Schema(description = "渠道名字")
    private String channelName;

    @Schema(description = "支付渠道id")
    private Integer payChannelId;

    @Schema(description = "支付渠道名字")
    private String payChannelName;

    @Schema(description = "支付产品包id")
    private Integer payPkgId;

    @Schema(description = "支付产品包名")
    private String payPkgName;


    public MPJLambdaWrapper<BenefitItemOrderDO> buildQueryWrapper(boolean asc) {
        BenefitItemOrderDO orderDO = MapstructUtils.convertNotNull(this, BenefitItemOrderDO.class);
        boolean isMobileLike = Objects.nonNull(orderDO.getMobile()) && orderDO.getMobile().toString().length() != 11;
        return new MPJLambdaWrapper<BenefitItemOrderDO>()
                .selectAll(BenefitItemOrderDO.class)
                .leftJoin(BenefitOrderExtensionDO.class, BenefitOrderExtensionDO::getExtensionId, BenefitItemOrderDO::getExtensionId)
                .setEntity(orderDO)
                // 主表模糊查询的几个字段
                .likeRight(isMobileLike, BenefitItemOrderDO::getMobile, orderDO.getMobile())
                .ge(callbackTimeStart != null, BenefitItemOrderDO::getCallbackTime, callbackTimeStart)
                .le(callbackTimeEnd != null, BenefitItemOrderDO::getCallbackTime, callbackTimeEnd)
                .ge(requestTimeStart != null, BenefitItemOrderDO::getRequestTime, requestTimeStart)
                .le(requestTimeEnd != null, BenefitItemOrderDO::getRequestTime, requestTimeEnd)
                .ge(createTimeStart != null, BenefitItemOrderDO::getCreateTime, createTimeStart)
                .le(createTimeEnd != null, BenefitItemOrderDO::getCreateTime, createTimeEnd)
                // join表
                .eq(channelOrderNo != null, BenefitOrderExtensionDO::getChannelOrderNo, channelOrderNo)
                .eq(orderNo != null, BenefitOrderExtensionDO::getOrderNo, orderNo)
                .eq(channelId != null, BenefitOrderExtensionDO::getChannelId, channelId)
                .eq(channelName != null, BenefitOrderExtensionDO::getChannelName, channelName)
                .eq(payChannelId != null, BenefitOrderExtensionDO::getPayChannelId, payChannelId)
                .eq(payChannelName != null, BenefitOrderExtensionDO::getPayChannelName, payChannelName)
                .eq(payPkgId != null, BenefitOrderExtensionDO::getPayPkgId, payPkgId)
                .eq(payPkgName != null, BenefitOrderExtensionDO::getPayPkgName, payPkgName)
                .orderBy(true, asc, BenefitItemOrderDO::getItemOrderId);
    }

}
