package com.yuelan.hermes.quanyi.controller.open;

import com.yuelan.hermes.quanyi.biz.service.RsOrderService;
import com.yuelan.hermes.quanyi.controller.request.RsRequest;
import com.yuelan.hermes.quanyi.controller.response.RsResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Tag(name = "荣数API")
@RequestMapping("/rs")
@RestController
public class RsOrderController {

    @Autowired
    private RsOrderService rsOrderService;

    @Operation(summary = "下单")
    @PostMapping(value = "/order/unified", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public RsResult createOrder(@Valid RsRequest request) {
        return rsOrderService.createOrder(request);
    }

}
