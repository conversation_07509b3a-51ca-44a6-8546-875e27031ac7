package com.yuelan.hermes.quanyi.controller.response;

import lombok.Data;

@Data
public class BWOrderNotifyRsp {

    private boolean success;

    public static BWOrderNotifyRsp create(boolean success) {
        BWOrderNotifyRsp rsp = new BWOrderNotifyRsp();
        rsp.setSuccess(success);
        return rsp;
    }

    public static BWOrderNotifyRsp success() {
        BWOrderNotifyRsp rsp = new BWOrderNotifyRsp();
        rsp.setSuccess(true);
        return rsp;
    }

    public static BWOrderNotifyRsp error() {
        BWOrderNotifyRsp rsp = new BWOrderNotifyRsp();
        rsp.setSuccess(false);
        return rsp;
    }
}
