package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.biz.service.EccChannelDOService;
import com.yuelan.hermes.quanyi.config.satoken.StpAdminUtil;
import com.yuelan.hermes.quanyi.controller.request.EccChannelListReq;
import com.yuelan.hermes.quanyi.controller.request.EccChannelSaveReq;
import com.yuelan.hermes.quanyi.controller.response.EccChannelResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> 2024/5/3 上午11:21
 * <p>
 * 电商卡推广渠道
 */
@Validated
@RestController
@Tag(name = "电商卡/后台api/推广渠道管理")
@RequiredArgsConstructor
@RequestMapping("/a/ecc/channel")
public class EccChannelController {

    private final EccChannelDOService eccChannelDOService;

    /**
     * 电商卡推广渠道分页
     */
    @Operation(summary = "电商卡推广渠道分页")
    @PostMapping("/list")
    public BizResult<PageData<EccChannelResp>> list(@RequestBody @Validated EccChannelListReq req) {
        return BizResult.create(eccChannelDOService.page(req));
    }

    @Operation(summary = "电商卡推广渠道选择项")
    @PostMapping("/selectOptions")
    public BizResult<List<EccChannelResp>> selectOptions() {
        return BizResult.create(eccChannelDOService.selectOptionsByAdmin());
    }
    /**
     * 新增电商卡推广渠道
     */
    @Log(title = "新增电商卡推广渠道", type = OperationType.INSERT)
    @Operation(summary = "新增电商卡推广渠道")
    @PostMapping("/save")
    public BizResult<Void> save(@Validated(AddGroup.class) @RequestBody EccChannelSaveReq req) {
        eccChannelDOService.save(req);
        return BizResult.ok();
    }

    /**
     * 编辑电商卡推广渠道
     */
    @Log(title = "编辑电商卡推广渠道", type = OperationType.UPDATE)
    @Operation(summary = "编辑电商卡推广渠道")
    @PostMapping("/edit")
    public BizResult<Void> edit(@Validated(EditGroup.class) @RequestBody EccChannelSaveReq req) {
        eccChannelDOService.updateById(req);
        return BizResult.ok();
    }
}
