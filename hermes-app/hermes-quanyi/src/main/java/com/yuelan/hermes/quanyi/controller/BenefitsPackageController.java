package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.core.validator.validate.QueryGroup;
import com.yuelan.hermes.quanyi.biz.service.BenefitsPackageService;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageDO;
import com.yuelan.hermes.quanyi.controller.request.*;
import com.yuelan.hermes.quanyi.controller.response.BenefitsPackageResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@Tag(name = "权益组合包/后台接口/权益包管理")
@RequiredArgsConstructor
@RequestMapping("/a/benefits/package")
public class BenefitsPackageController {

    private final BenefitsPackageService benefitsPackageService;

    @Operation(summary = "权益组合包列表")
    @PostMapping("/list")
    public BizResult<PageData<BenefitsPackageResp>> list(@Validated(QueryGroup.class) @RequestBody BenefitsPackageListReq req) {
        return BizResult.create(benefitsPackageService.list(req));
    }

    @Operation(summary = "权益组合包详情")
    @GetMapping("/detail/{packageId}")
    public BizResult<BenefitsPackageResp> detail(@PathVariable Long packageId) {
        BenefitsPackageDO packageDO = benefitsPackageService.getById(packageId);
        return BizResult.create(BenefitsPackageResp.buildResp(packageDO));
    }

    @Log(title = "新增权益包", type = OperationType.INSERT)
    @Operation(summary = "新增权益组合包")
    @PostMapping("/save")
    public BizResult<Void> save(@Validated(AddGroup.class) @RequestBody BenefitsPackageSaveReq req) {
        benefitsPackageService.save(req);
        return BizResult.ok();
    }

    @Log(title = "编辑权益包", type = OperationType.UPDATE)
    @Operation(summary = "编辑权益组合包")
    @PostMapping("/edit")
    public BizResult<Void> edit(@Validated(EditGroup.class) @RequestBody BenefitsPackageSaveReq req) {
        benefitsPackageService.update(req);
        return BizResult.ok();
    }

    @Log(title = "更新最大兑换次数", type = OperationType.UPDATE)
    @Operation(summary = "更新最大兑换次数")
    @PostMapping("/updateLimit")
    public BizResult<Void> updateLimit(@Validated(EditGroup.class) @RequestBody BenefitsPackageLimitReq req) {
        benefitsPackageService.updateLimit(req);
        return BizResult.ok();
    }

    @Log(title = "权益包上架/下架", type = OperationType.UPDATE)
    @Operation(summary = "权益组合包上架/下架")
    @PostMapping("/updateStatus")
    public BizResult<Void> updateStatus(@Validated(EditGroup.class) @RequestBody BenefitsPackageStatusReq req) {
        req.checkReq();
        benefitsPackageService.updateStatus(req);
        return BizResult.ok();
    }

}
