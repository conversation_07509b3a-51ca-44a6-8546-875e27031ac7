package com.yuelan.hermes.quanyi.controller.open;

import cn.hutool.extra.servlet.ServletUtil;
import com.yuelan.hermes.quanyi.biz.service.FeiHanService;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.00
 * @description: 飞翰ofpay
 * @ClassName: FeiHanController
 * @Date 2024/3/28 18:43
 */
@Slf4j
@Tag(name = "飞翰ofpay")
@RequestMapping("/feihan")
@RestController
public class FeiHanController {
    @Autowired
    private FeiHanService feiHanService;

    /**
     * 飞翰ofpay直充卡密类onlineOrder回调接口
     * http://openapi.ofpay.com/web.do#/webInterfaceDetail/12f28e4b-62fc-4d97-bb16-6b1beafd8e0f
     * ret_code=1&sporder_id=test001234567&ordersuccesstime=20160817140214&err_msg=
     */
    @Operation(summary = "飞翰ofpay直充卡密类onlineOrder回调接口")
    @PostMapping(value = "/callback/onlineOrder", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public void onlineOrderCallback(HttpServletRequest request) {
        Map<String, String> callbackMap = ServletUtil.getParamMap(request);
        log.info("飞翰ofpay直充卡密类onlineOrder回调参数 -> {}", callbackMap);
        feiHanService.onlineOrderCallback(callbackMap);
    }

    /**
     * 飞翰ofpay直充卡密类onlineOrder回调接口
     * http://openapi.ofpay.com/web.do#/webInterfaceDetail/12f28e4b-62fc-4d97-bb16-6b1beafd8e0f
     * ret_code=1&sporder_id=test001234567&ordersuccesstime=20160817140214&err_msg=
     */
    @Operation(summary = "飞翰ofpay直充卡密类offlineOrder回调接口")
    @PostMapping(value = "/callback/offlineOrder", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public void offlineOrderCallback(HttpServletRequest request) {
        Map<String, String> callbackMap = ServletUtil.getParamMap(request);
        log.info("飞翰ofpay直充卡密类offlineOrder回调参数 -> {}", callbackMap);
        feiHanService.offlineOrderCallback(callbackMap);
    }

    /**
     * 飞翰ofpay根据外部订单号查询订单充值状态接口。此接口用户下单之后查询充值状态。
     * http://openapi.ofpay.com/web.do#/webInterfaceDetail/47452c8d-5a10-429f-b2f7-56422bbe5704
     * 返回0、1、9、-1  0 表示充值中，充值中的订单需要等待结果。9 充值失败，可以给客户退款。1 充值成功。
     * -1 表示查不到此订单，此时不能作为失败处理该订单，需要联系欧飞人工核实。
     *
     * @param spOrderNo sp订单号
     */
    @Operation(summary = "飞翰ofpay根据外部订单号查询订单充值状态接口")
    @RequestMapping(value = "/order/query", method = RequestMethod.GET)
    public BizResult<Object> queryOrderStatus(@RequestParam("spOrderNo") String spOrderNo) {
        log.info("飞翰ofpay根据外部订单号查询订单充值状态请求参数 -> {}", spOrderNo);
        return BizResult.create(feiHanService.queryOrderStatus(spOrderNo).getCode());
    }
}