package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class RytOrderNotifyReq {

    @Schema(description = "商户订单号")
    private String orderId;

    @Schema(description = "1：充值中，2：充值成功；3充值失败")
    private Integer code;

    @Schema(description = "商户编号")
    private String businessCode;

    @Schema(description = "系统订单号")
    private String orderNo;

    @Schema(description = "签名")
    private String sign;

}
