package com.yuelan.hermes.quanyi.controller;


import com.yuelan.hermes.quanyi.biz.service.MerchantService;
import com.yuelan.hermes.quanyi.config.satoken.context.AdminContext;
import com.yuelan.hermes.quanyi.controller.request.MerchantListReq;
import com.yuelan.hermes.quanyi.controller.request.MerchantRegisterReq;
import com.yuelan.hermes.quanyi.controller.response.MerchantRegisterRsp;
import com.yuelan.hermes.quanyi.controller.response.MerchantRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(name = "商户管理")
@RestController
@RequestMapping("/a/merchant")
public class MerchantController extends AdminBaseController {
    @Autowired
    private MerchantService merchantService;

    @Operation(summary = "商户列表")
    @PostMapping("/list")
    public BizResult<PageData<MerchantRsp>> list(@Valid @RequestBody MerchantListReq req) {
        req.check();
        PageData<MerchantRsp> pageData = merchantService.list(req);
        return BizResult.create(pageData);
    }

    @Log(title = "新增商户", type = OperationType.INSERT)
    @Operation(summary = "新增商户")
    @PostMapping("/add")
    public BizResult<MerchantRegisterRsp> add(@Valid @RequestBody MerchantRegisterReq req) {
        req.check();
        AdminContext loginUser = this.getLoginUser();
        MerchantRegisterRsp result = merchantService.add(req, loginUser);
        return BizResult.create(result);
    }

    @Log(title = "重置商户密码", type = OperationType.UPDATE)
    @Operation(summary = "重置密码")
    @PostMapping("/{merchantId}/password/reset")
    public BizResult<MerchantRegisterRsp> rePassword(@PathVariable Long merchantId) {
        AdminContext loginUser = this.getLoginUser();
        MerchantRegisterRsp result = merchantService.rePassword(merchantId, loginUser);
        return BizResult.create(result);
    }
}
