package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.core.validator.validate.QueryGroup;
import com.yuelan.hermes.quanyi.biz.service.BenefitItemService;
import com.yuelan.hermes.quanyi.biz.service.BenefitsPackageItemService;
import com.yuelan.hermes.quanyi.controller.request.BenefitItemListReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageItemSaveReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitsPackageItemSortReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitItemResp;
import com.yuelan.hermes.quanyi.controller.response.BenefitsPackageItemListResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@Tag(name = "权益组合包/后台接口/权益包管理/权益包 商品关联关系")
@RequiredArgsConstructor
@RequestMapping("/a/benefits/packageItem")
public class BenefitsPackageItemController {

    private final BenefitItemService benefitItemService;
    private final BenefitsPackageItemService benefitsPackageItemService;

    @Operation(summary = "查询商品列表")
    @PostMapping("/itemList")
    public BizResult<PageData<BenefitItemResp>> itemList(@Validated(QueryGroup.class) @RequestBody BenefitItemListReq req) {
        return BizResult.create(benefitItemService.list(req));
    }

    @Operation(summary = "查询权益组合包关联商品列表")
    @GetMapping("/list/{packageId}")
    public BizResult<BenefitsPackageItemListResp> list(@NotNull(message = "权益包id不能为null") @PathVariable Long packageId) {
        return BizResult.create(benefitsPackageItemService.listByPackageId(packageId));
    }

    @Log(title = "批量新增权益组合包关联商品", type = OperationType.INSERT)
    @Operation(summary = "批量新增权益包关联商品")
    @PostMapping("/add")
    public BizResult<Void> add(@Validated(AddGroup.class) @RequestBody BenefitsPackageItemSaveReq req) {
        req.checkReq();
        benefitsPackageItemService.add(req);
        return BizResult.ok();
    }

    @Log(title = "更新权益组合包内商品排序", type = OperationType.UPDATE)
    @Operation(summary = "更新权益包内商品排序")
    @PostMapping("/updateSort")
    public BizResult<Void> updateSort(@Validated(EditGroup.class) @RequestBody BenefitsPackageItemSortReq req) {
        req.checkReq();
        benefitsPackageItemService.updateSort(req);
        return BizResult.ok();
    }

    @Log(title = "删除权益组合包关联商品", type = OperationType.DELETE)
    @Operation(summary = "删除权益包关联商品")
    @DeleteMapping("/delete/{packageItemId}")
    @Transactional(rollbackFor = Exception.class)
    public BizResult<Boolean> delete(@NotNull(message = "权益包itemId不能为null") @PathVariable Long packageItemId) {
        benefitsPackageItemService.delete(packageItemId);
        return BizResult.ok();
    }
}
