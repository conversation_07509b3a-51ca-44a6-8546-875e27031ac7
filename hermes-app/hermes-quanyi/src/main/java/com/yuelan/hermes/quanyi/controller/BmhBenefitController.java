package com.yuelan.hermes.quanyi.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.manager.BmhCdKeyUseManager;
import com.yuelan.hermes.quanyi.controller.response.BmhCDKNotifyReq;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2025/1/20
 * @description BmhBenefitController
 */
@Validated
@RestController
@Tag(name = "权益N选1/客户端接口/爆米花权益api")
@RequiredArgsConstructor
@RequestMapping("/bmh")
public class BmhBenefitController {

    @Resource
    private BmhCdKeyUseManager bmhCdKeyUseManager;
    @Operation(summary = "爆米花cdk核销通知接口")
    @SaIgnore
    @PostMapping(value = "/cdk/exchange/notify")
    public BizResult<Boolean> cdkExchangeNotify(@RequestBody BmhCDKNotifyReq params) {
        return BizResult.create(bmhCdKeyUseManager.deal(params));
    }
}
