package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.EnumLimit;
import com.yuelan.hermes.commons.enums.OrderOnlineManualResultEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class OrderOnlineManualResultReq {

    @NotBlank(message = "订单号不能为空")
    @Schema(description = "订单号")
    private String orderNo;

    @EnumLimit(message = "请选择处理结果", enumInterface = OrderOnlineManualResultEnum.class)
    @Schema(description = "处理结果 0失败,1成功,2补发")
    private Integer flag;

    @Schema(description = "备注")
    private String remark;
}