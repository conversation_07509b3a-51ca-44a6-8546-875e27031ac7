package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.properties.TeZhenProperties;
import com.yuelan.hermes.quanyi.remote.request.TezhenBaseReq;
import com.yuelan.hermes.quanyi.remote.request.VProductRechargeReq;
import com.yuelan.hermes.quanyi.remote.request.VProductReportReq;
import com.yuelan.hermes.quanyi.remote.response.VProductBalanceRsp;
import com.yuelan.hermes.quanyi.remote.response.VProductRechargeRsp;
import com.yuelan.hermes.quanyi.remote.response.VProductReportRsp;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;

@Slf4j
@Component
public class TezhenManager {

    private static final String HOST = "http://api.llxem.com";
    //虚拟产品下单
    private static final String V_PRODUCT_RECHARGE = HOST + "/api/vproductrecharge";
    //查单
    private static final String V_PRODUCT_REPORT = HOST + "/api/vproductreport";
    //余额查询
    private static final String V_PRODUCT_BALANCE = HOST + "/api/vproductbalance";

    @Autowired
    private TeZhenProperties teZhenProperties;

    /**
     * 虚拟产品下单
     */
    public VProductRechargeRsp vproductrecharge(String orderNo, String mobile, String productType, String reportUrl, StringBuilder reqBuild, StringBuilder respBuild) {
        if (!AppConstants.isReal()) {
            VProductRechargeRsp rechargeRsp = new VProductRechargeRsp();
            rechargeRsp.setOrderId(RandomUtil.randomString(10));
            rechargeRsp.setLinkId(orderNo);
            rechargeRsp.setMobile(mobile);
            rechargeRsp.setSellBalance(BigDecimal.ONE);
            rechargeRsp.setAccount(teZhenProperties.getAccount());
            rechargeRsp.setCode("000");
            rechargeRsp.setMsg("提交成功");
            return rechargeRsp;
        }
        VProductRechargeReq req = new VProductRechargeReq();
        req.setProductType(productType);
        req.setMobile(mobile);
        req.setPackagesize("1");
        req.setLinkId(orderNo);
        req.setReporturl(reportUrl);
        req.setAccount(teZhenProperties.getAccount());
        req.setTimestamp(System.currentTimeMillis() + "");
        req.setSign(sign(req.getTimestamp()));

        String body = JSON.toJSONString(req);
        reqBuild.append(body);
        VProductRechargeRsp rechargeRsp = null;
        try {
            String result = HttpUtil.post(V_PRODUCT_RECHARGE, body, 10000);
            respBuild.append(result);
            log.info("特祯充值下单.body:{},result:{}", body, result);
            rechargeRsp = JSON.parseObject(result, VProductRechargeRsp.class);
        } catch (Exception e) {
            log.error("特祯充值下单异常.body:{}", body, e);
            throw BizException.create(BizErrorCodeEnum.TEZHEN_ERROR, "充值下单失败");
        }
        if (Objects.isNull(rechargeRsp) || !rechargeRsp.isSuccess()) {
//            log.error("特祯充值下单失败.body:{},result:{}", body, rechargeRsp);
            throw BizException.create(BizErrorCodeEnum.TEZHEN_ERROR, "充值下单失败");
        }
        return rechargeRsp;
    }

    /**
     * 查单
     */
    public VProductReportRsp vproductreport(String orderNo, String outOrderNo) {
        VProductReportReq req = new VProductReportReq();
        req.setOrderId(outOrderNo);
        req.setLinkId(orderNo);
        req.setAccount(teZhenProperties.getAccount());
        req.setTimestamp(System.currentTimeMillis() + "");
        req.setSign(sign(req.getTimestamp()));

        String body = JSON.toJSONString(req);
        VProductReportRsp reportRsp = null;
        try {
            String result = HttpUtil.post(V_PRODUCT_REPORT, body, 10000);
            log.info("特祯订单查询.body:{},result:{}", body, result);
            reportRsp = JSON.parseObject(result, VProductReportRsp.class);
        } catch (Exception e) {
            log.error("特祯订单查询异常.body:{}", body, e);
            throw BizException.create(BizErrorCodeEnum.TEZHEN_ERROR, "查询订单失败");
        }
        if (Objects.isNull(reportRsp) || !reportRsp.isSuccess()) {
//            log.error("特祯订单查询失败.body:{},result:{}", body, reportRsp);
            throw BizException.create(BizErrorCodeEnum.TEZHEN_ERROR, "查询订单失败");
        }
        return reportRsp;
    }

    /**
     * 余额查询
     */
    public VProductBalanceRsp vproductbalance() {
        TezhenBaseReq req = new TezhenBaseReq();
        req.setAccount(teZhenProperties.getAccount());
        req.setTimestamp(System.currentTimeMillis() + "");
        req.setSign(sign(req.getTimestamp()));

        String body = JSON.toJSONString(req);
        VProductBalanceRsp balanceRsp = null;
        try {
            String result = HttpUtil.post(V_PRODUCT_BALANCE, body, 10000);
            log.info("特祯余额查询.body:{},result:{}", body, result);
            balanceRsp = JSON.parseObject(result, VProductBalanceRsp.class);
        } catch (Exception e) {
            log.error("特祯余额查询异常.body:{}", body, e);
            throw BizException.create(BizErrorCodeEnum.TEZHEN_ERROR, "查询余额失败");
        }
        if (Objects.isNull(balanceRsp) || !balanceRsp.isSuccess()) {
//            log.error("特祯余额查询失败.body:{},result:{}", body, balanceRsp);
            throw BizException.create(BizErrorCodeEnum.TEZHEN_ERROR, "查询余额失败");
        }
        return balanceRsp;
    }

    /**
     * 计算签名
     */
    private String sign(String timestamp) {
        String signData = "account=" + teZhenProperties.getAccount() +
                "&timestamp=" + timestamp +
                "&key=" + teZhenProperties.getSecretKey();
//        log.info("特祯签名原串：{}", signData);
        return DigestUtil.md5Hex(signData);
    }

}
