package com.yuelan.hermes.quanyi.remote.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2025/1/18
 * @description BmhCreatCDKResp
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BmhCreatCDKResp {

    /**
     * 主键
     */
    private String id;
    /**
     * CDK的唯一识别码
     */
    private String code;
    /**
     * cdk类型
     */
    private int type;
    /**
     * CDK的状态
     */
    private int status;
    /**
     * CDK的有效期截止日期 毫秒
     * 1740038811840
     */
    private Long expirationDate;
}
