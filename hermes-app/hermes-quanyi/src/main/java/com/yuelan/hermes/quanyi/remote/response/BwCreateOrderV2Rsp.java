package com.yuelan.hermes.quanyi.remote.response;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class BwCreateOrderV2Rsp {
    /**
     * 商户编号（由变蛙提供）
     */
    private Long bankId;
    /**
     * 产品编号（由变蛙提供）
     */
    private Long productId;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 商品ID
     */
    private Long goodsId;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 变蛙订单号
     */
    private String orderNo;
    /**
     * 商户订单号
     */
    private String outerTradeNo;
    /**
     * 充值账号
     */
    private String cpAccount;
    /**
     * 总金额
     */
    private BigDecimal totalPayAmount;

}
