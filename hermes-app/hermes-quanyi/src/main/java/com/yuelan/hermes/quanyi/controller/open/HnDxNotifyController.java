package com.yuelan.hermes.quanyi.controller.open;

import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.handler.impl.BenefitHaiNanDxPayChannel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2025/4/1
 * @since 2025/4/1
 */
@Slf4j
@Tag(name = "海南电信权益回调")
@RequestMapping("/hndx")
@RestController
@RequiredArgsConstructor
public class HnDxNotifyController {

    private final BenefitHaiNanDxPayChannel haiNanDxPayChannel;

    @Operation(summary = "海南电信权益回调")
    @RequestMapping("order/notify")
    public String orderNotify(@RequestBody String encryptStr) {
        log.info("海南电信权益回调: {}", encryptStr);
        JSONObject jsonObject = JSONObject.parseObject(encryptStr);
        String encryptBody = jsonObject.getString("encryptBody");
        return haiNanDxPayChannel.orderNotify(encryptBody);

    }

}
