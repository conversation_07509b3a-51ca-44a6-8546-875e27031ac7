package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.yuelan.hermes.commons.excel.LocalDateTimeConverter;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGdOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.EccCardStatusConverter;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.EccGdOrderStatusConverter;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.ExpressStatusConverter;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.PhoneNumSelectTypeConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> 2024/11/21
 * @since 2024/11/21
 */
@Data
@ContentRowHeight(15)
@HeadRowHeight(20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class EccGdOrderResp {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @ColumnWidth(20)
    @ExcelProperty("订单ID")
    private Long gdOrderId;
    /**
     * 我方订单号
     */
    @Schema(description = "我方订单号")
    @ColumnWidth(20)
    @ExcelProperty("我方订单号")
    private String orderNo;
    /**
     * 原始订单号（补发类型订单特有）
     */
    @Schema(description = "原始订单号（补发类型订单特有）")
    @ColumnWidth(20)
    @ExcelProperty("原始订单号")
    private String originalOrderNo;
    /**
     * 三方订单号或者运营商订单号
     */
    @Schema(description = "三方订单号或者运营商订单号")
    @ColumnWidth(20)
    @ExcelProperty("三方订单号")
    private String spOrderNo;
    /**
     * 其他运营商失败转入订单
     */
    @Schema(description = "其他运营商失败转入订单")
    @ColumnWidth(20)
    @ExcelProperty("其他运营商失败转入订单")
    private Integer transferOperator;
    /**
     * 我方电商卡权益包id
     */
    @Schema(description = "我方电商卡权益包id")
    @ColumnWidth(20)
    @ExcelProperty("权益包ID")
    private Long prodId;
    /**
     * 权益包商品名字
     */
    @Schema(description = "权益包商品名字")
    @ColumnWidth(20)
    @ExcelProperty("权益包商品名字")
    private String prodName;
    /**
     * 推广渠道id
     */
    @Schema(description = "推广渠道id")
    @ColumnWidth(20)
    @ExcelProperty("推广渠道ID")
    private Long channelId;
    /**
     * 推广渠道类型:1-内部渠道，2-外部渠道
     */
    @Schema(description = "推广渠道类型:1-内部渠道，2-外部渠道")
    @ColumnWidth(20)
    @ExcelProperty("推广渠道类型")
    private Integer channelType;
    /**
     * 推广渠道名字
     */
    @Schema(description = "推广渠道名字")
    @ColumnWidth(20)
    @ExcelProperty("推广渠道名字")
    private String channelName;
    /**
     * 外部渠道订单号
     */
    @Schema(description = "外部渠道订单号")
    @ColumnWidth(20)
    @ExcelProperty("外部渠道订单号")
    private String channelOrderNo;
    /**
     * 广告渠道id
     */
    @Schema(description = "广告渠道id")
    @ColumnWidth(20)
    @ExcelProperty("广告渠道ID")
    private Long adChannelId;
    /**
     * 广告渠道名字
     */
    @Schema(description = "广告渠道名字")
    @ColumnWidth(20)
    @ExcelProperty("广告渠道名字")
    private String adChannelName;
    /**
     * 身份证名字
     */
    @Schema(description = "身份证名字")
    @ColumnWidth(20)
    @ExcelProperty("身份证名字")
    private String idCardName;
    /**
     * 身份证号码
     */
    @Schema(description = "身份证号码")
    @ColumnWidth(20)
    @ExcelProperty("身份证号码")
    private String idCard;
    /**
     * 运营商或者三方分配的产品编码
     */
    @Schema(description = "运营商或者三方分配的产品编码")
    @ColumnWidth(20)
    @ExcelProperty("产品编码")
    private String gdGoodsId;
    /**
     * 省份
     */
    @Schema(description = "收货地址-省份")
    @ColumnWidth(20)
    @ExcelProperty("收货地址-省份")
    private String postProvince;
    /**
     * 市信息
     */
    @Schema(description = "收货地址-市")
    @ColumnWidth(20)
    @ExcelProperty("收货地址-市")
    private String postCity;
    /**
     * 区县信息
     */
    @Schema(description = "收货地址-区县信息")
    @ColumnWidth(20)
    @ExcelProperty("收货地址-区县信息")
    private String postDistrict;
    /**
     * 详细收货地址
     */
    @Schema(description = "详细收货地址")
    @ColumnWidth(20)
    @ExcelProperty("详细收货地址")
    private String address;
    /**
     * 收货手机号码
     */
    @Schema(description = "收货手机号码")
    @ColumnWidth(20)
    @ExcelProperty("收货手机号码")
    private String contactPhone;
    /**
     * 选择的手机号码
     */
    @Schema(description = "选择的手机号码")
    @ColumnWidth(20)
    @ExcelProperty("手机号码")
    private String phone;
    /**
     * 归属地省
     */
    @Schema(description = "归属地省")
    @ColumnWidth(20)
    @ExcelProperty("归属地省")
    private String province;
    /**
     * 归属地城市
     */
    @Schema(description = "归属地城市")
    @ColumnWidth(20)
    @ExcelProperty("归属地城市")
    private String city;
    /**
     * 选号类型：0-随机选号，1-用户选号
     */
    @Schema(description = "选号类型：0-随机选号，1-用户选号")
    @ColumnWidth(20)
    @ExcelProperty(value = "选号类型", converter = PhoneNumSelectTypeConverter.class)
    private Integer selectType;
    /**
     * 订单提交给三方或者运营商的response
     */
    @Schema(description = "订单提交给三方或者运营商的response")
    private String orderSubmitResp;
    /**
     * 订单状态：0-提交失败 1-审核中 2-领卡失败 3-领卡成功(运营商受理成功) 4-退单
     */
    @Schema(description = "订单状态：0-提交失败 1-审核中 2-领卡失败 3-领卡成功(运营商受理成功) 4-退单")
    @ColumnWidth(20)
    @ExcelProperty(value = "订单状态", converter = EccGdOrderStatusConverter.class)
    private Integer orderStatus;
    /**
     * sim卡状态：0-待激活 1-激活 2-停机 3-销户
     */
    @Schema(description = "sim卡状态：0-待激活 1-激活 2-停机 3-销户")
    @ColumnWidth(20)
    @ExcelProperty(value = "sim卡状态", converter = EccCardStatusConverter.class)
    private Integer cardStatus;
    /**
     * 物流状态：0-待发货 1-发货 2-签收 3-拒收
     */
    @Schema(description = "物流状态：0-待发货 1-发货 2-签收 3-拒收")
    @ColumnWidth(20)
    @ExcelProperty(value = "物流状态", converter = ExpressStatusConverter.class)
    private Integer expressStatus;
    /**
     * 物流公司
     */
    @Schema(description = "物流公司")
    @ColumnWidth(20)
    @ExcelProperty("物流公司")
    private String expressCompany;
    /**
     * 物流订单号
     */
    @Schema(description = "物流订单号")
    @ColumnWidth(20)
    @ExcelProperty("物流订单号")
    private String expressNo;
    /**
     * 激活时间
     */
    @Schema(description = "激活时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "激活时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime activateTime;
    /**
     * 停机时间
     */
    @Schema(description = "停机时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "停机时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime stopTime;
    /**
     * 销户时间
     */
    @Schema(description = "销户时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "销户时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime closeTime;
    /**
     * 首冲金额分
     */
    @Schema(description = "首冲金额-元")
    @ColumnWidth(20)
    @ExcelProperty("首冲金额")
    private String firstChargeAmount;
    /**
     * 首冲时间
     */
    @Schema(description = "首冲时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "首冲时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime firstChargeTime;
    /**
     * 领卡未成功原因
     */
    @Schema(description = "领卡未成功原因")
    @ColumnWidth(20)
    @ExcelProperty("领卡未成功原因")
    private String failReason;
    /**
     * 订单备注
     */
    @Schema(description = "订单备注")
    @ColumnWidth(20)
    @ExcelProperty("订单备注")
    private String remark;


    /**
     * 创建时间
     */
    @Schema(description = "订单时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "订单时间", converter = LocalDateTimeConverter.class)
    protected LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "更新时间", converter = LocalDateTimeConverter.class)
    protected LocalDateTime updateTime;

    public static EccGdOrderResp buildResp(EccGdOrderDO gdOrderDO) {
        if (gdOrderDO == null) {
            return null;
        }
        EccGdOrderResp resp = new EccGdOrderResp();
        resp.setGdOrderId(gdOrderDO.getGdOrderId());
        resp.setOrderNo(gdOrderDO.getOrderNo());
        resp.setOriginalOrderNo(gdOrderDO.getOriginalOrderNo());
        resp.setSpOrderNo(gdOrderDO.getSpOrderNo());
        resp.setTransferOperator(gdOrderDO.getTransferOperator());
        resp.setProdId(gdOrderDO.getProdId());
        resp.setProdName(gdOrderDO.getProdName());
        resp.setChannelId(gdOrderDO.getChannelId());
        resp.setChannelType(gdOrderDO.getChannelType());
        resp.setChannelName(gdOrderDO.getChannelName());
        resp.setChannelOrderNo(gdOrderDO.getChannelOrderNo());
        resp.setAdChannelId(gdOrderDO.getAdChannelId());
        resp.setAdChannelName(gdOrderDO.getAdChannelName());
        resp.setIdCardName(gdOrderDO.getIdCardName());
        resp.setIdCard(gdOrderDO.getIdCard());
        resp.setGdGoodsId(gdOrderDO.getGdGoodsId());
        resp.setPostProvince(gdOrderDO.getPostProvince());
        resp.setPostCity(gdOrderDO.getPostCity());
        resp.setPostDistrict(gdOrderDO.getPostDistrict());
        resp.setAddress(gdOrderDO.getAddress());
        resp.setContactPhone(gdOrderDO.getContactPhone());
        resp.setPhone(gdOrderDO.getPhone());
        resp.setProvince(gdOrderDO.getProvince());
        resp.setCity(gdOrderDO.getCity());
        resp.setSelectType(gdOrderDO.getSelectType());
        resp.setOrderSubmitResp(gdOrderDO.getOrderSubmitResp());
        resp.setOrderStatus(gdOrderDO.getOrderStatus());
        resp.setCardStatus(gdOrderDO.getCardStatus());
        resp.setExpressStatus(gdOrderDO.getExpressStatus());
        resp.setExpressCompany(gdOrderDO.getExpressCompany());
        resp.setExpressNo(gdOrderDO.getExpressNo());
        resp.setActivateTime(gdOrderDO.getActivateTime());
        resp.setStopTime(gdOrderDO.getStopTime());
        resp.setCloseTime(gdOrderDO.getCloseTime());
        if (Objects.nonNull(gdOrderDO.getFirstChargeAmount())) {
            // 分转成2为小数的圆显示hutool
            Number yuan = 100;
            resp.setFirstChargeAmount(NumberUtil.div(gdOrderDO.getFirstChargeAmount(), yuan, 2).toString());
        }
        resp.setFirstChargeTime(gdOrderDO.getFirstChargeTime());
        resp.setFailReason(gdOrderDO.getFailReason());
        resp.setRemark(gdOrderDO.getRemark());
        resp.setCreateTime(LocalDateTimeUtil.of(gdOrderDO.getCreateTime()));
        resp.setUpdateTime(LocalDateTimeUtil.of(gdOrderDO.getUpdateTime()));
        return resp;
    }
}
