package com.yuelan.hermes.quanyi.controller.open;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.service.TeZhenService;
import com.yuelan.hermes.quanyi.controller.request.TeZhenNotifyReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "特祯API")
@RequestMapping("/tz")
@RestController
public class TeZhenController {
    @Autowired
    private TeZhenService teZhenService;

    @Operation(summary = "虚拟产品在线订单回调")
    @PostMapping(value = "/order/on/notify")
    public String orderOnlineNotify(@RequestBody TeZhenNotifyReq request) {
        if (log.isDebugEnabled()) {
            log.info("特祯虚拟产品在线订单回调:{}", JSON.toJSONString(request));
        }
        boolean result = teZhenService.orderOnlineNotify(request);
        return result ? "success" : "error";
    }

    @Operation(summary = "虚拟产品手工订单回调")
    @PostMapping(value = "/order/off/notify")
    public String orderOfflineNotify(@RequestBody TeZhenNotifyReq request) {
        if (log.isDebugEnabled()) {
            log.info("特祯虚拟产品手工订单回调:{}", JSON.toJSONString(request));
        }
        boolean result = teZhenService.orderOfflineNotify(request);
        return result ? "success" : "error";
    }

    @Operation(summary = "通用订单回调")
    @PostMapping(value = "/common/order/notify")
    public String commonOrderNotify(@RequestBody TeZhenNotifyReq request) {
        if (log.isDebugEnabled()) {
            log.info("特祯虚拟产品通用订单回调:{}", JSON.toJSONString(request));
        }
        return teZhenService.commOrderNotify(request);
    }
}
