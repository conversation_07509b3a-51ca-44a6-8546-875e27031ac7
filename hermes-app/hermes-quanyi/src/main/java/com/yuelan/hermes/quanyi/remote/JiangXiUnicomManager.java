package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.commons.enums.SmsCodeType;
import com.yuelan.hermes.quanyi.biz.handler.UnicomJobHandler;
import com.yuelan.hermes.quanyi.biz.handler.factory.UnicomJobFactory;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.enums.JSUnicomWoPayPkgEnum;
import com.yuelan.hermes.quanyi.common.enums.JXUnicomWoPayPkgEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitUnicomPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.JXUnicomProperties;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.controller.response.jsunicom.JiangSuUnicomFlowResp;
import com.yuelan.hermes.quanyi.controller.response.jsunicom.JiangSuUnicomOrderDetailResp;
import com.yuelan.hermes.quanyi.controller.response.jsunicom.JiangSuUnicomOrderResp;
import com.yuelan.hermes.quanyi.controller.response.jsunicom.JiangSuUnicomProductResp;
import com.yuelan.plugins.redisson.util.RedisUtils;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JiangXiUnicomManager implements UnicomJobHandler {


    public static final String TAG = "[江西联通]";
    // 获取token接口
    public static final String GET_TOKEN = "/api/oauth/token";
    // 产品推荐接口
//    public static final String PRODUCT_RECOMMEND = "/ability/customerOperation_productRecommend";
    public static final String PRODUCT_RECOMMEND = "/3th_freerdjx/doPost_productRecommend";
    // 下发验证码
//    public static final String GET_SMS_CODE = "/ability/customerOperation_support_createCaptcha";
    public static final String GET_SMS_CODE = "/3th_freerdjx/doPost_createCaptcha";
    // 校验验证码
//    public static final String VERIFYCAPTCHA = "/ability/customerOperation_support_verifyCaptcha";
    public static final String VERIFYCAPTCHA = "/3th_freerdjx/doPost_verifyCaptcha";
    // 下单接口
//    public static final String PRODUCT_ORDER = "/ability/customerOperation_productOrder";
    public static final String PRODUCT_ORDER = "/3th_freerdjx/doPost_submitOrder";
    // 订单详情
//    public static final String PRODUCT_ORDER_DETAIL = "/ability/customerOperation_productOrderDetail";
    public static final String PRODUCT_ORDER_DETAIL = "/3th_freerdjx/doPost_productOrderDetail";
    // 业务办理初始化
//    public static final String BUSI_INIT = "/ability/customerOperation_busiInit";
    public static final String BUSI_INIT = "/3th_freerdjx/doPost_initOrder";

    // 客户浏览行为埋点接口
    public static final String BROWSE_BEHAVIOR = "/3th_freerdjx/doPost_recordBrowseBehavior";

    private static final String SUCCESS_CODE = "0000";
    private static final int TIME_OUT = 20000;

    @Resource
    private JXUnicomProperties jxUnicomProperties;
    @Resource
    private BenefitOrderLogService benefitOrderLogService;

    /**
     * 点击发送验证码
     */
    public boolean orderSendSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        String mobile = reqParams.getMobile();
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        JiangSuUnicomFlowResp flowResp = this.doInitFlow(mobile);
        JXUnicomWoPayPkgEnum pkgEnum = JXUnicomWoPayPkgEnum.of(productDO.getPayChannelPkgId());

        String commId = jxUnicomProperties.getProperties(pkgEnum).getCommonId();
        String templateId = jxUnicomProperties.getProperties(pkgEnum).getTemplateId();
        // 获取运营商推荐的商品
        JiangSuUnicomProductResp remoteProductResp = this.productRecommend(mobile, flowResp.getFLOW_ID());
        List<JiangSuUnicomProductResp.CommInfoDTO> commInfo = remoteProductResp.getCOMM_INFO();
        JiangSuUnicomProductResp.CommInfoDTO commInfoDTO = commInfo.stream().filter(x -> x.getCOMM_ID().equals(Long.valueOf(commId))).findFirst().orElse(null);
        if (commInfoDTO == null) {
            log.info(TAG + "运营商未推荐该产品。手机号:{},权益包产品code:{},运营商产品id:{}", reqParams.getMobile(), productDO.getProdCode(), commId);
            throw BizException.create(BizErrorCodeEnum.PROD_NOT_EXIST, "未找到相符推荐产品");
        }
        remoteProductResp.setCOMM_INFO(Lists.newArrayList(commInfoDTO));

        String businessId = remoteProductResp.getBUSINESS_ID();
        String commName = commInfoDTO.getCOMM_NAME();
        // 发送行为埋点
        this.pushBrowseBehavior("发送验证码", mobile, commId, commName, flowResp);
        // 运营商下发验证码
        boolean sendSmsCode = this.sendSmsCode(mobile, templateId, businessId, commId, commName, flowResp);
        if (!sendSmsCode) {
            return false;
        }
        // 放入Redis中
        String key = RedisKeys.getJiangSuUnicomProductKey(mobile);
        RedisUtils.setCacheObject(key, remoteProductResp, Duration.ofMinutes(15));

        // 缓存流水号
        String flowKey = RedisKeys.getJiangXiUnicomFlow(mobile);
        RedisUtils.setCacheObject(flowKey, flowResp, Duration.ofMinutes(30));
        return true;
    }

    /**
     * 申请订单
     */
    public BenefitPayResultBO createOrder(UserBenefitOrderReq req, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        BenefitUnicomPayResultBO payResultBO = new BenefitUnicomPayResultBO();
        payResultBO.setSuccess(false);
        String mobile = req.getMobile();

        JSUnicomWoPayPkgEnum pkgEnum = JSUnicomWoPayPkgEnum.of(productDO.getPayChannelPkgId());

        // 获取流水号缓存
        String flowKey = RedisKeys.getJiangXiUnicomFlow(mobile);
        JiangSuUnicomFlowResp flowResp = RedisUtils.getCacheObject(flowKey);
        if (flowResp == null) {
            // 缓存中没有产品信息,说明缓存过期,中间间隔的时间过久
            payResultBO.setMessage("流水号已过期");
            return payResultBO;
        }
        // 获取运营商推荐的商品
        String key = RedisKeys.getJiangSuUnicomProductKey(mobile);
        JiangSuUnicomProductResp productResp = RedisUtils.getCacheObject(key);
        if (productResp == null) {
            // 缓存中没有产品信息,说明缓存过期,中间间隔的时间过久
            payResultBO.setMessage("请重新发送验证码");
            return payResultBO;
        }
        String businessId = productResp.getBUSINESS_ID();
        JiangSuUnicomProductResp.CommInfoDTO commInfoDTO = productResp.getCOMM_INFO().stream().findFirst().orElse(null);
        if (commInfoDTO == null) {
            log.info(TAG + "运营商商品缓存没有数据.手机号:{},redisKey:{}", req.getMobile(), key);
            payResultBO.setMessage("订阅失败,请稍后重试");
            return payResultBO;
        }
        String strategyId = commInfoDTO.getSTRATEGY_ID();
        Long commId = commInfoDTO.getCOMM_ID();
        // 远程校验验证码
        boolean smsCodeFlag = this.checkSmsCode(mobile, businessId, commId, req.getSmsCode(), flowResp);
        if (!smsCodeFlag) {
            log.info(TAG + "运营商验证码校验失败.手机号:{}", req.getMobile());
            payResultBO.setMessage("订阅失败,请稍后重试");
            return payResultBO;
        }

        // 开始下单
        JiangSuUnicomOrderResp orderResp = this.productOrder(mobile, orderDO.getOrderNo(), businessId, commId, strategyId, flowResp);
        if (orderResp == null) {
            log.info(TAG + "运营商下单失败.手机号:{}", req.getMobile());
            payResultBO.setMessage("订阅失败,请稍后重试");
            return payResultBO;
        }
        orderDO.setPreorderContent(JSON.toJSONString(orderResp));
        if (StringUtils.isNotBlank(orderResp.getOutOrderId())) {
            // 预下单,订单号存入扩展字段中
            orderDO.setExtraData(new JSONObject() {{
                put("outOrderId", orderResp.getOutOrderId());
            }}.toString());
            orderDO.setPayStatus(PayStatusEnum.DEFAULT.getCode());
            orderDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
            payResultBO.setSuccess(true);
        } else {
            // 正式下单
            String respCode = orderResp.getRSP_CODE();
            if (SUCCESS_CODE.equals(respCode)) {
                payResultBO.setSuccess(true);
                orderDO.setOutOrderNo(orderResp.getRSP_DATA().getORDER_ID());
                orderDO.setPayStatus(PayStatusEnum.DEFAULT.getCode());
                orderDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
            }
        }
        return payResultBO;
    }

    /**
     * 获取token
     *
     * @return 返回token
     */
    private String getToken() {
        String key = RedisKeys.getJiangSuUnicomProductKey(jxUnicomProperties.getClientId());
        String token = RedisUtils.getCacheObject(key);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }

        Map<String, String> params = new HashMap<>();
        params.put("client_id", jxUnicomProperties.getClientId());
        params.put("client_secret", jxUnicomProperties.getClientSecret());
        String paramsPath = MapUtil.join(params, "&", "=");
        HttpRequest request = HttpRequest.get(jxUnicomProperties.getHost() + GET_TOKEN + "?" + paramsPath).timeout(TIME_OUT);
        log.info(TAG + "获取token:{}", request);
        HttpResponse response = request.execute();
        log.info(TAG + "获取token:{}", response);
        if (response.isOk()) {
            JSONObject jsonObject = JSON.parseObject(response.body(), JSONObject.class);
            if (SUCCESS_CODE.equals(jsonObject.getString("code"))) {
                String accessToken = jsonObject.getString("access_token");
                RedisUtils.setCacheObject(key, accessToken, Duration.ofSeconds(jsonObject.getLong("expiredseconds") - 1));
                return accessToken;
            } else {
                throw BizException.create(BaseErrorCodeEnum.SYSTEM_BUSY);
            }
        } else {
            throw BizException.create(BaseErrorCodeEnum.SYSTEM_BUSY);
        }
    }

    /**
     * 推送客户浏览行为埋点
     */
    private void pushBrowseBehavior(String event, String mobile, String commId, String commName, JiangSuUnicomFlowResp flowResp) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime end = LocalDateTime.now();
        LocalDateTime start = end.minusMinutes(1);

        Map<String, Object> SCAN_RECORD_REQ = new HashMap<>();
        SCAN_RECORD_REQ.put("FLOW_ID", flowResp.getFLOW_ID());
        SCAN_RECORD_REQ.put("INIT_KEY", flowResp.getINIT_KEY());
        SCAN_RECORD_REQ.put("CHANNEL_CODE", jxUnicomProperties.getChannelCode());
        SCAN_RECORD_REQ.put("SERIAL_NUMBER", mobile);
        SCAN_RECORD_REQ.put("COMM_ID", commId);
        SCAN_RECORD_REQ.put("COMM_NAME", commName);
        SCAN_RECORD_REQ.put("PAGE_SHOT", event);
        SCAN_RECORD_REQ.put("START_TIME", start.format(formatter));
        SCAN_RECORD_REQ.put("END_TIME", end.format(formatter));

        Map<String, Object> params = new HashMap<>();
        params.put("action", "3th_freerdjx");
        params.put("method", "doPost_recordBrowseBehavior");

        Map<String, Object> msg = new HashMap<>();
        msg.put("SCAN_RECORD_REQ", SCAN_RECORD_REQ);
        Map<String, Object> req = new HashMap<>();
        req.put("msg", msg);
        params.put("req", req);

        JSONObject object = this.sendRemoteRequest(BROWSE_BEHAVIOR, params, null);
        JSONObject initRsp = object.getJSONObject("SCAN_RECORD_RSP");
        String code = initRsp.getString("RESP_CODE");
        if (SUCCESS_CODE.equals(code)) {
            flowResp.setSCAN_KEY(initRsp.getString("SCAN_KEY"));
        } else {
            throw BizException.create(BizResult.error(code, initRsp.getString("RESP_DESC")));
        }
    }

    /**
     * 业务办理初始化
     */
    private JiangSuUnicomFlowResp doInitFlow(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        Map<String, Object> BUSI_INIT_REQ = new HashMap<>();
        BUSI_INIT_REQ.put("SERIAL_NUMBER", mobile);
        BUSI_INIT_REQ.put("CHANNEL_CODE", jxUnicomProperties.getChannelCode());

        Map<String, Object> params = new HashMap<>();
        params.put("action", "3th_freerdjx");
        params.put("method", "doPost_initOrder");

        Map<String, Object> msg = new HashMap<>();
        msg.put("BUSI_INIT_REQ", BUSI_INIT_REQ);
        Map<String, Object> req = new HashMap<>();
        req.put("msg", msg);
        params.put("req", req);

        JSONObject object = this.sendRemoteRequest(BUSI_INIT, params, null);
        JSONObject initRsp = object.getJSONObject("BUSI_INIT_RSP");
        String code = initRsp.getString("RESP_CODE");
        if (SUCCESS_CODE.equals(code)) {
            // 缓存流水号
            JiangSuUnicomFlowResp flowResp = JSON.parseObject(initRsp.toString(), JiangSuUnicomFlowResp.class);
            String flowKey = RedisKeys.getJiangXiUnicomFlow(mobile);
            RedisUtils.setCacheObject(flowKey, flowResp, Duration.ofMinutes(10));
            return flowResp;
        } else {
            throw BizException.create(BizResult.error(code, initRsp.getString("RESP_DESC")));
        }
    }

    /**
     * 运营商获取产品推荐
     */
    private JiangSuUnicomProductResp productRecommend(String mobile, String flowId) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        Map<String, Object> params = buildBaseParams(mobile);
//        params.put("method", "customerOperation_productRecommend");
        params.put("action", "3th_freerdjx");
        params.put("method", "doPost_productRecommend");
        Map<String, Object> req = new HashMap<>();
        Map<String, Object> msg = new HashMap<>();
        Map<String, Object> PRODUCT_RECOMMEND_REQ = new HashMap<>();
        PRODUCT_RECOMMEND_REQ.put("CHANNEL_CODE", jxUnicomProperties.getChannelCode());
        PRODUCT_RECOMMEND_REQ.put("QUERY_TYPE", "1");
        PRODUCT_RECOMMEND_REQ.put("SERIAL_NUMBER", mobile);
        PRODUCT_RECOMMEND_REQ.put("TRADE_ID", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_FORMAT) + RandomUtil.randomNumbers(3));
        PRODUCT_RECOMMEND_REQ.put("FLOW_ID", flowId);
        msg.put("PRODUCT_RECOMMEND_REQ", PRODUCT_RECOMMEND_REQ);
        req.put("msg", msg);
        params.put("req", req);

        JSONObject object = this.sendRemoteRequest(PRODUCT_RECOMMEND, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.PRODUCT, mobile));
        JSONObject recommendRsp = object.getJSONObject("PRODUCT_RECOMMEND_RSP");
        String code = recommendRsp.getString("RESP_CODE");
        if (SUCCESS_CODE.equals(code)) {
            JSONObject respData = recommendRsp.getJSONObject("RESP_DATA");
            JiangSuUnicomProductResp productResp = JSON.parseObject(JSON.toJSONString(respData), JiangSuUnicomProductResp.class);
            productResp.setBUSINESS_ID(recommendRsp.getString("TRADE_ID"));
            return productResp;
        } else {
            throw BizException.create(BizResult.error(code, recommendRsp.getString("RESP_DESC")));
        }
    }

    @NotNull
    private Map<String, Object> buildBaseParams(String mobile) {
        Map<String, Object> params = new HashMap<>();
        params.put("action", "ability");
        params.put("clientid", jxUnicomProperties.getClientId());
        params.put("serialnumber", mobile);
        params.put("type", "pc");
        params.put("prea", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN) + RandomUtil.randomNumbers(16));
        return params;
    }

    /**
     * 运营商下发短信验证码
     */
    private boolean sendSmsCode(String mobile, String templateId, String bussinessId, String commId, String commName, JiangSuUnicomFlowResp flowResp) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        Map<String, Object> params = buildBaseParams(mobile);
//        params.put("method", "customerOperation_support_createCaptcha");
        params.put("action", "3th_freerdjx");
        params.put("method", "doPost_createCaptcha");
        Map<String, Object> req = new HashMap<>();
        Map<String, Object> msg = new HashMap<>();
        Map<String, Object> CREATE_CAPTCHA_REQ = new HashMap<>();
        CREATE_CAPTCHA_REQ.put("CHANNEL_CODE", jxUnicomProperties.getChannelCode());
        CREATE_CAPTCHA_REQ.put("COMM_ID", commId);
        CREATE_CAPTCHA_REQ.put("BUSINESS_ID", bussinessId);
        CREATE_CAPTCHA_REQ.put("SERIAL_NUMBER", mobile);
        CREATE_CAPTCHA_REQ.put("TEMPLATE_ID", templateId);
        CREATE_CAPTCHA_REQ.put("TRADE_ID", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_FORMAT) + RandomUtil.randomNumbers(3));

        Map<String, Object> PARA = new HashMap<>();
        PARA.put("PARA_ID", "productName");
        PARA.put("PARA_VALUE", commName);
        CREATE_CAPTCHA_REQ.put("PARA", Lists.newArrayList(PARA));

        CREATE_CAPTCHA_REQ.put("FLOW_ID", flowResp.getFLOW_ID());
        CREATE_CAPTCHA_REQ.put("SCAN_KEY", flowResp.getSCAN_KEY());

        msg.put("CREATE_CAPTCHA_REQ", CREATE_CAPTCHA_REQ);
        req.put("msg", msg);
        params.put("req", req);

        JSONObject object = this.sendRemoteRequest(GET_SMS_CODE, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_SEND, mobile));
        JSONObject createCaptchaRsp = object.getJSONObject("CREATE_CAPTCHA_RSP");
        String code = createCaptchaRsp.getString("RESP_CODE");
        if (SUCCESS_CODE.equals(code)) {
            flowResp.setGEN_KEY(createCaptchaRsp.getString("GEN_KEY"));

            String key = createCaptchaRsp.getJSONObject("IDENTIFY_CODE").getString("KEY");
            // 存入缓存,
            String smsCodeKey = RedisKeys.getSmsCodeKey(mobile, SmsCodeType.BENEFIT_USER_ORDER);
            RedisUtils.setCacheObject(smsCodeKey, key, Duration.ofMinutes(10));
            return true;
        } else {
            throw BizException.create(BizResult.error(code, createCaptchaRsp.getString("RESP_DESC")));
        }
    }

    /**
     * 运营商校验验证码
     */
    private boolean checkSmsCode(String mobile, String bussinessId, Long commId, String smsCode, JiangSuUnicomFlowResp flowResp) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        String key = RedisKeys.getSmsCodeKey(mobile, SmsCodeType.BENEFIT_USER_ORDER);
        String smsCodeKey = RedisUtils.getCacheObject(key);
        if (StringUtils.isBlank(smsCodeKey) || StringUtils.isBlank(smsCode)) {
            throw BizException.create(BizErrorCodeEnum.SMS_CODE_EXPIRE);
        }
        Map<String, Object> params = buildBaseParams(mobile);
//        params.put("method", "customerOperation_support_verifyCaptcha");
        params.put("action", "3th_freerdjx");
        params.put("method", "doPost_verifyCaptcha");
        Map<String, Object> req = new HashMap<>();
        Map<String, Object> msg = new HashMap<>();
        Map<String, Object> VERIFY_CAPTCHA_REQ = new HashMap<>();
        VERIFY_CAPTCHA_REQ.put("CHANNEL_CODE", jxUnicomProperties.getChannelCode());
        VERIFY_CAPTCHA_REQ.put("CODE", smsCode);
        VERIFY_CAPTCHA_REQ.put("KEY", smsCodeKey);
        VERIFY_CAPTCHA_REQ.put("COMM_ID", commId + "");
        VERIFY_CAPTCHA_REQ.put("BUSINESS_ID", bussinessId);
        VERIFY_CAPTCHA_REQ.put("SERIAL_NUMBER", mobile);
        VERIFY_CAPTCHA_REQ.put("TRADE_ID", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_FORMAT) + RandomUtil.randomNumbers(3));
        VERIFY_CAPTCHA_REQ.put("TIMESTAMP", DateUtil.current() + "");
        VERIFY_CAPTCHA_REQ.put("FLOW_ID", flowResp.getFLOW_ID());
        VERIFY_CAPTCHA_REQ.put("GEN_KEY", flowResp.getGEN_KEY());
        msg.put("VERIFY_CAPTCHA_REQ", VERIFY_CAPTCHA_REQ);
        req.put("msg", msg);
        params.put("req", req);

        JSONObject object = this.sendRemoteRequest(VERIFYCAPTCHA, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_CHECK, mobile));
        JSONObject verifyCaptchaRsp = object.getJSONObject("VERIFY_CAPTCHA_RSP");
        String code = verifyCaptchaRsp.getString("RESP_CODE");
        if (SUCCESS_CODE.equals(code)) {
            flowResp.setVER_KEY(verifyCaptchaRsp.get("VER_KEY").toString());
            return true;
        } else {
            throw BizException.create(BizResult.error(code, verifyCaptchaRsp.getString("RESP_DESC")));
        }
    }

    /**
     * 下单
     */
    private JiangSuUnicomOrderResp productOrder(String mobile, String orderNo, String bussinessId, Long commId, String strategyId, JiangSuUnicomFlowResp flowResp) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        Map<String, Object> params = buildBaseParams(mobile);
//        params.put("method", "customerOperation_productOrder");
        params.put("action", "3th_freerdjx");
        params.put("method", "doPost_submitOrder");
        Map<String, Object> req = new HashMap<>();
        Map<String, Object> msg = new HashMap<>();
        Map<String, Object> PRODUCT_ORDER_REQ = new HashMap<>();
        PRODUCT_ORDER_REQ.put("SERIAL_NUMBER", mobile);
        PRODUCT_ORDER_REQ.put("BUSINESS_ID", bussinessId);
        PRODUCT_ORDER_REQ.put("PRICE_SUM", 0);
        PRODUCT_ORDER_REQ.put("PAY_TAG", "9");
        PRODUCT_ORDER_REQ.put("IS_PRINT_PAPER_LESS", "0");
        PRODUCT_ORDER_REQ.put("ORDER_TIME", DateUtil.now());
        PRODUCT_ORDER_REQ.put("TRADE_ID", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_FORMAT) + RandomUtil.randomNumbers(3));
        PRODUCT_ORDER_REQ.put("CHANNEL_CODE", jxUnicomProperties.getChannelCode());

        Map<String, Object> COMM_OBJECT = new HashMap<>();
        COMM_OBJECT.put("STRATEGY_ID", strategyId);
        COMM_OBJECT.put("MODIFY_TAG", "0");
        COMM_OBJECT.put("COMM_ID", commId + "");


        PRODUCT_ORDER_REQ.put("COMM_OBJECT", Lists.newArrayList(COMM_OBJECT));
        PRODUCT_ORDER_REQ.put("FLOW_ID", flowResp.getFLOW_ID());
        PRODUCT_ORDER_REQ.put("VER_KEY", flowResp.getVER_KEY());
        msg.put("PRODUCT_ORDER_REQ", PRODUCT_ORDER_REQ);
        req.put("msg", msg);
        params.put("req", req);

        JSONObject object = this.sendRemoteRequest(PRODUCT_ORDER, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER, mobile, orderNo));
        JSONObject productOrderRsp = object.getJSONObject("PRODUCT_ORDER_RSP");
        String code = productOrderRsp.getString("RESP_CODE");
        if (SUCCESS_CODE.equals(code)) {
            JSONObject respData = productOrderRsp.getJSONObject("RESP_DATA");
            return JSON.parseObject(JSON.toJSONString(respData), JiangSuUnicomOrderResp.class);
        } else {
            throw BizException.create(BizResult.error(code, productOrderRsp.getString("RESP_DESC")));
        }
    }

    /**
     * 订单详情查询
     *
     * @param orderId    订单id
     * @param outOrderId 外部订单id(如果为预下单的订单,传这个)
     */
    public JiangSuUnicomOrderDetailResp productOrderDetail(String mobile, String orderId, String outOrderId) {

        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        // 获取流水号缓存
        String flowKey = RedisKeys.getJiangXiUnicomFlow(mobile);
        JiangSuUnicomFlowResp flowResp = RedisUtils.getCacheObject(flowKey);

        Map<String, Object> params = buildBaseParams(mobile);
//        params.put("method", "customerOperation_productOrderDetail");
        params.put("action", "3th_freerdjx");
        params.put("method", "doPost_productOrderDetail");
        Map<String, Object> req = new HashMap<>();
        Map<String, Object> msg = new HashMap<>();
        Map<String, Object> PRODUCT_ORDER_DETAIL_REQ = new HashMap<>();
        if (StringUtils.isNotBlank(orderId)) {
            PRODUCT_ORDER_DETAIL_REQ.put("ORDER_ID", orderId);
        } else {
            PRODUCT_ORDER_DETAIL_REQ.put("TOUCH_ORDER_ID", outOrderId);
        }
        PRODUCT_ORDER_DETAIL_REQ.put("TRADE_ID", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_MS_FORMAT) + RandomUtil.randomNumbers(3));
        PRODUCT_ORDER_DETAIL_REQ.put("CHANNEL_CODE", jxUnicomProperties.getChannelCode());
        PRODUCT_ORDER_DETAIL_REQ.put("FLOW_ID", flowResp.getFLOW_ID());

        msg.put("PRODUCT_ORDER_DETAIL_REQ", PRODUCT_ORDER_DETAIL_REQ);
        req.put("msg", msg);
        params.put("req", req);

        JSONObject object = this.sendRemoteRequest(PRODUCT_ORDER_DETAIL, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER_QUERY, mobile, orderId));
        JSONObject productOrderDetailRsp = object.getJSONObject("PRODUCT_ORDER_DETAIL_RSP");
        return JSON.parseObject(JSON.toJSONString(productOrderDetailRsp), JiangSuUnicomOrderDetailResp.class);
    }

    private JSONObject sendRemoteRequest(String path, Map<String, Object> params, BenefitOrderLog.Args args) {
        String url = jxUnicomProperties.getHost() + path;

        String token = this.getToken();

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url + "?accessToken=" + token)
                .contentType(ContentType.JSON.getValue())
                .body(JSON.toJSONString(params))
                .timeout(TIME_OUT)
                .log(BenefitPayChannelEnum.JX_UNICOM, args);

        log.info(TAG + "请求前:{}", requestWrapper.getHttpRequest());
        HttpResponse response = benefitOrderLogService.http(requestWrapper);
        log.info(TAG + "请求后:{}", response);
        if (response.isOk()) {
            JSONObject jsonObject = JSON.parseObject(response.body(), JSONObject.class);
//            String code = jsonObject.getString("code");
//            if (SUCCESS_CODE.equals(code)) {
//                return jsonObject.getJSONObject("rep").getJSONObject("msg").getJSONObject("UNI_BSS_BODY");
//            } else {
//                throw BizException.create(BizResult.error(code, jsonObject.getString("detail")));
//            }
            return jsonObject;
        } else {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        UnicomJobFactory.registerStrategy(BenefitPayChannelEnum.JX_UNICOM.getCode(), this);
    }

    @Override
    public <T> T getOrderDetail(BenefitOrderDO benefitOrderDO, String orderId, String outOrderId, Class<T> cls) {
        if (benefitOrderDO == null) {
            return null;
        }
        JiangSuUnicomOrderDetailResp detailResp = this.productOrderDetail(benefitOrderDO.getPhone(), orderId, outOrderId);
        return BeanUtil.toBean(detailResp, cls);
    }
}
