package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.date.LocalDateTimeUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

@Slf4j
@Data
public class GamingOrderNotifyReq {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "供应商订单号")
    private String supplierOrderNo;

    @Schema(description = "道具下发时间(13位时间戳)")
    private Long sendTime;

    @Schema(description = "道具下发结果,1下发成功,0下发失败")
    private Integer sendResult;

    @Schema(description = "道具发放结果描述")
    private String sendMessage;

    @Schema(description = "请求时间(13位时间戳)")
    private Long timestamp;

    @Schema(description = "签名")
    private String sign;

    /**
     * 解析下发时间
     */
    public LocalDateTime parseSendTime() {
        if (sendTime == null) {
            return null;
        }
        try {
            return LocalDateTimeUtil.of(sendTime);
        } catch (Exception e) {
            log.error("parseSendTime error", e);
        }
        return null;
    }
}
