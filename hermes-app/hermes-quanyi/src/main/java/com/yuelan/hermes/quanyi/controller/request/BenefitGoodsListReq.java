package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR> 2024/4/2 16:24
 */

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BenefitGoodsListReq extends PageRequest {
    /**
     * 商品id
     */
    @Schema(description = "商品id")
    private Long goodsId;

    /**
     * 商品名
     */
    @Schema(description = "商品名")
    private String goodsName;

    /**
     * 供应商类型
     */
    @Schema(description = "供应商类型")
    private Long supplierType;

    /**
     * 供应商名字
     */
    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;

    /**
     * 上下架状态：0-下架；1-上架
     */
    @Schema(description = "上下架状态：0-下架；1-上架")
    private Integer goodsStatus;


    public static Wrapper<BenefitGoodsDO> buildQueryWrapper(BenefitGoodsListReq req) {
        return Wrappers.lambdaQuery(BenefitGoodsDO.class)
                .eq(Objects.nonNull(req.getGoodsId()), BenefitGoodsDO::getGoodsId, req.getGoodsId())
                .like(Objects.nonNull(req.getGoodsName()), BenefitGoodsDO::getGoodsName, req.getGoodsName())
                .eq(Objects.nonNull(req.getSupplierType()), BenefitGoodsDO::getSupplierType, req.getSupplierType())
                .eq(Objects.nonNull(req.getSupplierGoodsNo()), BenefitGoodsDO::getSupplierGoodsNo, req.getSupplierGoodsNo())
                .eq(Objects.nonNull(req.getGoodsStatus()), BenefitGoodsDO::getGoodsStatus, req.getGoodsStatus())
                .orderBy(Objects.equals(req.getOrderBy(), "createTime"), req.isAsc(), BenefitGoodsDO::getCreateTime)
                .orderByDesc(Objects.isNull(req.getOrderBy()), BenefitGoodsDO::getGoodsId);

    }


}
