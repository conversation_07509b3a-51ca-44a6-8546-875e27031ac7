package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.EccGdOrderService;
import com.yuelan.hermes.quanyi.config.satoken.StpAdminUtil;
import com.yuelan.hermes.quanyi.controller.request.EccGdOrderListReq;
import com.yuelan.hermes.quanyi.controller.response.EccGdOrderResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.hermes.quanyi.remote.response.WaNumOrderDetailResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> 2024/5/15 上午10:57
 */
@Validated
@Tag(name = "电商卡/后台api/广电卡领取记录")
@RestController
@RequestMapping("/a/ecc/gdOrder")
@AllArgsConstructor
public class EccGdOrderController {

    private final EccGdOrderService gdOrderService;

    @Operation(summary = "广电卡领卡记录")
    @PostMapping("/list")
    public BizResult<PageData<EccGdOrderResp>> list(@RequestBody EccGdOrderListReq req) {
        return BizResult.create(gdOrderService.pageList(req));
    }

    @Operation(summary = "导出广电卡领取记录")
    @PostMapping("/export")
    public BizResult<FileExportTaskCreateResp> export(@RequestBody EccGdOrderListReq req) {
        long adminId = StpAdminUtil.getLoginIdAsLong();
        return BizResult.create(gdOrderService.export(req, adminId));
    }

    @Operation(summary = "订单在三方或运营商的操作记录")
    @PostMapping("/orderOperatorRecord/{gdOrderId}")
    public BizResult<WaNumOrderDetailResp.OrderDetail> orderOperatorRecord(@PathVariable Long gdOrderId) {
        return BizResult.create(gdOrderService.orderOperatorRecord(gdOrderId));
    }
}
