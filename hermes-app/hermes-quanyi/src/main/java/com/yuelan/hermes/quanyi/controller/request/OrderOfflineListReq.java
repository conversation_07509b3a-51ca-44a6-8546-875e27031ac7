package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.Objects;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderOfflineListReq extends PageRequest {

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "虚拟商品ID")
    private Long goodsId;

    @Schema(description = "虚拟商品SKU编号")
    private String skuNo;

    @Schema(description = "集采客户名称")
    private String buyer;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "充值类型")
    private Integer goodsType;

    @Schema(description = "下单开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderStartTime;

    @Schema(description = "下单结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderEndTime;


    public void check() {
        if (Objects.nonNull(orderEndTime)) {
            orderEndTime = DateUtil.endOfDay(orderEndTime);
        }
    }

}