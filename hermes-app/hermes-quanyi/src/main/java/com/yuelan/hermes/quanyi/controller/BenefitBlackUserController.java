package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.hermes.quanyi.biz.service.BenefitBlackUserDOService;
import com.yuelan.hermes.quanyi.controller.request.BenefitBlackUserDeleteReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitBlackUserListReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitBlackUserSaveReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitBlackUserResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@Tag(name = "权益N选1/后台接口/黑名单管理")
@RequiredArgsConstructor
@RequestMapping("/a/benefit/black")
public class BenefitBlackUserController {

    private final BenefitBlackUserDOService benefitBlackUserDOService;

    @Operation(summary = "黑名单用户列表")
    @PostMapping("/list")
    public BizResult<PageData<BenefitBlackUserResp>> list(@RequestBody BenefitBlackUserListReq req) {
        return BizResult.create(benefitBlackUserDOService.pageList(req));
    }

    @Log(title = "新增黑名单用户", type = OperationType.INSERT)
    @Operation(summary = "新增黑名单用户")
    @PostMapping("/save")
    public BizResult<Void> save(@Validated(AddGroup.class) @RequestBody BenefitBlackUserSaveReq req) {
        benefitBlackUserDOService.userSave(req);
        return BizResult.ok();
    }

    @Log(title = "删除黑名单用户", type = OperationType.DELETE)
    @Operation(summary = "删除黑名单用户")
    @PostMapping("/delete")
    public BizResult<Void> delete(@RequestBody BenefitBlackUserDeleteReq req) {
        benefitBlackUserDOService.userDelete(req);
        return BizResult.ok();
    }

}
