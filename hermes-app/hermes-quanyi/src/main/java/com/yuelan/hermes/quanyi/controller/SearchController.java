package com.yuelan.hermes.quanyi.controller;


import com.yuelan.hermes.quanyi.biz.service.SearchService;
import com.yuelan.hermes.quanyi.controller.response.*;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "搜索API")
@RequestMapping("/search")
@RestController
public class SearchController {
    @Autowired
    private SearchService searchService;

    @Operation(summary = "商品查询")
    @GetMapping("/goods")
    public BizResult<List<GoodsVirtualSearchRsp>> searchGoods(@RequestParam(required = false) String keyword) {
        List<GoodsVirtualSearchRsp> list = searchService.searchGoods(keyword);
        return BizResult.create(list);
    }

    @Operation(summary = "SKU查询")
    @GetMapping("/sku")
    public BizResult<List<GoodsVirtualSkuSearchRsp>> searchSku(@RequestParam(required = false) Long goodsId, @RequestParam(required = false) String keyword) {
        List<GoodsVirtualSkuSearchRsp> list = searchService.searchSku(goodsId, keyword);
        return BizResult.create(list);
    }

    @Operation(summary = "供应商")
    @GetMapping("/online/supplier")
    public BizResult<List<String>> searchOnlineSupplier(@RequestParam(required = false) String keyword) {
        List<String> list = searchService.searchOnlineSupplier(keyword);
        return BizResult.create(list);
    }

    @Operation(summary = "集采客户")
    @GetMapping("/offline/buyer")
    public BizResult<List<String>> searchOfflineBuyer(@RequestParam(required = false) String keyword) {
        List<String> list = searchService.searchOfflineBuyer(keyword);
        return BizResult.create(list);
    }

    @Operation(summary = "商家查询")
    @GetMapping("/mch")
    public BizResult<List<MchRsp>> searchMch(@RequestParam(required = false) String keyword) {
        List<MchRsp> list = searchService.searchMch(keyword);
        return BizResult.create(list);
    }

    @Operation(summary = "产品查询")
    @GetMapping("/gaming/product")
    public BizResult<List<ProductSearchRsp>> searchGamingProduct(@RequestParam(required = false) String keyword) {
        List<ProductSearchRsp> list = searchService.searchProduct(keyword);
        return BizResult.create(list);
    }

    @Operation(summary = "电竞卡商品查询")
    @GetMapping("/gaming/goods")
    public BizResult<List<GamingGoodsSearchRsp>> searchGamingGoods(@RequestParam(required = false) String keyword) {
        List<GamingGoodsSearchRsp> list = searchService.searchGamingGoods(keyword);
        return BizResult.create(list);
    }
}
