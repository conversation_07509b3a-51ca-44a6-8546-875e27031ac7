package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2025/6/13
 * @since 2025/6/13
 */
@Data
public class ImeiActivationStatusResp {

    @Schema(description = "IMEI号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String imei;

    @Schema(description = "是否合法", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean valid;

    @Schema(title = "是否已经激活", description = "imei 合法时才存在该字段", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Boolean activated;

    @Schema(title = "激活时间", description = "示例值：2025-06-13 10:15:30", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime activationTime;

    @Schema(title = "终端发货时间", description = "示例值：2025-06-13 10:15:30", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private LocalDateTime deliveryTime;


}
