package com.yuelan.hermes.quanyi.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.controller.request.NXCreateCDKReq;
import com.yuelan.hermes.quanyi.controller.response.NxCreateCDKRes;
import com.yuelan.hermes.quanyi.remote.NXBenefitManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2025/1/18
 * @description NXBenefitController
 */
@Validated
@RestController
@Tag(name = "权益N选1/客户端接口/宁夏移动权益发放api")
@RequiredArgsConstructor
@RequestMapping("/nx/cmcc")
public class NXBenefitController {

    @Resource
    private NXBenefitManager nxBenefitManager;

    @Operation(summary = "宁夏移动获取爆米花cdk")
    @SaIgnore
    @PostMapping(value = "/bmh/createCDK")
    public NxCreateCDKRes createCDK(@RequestBody NXCreateCDKReq params) {
        return nxBenefitManager.createCDK(params);
    }
}
