package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class GamingProductStatusOptReq extends GamingProductOptReq {

    @NotNull(message = "请选择状态")
    @Schema(description = "上下架")
    private Integer status;
}
