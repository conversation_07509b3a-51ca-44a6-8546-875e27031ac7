package com.yuelan.hermes.quanyi.controller.open;

import com.yuelan.hermes.quanyi.remote.response.HndxSimCardResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2025/6/17
 * @since 2025/6/17
 */
@Slf4j
@Tag(name = "湖南电信信息流接口")
@RequestMapping("/hnxxl")
@RestController
@RequiredArgsConstructor
public class HndxXxlController {

    @Operation(summary = "信息流订单通知")
    @PostMapping("/order/notify")
    public HndxSimCardResp orderNotify(@RequestBody String reqBody) {
        log.info("湖南电信信息流订单通知: {}", reqBody);
        HndxSimCardResp hndxSimCardResp = new HndxSimCardResp();
        hndxSimCardResp.success();
        return hndxSimCardResp;
    }

}
