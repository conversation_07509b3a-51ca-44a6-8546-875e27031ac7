package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.RegexMatch;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
@Data
public class UserBenefitStatusReq {

    @Schema(description = "手机号码")
    @NotEmpty(message = "手机号码不能为空")
    @RegexMatch(regex = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;


    @Schema(description = "产品编码")
    @NotEmpty(message = "产品编码不能为空")
    private String prodCode;

}
