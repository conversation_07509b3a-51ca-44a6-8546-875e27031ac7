package com.yuelan.hermes.quanyi.remote.response;

import lombok.Data;

@Data
public class RytOrderRsp {

    /**
     * 订单id
     */
    private Long id;
    /**
     * 商品id
     */
    private Long goodsId;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 面值
     */
    private Long salePrice;
    /**
     * 0：失败；3充值中；4充值成功；5充值失败
     */
    private Integer status;
    /**
     * 交易账号
     */
    private Long tradeAccountCode;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 数量
     */
    private Integer num;
    /**
     * 类型，1是卡密，2是直冲
     */
    private String tradeType;
    /**
     * 卡密数据
     */
    private String cardInfos;
}
