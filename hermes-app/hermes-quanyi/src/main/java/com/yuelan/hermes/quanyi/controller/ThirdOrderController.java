package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.QueryGroup;
import com.yuelan.hermes.quanyi.biz.service.ThirdChannelOrderService;
import com.yuelan.hermes.quanyi.controller.request.third.ThirdChannelOrderListReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitPayChannelResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.hermes.quanyi.controller.response.third.ThirdChannelOrderResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@Tag(name = "第三方订单/后台接口/权益包订单")
@RequiredArgsConstructor
@RequestMapping("/a/third/order")
public class ThirdOrderController {

    private final ThirdChannelOrderService thirdChannelOrderService;

    @Operation(summary = "查询第三方渠道包列表")
    @GetMapping("/channelPkg")
    public BizResult<List<BenefitPayChannelResp>> getChannelPkg() {
        return BizResult.create(thirdChannelOrderService.getUniqueChannelPkg());
    }

    @Operation(summary = "查询渠道订单列表")
    @PostMapping("/list")
    public BizResult<PageData<ThirdChannelOrderResp>> list(@Validated(QueryGroup.class) @RequestBody ThirdChannelOrderListReq req) {
        return BizResult.create(thirdChannelOrderService.pageList(req));
    }

    @Operation(summary = "渠道订单导出")
    @PostMapping("/listExport")
    public BizResult<FileExportTaskCreateResp> listExport(@RequestBody ThirdChannelOrderListReq req) {
        return BizResult.create(thirdChannelOrderService.listExport(req));
    }

}
