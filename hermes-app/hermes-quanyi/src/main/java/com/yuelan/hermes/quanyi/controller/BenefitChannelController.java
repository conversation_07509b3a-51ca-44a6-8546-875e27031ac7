package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.biz.service.BenefitChannelDOService;
import com.yuelan.hermes.quanyi.controller.request.BenefitChannelListReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitChannelSaveReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitChannelResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> 2024/5/3 上午11:21
 * <p>
 * 推广渠道
 */
@Validated
@RestController
@Tag(name = "权益N选1/后台接口/渠道管理")
@RequiredArgsConstructor
@RequestMapping("/a/benefit/channel")
public class BenefitChannelController {

    private final BenefitChannelDOService benefitChannelDOService;

    /**
     * 推广渠道分页
     */
    @Operation(summary = "推广渠道分页")
    @PostMapping("/list")
    public BizResult<PageData<BenefitChannelResp>> list(@RequestBody @Validated BenefitChannelListReq req) {
        return BizResult.create(benefitChannelDOService.page(req));
    }

    @Operation(summary = "推广渠道选择项")
    @PostMapping("/selectOptions")
    public BizResult<List<BenefitChannelResp>> selectOptions() {
        return BizResult.create(benefitChannelDOService.selectOptions());
    }

    /**
     * 新增推广渠道
     */
    @Log(title = "新增推广渠道", type = OperationType.INSERT)
    @Operation(summary = "新增推广渠道")
    @PostMapping("/save")
    public BizResult<Void> save(@Validated(AddGroup.class) @RequestBody BenefitChannelSaveReq req) {
        benefitChannelDOService.save(req);
        return BizResult.ok();
    }

    /**
     * 编辑推广渠道
     */
    @Log(title = "编辑推广渠道", type = OperationType.UPDATE)
    @Operation(summary = "编辑推广渠道")
    @PostMapping("/edit")
    public BizResult<Void> edit(@Validated(EditGroup.class) @RequestBody BenefitChannelSaveReq req) {
        benefitChannelDOService.updateById(req);
        return BizResult.ok();
    }


}
