package com.yuelan.hermes.quanyi.remote.response;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class RytRsp {
    /**
     * 调用成功
     */
    private static final Integer SUCCESS = 200;

    private JSONObject datas;
    private String message;
    private Integer status;

    public static RytRsp success() {
        RytRsp rytRsp = new RytRsp();
        rytRsp.setStatus(SUCCESS);
        return rytRsp;
    }

    public static RytRsp error(Integer status, String message) {
        RytRsp rytRsp = new RytRsp();
        rytRsp.setMessage(message);
        rytRsp.setStatus(status);
        return rytRsp;
    }

    public <T> T getObject(Class<T> type) {
        return datas.getObject("values", type);
    }

    public <T> List<T> getList(Class<T> itemClass) {
        JSONObject values = datas.getJSONObject("values");

        return values.getList("lists", itemClass);
    }

    public Boolean isSuccess() {
        return Objects.equals(SUCCESS, status);
    }


}
