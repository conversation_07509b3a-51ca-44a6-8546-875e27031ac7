package com.yuelan.hermes.quanyi.remote;

import akka.protobufv3.internal.ServiceException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.pojo.bo.WoAINumPhoneRespBo;
import com.yuelan.hermes.quanyi.common.pojo.properties.WaNumProperties;
import com.yuelan.hermes.quanyi.remote.request.WaNumSubmitReq;
import com.yuelan.hermes.quanyi.remote.response.WaNumOrderDetailResp;
import com.yuelan.hermes.quanyi.remote.response.WaNumSubmitResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

/**
 * 我爱号码网-广电卡
 * <p>
 * <a href="https://www.showdoc.com.cn/2136752535074756">文档地址  密码：504026296</a>
 * 广电接 2.8  2.1  2.9
 *
 * <AUTHOR> 2024/11/19
 * @since 2024/11/19
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WoAiNumManager {

    private static final String PHONE_POOL_URL = "https://www.haoma517.com/api/order/gd-phonenumber-query";
    private static final String UNICOM_PHONE_POOL_URL = "http://www.haoma517.com/api/order/phoneNumber-query";

    private static final String PHONE_SUBMIT_URL = "http://www.haoma517.com/api/order/submit";
    private static final String QUERY_DETAIL_URL = "http://www.haoma517.com/api/order/query";
    private final WaNumProperties waNumProperties;

    /**
     * 检查时否是回调白名单ip
     */
    public boolean isWhiteIp(String ip) {
        if (Objects.isNull(ip)) {
            return false;
        }
        List<String> whiteIpList = waNumProperties.getWhiteIpList();
        return whiteIpList.contains(ip);
    }

    /**
     * 获取 某个归属地的广电卡号码
     *
     * @param regionId 给的excel内的 city_code
     * @param phoneKey 查询关键字（任意位置），必须是3个以上的数字且不能是192
     */
    public List<String> getBroadnetNumByCity(String regionId, String phoneKey) {
        try {
            JSONObject req = new JSONObject();
            req.put("regionId", regionId);
            if (Objects.nonNull(phoneKey)) {
                req.put("numberKey", phoneKey);
            }
            log.info("广电获取号码请求参数:{}", req);
            JSONObject encryptData = encryptData(req.toJSONString());
            log.info("广电获取号码请求参数加密后:{}", encryptData);
            String result = HttpRequest.post(PHONE_POOL_URL)
                    .body(encryptData.toJSONString())
                    .timeout(10000)
                    .execute()
                    .body();
            log.info("获取号码返回结果:{}", result);
            JSONObject res = JSONObject.parseObject(result);
            String status = res.getString("status");
            if (Objects.equals("0000", status)) {
                return res.getJSONArray("list").toJavaList(String.class);
            }
            throw new ServiceException("广电获取号码异常: " + result);
        } catch (Exception e) {
            log.error("广电获取号码异常", e);
        }
        return new ArrayList<>();
    }

    /**
     * 获取 联通号码
     *
     * @param goodsCode 商品编码
     * @param regionId  给的excel内的 city_code
     * @param phoneKey  查询关键字
     * @param cityName      城市  需要传入中文名字
     */
    public List<WoAINumPhoneRespBo> getUnicomPhonePool(String goodsCode, String regionId, String phoneKey, String provinceName, String cityName) {
        try {
            JSONObject req = new JSONObject();
            if (Objects.nonNull(phoneKey)) {
                req.put("numberKey", phoneKey);
            }
            req.put("goodsCode", goodsCode);
            req.put("province", provinceName);
            req.put("city", cityName);
            log.info("联通获取号码请求参数:{}", req);
            JSONObject encryptData = encryptData(req.toJSONString());
            log.info("联通获取号码请求参数加密后:{}", encryptData);
            String result = HttpRequest.post(UNICOM_PHONE_POOL_URL)
                    .body(encryptData.toJSONString())
                    .timeout(10000)
                    .execute()
                    .body();
            log.info("联通获取号码返回结果:{}", result);
            //{"status":"0000","list":[{"serialNumber":"16651128314","advanceLimit":"0","monthFeeLimit":"0","monthLimit":"0","province":"江苏","city":"苏州市"},{"serialNumber":"18550813503","advanceLimit":"0","monthFeeLimit":"0","monthLimit":"0","province":"江苏","city":"苏州市"},{"serialNumber":"17625318767","advanceLimit":"0","monthFeeLimit":"0","monthLimit":"0","province":"江苏","city":"苏州市"},{"serialNumber":"18662313361","advanceLimit":"0","monthFeeLimit":"0","monthLimit":"0","province":"江苏","city":"苏州市"},...]}
            JSONObject res = JSONObject.parseObject(result);
            String status = res.getString("status");
            if (Objects.equals("0000", status)) {
                return res.getJSONArray("list").toJavaList(WoAINumPhoneRespBo.class);
            }
            throw new ServiceException("联通获取号码异常: " + result);
        } catch (Exception e) {
            log.error("联通获取号码异常", e);
        }
        return new ArrayList<>();
    }

    /**
     * 订单详情
     */
    public WaNumOrderDetailResp queryOrder(String orderNo) throws ServiceException {
        try {
            JSONObject req = new JSONObject();
            req.put("orderNo", orderNo);
            log.info("订单追踪请求参数:{}", req);
            JSONObject encryptData = encryptData(req.toJSONString());
            log.info("订单追踪请求参数加密后:{}", encryptData);
            String result = HttpRequest.post(QUERY_DETAIL_URL)
                    .body(encryptData.toJSONString())
                    .timeout(10000)
                    .execute()
                    .body();
            log.info("订单追踪返回结果:{}", result);
            return JSONObject.parseObject(result, WaNumOrderDetailResp.class);
        } catch (Exception e) {
            log.error("订单追踪异常", e);
            throw new ServiceException("查询订单详情失败");
        }
    }

    /**
     * 我爱号码网络下单接口
     * <p>
     * 请求成功
     *
     * @param req 请求参数
     */
    public WaNumSubmitResp submitOrder(WaNumSubmitReq req) throws ServiceException {
        try {
            log.info("提交领取请求参数:{}", req);
            JSONObject encryptData = encryptData(JSONObject.toJSONString(req));
            log.info("提交领取请求参数加密后:{}", encryptData);
            HttpResponse response = HttpRequest.post(PHONE_SUBMIT_URL)
                    .body(encryptData.toJSONString())
                    .timeout(10000)
                    .execute();
            String result = response.body();
            log.info("提交返回http状态：{}结果:{}", response.getStatus(), result);

            JSONObject res = JSONObject.parseObject(result);
            WaNumSubmitResp waNumSubmitResp = new WaNumSubmitResp();
            waNumSubmitResp.setStatus(res.getString("status"));
            waNumSubmitResp.setMessage(res.getString("message"));
            waNumSubmitResp.setData(res.getString("data"));
            return waNumSubmitResp;
        } catch (Exception e) {
            log.error("提交请求异常", e);
            throw new ServiceException("提交请求异常");
        }
    }


    /**
     * 数据加密json
     * AES/ECB/PKCS5Padding
     */
    private JSONObject encryptData(String data) throws Exception {
        JSONObject req = new JSONObject();
        req.put("source", waNumProperties.getSource());
        req.put("body", AESUtil.encrypt(data, waNumProperties.getAesKey()));
        return req;
    }

    static class AESUtil {

        private static final String KEY_ALGORITHM = "AES";
        private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";

        public AESUtil() {
        }

        /**
         * AES加密算法
         *
         * @param content 原字符串
         * @param key     加密密钥
         */
        public static String encrypt(String content, String key) throws Exception {
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            byte[] byteContent = content.getBytes(StandardCharsets.UTF_8);
            cipher.init(1, getSecretKey(key));
            byte[] result = cipher.doFinal(byteContent);
            return Base64.getEncoder().encodeToString(result);
        }

        /**
         * AES解密算法
         *
         * @param content 原字符串
         * @param key     解密密钥
         */
        public static String decrypt(String content, String key) throws Exception {
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            cipher.init(2, getSecretKey(key));
            byte[] result = cipher.doFinal(Base64.getDecoder().decode(content));
            return new String(result, StandardCharsets.UTF_8);

        }

        private static SecretKeySpec getSecretKey(final String key) throws NoSuchAlgorithmException {
            KeyGenerator kg = null;
            kg = KeyGenerator.getInstance(KEY_ALGORITHM);
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(key.getBytes());
            kg.init(128, secureRandom);
            SecretKey secretKey = kg.generateKey();
            return new SecretKeySpec(secretKey.getEncoded(), KEY_ALGORITHM);
        }

    }

}
