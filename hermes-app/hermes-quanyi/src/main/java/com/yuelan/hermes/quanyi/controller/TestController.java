package com.yuelan.hermes.quanyi.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.manager.MerchantAccessManager;
import com.yuelan.hermes.quanyi.biz.service.*;
import com.yuelan.hermes.quanyi.common.pojo.bo.MchThirdConfigBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.controller.request.QquRechargeNotifyRsp;
import com.yuelan.hermes.quanyi.controller.request.RsCreateOrderRequest;
import com.yuelan.hermes.quanyi.controller.response.RsResult;
import com.yuelan.hermes.quanyi.remote.QquManager;
import com.yuelan.hermes.quanyi.remote.RytManager;
import com.yuelan.hermes.quanyi.remote.TezhenManager;
import com.yuelan.hermes.quanyi.remote.response.QquOrderQueryRsp;
import com.yuelan.hermes.quanyi.remote.response.VProductBalanceRsp;
import com.yuelan.hermes.quanyi.remote.response.VProductRechargeRsp;
import com.yuelan.hermes.quanyi.remote.response.VProductReportRsp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Tag(name = "测试API")
@Slf4j
@RequestMapping("/test")
@RestController
public class TestController {

    @Autowired
    private QquManager qquManager;
    @Autowired
    private RytManager rytManager;
    @Autowired
    private RsOrderService rsOrderService;
    @Autowired
    private MerchantAccessManager merchantAccessManager;
    @Autowired
    private ThirdAccountService thirdAccountService;
    @Autowired
    private QquService qquService;
    @Autowired
    private TezhenManager tezhenManager;
    @Autowired
    private OrderVirtualMQService orderVirtualMQService;
    @Autowired
    private BenefitOrderDOService benefitOrderDOService;
    @Autowired
    private BenefitPlatformService benefitPlatformService;

    @PostMapping("/order/virtual/{mchId}")
    @Operation(summary = "虚拟订单")
    public RsResult unifiedOrder(@RequestBody RsCreateOrderRequest request, @PathVariable String mchId) {
        MchThirdConfigBO mchThirdConfigBO = merchantAccessManager.findOne(mchId);
        return rsOrderService.createOrder(request, mchThirdConfigBO);
    }

    @Operation(summary = "软游通单笔订单查询")
    @GetMapping(value = "/ryt/order/detail")
    public Integer orderDetial(String orderNo) {
        return rytManager.orderDetial(orderNo);
    }


    @Operation(summary = "话费充值回调")
    @PostMapping(value = "/order/notify")
    public String notify(@RequestBody QquRechargeNotifyRsp request) {
        return qquService.notify(request);
    }

    @Operation(summary = "回调测试")
    @PostMapping(value = "/notity")
    public String notity(@RequestBody Map map) {
        log.info("回调测试：{}", JSON.toJSONString(map));
        return "SUCCESS";
    }

    @Operation(summary = "话费订单查询")
    @GetMapping(value = "/orderDetail")
    public BizResult<QquOrderQueryRsp> orderDetail(@RequestParam String supplierOrderNo) {
        QquOrderQueryRsp result = qquManager.queryOrder(supplierOrderNo);
        return BizResult.create(result);
    }


    @Operation(summary = "天翼物联网消息推送")
    @PostMapping(value = "/aiot/notify")
    public BizResult<Boolean> aiotNotify(@RequestBody Map map) {
        log.info("天翼物联网推送{}", JSON.toJSONString(map));
        return BizResult.create(true);
    }

    @Operation(summary = "科能恒启账户余额检查")
    @GetMapping(value = "/knhqAccountCheck")
    public BizResult<Boolean> knhqAccountCheck() {
        thirdAccountService.knhqAccountCheck();
        return BizResult.create(true);
    }

    @Operation(summary = "特祯充值下单")
    @GetMapping(value = "/vproductrecharge")
    public BizResult<VProductRechargeRsp> vproductrecharge() {
        VProductRechargeRsp vproductrecharge =
                tezhenManager.vproductrecharge(IdUtil.getSnowflakeNextIdStr(), "***********", "1", "https://g.51mfu.com/qy/test/notity", new StringBuilder(), new StringBuilder());
        return BizResult.create(vproductrecharge);
    }

    @Operation(summary = "特祯订单查询")
    @GetMapping(value = "/vproductreport")
    public BizResult<VProductReportRsp> vproductreport(@RequestParam(required = false) String orderNo,
                                                       @RequestParam(required = false) String outOrderNo) {
        VProductReportRsp vproductreport = tezhenManager.vproductreport(orderNo, outOrderNo);
        return BizResult.create(vproductreport);
    }

    @Operation(summary = "特祯余额查询")
    @GetMapping(value = "/vproductbalance")
    public BizResult<VProductBalanceRsp> vproductbalance() {
        VProductBalanceRsp vproductbalance = tezhenManager.vproductbalance();
        return BizResult.create(vproductbalance);
    }

    @Operation(summary = "时代星辰回调补数据")
    @GetMapping(value = "/shiDaiFix")
    public BizResult<Boolean> keDangFix() {
        List<String> phones = new ArrayList<String>() {{
            add("18789117338");
            add("13687537362");
            add("18976781536");
            add("13518040050");
            add("13006097192");
            add("15120705397");
            add("18976086867");
            add("13876052725");
            add("18976350544");
        }};
        for (String phone : phones) {
            BenefitOrderDO lastPaySuccessOrder = benefitOrderDOService.getLastPaySuccessOrder(phone, 85L);
            if (Objects.isNull(lastPaySuccessOrder)) {
                continue;
            }
            benefitPlatformService.notifyMediaOrderResultAsync(lastPaySuccessOrder, "success");
        }
        return BizResult.create(Boolean.TRUE);
    }

    @PostMapping(value = "/testJson")
    public BizResult<JSONObject> orderCreateMsg(@RequestBody String json) {

        return BizResult.create(JSONObject.parseObject(json));
    }
}
    