package com.yuelan.hermes.quanyi.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class RechargerOrderDetailRsp {

    @Schema(description = "平台订单号")
    private String orderNo;

    @Schema(description = "商户订单号")
    private String outTradeNo;

    @Schema(description = "商品编号")
    private String skuNo;

    @Schema(description = "充值的手机号")
    private String mobile;

    @Schema(description = "充值金额")
    private BigDecimal amount;

    @Schema(description = "到账时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finishTime;

    @Schema(description = "订单状态")
    private Integer status;
}
