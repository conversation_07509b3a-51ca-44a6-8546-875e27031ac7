package com.yuelan.hermes.quanyi.remote.response;

import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.Data;

import java.util.Objects;

@Data
public class KnhqBaseRsp<T> {

    private Integer code;

    private String message;

    private T data;

    public boolean checkResult() {
        if (Objects.equals(0, code)) {
            return true;
        }
        throw BizException.create(BizErrorCodeEnum.KNHQ_ERROR, message);
    }
}
