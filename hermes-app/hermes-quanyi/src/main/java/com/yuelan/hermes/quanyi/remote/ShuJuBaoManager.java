package com.yuelan.hermes.quanyi.remote;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.pojo.properties.ShuJuBaoProperties;
import com.yuelan.hermes.quanyi.remote.request.ShuJuBaoThreeFactorAuthReq;
import com.yuelan.hermes.quanyi.remote.request.ShuJuBaoThreeFactorAuthResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR> 2025/6/6
 * @since 2025/6/6
 * <p>
 * 数据表三要素认证
 */
@Service
public class ShuJuBaoManager {
    private static final String URL = "https://api.chinadatapay.com/communication/personal/9725";

    @Resource
    private ShuJuBaoProperties shuJuBaoProperties;

    public static void main(String[] args) {
        ShuJuBaoManager shuJuBaoManager = new ShuJuBaoManager();
        shuJuBaoManager.shuJuBaoProperties = new ShuJuBaoProperties();

        ShuJuBaoThreeFactorAuthResp resp = shuJuBaoManager.getThreeFactorAuth("王强", "340222199110286619", "13989458839");
        System.out.println(JSONObject.toJSONString(resp));

    }

    public ShuJuBaoThreeFactorAuthResp getThreeFactorAuth(String name, String idCard, String mobile) {
        ShuJuBaoThreeFactorAuthReq authReq = new ShuJuBaoThreeFactorAuthReq();
        authReq.setKey(shuJuBaoProperties.getKey());
        authReq.setName(name);
        authReq.setIdcard(idCard);
        authReq.setMobile(mobile);
        // 对象转 map
        Map<String, Object> paramMap = JSONObject.from(authReq);

        String resp = HttpUtil.post(URL, paramMap);
        return JSONObject.parseObject(resp, ShuJuBaoThreeFactorAuthResp.class);
    }


}
