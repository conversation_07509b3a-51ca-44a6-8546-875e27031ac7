package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.yuelan.hermes.commons.enums.ZopOrderStatusEnum;
import com.yuelan.hermes.commons.excel.LocalDateTimeConverter;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.excel.converter.*;
import com.yuelan.hermes.quanyi.common.util.ZopOrderFailReasonUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> 2024/5/3 下午2:20
 */
@Data
@ContentRowHeight(15)
@HeadRowHeight(20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class EccZopOrderResp {

    /**
     * 主键
     */
    @Schema(description = "主键")
    @ColumnWidth(20)
    @ExcelProperty("订单ID")
    private Long zopOrderId;


    @Schema(description = "订单来源：1-API订单 2-商城订单（导入）")
    @ColumnWidth(20)
    @ExcelProperty(value = "订单来源", converter = ZopOrderSourceEnumConverter.class)
    private Integer zopOrderSource;

    /**
     * 我方订单号
     */
    @Schema(description = "我方订单号")
    @ColumnWidth(20)
    @ExcelProperty("悦蓝订单号")
    private String orderNo;

    @Schema(description = "原始订单号")
    @ColumnWidth(20)
    @ExcelProperty("原始订单号")
    private String originalOrderNo;

    /**
     * 渠道订单号
     */
    @Schema(description = "渠道订单号")
    @ColumnWidth(20)
    @ExcelProperty("渠道订单号")
    private String channelOrderNo;


    /**
     * 我方电商卡权益包id
     */
    @Schema(description = "我方电商卡权益包id")
    @ExcelIgnore
    private Long prodId;


    /**
     * 我方电商卡权益包名字
     */
    @Schema(description = "我方电商卡权益包名字")
    @ColumnWidth(20)
    @ExcelProperty("权益包")
    private String prodName;

    /**
     * 渠道类型
     *
     * @see com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum
     */
    @Schema(description = "渠道类型:1-内部渠道,2-外部渠道")
    @ColumnWidth(20)
    @ExcelProperty(value = "渠道类型", converter = EccChannenTypeConverter.class)
    private Integer channelType;
    /**
     * 推广渠道id
     */
    @Schema(description = "推广渠道id")
    @ExcelIgnore
    private Long channelId;

    /**
     * 推广渠道名字
     */
    @Schema(description = "推广渠道名字")
    @ColumnWidth(20)
    @ExcelProperty("推广渠道")
    private String channelName;


    /**
     * 身份证名字
     */
    @Schema(description = "身份证名字")
    @ColumnWidth(20)
    @ExcelProperty("身份证姓名")
    private String idCardName;

    /**
     * 身份证号码
     */
    @Schema(description = "身份证号码")
    @ColumnWidth(20)
    @ExcelProperty("身份证号码")
    private String idCard;


    /**
     * 省份
     */
    @Schema(description = "省份")
    @ColumnWidth(20)
    @ExcelProperty("省份")
    private String postProvince;


    /**
     * 市信息
     */
    @Schema(description = "市信息")
    @ColumnWidth(20)
    @ExcelProperty("市")
    private String postCity;


    /**
     * 区县信息
     */
    @Schema(description = "区县信息")
    @ColumnWidth(20)
    @ExcelProperty("区县")
    private String postDistrict;

    /**
     * 详细收货地址
     */
    @Schema(description = "详细收货地址")
    @ColumnWidth(20)
    @ExcelProperty("详细地址")
    private String address;

    /**
     * 收货手机号码
     */
    @Schema(description = "收货手机号码")
    @ColumnWidth(20)
    @ExcelProperty("收货手机号")
    private String contactPhone;

    /**
     * 选择的手机号码
     */
    @Schema(description = "选择的手机号码")
    @ColumnWidth(20)
    @ExcelProperty("领取号码")
    private String phone;

    /**
     * 号码归属地-省
     */
    @Schema(description = "号码归属地-省")
    @ColumnWidth(20)
    @ExcelProperty("号码归属地-省")
    private String province;


    /**
     * 号码归属地-市
     */
    @Schema(description = "号码归属地-市")
    @ColumnWidth(20)
    @ExcelProperty("号码归属地-市")
    private String city;


    /**
     * 联通zop发展人编号
     */
    @Schema(description = "联通zop发展人编号")
    @ColumnWidth(20)
    @ExcelProperty("联通发展人编号")
    private String zopReferrerCode;

    /**
     * 风控审核状态：0-默认状态，1-审核通过，2-审核不通过
     */
    @Schema(description = "风控审核状态：0-默认状态，1-审核通过，2-审核不通过")
    @ColumnWidth(20)
    @ExcelProperty(value = "风控审核状态", converter = EccRiskCheckStatusConverter.class)
    private Integer riskCheckStatus;

    /**
     * 风控审核不通过原因 只有审核不通过才有值
     */
    @Schema(description = "风控审核不通过原因 只有审核不通过才有值")
    @ColumnWidth(20)
    @ExcelProperty("风控审核拦截原因")
    private String riskCheckResp;


    /**
     * 选号接口状态：0-默认状态，1-选号成功，2-选号失败
     */
    @Schema(description = "选号接口状态：0-默认状态，1-选号成功，2-选号失败")
    @ColumnWidth(20)
    @ExcelProperty(value = "选号状态", converter = EccSelectNumStatusConverter.class)
    private Integer selectNumStatus;

    /**
     * 选号响应
     */
    @Schema(description = "选号响应")
    @ColumnWidth(20)
    @ExcelProperty("选号响应")
    private String selectNumResp;

    /**
     * 意向单状态：0-默认状态，1-下单成功，2-下单失败
     */
    @Schema(description = "意向单状态：0-默认状态，1-下单成功，2-下单失败")
    @ColumnWidth(20)
    @ExcelProperty(value = "意向单状态", converter = EccPreOrderStatusConverter.class)
    private Integer preOrderStatus;

    /**
     * 意向单响应结果
     */
    @Schema(description = "意向单响应结果")
    @ColumnWidth(20)
    @ExcelProperty("意向单响应")
    private String preOrderResp;

    /**
     * 预下单返回的订单号
     */
    @Schema(description = "预下单返回的订单号")
    @ColumnWidth(20)
    @ExcelProperty("联通订单号")
    private String preOrderNo;

    /**
     * 正式单同步状态：0-默认状态，1-同步成功，2-同步单失败
     */
    @Schema(description = "正式单同步状态：0-默认状态，1-同步成功，2-同步单失败")
    @ColumnWidth(20)
    @ExcelProperty(value = "正式单同步状态", converter = EccOrderSyncStatusConverter.class)
    private Integer orderSyncStatus;

    /**
     * 正式单同步响应内容
     */
    @Schema(description = "正式单同步响应内容")
    @ColumnWidth(20)
    @ExcelProperty("正式单同步响应")
    private String orderSyncResp;

    /**
     * 正式单同步之后响应的订单号
     */
    @Schema(description = "正式单同步之后响应的订单号")
    @ColumnWidth(20)
    @ExcelProperty("正式单同步订单号")
    private String syncOrderNo;

    /**
     * zop订单状态
     */
    @Schema(title = "zop订单状态", description = "1：激活,2：退单(激活前),3:转套餐(要根据产品id判断是否为享有特权的套餐),4：销户(激活后),6:首充数据同步,C1：开户完成,E0：发货")
    @ColumnWidth(20)
    @ExcelProperty(value = "zop订单状态", converter = ZopStateConverter.class)
    private String zopOrderState;

    @Schema(title = "首次充值金额")
    @ColumnWidth(20)
    @ExcelProperty("首充金额(分)")
    private String firstRechargeAmount;
    /**
     * 订单备注
     */
    @Schema(description = "订单备注")
    @ColumnWidth(20)
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "失败原因")
    @ColumnWidth(20)
    @ExcelProperty("失败原因")
    private String failReason;

    @Schema(description = "领卡状态")
    @ColumnWidth(20)
    @ExcelProperty("领卡状态")
    private String receiveCardStatus;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @ColumnWidth(20)
    @ExcelProperty(value = "创建时间", converter = LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "更新时间", converter = LocalDateTimeConverter.class)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    public static EccZopOrderResp buildResp(EccZopOrderDO eccZopOrderDO) {
        if (eccZopOrderDO == null) {
            return null;
        }
        EccZopOrderResp resp = new EccZopOrderResp();
        resp.setZopOrderId(eccZopOrderDO.getZopOrderId());
        resp.setZopOrderSource(eccZopOrderDO.getZopOrderSource());
        resp.setOrderNo(eccZopOrderDO.getOrderNo());
        resp.setOriginalOrderNo(eccZopOrderDO.getOriginalOrderNo());
        resp.setChannelOrderNo(eccZopOrderDO.getChannelOrderNo());
        resp.setProdId(eccZopOrderDO.getProdId());
        resp.setChannelType(eccZopOrderDO.getChannelType());
        resp.setProdName(eccZopOrderDO.getProdName());
        resp.setChannelId(eccZopOrderDO.getChannelId());
        resp.setChannelName(eccZopOrderDO.getChannelName());
        resp.setIdCardName(eccZopOrderDO.getIdCardName());
        resp.setIdCard(eccZopOrderDO.getIdCard());
        resp.setPostProvince(eccZopOrderDO.getPostProvince());
        resp.setPostCity(eccZopOrderDO.getPostCity());
        resp.setPostDistrict(eccZopOrderDO.getPostDistrict());
        resp.setAddress(eccZopOrderDO.getAddress());
        resp.setContactPhone(eccZopOrderDO.getContactPhone());
        resp.setPhone(eccZopOrderDO.getPhone());
        resp.setProvince(eccZopOrderDO.getProvince());
        resp.setCity(eccZopOrderDO.getCity());
        resp.setZopReferrerCode(eccZopOrderDO.getZopReferrerCode());
        resp.setRiskCheckStatus(eccZopOrderDO.getRiskCheckStatus());
        resp.setRiskCheckResp(eccZopOrderDO.getRiskCheckResp());
        resp.setPreOrderStatus(eccZopOrderDO.getPreOrderStatus());
        resp.setPreOrderResp(eccZopOrderDO.getPreOrderResp());
        resp.setPreOrderNo(eccZopOrderDO.getPreOrderNo());
        resp.setSelectNumStatus(eccZopOrderDO.getSelectNumStatus());
        resp.setSelectNumResp(eccZopOrderDO.getSelectNumResp());
        resp.setOrderSyncStatus(eccZopOrderDO.getOrderSyncStatus());
        resp.setOrderSyncResp(eccZopOrderDO.getOrderSyncResp());
        resp.setSyncOrderNo(eccZopOrderDO.getSyncOrderNo());
        resp.setZopOrderState(eccZopOrderDO.getZopOrderState());
        Integer firstRecharge = eccZopOrderDO.getFirstRecharge();
        if (Objects.nonNull(firstRecharge)) {
            // 分转成2为小数的圆显示hutool
            Number yuan = 100;
            resp.setFirstRechargeAmount(NumberUtil.div(firstRecharge, yuan, 2).toString());
        }
        resp.setRemark(eccZopOrderDO.getRemark());
        resp.setCreateTime(LocalDateTimeUtil.of(eccZopOrderDO.getCreateTime()));
        resp.setUpdateTime(LocalDateTimeUtil.of(eccZopOrderDO.getUpdateTime()));
        resp.setFailReason(ZopOrderFailReasonUtil.getFailReason(eccZopOrderDO));
        resp.setReceiveCardStatus(receiveCardStatus(eccZopOrderDO));
        return resp;
    }

    /**
     * 领卡状态
     */
    public static String receiveCardStatus(EccZopOrderDO eccZopOrderDO) {
        if (ZopOrderStatusEnum.SUCCESS.getCode().equals(eccZopOrderDO.getOrderSyncStatus())) {
            return "领卡成功";
        }
        if (ZopOrderStatusEnum.FAIL.getCode().equals(eccZopOrderDO.getRiskCheckStatus())) {
            return "风控拦截";
        }
        if (ZopOrderStatusEnum.FAIL.getCode().equals(eccZopOrderDO.getPreOrderStatus())) {
            return "预下单失败";
        }
        if (ZopOrderStatusEnum.FAIL.getCode().equals(eccZopOrderDO.getSelectNumStatus())) {
            return "选号失败";
        }
        if (ZopOrderStatusEnum.FAIL.getCode().equals(eccZopOrderDO.getOrderSyncStatus())) {
            return "同步订单失败";
        }
        return "领卡失败";
    }


}
