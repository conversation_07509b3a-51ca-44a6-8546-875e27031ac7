package com.yuelan.hermes.quanyi.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 话费订单
 */
@Data
public class OrderMobileRsp {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "平台订单号")
    private String outTradeNo;

    @Schema(description = "订单明细单号")
    private String itemNo;

    @Schema(description = "商户号")
    private String mchId;

    @Schema(description = "商户名称")
    private String merchantName;

    @Schema(description = "运营商")
    private Integer sp;

    @Schema(description = "充值类型")
    private Integer hasSlow;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "充值面额")
    private BigDecimal rechargeAmount;

    @Schema(description = "SKU编号")
    private String skuNo;

    @Schema(description = "SKU名称")
    private String skuName;

    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    @Schema(description = "下单时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "回调状态")
    private Integer notifyStatus;

    @Schema(description = "完成时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finishTime;

    @Schema(description = "充值明细链接")
    private String voucher;
}