package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.hermes.quanyi.common.enums.DispatchTimingEnum;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class BenefitsPackageItemSaveReq {

    @NotNull(message = "关联权益包id不能为空", groups = {AddGroup.class})
    @Schema(description = "关联权益包id")
    private Long packageId;

    @NotNull(message = "发放时机不能为空", groups = {AddGroup.class})
    @Schema(description = "发放时机：1-即时  2-用户领取")
    private Integer dispatchTiming;

    @NotEmpty(message = "关联商品ids不能为空", groups = {AddGroup.class})
    @Size(min = 1, message = "关联商品ids至少1个")
    @Schema(description = "关联商品ids")
    private List<Long> itemIds;

    public void checkReq() {
        DispatchTimingEnum statusEnum = LocalEnumUtils.findByCode(DispatchTimingEnum.class, dispatchTiming);
        if (Objects.isNull(statusEnum)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "发放时机值不合法");
        }
    }

}
