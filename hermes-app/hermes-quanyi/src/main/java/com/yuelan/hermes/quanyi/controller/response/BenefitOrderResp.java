package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/4/19 下午3:52
 */
@Data
public class BenefitOrderResp {


    /**
     * 订单id
     */
    @Schema(description = "订单id")
    private Long orderId;
    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;
    /**
     * 三方订单号(支付通道订单号)
     */
    @Schema(description = "三方订单号(支付通道订单号)")
    private String outOrderNo;
    /**
     * 投放渠道
     */
    @Schema(description = "投放渠道")
    private String distributionChannel;
    /**
     * 支付通道id
     */
    @Schema(description = "支付通道id")
    private Integer payChannelId;
    /**
     * 支付通道
     */
    @Schema(description = "支付通道名字")
    private String payChannel;
    /**
     * 支付通道id
     */
    @Schema(description = "支付通道包id")
    private Integer payChannelPkgId;
    /**
     * 支付通道
     */
    @Schema(description = "支付通道包名字")
    private String payChannelPkgName;
    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;
    /**
     * 权益包id
     */
    @Schema(description = "权益包id")
    private Long prodId;
    /**
     * 权益包名字
     */
    @Schema(description = "权益包名字")
    private String prodName;
    /**
     * 兑换次数限制
     */
    @Schema(description = "兑换次数限制(总可兑换次数)")
    private Integer redeemLimit;

    /**
     * 剩余可领取次数
     */
    @Schema(description = "剩余可领取次数")
    private Integer redeemRemain;


    /**
     * 周期类型：0-终生 1-自然月
     */
    @Schema(description = "周期类型：0-终生 1-自然月")
    private Integer cycleType;

    /**
     * 每个周期可以兑换的次数
     */
    @Schema(description = "每个周期可以兑换的次数")
    private Integer cycleRedeemLimit;

    /**
     * 金额
     */
    @Schema(description = "金额")
    private BigDecimal orderAmount;
    /**
     * 预下单状态：0-默认状态，1-下单成功，2-下单失败
     */
    @Schema(description = "预下单状态：0-默认状态，1-下单成功，2-下单失败")
    private Integer preorderStatus;
    /**
     * 下单响应内容
     */
    @Schema(description = "下单响应内容")
    private String preorderContent;
    /**
     * 支付状态: 0-等待用户支付 1-支付成功 2-支付失败
     */
    @Schema(description = "支付状态: 0-等待用户支付 1-支付成功 2-支付失败")
    private Integer payStatus;
    /**
     * 支付回调内容
     */
    @Schema(description = "支付回调内容")
    private String payNotifyContent;
    /**
     * 回调时间
     */
    @Schema(description = "回调时间")
    private LocalDateTime payNotifyTime;
    /**
     * 订单状态: 0-处理中 1-交易成功 2-交易失败 3-订单异常
     */
    @Schema(description = "订单状态: 0-处理中 1-交易成功 2-交易失败 3-订单异常")
    private Integer orderStatus;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    /**
     * 外部渠道名字
     */
    @Schema(description = "外部渠道名字")
    private String outChannelName;

    @Schema(description = "包名")
    private String packageName;

    @Schema(description = "城市地域编码")
    private Integer cityAdcode;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "提交验证码")
    private String smsCode;

    public static BenefitOrderResp buildResp(BenefitOrderDO orderDO) {
        if (orderDO == null) {
            return null;
        }
        BenefitOrderResp benefitOrderResp = new BenefitOrderResp();
        benefitOrderResp.setOrderId(orderDO.getOrderId());
        benefitOrderResp.setOrderNo(orderDO.getOrderNo());
        benefitOrderResp.setOutOrderNo(orderDO.getOutOrderNo());
        benefitOrderResp.setDistributionChannel(orderDO.getDistributionChannel());
        benefitOrderResp.setPayChannelId(orderDO.getPayChannelId());
        benefitOrderResp.setPayChannel(orderDO.getPayChannel());
        benefitOrderResp.setPayChannelPkgId(orderDO.getPayChannelPkgId());
        benefitOrderResp.setPayChannelPkgName(orderDO.getPayChannelPkgName());
        benefitOrderResp.setPhone(orderDO.getPhone());
        benefitOrderResp.setProdId(orderDO.getProdId());
        benefitOrderResp.setProdName(orderDO.getProdName());
        benefitOrderResp.setRedeemLimit(orderDO.getRedeemLimit());
        benefitOrderResp.setRedeemRemain(orderDO.getRedeemRemain());
        benefitOrderResp.setCycleType(orderDO.getCycleType());
        benefitOrderResp.setCycleRedeemLimit(orderDO.getCycleRedeemLimit());
        benefitOrderResp.setOrderAmount(orderDO.getOrderAmount());
        benefitOrderResp.setPreorderStatus(orderDO.getPreorderStatus());
        benefitOrderResp.setPreorderContent(orderDO.getPreorderContent());
        benefitOrderResp.setPayStatus(orderDO.getPayStatus());
        benefitOrderResp.setPayNotifyContent(orderDO.getPayNotifyContent());
        benefitOrderResp.setPayNotifyTime(orderDO.getPayNotifyTime());
        benefitOrderResp.setOrderStatus(orderDO.getOrderStatus());
        benefitOrderResp.setOutChannelName(orderDO.getOutChannelName());
        benefitOrderResp.setPackageName(orderDO.getPackageName());
        benefitOrderResp.setCityAdcode(orderDO.getCityAdcode());
        benefitOrderResp.setProvinceName(orderDO.getProvinceName());
        benefitOrderResp.setCityName(orderDO.getCityName());
        benefitOrderResp.setSmsCode(orderDO.getSmsCode());

        // date -> localDateTime
        benefitOrderResp.setCreateTime(LocalDateTimeUtil.of(orderDO.getCreateTime()));
        benefitOrderResp.setUpdateTime(LocalDateTimeUtil.of(orderDO.getUpdateTime()));

        return benefitOrderResp;
    }

}
