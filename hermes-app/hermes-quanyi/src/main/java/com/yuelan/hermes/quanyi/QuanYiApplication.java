package com.yuelan.hermes.quanyi;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableAsync
@EnableCaching
@EnableTransactionManagement
@SpringBootApplication
@EnableConfigurationProperties
@MapperScan("com.yuelan.hermes.quanyi.mapper")
public class QuanYiApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(QuanYiApplication.class);
        ConfigurableApplicationContext context = application.run(args);
        // 注册关闭钩子
        context.registerShutdownHook();
    }
}

