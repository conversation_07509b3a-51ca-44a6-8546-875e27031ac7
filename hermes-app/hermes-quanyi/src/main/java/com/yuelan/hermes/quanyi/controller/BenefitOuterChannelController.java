package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.biz.service.BenefitPlatformDOService;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitPlatformDO;
import com.yuelan.hermes.quanyi.controller.request.BenefitOuterChannelListReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitOuterChannelSaveReq;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Validated
@RestController
@Tag(name = "权益N选1/后台接口/外部渠道管理")
@RequiredArgsConstructor
@RequestMapping("/a/benefit/outerChannel")
public class BenefitOuterChannelController {


    @Resource
    private BenefitPlatformDOService benefitPlatformDOService;

    /**
     * 新增外部渠道
     */
    @Log(title = "新增权益外部渠道", type = OperationType.INSERT)
    @Operation(summary = "新增权益外部渠道")
    @PostMapping("/add")
    public BizResult<Void> add(@RequestBody @Validated(AddGroup.class) BenefitOuterChannelSaveReq req) {
        benefitPlatformDOService.save(req);
        return BizResult.ok();
    }

    /**
     * 更新外部渠道
     */
    @Log(title = "更新权益外部渠道", type = OperationType.UPDATE)
    @Operation(summary = "更新权益外部渠道")
    @PostMapping("/edit")
    public BizResult<Void> edit(@RequestBody @Validated(EditGroup.class) BenefitOuterChannelSaveReq req) {
        benefitPlatformDOService.update(req);
        return BizResult.ok();
    }

    /**
     * 权益外部渠道分页列表
     */
    @Operation(summary = "权益外部渠道分页列表")
    @PostMapping("/page")
    public BizResult<PageData<BenefitPlatformDO>> page(@RequestBody BenefitOuterChannelListReq req) {
        return BizResult.create(benefitPlatformDOService.pageList(req));
    }
}
