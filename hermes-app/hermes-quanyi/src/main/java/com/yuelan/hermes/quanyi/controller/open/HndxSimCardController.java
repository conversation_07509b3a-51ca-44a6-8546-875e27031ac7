package com.yuelan.hermes.quanyi.controller.open;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.manager.SuiXiaoCaManager;
import com.yuelan.hermes.quanyi.controller.request.HuNanDxOrderStatusChangeReq;
import com.yuelan.hermes.quanyi.controller.request.SxcOrderCreateReq;
import com.yuelan.hermes.quanyi.remote.response.HndxSimCardResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2025/6/17
 * @since 2025/6/17
 */
@Slf4j
@Tag(name = "湖南电信随销卡接口")
@RequestMapping("/sxc")
@RestController
@RequiredArgsConstructor
public class HndxSimCardController {

    private final SuiXiaoCaManager suiXiaoCaManager;

    /**
     * docs/随销卡/1-订单同步接口汇总（加入CPS规范、选号流程）20210705.docx-BPS互联网卡订单状态同步接口
     */
    @Operation(summary = "BPS订单状态变同步接口")
    @RequestMapping("/activation/notify")
    public HndxSimCardResp activationNotify(@RequestBody String reqBody) {
        log.info("BPS订单状态变同步接口: {} ", reqBody);
        try {
            HuNanDxOrderStatusChangeReq req = JSON.parseObject(reqBody, HuNanDxOrderStatusChangeReq.class);
            suiXiaoCaManager.orderStatusChange(req);
        } catch (Exception e) {
            log.error("BPS订单状态变同步接口处理异常: {}", e.getMessage(), e);
            HndxSimCardResp hndxSimCardResp = new HndxSimCardResp();
            hndxSimCardResp.setCode(500);
            hndxSimCardResp.setMessage("处理异常");
            return hndxSimCardResp;
        }

        HndxSimCardResp hndxSimCardResp = new HndxSimCardResp();
        hndxSimCardResp.success();
        return hndxSimCardResp;
    }


    @Operation(summary = "网厅-订单创建通知")
    @PostMapping("/order/create/notify")
    public HndxSimCardResp orderCreateNotify(@RequestBody String reqBody) {
        // 这里不用对象接受了 因为是运营商回调不确定是不是会有额外的参数
        log.info("网厅-订单创建通知: {}", reqBody);
        HndxSimCardResp hndxSimCardResp = new HndxSimCardResp();
        hndxSimCardResp.success();
        try {
            JSONObject reqJson = JSONObject.parseObject(reqBody);
            SxcOrderCreateReq sxcOrderCreateReq = reqJson.to(SxcOrderCreateReq.class);
            suiXiaoCaManager.createNcOrder(sxcOrderCreateReq);
        } catch (Exception e) {
            log.error("网厅-订单创建通知处理异常: {}", e.getMessage(), e);
            hndxSimCardResp.setCode(500);
            hndxSimCardResp.setMessage("处理异常");
            return hndxSimCardResp;
        }
        return hndxSimCardResp;
    }
}
