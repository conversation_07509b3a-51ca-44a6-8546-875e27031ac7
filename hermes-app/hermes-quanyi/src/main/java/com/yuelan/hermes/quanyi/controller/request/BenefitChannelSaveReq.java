package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitChannelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/4/2 16:24
 */
@Data
public class BenefitChannelSaveReq {
    /**
     * 渠道id
     */
    @NotNull(message = "渠道id不能为空", groups = {EditGroup.class})
    @Schema(description = "渠道id")
    private Long channelId;

    /**
     * 渠道名字
     */
    @NotEmpty(message = "渠道名字不能为空", groups = {AddGroup.class})
    @Schema(description = "渠道名字")
    private String channelName;

    public BenefitChannelDO convert() {
        BenefitChannelDO channelDO = new BenefitChannelDO();
        channelDO.setChannelId(channelId);
        channelDO.setChannelName(channelName);
        return channelDO;
    }
}
