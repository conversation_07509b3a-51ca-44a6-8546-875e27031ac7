package com.yuelan.hermes.quanyi.remote.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2024/11/22
 * @since 2024/11/22
 */
@Data
public class WaNumOrderDetailResp {

    /**
     * 状态码：0000
     */
    public String status;

    /**
     * 订单详情
     */
    public OrderDetail data;


    public boolean isSuccessful() {
        return "0000".equals(status);
    }


    @Data
    public static class OrderDetail {

        /**
         * 订单号
         */
        @Schema(description = "订单号")
        private String orderNo;

        /**
         * 状态中文描述
         */
        @Schema(description = "状态中文描述")
        private String status;

        /**
         * 三方订单号
         */
        @Schema(description = "三方订单号")
        private String serialNumber;

        /**
         * phoneNumber
         */
        @Schema(description = "选购的手机号码")
        private String phoneNumber;

        /**
         * expressCompany
         */
        @Schema(description = "快递公司")
        private String expressCompany;

        /**
         * expressNo
         */
        @Schema(description = "快递单号")
        private String expressNo;

        /**
         * 首冲金额 元
         */
        @Schema(description = "首冲金额 元")
        private String firstChargeFee;

        /**
         * 订单备注（下单失败时为失败原因）
         */
        @Schema(description = "订单备注（下单失败时为失败原因）")
        private String remark;

        /**
         * 操作记录（目前只有宽带产品才有）
         */
        private List<OperatorRecord> operatorList;
    }

    /**
     * 操作记录
     */
    @Data
    public static class OperatorRecord {

        /**
         * 操作类型描述
         */
        private String operatorType;

        /**
         * 详细描述
         */
        private String dealContent;

        /**
         * 操作时间
         */
        private String operatorTime;
    }


}
