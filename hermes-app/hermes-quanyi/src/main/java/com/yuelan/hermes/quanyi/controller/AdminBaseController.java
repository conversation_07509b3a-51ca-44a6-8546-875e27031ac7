package com.yuelan.hermes.quanyi.controller;

import cn.dev33.satoken.session.SaSession;
import com.yuelan.hermes.quanyi.config.satoken.StpAdminUtil;
import com.yuelan.hermes.quanyi.config.satoken.context.AdminContext;
import com.yuelan.plugins.satoken.able.IController;
import com.yuelan.plugins.satoken.utils.SaUtils;

/**
 * <p>用户</p>
 *
 * <AUTHOR>
 * @date 2021/12/10
 */
public abstract class AdminBaseController implements IController<AdminContext> {

    @Override
    public AdminContext getLoginUser() {
        SaSession session = StpAdminUtil.getSession();
        return SaUtils.getUserInfo(session, AdminContext.class);
    }
}
