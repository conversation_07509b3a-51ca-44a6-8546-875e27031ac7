package com.yuelan.hermes.quanyi.controller;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderDOService;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderLogDO;
import com.yuelan.hermes.quanyi.controller.request.BenefitOrderListReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitOrderLogReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitOrderResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.hermes.quanyi.mapper.BenefitOrderLogDOMapper;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2024/4/19 下午3:41
 */
@Validated
@RestController
@Tag(name = "权益N选1/后台接口/权益包订单")
@RequiredArgsConstructor
@RequestMapping("/a/benefit/order")
public class BenefitOrderController {

    private final BenefitOrderDOService benefitOrderDOService;
    private final BenefitOrderLogDOMapper benefitOrderLogDOMapper;

    @Operation(summary = "权益包订单列表")
    @PostMapping("/list")
    public BizResult<PageData<BenefitOrderResp>> list(@RequestBody BenefitOrderListReq req) {
        return BizResult.create(benefitOrderDOService.pageList(req));
    }

    @Operation(summary = "导出权益包订单列表")
    @PostMapping(value = "/exportListExcel")
    public BizResult<FileExportTaskCreateResp> downloadTemplate(@RequestBody BenefitOrderListReq req) {
        return BizResult.create(benefitOrderDOService.exportListExcel(req));
    }

    @Operation(summary = "权益包订单列表")
    @PostMapping("/log")
    public BizResult<BenefitOrderLogDO> log(@RequestBody BenefitOrderLogReq req) {
        BenefitOrderLogDO item = benefitOrderLogDOMapper.selectOne(Wrappers.<BenefitOrderLogDO>lambdaQuery()
                .eq(BenefitOrderLogDO::getOrderNo, req.getOrderNo()));

        if (item == null) {
            return BizResult.create(null);
        }

        // 装配响应
        List<BenefitOrderLog.Item> list = JSON.parseArray("[" + item.getContent() + "]", BenefitOrderLog.Item.class);
        item.setContent(JSON.toJSONString(list));

        return BizResult.create(item);
    }

    @Operation(summary = "下游回调重发（技术使用，无前端）")
    @PostMapping("/reCallback")
    public BizResult<Integer> reCallback(@RequestBody Map<String, List<String>> reqMap) {
        Integer result = benefitOrderDOService.reCallback(reqMap.get("orderNoList"));
        return BizResult.create(result);
    }

    @Operation(summary = "回调第三方订单（技术使用，无前端）")
    @PostMapping("/third/callback")
    public BizResult<Void> thirdCallback(@RequestBody BenefitOrderListReq req) {
        benefitOrderDOService.thirdCallback(req);
        return BizResult.ok();
    }

    @Operation(summary = "异步请求，刷新查询订单的归属地（技术使用，无前端）")
    @PostMapping("/async/reAreaCode")
    public BizResult<Void> reAreaCode(@RequestBody BenefitOrderListReq req) {
        benefitOrderDOService.asyncReAreaCode(req);
        return BizResult.ok();
    }

}
