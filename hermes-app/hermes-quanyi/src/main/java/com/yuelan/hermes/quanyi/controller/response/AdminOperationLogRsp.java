package com.yuelan.hermes.quanyi.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 管理员操作日志
 */
@Data
public class AdminOperationLogRsp {

    @Schema(description = "日志ID")
    private Long logId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "操作时间")
    private Date logTime;

    @Schema(description = "管理员ID")
    private Long adminId;

    @Schema(description = "管理员名称")
    private String adminName;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "请求url")
    private String url;

    @Schema(description = "操作ip")
    private String ip;

    @Schema(description = "操作状态（0正常 1异常）")
    private Integer status;

    @Schema(description = "错误信息")
    private String errorMsg;


}