package com.yuelan.hermes.quanyi.controller;


import com.yuelan.hermes.commons.enums.AdminStatusEnum;
import com.yuelan.hermes.quanyi.biz.service.AdminService;
import com.yuelan.hermes.quanyi.config.satoken.context.AdminContext;
import com.yuelan.hermes.quanyi.controller.request.AdminListReq;
import com.yuelan.hermes.quanyi.controller.request.AdminLogReq;
import com.yuelan.hermes.quanyi.controller.request.AdminModifyPasswordReq;
import com.yuelan.hermes.quanyi.controller.request.AdminRegisterReq;
import com.yuelan.hermes.quanyi.controller.response.AdminOperationLogRsp;
import com.yuelan.hermes.quanyi.controller.response.AdminRegisterRsp;
import com.yuelan.hermes.quanyi.controller.response.AdminRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(name = "管理员API")
@RestController
@RequestMapping("/a/admin")
public class AdminController extends AdminBaseController {

    @Autowired
    private AdminService adminService;

    @Operation(summary = "管理员列表")
    @PostMapping("/list")
    public BizResult<PageData<AdminRsp>> adminList(@Valid @RequestBody AdminListReq adminListReq) {
        PageData<AdminRsp> result = adminService.adminList(adminListReq);
        return BizResult.create(result);
    }

    @Operation(summary = "管理员用户详情")
    @GetMapping("/detail")
    public BizResult<AdminRsp> adminDetail(@RequestParam Long adminId) {
        AdminRsp adminRsp = adminService.adminDetail(adminId);
        return BizResult.create(adminRsp);
    }

    @Log(title = "新增管理员", type = OperationType.INSERT)
    @Operation(summary = "新增管理员")
    @PostMapping("/add")
    public BizResult<AdminRegisterRsp> register(@Valid @RequestBody AdminRegisterReq registerReq) {
        AdminContext loginUser = this.getLoginUser();
        AdminRegisterRsp adminRegisterRsp = adminService.addAdmin(registerReq, loginUser);
        return BizResult.create(adminRegisterRsp);
    }

    @Log(title = "禁用管理员账号", type = OperationType.UPDATE)
    @Operation(summary = "禁用账号")
    @PostMapping("/{adminId}/disable")
    public BizResult<Boolean> disableAdmin(@PathVariable Long adminId) {
        AdminContext loginUser = this.getLoginUser();
        Boolean result = adminService.disableOrEnableAdmin(adminId, AdminStatusEnum.DISABLE, loginUser);
        return BizResult.create(result);
    }

    @Log(title = "启用管理员账号", type = OperationType.UPDATE)
    @Operation(summary = "启用账号")
    @PostMapping("/{adminId}/enable")
    public BizResult<Boolean> enableAdmin(@PathVariable Long adminId) {
        AdminContext loginUser = this.getLoginUser();
        Boolean result = adminService.disableOrEnableAdmin(adminId, AdminStatusEnum.ENABLE, loginUser);
        return BizResult.create(result);
    }

    @Log(title = "重置管理员密码", type = OperationType.UPDATE)
    @Operation(summary = "重置密码")
    @PostMapping("/{adminId}/password/reset")
    public BizResult<AdminRegisterRsp> reset(@PathVariable Long adminId) {
        AdminContext loginUser = this.getLoginUser();
        AdminRegisterRsp result = adminService.resetPassword(adminId, loginUser);
        return BizResult.create(result);
    }

    @Log(title = "管理员修改密码", type = OperationType.UPDATE)
    @Operation(summary = "修改密码")
    @PostMapping("/password/modify")
    public BizResult<Boolean> modify(@Valid @RequestBody AdminModifyPasswordReq passwordReq) {
        AdminContext loginUser = this.getLoginUser();
        Boolean result = adminService.modifyPassword(passwordReq, loginUser);
        return BizResult.create(result);
    }

    @Operation(summary = "操作日志")
    @PostMapping("/logs")
    public BizResult<PageData<AdminOperationLogRsp>> logs(@RequestBody AdminLogReq req) {
        PageData<AdminOperationLogRsp> result = adminService.logs(req);
        return BizResult.create(result);
    }
}
