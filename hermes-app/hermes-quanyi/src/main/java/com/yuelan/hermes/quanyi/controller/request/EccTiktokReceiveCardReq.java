package com.yuelan.hermes.quanyi.controller.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/9/6 19:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EccTiktokReceiveCardReq extends EccInnerChannelGetCardSelectPhoneReq {
    /**
     * 抖音用户用户ID
     */
    private String openId;

    private String appId;
    /**
     * 我方系统sku信息 因为有时候一个电商卡 对应多个sku信息比如有时候117G投放 有时候按135投放
     */
    @NotEmpty(message = "skuId不能为空")
    private String skuId;

}
