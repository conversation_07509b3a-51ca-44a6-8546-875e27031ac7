package com.yuelan.hermes.quanyi.controller.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.SortEnum;
import com.yuelan.hermes.quanyi.common.enums.GoodsVirtualStockSortFiledEnum;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Objects;


@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GoodsVirtualStockReq extends PageRequest {

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "SKU ID")
    private Long skuId;

    @Schema(description = "SKU名称")
    private String skuName;

    @Schema(description = "SKU编号")
    private String skuNo;

    @Schema(description = "排序字段")
    private Integer filed;

    @Schema(description = "排序方式")
    private Integer direction;
    /**
     * 排序
     */
    @JsonIgnore
    private String orderBy;

    public void check() {
        GoodsVirtualStockSortFiledEnum goodsVirtualStockSortFiledEnum = LocalEnumUtils.findByCode(GoodsVirtualStockSortFiledEnum.class, filed);
        if (Objects.isNull(goodsVirtualStockSortFiledEnum)) {
            goodsVirtualStockSortFiledEnum = GoodsVirtualStockSortFiledEnum.SUK_ID;
        }
        SortEnum sortEnum = LocalEnumUtils.findByCode(SortEnum.class, direction);
        if (Objects.isNull(sortEnum)) {
            sortEnum = SortEnum.DESC;
        }
        orderBy = goodsVirtualStockSortFiledEnum.getFiled() + " " + sortEnum.getDirection();
    }
}

