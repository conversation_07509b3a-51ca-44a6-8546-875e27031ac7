package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR> 2024/5/2 上午10:36
 */
@Data
public class EccGoodsResp {


    /**
     * id
     */
    @Schema(description = "商品id")
    private Long goodsId;

    /**
     * 权益商品名字
     */
    @Schema(description = "权益商品名字")
    private String goodsName;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Integer supplierId;

    /**
     * 供应商名字
     */
    @Schema(description = "供应商名字")
    private String supplierName;

    /**
     * 兑换方式：1-直充无库存，2-cdKey(兑换码)
     */
    @Schema(description = "兑换方式：1-直充无库存，2-兑换码")
    private Integer redeemType;


    /**
     * 供应商商品编码 （兑换方式=直充时，直充面值和供应商商品编码必填其一）
     */
    @Schema(description = "供应商商品编码 （兑换方式=直充时，直充面值和供应商商品编码必填其一）")
    private String supplierGoodsNo;


    /**
     * 充值面值 （兑换方式=直充时，直充面值和供应商商品编码必填其一）
     */
    @Schema(description = "兑换方式 （兑换方式=直充时，直充面值和供应商商品编码必填其一）")
    private BigDecimal parValue;

    /**
     * 销售价格
     */
    @Schema(description = "销售价格")
    private BigDecimal price;

    /**
     * 成本价格
     */
    @Schema(description = "成本价格")
    private BigDecimal costPrice;

    /**
     * 商品上下架状态：0-下架，1-上架
     */
    @Schema(description = "商品上下架状态：0-下架，1-上架")
    private Integer goodsStatus;


    @Schema(description = "库存数量(未过期未使用)")
    private Integer stock;



    public static EccGoodsResp buildResp(EccGoodsDO goodsDO) {
        if (Objects.isNull(goodsDO)) {
            return null;
        }
        EccGoodsResp resp = new EccGoodsResp();
        resp.setGoodsId(goodsDO.getGoodsId());
        resp.setGoodsName(goodsDO.getGoodsName());
        resp.setSupplierId(goodsDO.getSupplierId());
        resp.setSupplierName(goodsDO.getSupplierName());
        resp.setRedeemType(goodsDO.getRedeemType());
        resp.setSupplierGoodsNo(goodsDO.getSupplierGoodsNo());
        resp.setParValue(goodsDO.getParValue());
        resp.setPrice(goodsDO.getPrice());
        resp.setCostPrice(goodsDO.getCostPrice());
        resp.setGoodsStatus(goodsDO.getGoodsStatus());
        resp.setStock(goodsDO.getStock());
        return resp;
    }

}
