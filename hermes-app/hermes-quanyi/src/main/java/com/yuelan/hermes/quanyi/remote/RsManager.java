package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.commons.enums.NotifyStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.RsOrderStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.MchThirdConfigBO;
import com.yuelan.hermes.quanyi.common.pojo.converter.RSConverter;
import com.yuelan.hermes.quanyi.common.pojo.domain.OrderOnlineDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.OrderOnlineItemDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.RsProperties;
import com.yuelan.hermes.quanyi.common.util.RSAHelper;
import com.yuelan.hermes.quanyi.controller.response.RsVoucherResult;
import com.yuelan.hermes.quanyi.mapper.OrderOnlineMapper;
import com.yuelan.hermes.quanyi.remote.request.RsAsyncNotifyReq;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class RsManager {


    @Autowired
    private RsProperties rsProperties;
    @Autowired
    private OrderOnlineMapper orderOnlineMapper;


    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public boolean orderComplete(OrderOnlineDO orderOnlineDO, List<OrderOnlineItemDO> itemList, MchThirdConfigBO mchThirdConfigBO) {
        //荣数信息订单号
        String cupdOrderNo = orderOnlineDO.getOutOrderNo();
        //第三方供应商订单号
        String thirdOrderNo = orderOnlineDO.getOrderNo();
        //订单状态
        RsOrderStatusEnum rsOrderStatusEnum = RsOrderStatusEnum.findByOrderStatus(orderOnlineDO.getOrderStatus());
        //参数
        RsAsyncNotifyReq rsAsyncNotifyReq = new RsAsyncNotifyReq();
        rsAsyncNotifyReq.setThirdOrderStatus(rsOrderStatusEnum.getCode());
        //是否卡密
        if (CollectionUtil.isNotEmpty(itemList)) {
            //卡密或代销
            List<RsVoucherResult> ordVoucherList = RSConverter.toRsVoucherResultList(itemList);
            rsAsyncNotifyReq.setOrdVoucherList(ordVoucherList);
        }

        //查询配置
        String sign = "";
        String rsaData = "";
        try {
            RSAPublicKey pemRSAPublicKey = RSAHelper.getPemRSAPublicKey(mchThirdConfigBO.getThirdPublicKeyBase64());
            RSAPrivateKey rsaPrivateKey = RSAHelper.getPemRSAPrivateKey(mchThirdConfigBO.getPrivateKeyBase64());
            String data = JSON.toJSONString(rsAsyncNotifyReq);
            rsaData = RSAHelper.encrypt(pemRSAPublicKey, data);
            sign = RSAHelper.sign(data, rsaPrivateKey);
        } catch (Exception e) {
            log.error("荣数下单异步通知,请求参数加密错误.orderNo:{},", thirdOrderNo, e);
            throw BizException.create(BizErrorCodeEnum.RS_SIGN_FAIL);
        }
        String result = null;
        if (!AppConstants.isReal()) {
            result = "SUCCESS";
        } else {
            JSONObject body = new JSONObject();
            body.put("thirdOrderNo", thirdOrderNo);
            body.put("cupdOrderNo", cupdOrderNo);
            body.put("data", rsaData);
            body.put("sign", sign);
            result = HttpUtil.post(rsProperties.getCallbackNotifyUrl(), body, 10000);
            log.info("荣数下单异步通知.body:{},result:{}", body.toJSONString(), result);
        }
        //更新通知状态
        NotifyStatusEnum notifyStatus = null;
        boolean flag = false;
        if (Objects.equals(result, "SUCCESS")) {
            notifyStatus = NotifyStatusEnum.SUCCESS;
            flag = true;
        } else {
            notifyStatus = NotifyStatusEnum.FAIL;
        }
        orderOnlineMapper.updateNotifyStatus(orderOnlineDO.getId(), notifyStatus.getCode());
        return flag;
    }

}
