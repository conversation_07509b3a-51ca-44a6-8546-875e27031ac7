package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.handler.impl.BenefitMiGuProxyPayChannel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@Tag(name = "新游-咪咕全国代理版本")
@RequestMapping("/newgame/migu")
@SaIgnore
@RestController
public class MiGuProxyController {
    @Resource
    private BenefitMiGuProxyPayChannel benefitMiGuProxyPayChannel;

    @Operation(summary = "回调")
    @PostMapping(value = "/order/notify")
    public String notify(@RequestBody String params) {
        return benefitMiGuProxyPayChannel.payNotify(params);
    }

}
