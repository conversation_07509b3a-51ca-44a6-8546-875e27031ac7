package com.yuelan.hermes.quanyi.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> 2025/3/17
 * @since 2025/3/17
 */
@Slf4j
@SaIgnore
@RestController
@RequestMapping("/mall/login/smsCode")
@RequiredArgsConstructor
public class MallLoginSmsCodeController {

    private final RedissonClient redissonClient;


    /**
     * 商城登录短信发送到运营手机转发到手机端
     */
    @RequestMapping("/notify")
    @Operation(summary = "接受手机端收到的短信")
    public BizResult<Void> smsNotify(@RequestBody String smsContent) {
        // {"data":{"text":"【中国联通】验证码：663390，感谢您使用网上营业厅"},"msg":null,"code":"0"}
        // 从短信内容中提取验证码
        log.info("接收到短信内容: {}", smsContent);
        JSONObject jsonObject = JSONObject.parseObject(smsContent);
        String smsText = jsonObject.getString("text");
        // 提取验证码 正则6位数字
        Pattern pattern = Pattern.compile("\\d{6}");
        Matcher matcher = pattern.matcher(smsText);
        if (matcher.find()) {
            String smsCode = matcher.group();
            log.info("提取到验证码: {}", smsCode);
            // push到redis
            redissonClient.getTopic("mall_login_sms_code").publish(smsCode);
        }
        return BizResult.ok();
    }


}
