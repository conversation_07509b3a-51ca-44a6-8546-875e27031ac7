package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.hermes.quanyi.remote.request.QquBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
public class QquRechargeNotifyRsp extends QquBaseReq {

    /**
     * 平台订单号
     */
    private String number;
    /**
     * 用户订单号
     */
    private String orderId;
    /**
     * 充值金额
     */
    private BigDecimal amount;
    /**
     * 充值的手机号
     */
    private String mobile;
    /**
     * 充值到账时间
     */
    private String createdAt;
    /**
     * SUCCESS 充值成功
     * FAIL 充值失败
     */
    private String status;
    /**
     * 充值明细链接
     */
    private String voucher;
    /**
     * 运营商
     */
    private String phoneType;
}
