package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.BenefitOrderItemDOService;
import com.yuelan.hermes.quanyi.controller.request.BenefitOrderItemListReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitOrderItemResp;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskCreateResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/4/19 下午3:41
 */
@Validated
@RestController
@Tag(name = "权益N选1/后台接口/权益包子订单")
@RequiredArgsConstructor
@RequestMapping("/a/benefit/orderItem")
public class BenefitOrderItemController {

    private final BenefitOrderItemDOService benefitOrderItemDOService;

    @Operation(summary = "权益包子订单")
    @PostMapping("/list")
    public BizResult<PageData<BenefitOrderItemResp>> list(@RequestBody BenefitOrderItemListReq req) {
        return BizResult.create(benefitOrderItemDOService.pageList(req));
    }

    @Operation(summary = "权益包子订单导出")
    @PostMapping("/listExport")
    public BizResult<FileExportTaskCreateResp> listExport(@RequestBody BenefitOrderItemListReq req) {
        return BizResult.create(benefitOrderItemDOService.listExport(req));
    }

}
