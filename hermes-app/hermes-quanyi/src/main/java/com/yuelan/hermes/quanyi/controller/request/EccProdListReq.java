package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR> 2024/4/2 16:24
 */

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EccProdListReq extends PageRequest {
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long prodId;

    /**
     * 产品名字
     */
    @Schema(description = "产品名字")
    private String prodName;

    /**
     * 权益包编码
     */
    @Schema(description = "权益包编码")
    private String prodCode;


    /**
     * 上下架状态：0-下架；1-上架
     */
    @Schema(description = "上下架状态：0-下架；1-上架")
    private Integer prodStatus;

    public Wrapper<EccProductDO> buildQueryWrapper() {
        return Wrappers.lambdaQuery(EccProductDO.class)
                .eq(prodId != null, EccProductDO::getProdId, prodId)
                .like(!StrUtil.isBlank(prodName), EccProductDO::getProdName, prodName)
                .eq(Objects.nonNull(prodCode), EccProductDO::getProdCode, prodCode)
                .eq(Objects.nonNull(prodStatus), EccProductDO::getProdStatus, prodStatus)
                .orderByDesc(EccProductDO::getProdId);
    }
}
