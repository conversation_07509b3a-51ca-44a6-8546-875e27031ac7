package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR> 2024/4/2 16:24
 */

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BenefitProdListReq extends PageRequest {
    /**
     * 权益包id
     */
    @Schema(description = "权益包id")
    private Long prodId;

    /**
     * 权益包名字
     */
    @Schema(description = "权益包名字")
    private String prodName;

    /**
     * 支付通道id
     */
    @Schema(description = "支付通道id")
    private Integer payChannelId;


    /**
     * 支付通道包id
     */
    @Schema(description = "支付通道包id")
    private Integer payChannelPkgId;

    /**
     * 投放渠道
     */
    @Schema(description = "投放渠道")
    private String distributionChannel;

    /**
     * 上下架状态：0-下架；1-上架
     */
    @Schema(description = "上下架状态：0-下架；1-上架")
    private Integer prodStatus;

    public Wrapper<BenefitProductDO> buildQueryWrapper() {
        return Wrappers.lambdaQuery(BenefitProductDO.class)
                .eq(getProdId() != null, BenefitProductDO::getProdId, getProdId())
                .like(getProdName() != null, BenefitProductDO::getProdName, getProdName())
                .eq(getPayChannelId() != null, BenefitProductDO::getPayChannelId, getPayChannelId())
                .eq(getPayChannelPkgId() != null, BenefitProductDO::getPayChannelPkgId, getPayChannelPkgId())
                .like(getDistributionChannel() != null, BenefitProductDO::getDistributionChannel, getDistributionChannel())
                .eq(getProdStatus() != null, BenefitProductDO::getProdStatus, getProdStatus())
                .orderBy(Objects.equals(getOrderBy(), "createTime"), isAsc(), BenefitProductDO::getCreateTime)
                .orderByDesc(Objects.isNull(getOrderBy()), BenefitProductDO::getProdId);
    }
}
