package com.yuelan.hermes.quanyi.controller.open;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.service.RytService;
import com.yuelan.hermes.quanyi.controller.request.RytOrderNotifyReq;
import com.yuelan.hermes.quanyi.remote.response.RytRsp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "软游通API")
@RequestMapping("/ryt")
@RestController
public class RytController {
    @Autowired
    private RytService rytService;


    @Operation(summary = "线上订单充值回调")
    @RequestMapping(value = "/order/notify", method = {RequestMethod.GET, RequestMethod.POST})
    public RytRsp orderOnlineNotify(RytOrderNotifyReq request) {
        if (log.isDebugEnabled()) {
            log.info("软游通充值回调:{}", JSON.toJSONString(request));
        }
        //TODO 这里我们不验签（正常应验签），直接处理业务逻辑
        boolean result = rytService.orderOnlineNotify(request);
        if (result) {
            return RytRsp.success();
        } else {
            return RytRsp.error(500, "充值回调处理失败");
        }
    }

    @Operation(summary = "线下订单充值回调")
    @RequestMapping(value = "/orderOffline/notify", method = {RequestMethod.GET, RequestMethod.POST})
    public RytRsp orderOfflineNotify(RytOrderNotifyReq request) {
        log.info("软游通充值回调:{}", JSON.toJSONString(request));
        //TODO 这里我们不验签（正常应验签），直接处理业务逻辑
        boolean result = rytService.orderOfflineNotify(request);
        if (result) {
            return RytRsp.success();
        } else {
            return RytRsp.error(500, "充值回调处理失败");
        }
    }

    /**
     * 通用回调、通过event 到不同业务处理 以后不需要再写多个接口
     */
    @Operation(summary = "软游通通用回调")
    @RequestMapping(value = "/common/order/notify", method = {RequestMethod.GET, RequestMethod.POST})
    public RytRsp commOrderNotify(RytOrderNotifyReq request) {
        log.info("软游通通用充值回调:{}", JSON.toJSONString(request));
        return rytService.commonOrderNotify(request);
    }


}
