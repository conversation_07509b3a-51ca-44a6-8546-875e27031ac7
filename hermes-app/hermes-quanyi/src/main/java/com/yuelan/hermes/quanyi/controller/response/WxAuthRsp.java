package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class WxAuthRsp {

    @Schema(description = "用户唯一标识")
    private String openid;

    @Schema(description = "微信开放平台用户ID")
    private String unionId;

    @Schema(description = "网页授权接口调用凭证")
    private String accessToken;

    @Schema(description = "接口调用凭证过期时间")
    private Long expiresAt;

    @Schema(description = "用户刷新access_token")
    private String refreshToken;

    @Schema(description = "用户授权的作用域，使用,分隔")
    private String scope;
}
