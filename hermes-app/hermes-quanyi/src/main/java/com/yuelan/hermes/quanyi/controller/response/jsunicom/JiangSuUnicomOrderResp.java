package com.yuelan.hermes.quanyi.controller.response.jsunicom;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/6/25
 * @description:
 */

@NoArgsConstructor
@Data
public class JiangSuUnicomOrderResp {

    @Schema(description = "调用结果编码，标记下单是否成功，0000为下单成功，下单成功不代表订购成功，但下单失败即订购失败")
    private String RSP_CODE;

    private RspDataDTO RSP_DATA;

    private String RSP_DESC;

    @Schema(description = "预下单的时候返回,正常下单不返回")
    private String outOrderId;

    @Data
    public static class RspDataDTO {

        @Schema(description = "透传订单中心的值，对于需要支付的商品，有个跳转支付的连接")
        private String PAY_URL;

        @Schema(description = "订单号")
        private String ORDER_ID;
    }
}
