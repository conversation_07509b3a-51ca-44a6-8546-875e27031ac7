package com.yuelan.hermes.quanyi.controller;


import com.yuelan.hermes.quanyi.biz.service.GoodsVirtualService;
import com.yuelan.hermes.quanyi.controller.request.GoodsVirtualListReq;
import com.yuelan.hermes.quanyi.controller.request.GoodsVirtualReq;
import com.yuelan.hermes.quanyi.controller.response.GoodsVirtualDetailRsp;
import com.yuelan.hermes.quanyi.controller.response.GoodsVirtualRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(name = "虚拟商品管理")
@RequestMapping("/a/goodsVirtual")
@RestController
public class GoodsVirtualController extends AdminBaseController {
    @Autowired
    GoodsVirtualService goodsVirtualService;


    @Operation(summary = "虚拟商品列表")
    @PostMapping("/list")
    public BizResult<PageData<GoodsVirtualRsp>> goodsVirtualList(@RequestBody GoodsVirtualListReq req) {
        PageData<GoodsVirtualRsp> resultData = goodsVirtualService.goodsVirtualList(req);
        return BizResult.create(resultData);
    }

    @Operation(summary = "虚拟商品详情")
    @GetMapping("/detail")
    public BizResult<GoodsVirtualDetailRsp> goodsVirtualDetail(@RequestParam Long goodsId) {
        GoodsVirtualDetailRsp resultData = goodsVirtualService.goodsVirtualDetail(goodsId);
        return BizResult.create(resultData);
    }

    @Operation(summary = "获取SKU编号")
    @GetMapping("/skuNo")
    public BizResult<String> getSkuNo() {
        String skuNo = goodsVirtualService.getSkuNo();
        return BizResult.create(skuNo);
    }

    @Log(title = "虚拟商品新增/编辑", type = OperationType.OTHER)
    @PostMapping("/save")
    @Operation(summary = "虚拟商品新增/编辑")
    public BizResult<Long> goodsVirtualSave(@Valid @RequestBody GoodsVirtualReq goodsVirtualReq) {
        Long id = goodsVirtualService.goodsVirtualSave(goodsVirtualReq);
        return BizResult.create(id);
    }

    @Log(title = "虚拟商品删除", type = OperationType.DELETE)
    @PostMapping("/delete")
    @Operation(summary = "虚拟商品删除")
    public BizResult<Boolean> goodsVirtualDel(@RequestParam Long id) {
        Boolean result = goodsVirtualService.goodsVirtualDel(id);
        return BizResult.create(result);
    }


}
