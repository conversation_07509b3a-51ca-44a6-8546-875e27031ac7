package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.FileExportTaskDOService;
import com.yuelan.hermes.quanyi.controller.request.FileExportTaskReq;
import com.yuelan.hermes.quanyi.controller.response.FileExportTaskPageResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/7/11 上午10:15
 */
@Validated
@Tag(name = "导出任务下载中心")
@RestController
@RequestMapping("/fileExportTask")
@AllArgsConstructor
public class FileExportTaskController {
    private FileExportTaskDOService fileExportTaskDOService;

    @Operation(summary = "列表")
    @PostMapping("/page")
    public BizResult<PageData<FileExportTaskPageResp>> list(@RequestBody FileExportTaskReq req) {
        return BizResult.create(fileExportTaskDOService.pageList(req));
    }


}
