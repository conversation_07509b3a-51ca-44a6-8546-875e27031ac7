package com.yuelan.hermes.quanyi.remote.response;


import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.Objects;

@Data
public class TeZhenResult {
    /**
     * 000.提交成功
     * 001.账号错误
     * 002.加密错误
     * 003.时间戳错误
     * 004.号码错误
     * 005.产品错误
     * 006.产品类型错误
     * 007.订单号重复(异常处理，需要人工核实)
     * 008.余额不足
     * 009.数据异常(异常处理，需要人工核实)
     * 010.系统异常(异常处理，需要人工核实)
     */
    @JSONField(name = "Code")
    private String code;
    @JSONField(name = "Msg")
    private String msg;

    public boolean isSuccess() {
        return Objects.equals("000", code);
    }

}
