package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.config.satoken.context.AdminContext;
import com.yuelan.plugins.satoken.able.IController;

/**
 * 渠道用户基础控制器
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public abstract class ChannelBaseController implements IController<AdminContext> {

    @Override
    public AdminContext getLoginUser() {
        // SaSession session = StpChannelUtil.getSession();
        // return SaUtils.getUserInfo(session, ChannelContext.class);
        return null;
    }

    /**
     * 获取当前登录的渠道ID
     *
     * @return 渠道ID
     */
    protected Long getCurrentChannelId() {
        return null;
        // ChannelContext context = getLoginUser();
        // return context != null ? context.getOuterChannelId() : null;
    }

    /**
     * 获取当前登录的管理员ID
     *
     * @return 管理员ID
     */
    protected Long getCurrentAdminId() {
        return null;
        // ChannelContext context = getLoginUser();
        // return context != null ? context.getAdminId() : null;
    }

    /**
     * 检查当前用户是否可以管理下级渠道
     *
     * @return true表示可以管理
     */
    protected boolean canManageSubChannels() {
        return false;
        // ChannelContext context = getLoginUser();
        // return context != null && context.getChannelDepth() != null && context.getChannelDepth() < 2;
    }

    /**
     * 检查当前用户是否可以设置扣量比例
     *
     * @return true表示可以设置
     */
    protected boolean canSetDeductionRate() {
        return false;
        // ChannelContext context = getLoginUser();
        // return context != null && context.getChannelDepth() != null && context.getChannelDepth() < 2;
    }
}
