package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.properties.FeiHanProperties;
import com.yuelan.hermes.quanyi.remote.request.FeiHanPayReq;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.00
 * @description: 飞翰ofpay
 * http://openapi.ofpay.com/web.do#/webWebPage/detail/PAGE/WELCOME
 * @ClassName: FeiHanManager
 * @Date 2024/3/28 17:50
 */
@Slf4j
@Component
public class FeiHanManager {
    @Autowired
    private FeiHanProperties feiHanProperties;

    /**
     * 飞翰ofpay直充卡密类通用接口（供应商下单）
     * http://openapi.ofpay.com/web.do#/webInterfaceDetail/12f28e4b-62fc-4d97-bb16-6b1beafd8e0f
     *
     * @param payReq 要用到的订单参数
     */
    public String sendOrder(FeiHanPayReq payReq) {
//        if (!AppConstants.isReal()) {
//            return IdUtil.fastSimpleUUID();
//        }
        String userId = feiHanProperties.getUserId();
        String userPws = feiHanProperties.getUserPws();
        String cardId = payReq.getSupplierGoodsNo();
        String buyNum = "1";
        String spOrderId = payReq.getOrderNo();
        String orderTime = DateUtil.format(payReq.getOrderTime(), "yyyyMMddHHmmss");
        String customerNo = payReq.getUsePhone();
        String md5Key = feiHanProperties.getMd5Key();
        String reqUrl = feiHanProperties.getPayReqUrl();
        String callbackUrl = payReq.getCallbackUrl();

        Map<String, Object> reqMap = new HashMap<String, Object>() {{
            // userId - 为SP项目编码， 登陆api.ofpay.com创建，在我司商务审核通过后，会自动生成一个A开头的编号 如：A08566
            put("userId", userId);
            // userPws - 为项目密码的md5值(32位小写) 如md5(of111111) 新建项目在审核通过后系统会自动生成项目密码发送短信至创建人手机上
            put("userPws", userPws);
            // cardId - 所需提货商品的编码固定值由我司商务提供。话费固定值为140101
            put("cardId", cardId);
            // buyNum - 购买数量
            put("buyNum", buyNum);
            // spOrderId - Sp商家的订单号 唯一
            put("spOrderId", spOrderId);
            // orderTime - 订单时间 （yyyyMMddHHmmss 如：20070323140214）
            put("orderTime", orderTime);
            // customerNo - 充值账号(中文需要URLEncode编码指定字符集GBK)
            put("customerNo", customerNo);
            // md5Str - 包体= userId+userPws+cardId+buyNum+spOrderId+orderTime+customerNo。1: 对: “包体+KeyStr” 这个串进行md5 的32位值. 结果大写；2:KeyStr实际上线需要联系商务获取（可以修改）。；3:
            // KeyStr 不在接口间进行传送。；4.符号+不参与加密
            put("md5Str", DigestUtil.md5Hex(userId + userPws + cardId + buyNum + spOrderId + orderTime + customerNo + md5Key).toUpperCase());
            // retUrl - 充值回调地址，回调说明参考1.3章节。不传，可通过基础接口中查询订单状态接口获取充值结果
            put("retUrl", callbackUrl);
            // version - 固定值 6.0
            put("version", "6.0");
            // memberId - C端用户唯一标识
            // put("memberId", "CZY");
            // extendParams - 根据实际需要传值,格式：{"specialKey":"立减金批次","appId":"微信官方分配的appId","flowId":"行方流水号","marketingActivityId":"活动编号","bankTag1":"分行标识1",
            // "bankTag2":"分行标识2","integralValue":"积分（金币）金额","couponValue":"券金额","cashValue":"现金","paymentId":"支付流水"} ,值encodeUrlGBK传入.
            // put("extendParams", JSONUtil.createObj().set("test", "555"));
            // format - 需要报文返回json格式时传
            put("format", "json");
        }};
        JSONObject respJson;
        try {
            reqUrl = reqUrl + "?" + HttpUtil.toParams(reqMap, CharsetUtil.CHARSET_UTF_8, true);
            log.info("飞翰ofpay直充下单请求地址 -> {}", reqUrl);
            String respStr = HttpUtil.post(reqUrl, "", 10000);
            log.info("飞翰ofpay直充下单响应参数 -> {}", respStr);
            // {"orderCash":1,"buyNum":"1","cardName":"浙江移动手机快充1元","orderId":"S2403285022890","cardId":"141510","errMsg":"",
            // "spOrderId":"0a41d49664324422bbd363962291e547","retCode":"1","customerNo":"***********","orderState":0}
            respJson = JSONUtil.parseObj(respStr);
        } catch (Exception e) {
            log.error("飞翰ofpay直充下单异常 -> {}", payReq, e);
            throw BizException.create(BizErrorCodeEnum.FEIHAN_ERROR, "飞翰ofpay直充下单异常");
        }

        String retCode = respJson.getStr("retCode");
        if (StrUtil.equals(retCode, "1")) {
            // orderId - 飞翰订单号
            return respJson.getStr("orderId");
        } else {
            throw BizException.create(BizErrorCodeEnum.FEIHAN_ERROR, "飞翰ofpay直充下单失败");
        }
    }


    /**
     * 飞翰ofpay根据外部订单号查询订单充值状态。此接口用户下单之后查询充值状态。
     * http://openapi.ofpay.com/web.do#/webInterfaceDetail/47452c8d-5a10-429f-b2f7-56422bbe5704
     * 返回0、1、9、-1  0 表示充值中，充值中的订单需要等待结果。9 充值失败，可以给客户退款。1 充值成功。
     * -1 表示查不到此订单，此时不能作为失败处理该订单，需要联系欧飞人工核实。
     *
     * @param spOrderNo sp订单号
     */
    public String queryOrderStatus(String spOrderNo) {
        String userId = feiHanProperties.getUserId();
        String reqUrl = feiHanProperties.getQueryOrderUrl();
        Map<String, Object> reqMap = new HashMap<String, Object>() {{
            // userid - 为SP项目编码， 登陆api.ofpay.com创建，在我司商务审核通过后，会自动生成一个A开头的编号 如：A08566
            put("userid", userId);
            // spbillid - sp商户订单号
            put("spbillid", spOrderNo);
        }};
        String orderStatus = "";
        try {
            log.info("飞翰ofpay查询订单充值状态请求参数 -> {}", reqMap);
            // 返回0、1、9、-1  0 表示充值中，充值中的订单需要等待结果。9 充值失败，可以给客户退款。1 充值成功。-1 表示查不到此订单，此时不能作为失败处理该订单，需要联系欧飞人工核实。
            orderStatus = HttpUtil.get(reqUrl, reqMap);
            log.info("飞翰ofpay查询订单充值状态响应参数 -> {}", orderStatus);
        } catch (Exception e) {
            throw BizException.create(BizErrorCodeEnum.FEIHAN_ERROR, "飞翰ofpay查询订单充值状态异常");
        }
        return orderStatus;
    }
}
