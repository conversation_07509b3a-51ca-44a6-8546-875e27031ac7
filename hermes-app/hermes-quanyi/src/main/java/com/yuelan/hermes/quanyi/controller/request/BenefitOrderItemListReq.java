package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderItemDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/4/19 下午3:48
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class BenefitOrderItemListReq extends PageRequest {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long itemId;
    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;
    /**
     * 关联总订单号
     */
    @Schema(description = "关联总订单号")
    private String orderNo;
    /**
     * 明细订单号
     */
    @Schema(description = "明细订单号")
    private String itemNo;
    /**
     * 关联权益包id
     */
    @Schema(description = "关联权益包id")
    private Long prodId;
    /**
     * 关联权益商品id
     */
    @Schema(description = "关联权益商品id")
    private Long goodsId;
    /**
     * 供应商类型
     */
    @Schema(description = "供应商类型")
    private Integer supplierType;
    /**
     * 供应商商品编号
     */
    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;
    /**
     * 供应商订单号
     */
    @Schema(description = "供应商订单号")
    private String supplierOrderNo;
    /**
     * 预下单状态，预下单状态：0-默认状态，1-下单成功，2-下单失败
     */
    @Schema(description = "预下单状态，预下单状态：0-默认状态，1-下单成功，2-下单失败")
    private Integer preorderStatus;
    /**
     * 订单状态0处理中1交易成功2交易失败3订单异常
     */
    @Schema(description = "订单状态:0-处理中 1-交易成功 2-交易失败 3-订单异常")
    private Integer orderStatus;

    /**
     * 创建时间-开始
     */
    @Schema(description = "兑换发起时间-开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间-结束
     */
    @Schema(description = "兑换发起时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "包名")
    private String packageName;


    public Wrapper<BenefitOrderItemDO> buildQueryWrapper() {
        return Wrappers.<BenefitOrderItemDO>lambdaQuery()
                .eq(itemId != null, BenefitOrderItemDO::getItemId, itemId)
                .eq(phone != null, BenefitOrderItemDO::getPhone, phone)
                .eq(orderNo != null, BenefitOrderItemDO::getOrderNo, orderNo)
                .eq(itemNo != null, BenefitOrderItemDO::getItemNo, itemNo)
                .eq(prodId != null, BenefitOrderItemDO::getProdId, prodId)
                .eq(goodsId != null, BenefitOrderItemDO::getGoodsId, goodsId)
                .eq(supplierType != null, BenefitOrderItemDO::getSupplierType, supplierType)
                .eq(supplierGoodsNo != null, BenefitOrderItemDO::getSupplierGoodsNo, supplierGoodsNo)
                .eq(preorderStatus != null, BenefitOrderItemDO::getPreorderStatus, preorderStatus)
                .eq(orderStatus != null, BenefitOrderItemDO::getOrderStatus, orderStatus)
                .ge(createTimeStart != null, BenefitOrderItemDO::getCreateTime, createTimeStart)
                .le(createTimeEnd != null, BenefitOrderItemDO::getCreateTime, createTimeEnd)
                .orderByDesc(BenefitOrderItemDO::getItemId);
    }
}
