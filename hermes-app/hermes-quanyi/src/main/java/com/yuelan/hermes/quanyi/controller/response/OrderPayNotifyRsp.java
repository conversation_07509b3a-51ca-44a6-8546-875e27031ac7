package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class OrderPayNotifyRsp {

    @Schema(description = "返回状态码")
    private String code;

    @Schema(description = "返回信息")
    private String msg;

    public static OrderPayNotifyRsp success() {
        OrderPayNotifyRsp orderPayNotifyRsp = new OrderPayNotifyRsp();
        orderPayNotifyRsp.setCode("success");
        orderPayNotifyRsp.setMsg("成功");
        return orderPayNotifyRsp;
    }

    public static OrderPayNotifyRsp fail() {
        OrderPayNotifyRsp orderPayNotifyRsp = new OrderPayNotifyRsp();
        orderPayNotifyRsp.setCode("fail");
        orderPayNotifyRsp.setMsg("失败");
        return orderPayNotifyRsp;
    }

    public static OrderPayNotifyRsp fail(String msg) {
        OrderPayNotifyRsp orderPayNotifyRsp = new OrderPayNotifyRsp();
        orderPayNotifyRsp.setCode("fail");
        orderPayNotifyRsp.setMsg(msg);
        return orderPayNotifyRsp;
    }
}
