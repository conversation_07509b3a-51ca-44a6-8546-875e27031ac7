package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2025/6/13
 * @since 2025/6/13
 */
@Data
public class ImeiActivationStatusQueryReq {

    @Schema(description = "IMEI号")
    @NotEmpty(message = "IMEI号不能为空")
    private String imei;

    @Schema(description = "请求时间戳（秒）")
    @NotNull(message = "请求时间戳不能为空")
    private Long timestamp;

    @Schema(description = "apiKey（我方提供）")
    @NotEmpty(message = "apiKey不能为空")
    private String apiKey;

    @Schema(description = "签名(签名算法请参考文档说明)")
    @NotEmpty(message = "签名不能为空")
    private String sign;
}
