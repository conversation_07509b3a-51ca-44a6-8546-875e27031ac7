package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.handler.impl.TelecomServerByKeDangImpl;
import com.yuelan.hermes.quanyi.remote.request.KeDangCallbackDataReq;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/12/31
 * @since 2024/12/31
 */
@SaIgnore
@Slf4j
@Tag(name = "可当号卡")
@RequestMapping("/kedang")
@RestController
@AllArgsConstructor
public class KeDangController {

    private final TelecomServerByKeDangImpl keDangNumberCardServer;

    @PostMapping("/notify")
    public String callback(@RequestBody String body) {
        log.info("KeDangController notify: {}", body);
        try {
            KeDangCallbackDataReq keDangCallbackDataReq = JSONObject.parseObject(body, KeDangCallbackDataReq.class);
            keDangNumberCardServer.dealCallback(keDangCallbackDataReq);
            return "success";
        } catch (Exception e) {
            log.error("KeDangController notify error", e);
        }
        return "fail";
    }
}
