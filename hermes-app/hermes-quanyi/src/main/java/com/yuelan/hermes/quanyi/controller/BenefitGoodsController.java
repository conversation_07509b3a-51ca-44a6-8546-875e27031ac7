package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.core.validator.validate.QueryGroup;
import com.yuelan.hermes.quanyi.biz.service.BenefitGoodsService;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO;
import com.yuelan.hermes.quanyi.controller.request.BenefitGoodsListReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitGoodsSaveReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitGoodsUpdateStatusReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitGoodsResp;
import com.yuelan.hermes.quanyi.controller.response.SupplierResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> 2024/4/2 15:46
 */
@Validated
@RestController
@Tag(name = "权益N选1/后台接口/权益商品")
@RequiredArgsConstructor
@RequestMapping("/a/benefit/goods")
public class BenefitGoodsController {

    private final BenefitGoodsService benefitGoodsService;

    @Operation(summary = "权益商品详情")
    @GetMapping("/detail/{goodsId}")
    public BizResult<BenefitGoodsResp> detail(@PathVariable Long goodsId) {
        BenefitGoodsDO goodsDO = benefitGoodsService.getById(goodsId);
        return BizResult.create(BenefitGoodsResp.buildResp(goodsDO));
    }

    @Operation(summary = "权益商品列表")
    @PostMapping("/list")
    public BizResult<PageData<BenefitGoodsResp>> list(@Validated(QueryGroup.class) @RequestBody BenefitGoodsListReq req) {
        return BizResult.create(benefitGoodsService.list(req));
    }

    @Log(title = "新增权益商品", type = OperationType.INSERT)
    @Operation(summary = "新增权益商品")
    @PostMapping("/save")
    public BizResult<Void> save(@Validated(AddGroup.class) @RequestBody BenefitGoodsSaveReq req) {
        benefitGoodsService.save(req);
        return BizResult.ok();
    }

    @Log(title = "编辑权益商品", type = OperationType.UPDATE)
    @Operation(summary = "编辑权益商品")
    @PostMapping("/edit")
    public BizResult<Void> edit(@Validated(EditGroup.class) @RequestBody BenefitGoodsSaveReq req) {
        benefitGoodsService.updateById(req);
        return BizResult.ok();
    }

    @Log(title = "权益商品上架/下架", type = OperationType.UPDATE)
    @Operation(summary = "权益商品上架/下架")
    @PostMapping("/updateStatus")
    public BizResult<Void> updateStatus(@RequestBody @Validated BenefitGoodsUpdateStatusReq req) {
        req.checkReq();
        benefitGoodsService.updateStatusById(req);
        return BizResult.ok();
    }

    @Operation(summary = "获取权益商品支持的供应商")
    @GetMapping("/getSuppliers")
    public BizResult<List<SupplierResp>> getSuppliers() {
        return BizResult.create(benefitGoodsService.getSuppliers());
    }

}
