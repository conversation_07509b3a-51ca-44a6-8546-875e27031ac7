package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class BenefitBlackUserResp {

    @Schema(description = "id")
    private Long id;
    /**
     * 支付通道id
     */
    @Schema(description = "支付通道id")
    private Integer payChannelId;
    /**
     * 支付通道
     */
    @Schema(description = "支付通道名字")
    private String payChannel;
    /**
     * 支付通道id
     */
    @Schema(description = "支付通道包id")
    private Integer payChannelPkgId;
    /**
     * 支付通道
     */
    @Schema(description = "支付通道包名字")
    private String payChannelPkgName;
    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;
    /**
     * 包名
     */
    @Schema(description = "包名")
    private String packageName ;
    /**
     * 业务类型
     */
    @Schema(description = "1-手机号 2-包名")
    private Integer bizType;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;
}
