package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.converters.bigdecimal.BigDecimalStringConverter;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuelan.hermes.commons.excel.GamingOrderStatusPageDescConverter;
import com.yuelan.hermes.commons.excel.MgMonthlyTypeEnumConverter;
import com.yuelan.result.entity.KeyValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 电竞卡订单
 */
@Data
@ContentRowHeight(15)
@HeadRowHeight(20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class GamingOrderListRsp {

    @ExcelIgnore
    @Schema(description = "订单ID")
    private Long orderId;

    @ColumnWidth(20)
    @ExcelProperty(value = "产品订单号")
    @Schema(description = "订单号")
    private String orderNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "商户订单号")
    @Schema(description = "商户订单号")
    private String outOrderNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "商户ID")
    @Schema(description = "商户ID")
    private Long merchantId;

    @ColumnWidth(20)
    @ExcelProperty(value = "商户名称")
    @Schema(description = "商户名称")
    private String merchantName;

    @ExcelIgnore
    @Schema(description = "第三方用户ID")
    private String outUserId;

    @ColumnWidth(20)
    @ExcelProperty(value = "用户手机号")
    @Schema(description = "第三方用户手机号")
    private String phone;

    @ExcelIgnore
    @Schema(description = "产品ID")
    private Long productId;

    @ColumnWidth(20)
    @ExcelProperty(value = "产品编码")
    @Schema(description = "产品编码")
    private String productCode;

    @ColumnWidth(20)
    @ExcelProperty(value = "产品名称")
    @Schema(description = "产品名称")
    private String productName;

    @ColumnWidth(20)
    @ExcelProperty(value = "订单金额", converter = BigDecimalStringConverter.class)
    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "包月类型", converter = MgMonthlyTypeEnumConverter.class)
    @Schema(description = "包月类型 0-首次订购/点播 1-续订")
    private Integer monthlyType;

    @ExcelIgnore
    @Schema(description = "产品类型 1-点播 2-权益（包月）")
    private Integer productType;

    @ColumnWidth(20)
    @ExcelProperty(value = "兑换状态", converter = GamingOrderStatusPageDescConverter.class)
    @Schema(description = "订单状态")
    private KeyValue<Integer, String> orderStatus;

    @ExcelIgnore
    @Schema(description = "通知状态")
    private KeyValue<Integer, String> notifyStatus;

    @ExcelIgnore
    @Schema(description = "短信发送状态，用户短信下发状态 0-待发送 1-短信下发成功 2-短信下发失败 null-无需短信通知")
    private Integer smsSendStatus;

    @ExcelIgnore
    @Schema(description = "权益领取通知状态(用户首次领取兑换码类型权益成功后通知咪咕接口) 0-待领取后通知 1-通知成功 2-通知失败 null-无需通知")
    private Integer obtainStatusNotify;


    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "订单时间")
    @Schema(description = "下单时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "更新时间")
    @Schema(description = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}