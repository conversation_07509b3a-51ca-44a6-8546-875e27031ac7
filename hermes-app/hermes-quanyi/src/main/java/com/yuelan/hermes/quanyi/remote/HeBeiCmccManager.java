package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.commons.util.ExecutorServiceUtils;
import com.yuelan.hermes.quanyi.biz.service.BenefitPlatformService;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.HeBeiCityCodeEnum;
import com.yuelan.hermes.quanyi.common.enums.HeBeiCmccNotRecommendedEnum;
import com.yuelan.hermes.quanyi.common.enums.HeBeiPayPkgEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitUnicomPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.HbCmccProperties;
import com.yuelan.hermes.quanyi.controller.request.HeBeiCmccOrderReq;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.controller.response.HeBeiCmccBaseRes;
import com.yuelan.hermes.quanyi.controller.response.HeBeiCmccOrderRes;
import com.yuelan.hermes.quanyi.controller.response.HeBeiCmccRecommendedRes;
import com.yuelan.hermes.quanyi.controller.response.HeBeiCmccUserInfoRes;
import com.yuelan.plugins.redisson.util.RedisUtils;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
@RequiredArgsConstructor
public class HeBeiCmccManager {

    public static final String TAG = "[河北移动]";

    private static final Integer TIME_OUT = 10000;
    private static final String SUCCESS_CODE = "0";

    // 获取请求token的接口
    private static final String CLIENT_OAUTH_URL = "/OAuth/restOauth2Server/access_token";
    // 客户归属地判断接口
    private static final String GET_USER_INFO_URL = "/OpenEbus/httpService/UserService?method=QryUserInfoFW&format=json";
    // 发送短信验证码
    private static final String SEND_SMS_CODE_URL = "/OpenEbus/httpService/UserService?method=SendSmsRandomPassForGoods&format=json";
    // 创建订单
    private static final String CREATE_ORDER_URL = "/OpenEbus/httpService/UserService?method=PTCommonOrderCommitForCP&format=json";
    // 精准营销
    private static final String RECOMMENDED_OFFER_NEW = "/OpenEbus/httpService/UserQueryService?method=getRecommendedOfferNew&format=json";

    @Resource
    private HbCmccProperties hbCmccProperties;
    @Resource
    @Lazy
    private BenefitPlatformService benefitPlatformService;

    /**
     * 应用认证 并且设置缓存
     *
     * @return token值 用在其他接口中
     */
    private String clientOAuth() {
        String url = hbCmccProperties.getHost() + CLIENT_OAUTH_URL;
        String body = String.format("grant_type=client_credentials&client_id=%s&client_secret=%s", hbCmccProperties.getAppId(), hbCmccProperties.getSecretKey());
        HttpRequest request = HttpRequest.post(url)
                .contentType(ContentType.FORM_URLENCODED.getValue())
                .body(body)
                .timeout(TIME_OUT);
        log.info(TAG + "发送[应用认证] 请求:{}", request);
        HttpResponse response = request.execute();
        log.info(TAG + "请求[应用认证] 响应:{}", response);
        if (!response.isOk()) {
            throw BizException.create(BaseErrorCodeEnum.INTERNAL_SERVER_ERROR);
        }
        JSONObject respJson = JSONObject.parseObject(response.body());
        String access_token = respJson.getString("access_token");
        Integer expiresIn = respJson.getInteger("expires_in");
        String cacheKey = RedisKeys.getHbCmccToken(hbCmccProperties.getAppId());
        RedisUtils.setCacheObject(cacheKey, access_token, Duration.ofSeconds(expiresIn));
        return access_token;
    }

    /**
     * 获取 请求的token
     */
    private String getToken() {
        String cacheKey = RedisKeys.getHbCmccToken(hbCmccProperties.getAppId());
        Object cacheVal = RedisUtils.getCacheObject(cacheKey);
        if (Objects.nonNull(cacheVal)) {
            return String.valueOf(cacheVal);
        }
        return clientOAuth();
    }

    /**
     * 点击发送验证码
     */
    public boolean orderSendSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        String mobile = reqParams.getMobile();
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        HeBeiPayPkgEnum pkgEnum = HeBeiPayPkgEnum.of(productDO.getPayChannelPkgId());
        HbCmccProperties.Config config = hbCmccProperties.getProperties(pkgEnum);
        // 校验用户
        this.checkUserInfo(mobile);
        // 精准营销
        this.checkRecommendedOfferNew(mobile, config.getGOODSID());
        // 运营商下发验证码
        return this.sendSmsCode(mobile, config.getGOODSID());
    }

    /**
     * 申请订单
     */
    public BenefitPayResultBO createOrder(UserBenefitOrderReq req, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        BenefitUnicomPayResultBO payResultBO = new BenefitUnicomPayResultBO();
        payResultBO.setSuccess(false);
        String mobile = req.getMobile();
        HeBeiPayPkgEnum pkgEnum = HeBeiPayPkgEnum.of(productDO.getPayChannelPkgId());
        HbCmccProperties.Config config = hbCmccProperties.getProperties(pkgEnum);
        // 开始下单
        HeBeiCmccOrderRes orderResp = this.productOrder(mobile, req.getSmsCode(), pkgEnum, config, orderDO);
        if (SUCCESS_CODE.equals(orderResp.getRes_code())) {
            payResultBO.setSuccess(true);
            orderDO.setPayStatus(PayStatusEnum.SUCCESS.getCode());
            orderDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
            orderDO.setOutOrderNo(orderResp.getResult().getRECOID());
            payResultBO.setMessage("success");
        } else {
            payResultBO.setMessage(orderResp.getRes_desc());
            orderDO.setPayStatus(PayStatusEnum.FAIL.getCode());
        }
        orderDO.setPayNotifyContent(JSON.toJSONString(orderResp));
        orderDO.setPayNotifyTime(LocalDateTime.now());
        ExecutorServiceUtils.execute(() -> {
            try {
                // 延迟通知给下游
                Thread.sleep(5000);
                benefitPlatformService.notifyMediaOrderResult(orderDO, payResultBO.getMessage());
            } catch (Exception e) {
                log.info(TAG + "通知媒体失败.订单id:{}", orderDO.getOrderId(), e);
            }
        });
        return payResultBO;
    }

    private HeBeiCmccOrderRes productOrder(String mobile, String smsCode, HeBeiPayPkgEnum pkgEnum, HbCmccProperties.Config config, BenefitOrderDO orderDO) {
        // 获取客户归属地
        String key = RedisKeys.getHbCmccUserInfo(mobile);
        HeBeiCmccUserInfoRes cmccUserInfoRes = RedisUtils.getCacheObject(key);
        if (Objects.isNull(cmccUserInfoRes)) {
            throw BizException.create(BizErrorCodeEnum.ORDER_UNIFIED_ERROR, "客户归属地无缓存");
        }
        HeBeiCityCodeEnum cityCodeEnum = HeBeiCityCodeEnum.of(cmccUserInfoRes.getRegion());
        if (Objects.isNull(cityCodeEnum)) {
            throw BizException.create(BizErrorCodeEnum.ORDER_UNIFIED_ERROR, "客户归属地未知");
        }

        HeBeiCmccOrderReq req = new HeBeiCmccOrderReq();
        req.setCMDID("PTCommonOrderCommit");
        req.setLog(true);
        req.setCHANNELID(hbCmccProperties.getChannelId());
        req.setSeqid("");
        req.setOutTime("30000");

        HeBeiCmccOrderReq.CustomerOrderDTO customerOrder = new HeBeiCmccOrderReq.CustomerOrderDTO();
        customerOrder.setSupportProdNotLoadNcodeMap("1");
        customerOrder.setSMSRANDOMPASS(smsCode);
        customerOrder.setOperID(cityCodeEnum.getOperId());
        customerOrder.setServerNumber(mobile);
        customerOrder.setOuterOrderID(orderDO.getOrderNo());
        customerOrder.setAccessType(hbCmccProperties.getChannelId());
        customerOrder.setRegion(cityCodeEnum.getCode());
        customerOrder.setIsNotify("1");

        HeBeiCmccOrderReq.CustomerOrderDTO.OrderRecListDTO orderRecListDTO = new HeBeiCmccOrderReq.CustomerOrderDTO.OrderRecListDTO();
        orderRecListDTO.setRecType("ChangeProduct");
        orderRecListDTO.setAuthType("AuthCheckA");
        HeBeiCmccOrderReq.CustomerOrderDTO.OrderRecListDTO.RecExtAttrDTO recExtAttrDTO = new HeBeiCmccOrderReq.CustomerOrderDTO.OrderRecListDTO.RecExtAttrDTO();
        recExtAttrDTO.setExtentMsg(pkgEnum.getChannelPkgName());
        orderRecListDTO.setRecExtAttr(recExtAttrDTO);
        HeBeiCmccOrderReq.CustomerOrderDTO.OrderRecListDTO.OfferingListDTO offeringListDTO = new HeBeiCmccOrderReq.CustomerOrderDTO.OrderRecListDTO.OfferingListDTO();
        offeringListDTO.setActionType("A");
        offeringListDTO.setOfferType("prod");
        offeringListDTO.setOfferCode(config.getGOODSID());
        offeringListDTO.setEffectType("2");
        orderRecListDTO.setOfferingList(Collections.singletonList(offeringListDTO));
        customerOrder.setOrderRecList(Collections.singletonList(orderRecListDTO));
        req.setCustomerOrder(customerOrder);
        return this.sendRemoteRequest(CREATE_ORDER_URL, BeanUtil.beanToMap(req), HeBeiCmccOrderRes.class);
    }

    /**
     * 发送短信验证码
     */
    private boolean sendSmsCode(String mobile, String goodsId) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        String cmdId = "IntAppendProductRec";
        if (goodsId.startsWith("OFFER")) {
            cmdId = "PTCommonOrderCommit";
        }
        Map<String, Object> params = new HashMap<>();
        params.put("TELNUM", mobile);
        params.put("GOODSID", goodsId);
        params.put("CMDID", cmdId);
        params.put("TEMPLATENO", "RNDPWD_BUSIINFO");
        params.put("CHANNELID", hbCmccProperties.getChannelId());
        HeBeiCmccBaseRes res = this.sendRemoteRequest(SEND_SMS_CODE_URL, params, HeBeiCmccBaseRes.class);
        if (!SUCCESS_CODE.equals(res.getRes_code())) {
            throw BizException.create(BizResult.error(res.getRes_code(), res.getRes_msg()));
        }
        return "success".equals(res.getRes_msg());
    }

    /**
     * 精准营销校验
     */
    private boolean checkRecommendedOfferNew(String mobile, String goodsId) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("CHANNELID", hbCmccProperties.getChannelId());

        Map<String, Object> requestHeader = new HashMap<>();
        requestHeader.put("accessChannel", "102188");
        requestHeader.put("beId", "101");
        requestHeader.put("language", "2");
        requestHeader.put("operator", "Campaign");
        requestHeader.put("password", "q3geiItxj4ljNLkI6OINDA==");
        requestHeader.put("transactionId", "112211");

        Map<String, Object> eventBody = new HashMap<>();
        eventBody.put("msisdn", mobile);
        eventBody.put("eventCode", "AND_LOGIN");

        params.put("requestHeader", requestHeader);
        params.put("eventBody", eventBody);

        HeBeiCmccRecommendedRes res = this.sendRemoteRequest(RECOMMENDED_OFFER_NEW, params, HeBeiCmccRecommendedRes.class);
        if (!SUCCESS_CODE.equals(res.getRes_code())) {
            throw BizException.create(BizResult.error(res.getRes_code(), res.getRes_msg()));
        }
        if (Objects.nonNull(res.getResult()) && !res.getResult().isEmpty()) {
            for (HeBeiCmccRecommendedRes.ResultDTO resultDTO : res.getResult()) {
                //只校验要下单的商品id
                if (resultDTO.getOfferId().equals(goodsId)) {
                    List<String> list = resultDTO.getOfferAttrMap().stream().map(HeBeiCmccRecommendedRes.ResultMapDTO::getValue).collect(Collectors.toList());
                    for (HeBeiCmccNotRecommendedEnum enumObj : HeBeiCmccNotRecommendedEnum.values()) {
                        if (list.contains(enumObj.getActivityId()) || list.contains(enumObj.getCampaignId())) {
                            throw BizException.create(BizResult.error(BizErrorCodeEnum.BLACK_USER_PHONE, enumObj.getDesc()));
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 校验用户信息
     */
    private void checkUserInfo(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BizErrorCodeEnum.PHONE_NUMBER_IS_INCORRECT);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("TelNum", mobile);
        params.put("CHANNELID", hbCmccProperties.getChannelId());

        HeBeiCmccUserInfoRes userInfoRes = this.sendRemoteRequest(GET_USER_INFO_URL, params, HeBeiCmccUserInfoRes.class);
        if (!SUCCESS_CODE.equals(userInfoRes.getRes_code())) {
            throw BizException.create(BizResult.error(userInfoRes.getRes_code(), userInfoRes.getRes_desc()));
        }
        if (!"1".equals(userInfoRes.getIsYD())) {
            throw BizException.create(BizResult.error(userInfoRes.getIsYD(), "非河北移动号码"));
        }
        if (!"US10".equals(userInfoRes.getUserSatus())) {
            throw BizException.create(BizResult.error(userInfoRes.getUserSatus(), "号码状态不可用"));
        }

        // 放入Redis中
        String key = RedisKeys.getHbCmccUserInfo(mobile);
        RedisUtils.setCacheObject(key, userInfoRes, Duration.ofMinutes(15));
    }

    private <T> T sendRemoteRequest(String path, Map<String, Object> params, Class<T> cls) {
        String url = hbCmccProperties.getHost() + path;
        String token = this.getToken();
        HttpRequest request = HttpRequest.post(url + "&access_token=" + token).timeout(TIME_OUT);
        request.contentType(ContentType.FORM_URLENCODED.getValue());
        request.body(JSON.toJSONString(params));
        log.info(TAG + "请求前:{}", request);
        HttpResponse response = request.execute();

        String responseStr = response.body();
        if (responseStr.chars().anyMatch(c -> c == 0xFFFD)) {
            responseStr = new String(response.bodyBytes(), Charset.forName("GBK"));
        }
        log.info(TAG + "请求后:{}", responseStr);
        if (response.isOk()) {
            return JSON.parseObject(responseStr, cls);
        } else {
            throw BizException.create(BaseErrorCodeEnum.FALLBACK);
        }
    }


}
