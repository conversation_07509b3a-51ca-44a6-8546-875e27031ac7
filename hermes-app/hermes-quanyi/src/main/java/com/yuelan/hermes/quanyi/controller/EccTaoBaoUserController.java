package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.EccTaoBaoUserDOService;
import com.yuelan.hermes.quanyi.controller.request.EccTaoBaoUserListReq;
import com.yuelan.hermes.quanyi.controller.response.EccTaoBaoUserResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/5/15 上午10:57
 */
@Validated
@Tag(name = "电商卡/后台api/电商卡淘宝用户")
@RestController
@RequestMapping("/a/ecc/taobaoUser")
@AllArgsConstructor
public class EccTaoBaoUserController {

    private final EccTaoBaoUserDOService eccTaoBaoUserDOService;

    @Operation(summary = "淘宝用户分页列表")
    @PostMapping("/list")
    public BizResult<PageData<EccTaoBaoUserResp>> list(@RequestBody EccTaoBaoUserListReq req) {
        return BizResult.create(eccTaoBaoUserDOService.pageList(req));
    }

}
