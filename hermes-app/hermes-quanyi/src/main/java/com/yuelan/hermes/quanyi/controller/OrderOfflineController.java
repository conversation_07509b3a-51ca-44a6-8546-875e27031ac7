package com.yuelan.hermes.quanyi.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.yuelan.hermes.commons.util.EasyExcelUtil;
import com.yuelan.hermes.quanyi.biz.service.OrderOfflineService;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.config.satoken.context.AdminContext;
import com.yuelan.hermes.quanyi.controller.request.*;
import com.yuelan.hermes.quanyi.controller.response.OrderOfflineItemListRsp;
import com.yuelan.hermes.quanyi.controller.response.OrderOfflineItemRsp;
import com.yuelan.hermes.quanyi.controller.response.OrderOfflineRsp;
import com.yuelan.hermes.quanyi.mq.producer.ProducerHelper;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@Tag(name = "线下订单")
@RequestMapping("/a/orderOffline")
@RestController
public class OrderOfflineController extends AdminBaseController {

    @Autowired
    private OrderOfflineService orderOfflineService;
    @Autowired
    private ProducerHelper producerHelper;

    @Log(title = "手动充值", type = OperationType.INSERT)
    @Operation(summary = "手动充值")
    @PostMapping("/add")
    public BizResult<String> add(@RequestBody OrderOfflineAddReq req) {
        AdminContext loginUser = this.getLoginUser();
        String orderNo = orderOfflineService.add(req, loginUser);
        //发送MQ消息
        producerHelper.sendOrderOfflineCreateMsg(orderNo);
        return BizResult.create(orderNo);
    }

    @Operation(summary = "手动充值模板下载")
    @GetMapping("/template/download")
    public void templateDownload(HttpServletResponse response) throws IOException {
        UserAccountReq template = new UserAccountReq();
        template.setPhone("151********");
        template.setUserAccount("");
        EasyExcelUtil.download(response, "手动充值模板", UserAccountReq.class, Lists.newArrayList(template));
    }

    @Operation(summary = "线下订单列表")
    @PostMapping("/list")
    public BizResult<PageData<OrderOfflineRsp>> list(@RequestBody OrderOfflineListReq req) {
        req.check();
        PageData<OrderOfflineRsp> pageData = orderOfflineService.list(req);
        return BizResult.create(pageData);
    }

    @Operation(summary = "订单详情")
    @GetMapping("/detail")
    public BizResult<OrderOfflineRsp> detail(@RequestParam String orderNo) {
        OrderOfflineRsp detail = orderOfflineService.detail(orderNo);
        return BizResult.create(detail);
    }

    @Operation(summary = "订单明细")
    @PostMapping("/item")
    public BizResult<PageData<OrderOfflineItemListRsp>> item(@RequestBody OrderOfflineItemListReq req) {
        PageData<OrderOfflineItemListRsp> pageData = orderOfflineService.item(req);
        return BizResult.create(pageData);
    }

    @Log(title = "导出线下订单", type = OperationType.OTHER)
    @Operation(summary = "导出线下订单")
    @PostMapping("/export")
    public void export(@RequestBody OrderOfflineExportReq req, HttpServletResponse response) throws IOException {
        List<OrderOfflineItemRsp> list = orderOfflineService.export(req);
        if (CollectionUtil.isEmpty(list)) {
            throw BizException.create(BizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        EasyExcelUtil.download(response, "线下订单" + req.getOrderNo(), OrderOfflineItemRsp.class, list);
    }

    @Log(title = "批量导出线下订单", type = OperationType.OTHER)
    @Operation(summary = "批量导出线下订单")
    @PostMapping("/export/batch")
    public void batchExport(@RequestBody OrderOfflineBatchExportReq req, HttpServletResponse response) throws IOException {
        List<OrderOfflineItemRsp> list = orderOfflineService.batchExport(req);
        if (CollectionUtil.isEmpty(list)) {
            throw BizException.create(BizErrorCodeEnum.ORDER_NOT_EXIST);
        }
        EasyExcelUtil.download(response, "线下订单", OrderOfflineItemRsp.class, list);
    }

    @Log(title = "手动订单发送短信", type = OperationType.OTHER)
    @Operation(summary = "发送短信")
    @PostMapping("/sendSms")
    public BizResult<Boolean> sendSms(@RequestBody OrderOfflineItemSmsSendReq req) {
        Boolean result = orderOfflineService.sendSms(req);
        return BizResult.create(result);
    }
}
