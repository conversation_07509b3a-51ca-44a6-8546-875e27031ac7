package com.yuelan.hermes.quanyi.controller.hsh;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.session.SaSession;
import com.yuelan.hermes.quanyi.biz.manager.GamingBenefitManager;
import com.yuelan.hermes.quanyi.common.pojo.bo.HshUserSessionUser;
import com.yuelan.hermes.quanyi.config.satoken.StpHshUserUtil;
import com.yuelan.hermes.quanyi.controller.request.GamingBenefitItemRedeemReq;
import com.yuelan.hermes.quanyi.controller.request.GamingBenefitListReq;
import com.yuelan.hermes.quanyi.controller.request.GamingBenefitRedeemReq;
import com.yuelan.hermes.quanyi.controller.response.GamingRedeemCodeSimpleResp;
import com.yuelan.hermes.quanyi.controller.response.HshGamingOrderBenefitResp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> 2024/7/30 下午6:51
 */
@Validated
@RestController
@Tag(name = "惠生活/用户权益接口")
@RequiredArgsConstructor
@RequestMapping("/hshBenefit")
@SaCheckLogin(type = StpHshUserUtil.TYPE)
public class HshUserBenefitController {

    private final GamingBenefitManager gamingBenefitManager;

    @Operation(summary = "电竞卡权益列表")
    @PostMapping("/gamingBenefit")
    public BizResult<List<HshGamingOrderBenefitResp>> gamingBenefit(@Validated @RequestBody GamingBenefitListReq req) {
        HshUserSessionUser sessionUser = StpHshUserUtil.getSession().getModel(SaSession.USER, HshUserSessionUser.class);
        return BizResult.create(gamingBenefitManager.gamingBenefit(sessionUser.getPhone(), req.getPage(), req.getPageSize()));
    }


    @Operation(summary = "电竞卡权益批量领取")
    @PostMapping("/gamingOrderRedeem")
    public BizResult<Void> confirmRedeem(@Validated @RequestBody GamingBenefitRedeemReq req) {
        HshUserSessionUser sessionUser = StpHshUserUtil.getSession().getModel(SaSession.USER, HshUserSessionUser.class);
        gamingBenefitManager.batchGetRedeemCode(sessionUser.getUserId(), req.getOrderId(), sessionUser.getPhone());
        return BizResult.ok();
    }

    @Operation(summary = "电竞卡权益领取")
    @PostMapping("/gamingOrderItemRedeem")
    public BizResult<GamingRedeemCodeSimpleResp> confirmItemRedeem(@Validated @RequestBody GamingBenefitItemRedeemReq req) {
        HshUserSessionUser sessionUser = StpHshUserUtil.getSession().getModel(SaSession.USER, HshUserSessionUser.class);
        return BizResult.create(gamingBenefitManager.getRedeemCodeResp(sessionUser.getUserId(), req.getItemId(), sessionUser.getPhone()));
    }
}
