package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.yuelan.core.util.MapstructUtils;
import com.yuelan.hermes.quanyi.biz.service.impl.EccLicenseServiceImpl;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccLicenseDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductPageDO;
import com.yuelan.result.enums.YesOrNoEnum;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import io.github.linpeilie.annotations.ReverseAutoMapping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2025/5/22
 * @since 2025/5/22
 */
@Data
@AutoMapper(target = EccProductPageDO.class)
public class EccProductPageResp {

    /**
     * 落地页id
     */
    @Schema(description = "落地页id")
    private Long pageId;


    /**
     * 落地页名称
     */
    @Schema(description = "落地名称")
    private String pageName;

    /**
     * 号卡产品 id
     */
    @Schema(description = "号卡产品 id")
    private Long productId;

    /**
     * 首屏
     */
    @Schema(description = "首屏")
    private String indexImg;

    /**
     * 下单按键图
     */
    @Schema(description = "下单按键图")
    private String orderButtonImg;

    /**
     * 详情图
     */
    @Schema(description = "详情图")
    private String pricingDetailImg;

    /**
     * 产品介绍图
     */
    @Schema(description = "产品介绍图")
    private String productDetailImg;

    /**
     * 授权牌 id
     */
    @Schema(description = "授权牌 id")
    private Long licenseId;

    @Schema(description = "授权牌信息")
    private EccLicenseResp license;

    /**
     * 附加扩张图
     */
    @Schema(description = "附加扩张图")
    @AutoMapping(ignore = true)
    @ReverseAutoMapping(ignore = true)
    private List<String> extraImg;

    /**
     * 可折叠图
     */
    @Schema(description = "可折叠图")
    @AutoMapping(ignore = true)
    @ReverseAutoMapping(ignore = true)
    private List<String> collapseImg;

    @Schema(description = "背景颜色")
    private String bgColor;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;


    @Schema(description = "是否启用")
    private Boolean enabled;


    public static EccProductPageResp build(EccProductPageDO pageDO) {
        if (Objects.isNull(pageDO)) {
            return null;
        }
        EccProductPageResp pageResp = MapstructUtils.convertNotNull(pageDO, EccProductPageResp.class);
        if (pageDO.getLicenseId() != null) {
            EccLicenseServiceImpl eccLicenseService = SpringUtil.getBean(EccLicenseServiceImpl.class);
            EccLicenseDO licenseDO = eccLicenseService.getById(pageDO.getLicenseId());
            if (licenseDO != null) {
                pageResp.setLicense(MapstructUtils.convertNotNull(licenseDO, EccLicenseResp.class));
            }
        }
        if (pageDO.getExtraImg() != null) {
            pageResp.setExtraImg(JSON.parseArray(pageDO.getExtraImg(), String.class));
        }
        if (pageDO.getCollapseImg() != null) {
            pageResp.setCollapseImg(JSON.parseArray(pageDO.getCollapseImg(), String.class));
        }
        if (YesOrNoEnum.isYes(pageDO.getActivated())) {
            pageResp.setEnabled(Boolean.TRUE);
        } else {
            pageResp.setEnabled(Boolean.FALSE);
        }
        return pageResp;
    }
}
