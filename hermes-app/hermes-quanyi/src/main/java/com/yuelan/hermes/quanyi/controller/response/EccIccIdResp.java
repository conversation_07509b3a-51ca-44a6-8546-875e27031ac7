package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccIccIdDO;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ICCID响应对象
 *
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Data
@Schema(description = "ICCID响应")
@AutoMapper(target = EccIccIdDO.class)
public class EccIccIdResp {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "ICCID号码")
    private String iccId;

    @Schema(description = "产品Id")
    private Long productId;

    @Schema(description = "渠道id")
    private Long channelId;

    @Schema(description = "批次号")
    private Long batchId;

    @Schema(description = "有效期")
    private LocalDate validityPeriod;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否过期")
    private Boolean expired;


    @Schema(description = "渠道类型:1-内部渠道,2-外部渠道")
    private Integer channelType;

    @Schema(description = "产品名称")
    private String prodName;

    @Schema(description = "渠道name")
    private String channelName;

    @Schema(description = "状态描述")
    private String statusDesc;

    /**
     * 设置状态描述
     */
    public void setStatusDesc() {
        if (validityPeriod == null) {
            this.statusDesc = "无有效期限制";
            this.expired = false;
        } else if (validityPeriod.isBefore(LocalDate.now())) {
            this.statusDesc = "已过期";
            this.expired = true;
        } else {
            this.statusDesc = "有效";
            this.expired = false;
        }
    }
}
