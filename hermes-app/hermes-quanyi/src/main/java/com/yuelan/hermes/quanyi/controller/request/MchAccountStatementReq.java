package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class MchAccountStatementReq extends PageRequest {

    @NotBlank(message = "商户ID不能为空")
    @Schema(description = "商户ID")
    private Long merchantId;

    @Schema(description = "收支类型")
    private Integer tradeType;

    @Schema(description = "业务类型")
    private Integer bizType;

    @Schema(description = "业务单号")
    private String bizNo;

}
