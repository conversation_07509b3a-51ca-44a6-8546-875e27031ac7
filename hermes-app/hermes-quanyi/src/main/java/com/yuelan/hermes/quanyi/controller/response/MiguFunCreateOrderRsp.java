package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MiguFunCreateOrderRsp {

    @Schema(description = "响应状态,1 响应成功0 响应失败")
    private String returnCode;

    @Schema(description = "响应描述")
    private String message;

    @Schema(description = "是否直充1 是0 否")
    private String isDirect;

    public static MiguFunCreateOrderRsp success() {
        MiguFunCreateOrderRsp rsp = new MiguFunCreateOrderRsp();
        rsp.setReturnCode("1");
        return rsp;
    }

    public static MiguFunCreateOrderRsp error(String message) {
        MiguFunCreateOrderRsp rsp = new MiguFunCreateOrderRsp();
        rsp.setReturnCode("0");
        rsp.setMessage(message);
        return rsp;
    }
}
