package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.EnumLimit;
import com.yuelan.hermes.commons.enums.EnableStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class GoodsVirtualStockItemEnableReq {

    @NotNull(message = "明细ID不能为空")
    @Schema(description = "库存明细主键")
    private Long stockItemId;

    @EnumLimit(message = "请选择是否启用", enumInterface = EnableStatusEnum.class)
    @Schema(description = "0禁用1启用")
    private Integer enable;

}
