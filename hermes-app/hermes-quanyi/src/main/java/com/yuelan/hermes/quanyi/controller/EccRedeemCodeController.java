package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.EccRedeemCodeDOService;
import com.yuelan.hermes.quanyi.controller.request.EccRedeemCodeListReq;
import com.yuelan.hermes.quanyi.controller.response.EccRedeemCodeResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> 2024/5/3 下午11:10
 */
@Validated
@RestController
@Tag(name = "电商卡/后台api/权益商品兑换码")
@RequiredArgsConstructor
@RequestMapping("/a/ecc/redeemCode")
public class EccRedeemCodeController {

    private final EccRedeemCodeDOService eccRedeemCodeDOService;

    @Operation(summary = "权益商品兑换码分页列表")
    @PostMapping("/list")
    public BizResult<PageData<EccRedeemCodeResp>> list(@RequestBody EccRedeemCodeListReq req) {
        return BizResult.create(eccRedeemCodeDOService.page(req));
    }

    @Operation(summary = "下载兑换码导入模板")
    @GetMapping(value = "/download/template", produces = "application/octet-stream")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        eccRedeemCodeDOService.downloadTemplate(response);
    }

    @Operation(summary = "导入兑换码", description = "必须先下载模板，导入选择指定商品，再导入")
    @PostMapping(value = "/import/{goodsId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BizResult<Void> importRedeemCode(@PathVariable Long goodsId, @RequestParam("file") MultipartFile file) throws IOException {
        eccRedeemCodeDOService.importRedeemCode(goodsId, file);
        return BizResult.ok();
    }


}
