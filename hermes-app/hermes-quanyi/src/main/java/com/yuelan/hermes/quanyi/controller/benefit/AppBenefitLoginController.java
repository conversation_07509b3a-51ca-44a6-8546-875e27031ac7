package com.yuelan.hermes.quanyi.controller.benefit;

import com.yuelan.hermes.commons.enums.SmsCodeType;
import com.yuelan.hermes.quanyi.biz.service.BenefitProductDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitUserDOService;
import com.yuelan.hermes.quanyi.biz.service.SmsCodeService;
import com.yuelan.hermes.quanyi.controller.request.BenefitUserLoginReq;
import com.yuelan.hermes.quanyi.controller.request.SmsCodeReq;
import com.yuelan.hermes.quanyi.controller.response.AppBenefitUserLoginResp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/4/7 20:16
 */
@Tag(name = "权益N选1/客户端接口/权益包登录api")
@Validated
@RestController
@RequestMapping("/benefit/login")
@RequiredArgsConstructor
public class AppBenefitLoginController {

    private final BenefitProductDOService benefitProductDOService;
    private final SmsCodeService smsCodeService;
    private final BenefitUserDOService benefitUserDOService;

    /**
     * 获取登录短信验证码
     */
    @Operation(summary = "获取登录短信验证码")
    @PostMapping("/getSmsCode")
    public BizResult<Void> getSmsCode(@Validated @RequestBody SmsCodeReq req) {
        smsCodeService.reqCaptcha(req.getPhone(), SmsCodeType.BENEFIT_USER_LOGIN);
        return BizResult.ok();
    }

    /**
     * 登录返回token
     */
    @Operation(summary = "登录返回token")
    @PostMapping("/login")
    public BizResult<AppBenefitUserLoginResp> login(@Validated @RequestBody BenefitUserLoginReq req) {
        return BizResult.create(benefitUserDOService.login(req));
    }

    /**
     * 登录返回token
     */
    @Operation(summary = "token登录接口", description = "携带header=x-benefit-token,value={token}登录检测过期状态,接口正常返回code=0,其他code需要重新登录")
    @PostMapping("/tokenLogin")
    public BizResult<AppBenefitUserLoginResp> loginByToken() {
        return benefitUserDOService.loginByToken();
    }

}
