package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/5/15 下午3:34
 */
@Data
public class EccOrderItemListReq {

    /**
     * 月份
     */
    @Schema(description = "月份时间字符串，格式yyyy-MM")
    @NotBlank(message = "月份不能为空")
    private String monthTime;

    /**
     * 主订单号
     */
    @Schema(description = "主订单id")
    @NotNull(message = "主订单id不能为空")
    private Long orderId;

}
