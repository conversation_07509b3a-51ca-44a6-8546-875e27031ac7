package com.yuelan.hermes.quanyi.controller.open;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.service.QquService;
import com.yuelan.hermes.quanyi.controller.request.QquRechargeNotifyRsp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "奇趣创享API")
@RequestMapping("/qqucx")
@RestController
public class QqcxController {

    @Autowired
    private QquService qquService;

    @Operation(summary = "话费充值回调")
    @RequestMapping(value = "/order/notify", method = {RequestMethod.GET, RequestMethod.POST})
    public String notify(QquRechargeNotifyRsp request) {
        if (log.isDebugEnabled()) {
            log.info("奇趣充值回调:{}", JSON.toJSONString(request));
        }
        //TODO 这里我们不验签（正常应验签），直接处理业务逻辑
        return qquService.notify(request);
    }

}
