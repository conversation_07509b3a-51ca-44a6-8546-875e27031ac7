package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Data
public class BenefitOuterChannelListReq extends PageRequest {

    @Schema(description = "外部渠道id")
    public Long outerChannelId;

    @Schema(description = "渠道名字")
    private String platformName;

    @Schema(description = "渠道标识")
    private String platformCode;


}
