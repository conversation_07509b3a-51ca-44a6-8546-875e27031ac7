package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/5/9 下午4:58
 */
@Data
public class EccBindPhoneReq {
    @Schema(description = "手机号")
    @NotEmpty(message = "请输入联通手机号码")
    @Length(min = 11, max = 11, message = "请输入11位联通手机号码")
    private String phone;


    @Schema(description = "短信验证码")
    @NotEmpty(message = "请输入短信验证码")
    private String smsCode;

}
