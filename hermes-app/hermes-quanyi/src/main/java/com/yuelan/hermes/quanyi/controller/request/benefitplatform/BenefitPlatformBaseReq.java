package com.yuelan.hermes.quanyi.controller.request.benefitplatform;

import com.yuelan.core.validator.constraints.RegexMatch;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/7/19
 * @description:
 */

@Data
public class BenefitPlatformBaseReq {

    @Schema(description = "商家唯一标识")
    @NotBlank(message = "appId不能为空")
    private String appId;

    @Schema(description = "权益平台商品id")
    @NotBlank(message = "商品id不能为空")
    private String prodCode;

    @Schema(description = "渠道推荐商品id")
    private String commonId;

    @Schema(description = "订购手机号")
    @NotBlank(message = "手机号不能为空")
    @RegexMatch(regex = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;

    @Schema(description = "包名")
    private String packageName; // 长度64

}
