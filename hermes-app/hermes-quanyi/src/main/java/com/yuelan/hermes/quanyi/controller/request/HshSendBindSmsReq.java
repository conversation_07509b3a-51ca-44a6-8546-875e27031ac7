package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/5/9 下午3:19
 */
@Data
public class HshSendBindSmsReq {

    @Schema(description = "手机号")
    @NotEmpty(message = "请输入11位手机号码")
    @Length(min = 11, max = 11, message = "请输入11位手机号码")
    private String phone;


}
