package com.yuelan.hermes.quanyi.controller;

import com.google.common.collect.Lists;
import com.yuelan.hermes.commons.util.EasyExcelUtil;
import com.yuelan.hermes.quanyi.biz.service.GoodsVirtualStockItemService;
import com.yuelan.hermes.quanyi.biz.service.GoodsVirtualStockService;
import com.yuelan.hermes.quanyi.common.pojo.excel.GoodsVirtualStockImportExcel;
import com.yuelan.hermes.quanyi.controller.request.*;
import com.yuelan.hermes.quanyi.controller.response.GoodsVirtualStockDetailRsp;
import com.yuelan.hermes.quanyi.controller.response.GoodsVirtualStockRsp;
import com.yuelan.hermes.quanyi.controller.response.GoodsVirtualStockStatisticsRsp;
import com.yuelan.hermes.quanyi.controller.response.VoucherPasswordRsp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;


@Tag(name = "虚拟商品库存管理")
@RequestMapping("/a/goodsVirtualStock")
@RestController
public class GoodsVirtualStockController extends AdminBaseController {
    @Autowired
    GoodsVirtualStockService goodsVirtualStockService;
    @Autowired
    GoodsVirtualStockItemService goodsVirtualStockItemService;

    @Operation(summary = "虚拟商品库存列表")
    @PostMapping("/list")
    public BizResult<PageData<GoodsVirtualStockRsp>> goodsVirtualStockList(@RequestBody GoodsVirtualStockReq req) {
        req.check();
        PageData<GoodsVirtualStockRsp> resultData = goodsVirtualStockService.goodsVirtualStockList(req);
        return BizResult.create(resultData);
    }

    @Operation(summary = "虚拟商品库存统计")
    @PostMapping("/statistics")
    public BizResult<GoodsVirtualStockStatisticsRsp> goodsVirtualStockStatistics(@RequestBody GoodsVirtualStockReq req) {
        req.check();
        GoodsVirtualStockStatisticsRsp result = goodsVirtualStockService.goodsVirtualStockStatistics(req);
        return BizResult.create(result);
    }

    @Operation(summary = "虚拟商品库存详情")
    @GetMapping("/detail")
    public BizResult<GoodsVirtualStockRsp> goodsVirtualStockDetail(@RequestParam Long skuId) {
        GoodsVirtualStockRsp goodsVirtualStockRsp = goodsVirtualStockService.goodsVirtualStockDetail(skuId);
        return BizResult.create(goodsVirtualStockRsp);
    }

    @Operation(summary = "新增库存预警")
    @PostMapping("/stock/alarm")
    public BizResult<Boolean> addStockAlarm(@RequestBody GoodsVirtualStockAlarmReq req) {
        Boolean result = goodsVirtualStockService.addStockAlarm(req);
        return BizResult.create(result);
    }

    @Operation(summary = "刷新虚拟商品库存")
    @PostMapping("/refresh")
    public BizResult<Boolean> refreshGoodsVirtualStock(@RequestParam Long skuId) {
        Boolean result = goodsVirtualStockService.refreshGoodsVirtualStock(skuId);
        return BizResult.create(result);
    }

    @Log(title = "虚拟商品导入", type = OperationType.OTHER)
    @Operation(summary = "虚拟商品导入")
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BizResult<Boolean> goodsVirtualImport(@RequestParam("file") MultipartFile file, @RequestParam Long skuId) {
        boolean result = goodsVirtualStockService.goodsVirtualImport(file, skuId);
        return BizResult.create(result);
    }

    @Operation(summary = "虚拟商品导入模板下载")
    @GetMapping("/templateDownload")
    public void templateDownload(HttpServletResponse response) throws IOException {
        GoodsVirtualStockImportExcel template = new GoodsVirtualStockImportExcel();
        template.setGoodsName("非必填");
        template.setSkuName("非必填");
        template.setSkuNo("非必填");
        template.setSupplier("悦蓝");
        template.setPurchasePrice(BigDecimal.TEN);
        template.setPurchaseDate(new Date());
        template.setVoucherCode("12345");
        template.setVoucherPassword("67890");
        template.setQrCodeUrl("http://ssds.com");
        template.setStartDate(template.getPurchaseDate());
        template.setEndDate(template.getPurchaseDate());
        template.setRemark("");
        EasyExcelUtil.download(response, "虚拟商品导入模板", GoodsVirtualStockImportExcel.class, Lists.newArrayList(template));
    }


    @Operation(summary = "虚拟商品库存出入库明细")
    @PostMapping("/detail/list")
    public BizResult<PageData<GoodsVirtualStockDetailRsp>> goodsVirtualStockDetailList(@RequestBody GoodsVirtualStockDetailReq req) {
        req.check();
        PageData<GoodsVirtualStockDetailRsp> resultData = goodsVirtualStockItemService.goodsVirtualStockDetailList(req);
        return BizResult.create(resultData);
    }

    @Log(title = "出入库明细导出", type = OperationType.OTHER)
    @Operation(summary = "出入库明细导出")
    @PostMapping("/detail/export")
    public void detailExport(@Valid @RequestBody GoodsVirtualStockDetailReq req, HttpServletResponse response) throws IOException {
        goodsVirtualStockItemService.detailExport(req, response);
    }

    @Operation(summary = "虚拟商品卡密查询列表")
    @PostMapping("/voucherPassword/list")
    public BizResult<PageData<VoucherPasswordRsp>> voucherPasswordList(@RequestBody GoodsVirtualStockDetailReq req) {
        req.check();
        PageData<VoucherPasswordRsp> resultData = goodsVirtualStockItemService.voucherPasswordList(req);
        return BizResult.create(resultData);
    }

    @Operation(summary = "卡密查询库存统计")
    @PostMapping("/voucherPasswordStatistics")
    public BizResult<GoodsVirtualStockStatisticsRsp> voucherPasswordStatistics(@RequestBody GoodsVirtualStockDetailReq req) {
        GoodsVirtualStockStatisticsRsp result = goodsVirtualStockItemService.voucherPasswordStatistics(req);
        return BizResult.create(result);
    }

    @Log(title = "卡密列表导出", type = OperationType.OTHER)
    @Operation(summary = "卡密列表导出")
    @PostMapping("/voucherPassword/export")
    public void voucherPasswordExport(@Valid @RequestBody GoodsVirtualStockDetailReq req, HttpServletResponse response) throws IOException {
        goodsVirtualStockItemService.voucherPasswordExport(req, response);
    }

    @Log(title = "卡密启用/禁用", type = OperationType.UPDATE)
    @Operation(summary = "卡密启用/禁用")
    @PostMapping("/voucherPassword/enable")
    public BizResult<Boolean> voucherPasswordEnable(@Valid @RequestBody GoodsVirtualStockItemEnableReq req) {
        Boolean result = goodsVirtualStockItemService.voucherPasswordEnable(req);
        return BizResult.create(result);
    }

    @Log(title = "批量删除卡密", type = OperationType.DELETE)
    @Operation(summary = "批量删除卡密")
    @PostMapping("/voucherPassword/delete")
    public BizResult<Integer> voucherPasswordDelete(@Valid @RequestBody GoodsVirtualStockItemDeleteReq req) {
        Integer result = goodsVirtualStockItemService.voucherPasswordDelete(req);
        return BizResult.create(result);
    }


}
