package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingRedeemCodeDO;
import com.yuelan.result.entity.PageRequest;
import com.yuelan.result.enums.YesOrNoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR> 2024/4/19 下午3:48
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class GamingRedeemCodePageReq extends PageRequest {

    @NotNull(message = "商品id不能为空")
    @Schema(description = "商品id")
    private Long goodsId;

    @Schema(description = "是否已使用")
    private Boolean used;

    @Schema(description = "是否已经过期")
    private Boolean expired;

    @Schema(description = "兑换码或者卡密查询（严格匹配）")
    private String keyOrPwd;


    public Wrapper<GamingRedeemCodeDO> buildQueryWrapper() {
        return Wrappers.<GamingRedeemCodeDO>lambdaQuery()
                .eq(goodsId != null, GamingRedeemCodeDO::getGoodsId, goodsId)
                .eq(used != null, GamingRedeemCodeDO::getUsed, BooleanUtil.isTrue(used) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
                // 过期了
                .lt(BooleanUtil.isTrue(expired), GamingRedeemCodeDO::getExpireDate, LocalDate.now())
                .ge(BooleanUtil.isFalse(expired), GamingRedeemCodeDO::getExpireDate, LocalDate.now())
                .eq(keyOrPwd != null, GamingRedeemCodeDO::getRedeemCodeKey, keyOrPwd)
                .or()
                .eq(keyOrPwd != null, GamingRedeemCodeDO::getRedeemCodePwd, keyOrPwd)
                // 未使用 将要过期拍前面
                .orderByAsc(GamingRedeemCodeDO::getUsed)
                .orderByAsc(GamingRedeemCodeDO::getExpireDate);
    }
}
