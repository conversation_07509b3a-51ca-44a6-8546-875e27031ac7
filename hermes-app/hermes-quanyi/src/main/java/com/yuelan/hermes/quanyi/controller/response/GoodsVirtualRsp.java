package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


@Data
public class GoodsVirtualRsp {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "商品名称")
    private String name;

    @Schema(description = "商品主图")
    private String image;

    @Schema(description = "商品类型1卡密2直冲")
    private Integer type;

    @Schema(description = "上游供应商")
    private Integer supplier;

    @Schema(description = "第三方商品编号")
    private String thirdGoodsNo;

    @Schema(description = "状态0下架1上架")
    private Integer status;

    @Schema(description = "最低销售价")
    private BigDecimal minSalePrice;

    @Schema(description = "商品规格")
    private List<GoodsVirtualSkuRsp> skuList;


}
