package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitWhiteUserDO;
import com.yuelan.result.enums.YesOrNoEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class BenefitWhiteUserSaveReq {

    @NotNull(message = "主键id不能为空", groups = {EditGroup.class})
    @Schema(description = "主键id")
    private Long id;

    @NotNull(message = "产品id不能为空", groups = {AddGroup.class})
    @Schema(description = "产品id")
    private Long prodId;

    @NotNull(message = "产品名字不能为空", groups = {AddGroup.class})
    @Schema(description = "产品名字")
    private String prodName;

    @Schema(description = "状态 0-无效 1-有效")
    private Integer status;

    /**
     * 首页图
     */
    @NotEmpty(message = "手机号码不能为空", groups = {AddGroup.class})
    @Schema(description = "手机号码")
    private String phone;


    public static BenefitWhiteUserDO convert(BenefitWhiteUserSaveReq req) {
        BenefitWhiteUserDO whiteUserDO = new BenefitWhiteUserDO();
        whiteUserDO.setProdId(req.getProdId());
        whiteUserDO.setProdName(req.getProdName());
        if (StringUtils.isNotBlank(req.getPhone())) {
            whiteUserDO.setPhone(req.getPhone());
        }
        if (req.getStatus() != null) {
            whiteUserDO.setStatus(req.getStatus());
        } else {
            whiteUserDO.setStatus(YesOrNoEnum.YES.getCode());
        }
        return whiteUserDO;
    }


}
