package com.yuelan.hermes.quanyi.controller.ecommerce;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.biz.manager.AdManager;
import com.yuelan.hermes.quanyi.biz.manager.GdCardManager;
import com.yuelan.hermes.quanyi.biz.service.EccAreaService;
import com.yuelan.hermes.quanyi.common.enums.EccChannelTypeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.EccGetCardResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.PhonePrettyTagBO;
import com.yuelan.hermes.quanyi.controller.request.EccInnerChannelGetCardSelectPhoneReq;
import com.yuelan.hermes.quanyi.controller.request.GdPhoneKeySearchReq;
import com.yuelan.hermes.quanyi.controller.response.EccPostAreaResp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> 2024/4/26 上午10:48
 */
@Tag(name = "电商卡/app/领取广电卡相关接口")
@Slf4j
@Validated
@RestController
@RequestMapping("/ecc/gd")
@RequiredArgsConstructor
public class AppEccGdController {

    private final GdCardManager gdCardManager;
    private final EccAreaService eccAreaService;
    private final AdManager adManager;

    /**
     * 用户通过推广页领取广电卡
     */
    @SaIgnore
    @Operation(summary = "用户通过推广页领取广电卡")
    @PostMapping("/receiveSimCard")
    public BizResult<Void> receiveSimCard(@RequestBody @Validated EccInnerChannelGetCardSelectPhoneReq req) {
        EccGetCardResultBO eccGetCardResultBO = gdCardManager.receiveSimCard(req, EccChannelTypeEnum.INNER);
        if (!AppConstants.isReal()) {
            log.info("非正式环境模拟选号完成，手机号码：{}", eccGetCardResultBO.getPhone());
        }
        return BizResult.ok();
    }

    /**
     * 号码搜索
     */
    @SaIgnore
    @Operation(summary = "号码搜索")
    @PostMapping("/phoneSearch")
    public BizResult<List<PhonePrettyTagBO>> searchPhone(@Validated @RequestBody GdPhoneKeySearchReq req) {
        return BizResult.create(gdCardManager.searchPhone(req));
    }

    /**
     * 获取省份信息列表，只有省份这一层
     */
    @Operation(summary = "获取省份信息列表")
    @GetMapping("/listProvince")
    public BizResult<List<EccPostAreaResp>> listProvince() {
        return BizResult.create(eccAreaService.listProvince(SpEnum.CBN));
    }

    /**
     * 获取某一个省份下的地区信息列表
     *
     * @param postProvinceCode 省份编码
     */
    @Operation(summary = "获取某一个省的，地区信息列表")
    @Parameters({
            @Parameter(name = "postProvinceCode", description = "省份编码", required = true)
    })
    @GetMapping("/listArea")
    public BizResult<List<EccPostAreaResp>> listArea(@RequestParam("postProvinceCode") String postProvinceCode) {
        return BizResult.create(eccAreaService.listArea(SpEnum.CBN, postProvinceCode));
    }


}
