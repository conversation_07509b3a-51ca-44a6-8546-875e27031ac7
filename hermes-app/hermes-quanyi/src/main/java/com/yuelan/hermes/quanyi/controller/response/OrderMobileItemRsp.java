package com.yuelan.hermes.quanyi.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 话费订单明细
 */
@Data
public class OrderMobileItemRsp {

    @Schema(description = "订单明细ID")
    private Long id;

    @Schema(description = "订单明细单号")
    private String itemNo;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "供应商")
    private String supplier;

    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;

    @Schema(description = "供应商SKU编号")
    private String supplierSkuNo;

    @Schema(description = "供应商订单号")
    private String supplierOrderNo;

    @Schema(description = "供应商价格")
    private BigDecimal supplierPrice;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "失败原因")
    private String failReason;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}