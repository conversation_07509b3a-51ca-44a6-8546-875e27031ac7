package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsRecordDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> 2024/5/3 下午2:20
 */
@Data
public class EccGoodsRecordResp {

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;

    /**
     * ecc用户id
     */
    @Schema(description = "ecc用户id")
    private Long eccUserId;

    /**
     * 关联子订单号
     */
    @Schema(description = "关联子订单号")
    private String eccItemNo;

    /**
     * 几月的权益（yyyyMM格式）
     */
    @Schema(description = "几月的权益（yyyyMM格式）")
    private String eccOrderMonth;

    /**
     * 电商卡权益包id
     */
    @Schema(description = "电商卡权益包id")
    private Long prodId;


    /**
     * 电商卡商品id
     */
    @Schema(description = "电商卡权益包名字")
    private String prodName;

    /**
     * 电商卡商品id
     */
    @Schema(description = "电商卡商品id")
    private Long goodsId;


    @Schema(description = "电商卡商品名字")
    private String goodsName;


    @Schema(description = "兑换类型:1-直充无库存,2-兑换码")
    private Integer redeemType;

    /**
     * 兑换码
     */
    @Schema(description = "兑换码")
    private String redeemCodeKey;

    /**
     * 兑换密码
     */
    @Schema(description = "兑换密码")
    private String redeemCodePwd;

    /**
     * 发放时间
     */
    @Schema(description = "发放时间")
    private LocalDateTime createTime;


    public static EccGoodsRecordResp buildResp(EccGoodsRecordDO eccGoodsRecordDO) {
        if (Objects.isNull(eccGoodsRecordDO)) {
            return null;
        }
        EccGoodsRecordResp resp = new EccGoodsRecordResp();
        resp.setPhone(eccGoodsRecordDO.getPhone());
        resp.setEccUserId(eccGoodsRecordDO.getEccUserId());
        resp.setEccItemNo(eccGoodsRecordDO.getEccItemNo());
        resp.setEccOrderMonth(eccGoodsRecordDO.getEccOrderMonth());
        resp.setGoodsId(eccGoodsRecordDO.getGoodsId());
        resp.setGoodsName(eccGoodsRecordDO.getGoodsName());
        resp.setRedeemType(eccGoodsRecordDO.getRedeemType());
        resp.setProdId(eccGoodsRecordDO.getProdId());
        resp.setProdName(eccGoodsRecordDO.getProdName());
        resp.setRedeemCodeKey(eccGoodsRecordDO.getRedeemCodeKey());
        resp.setRedeemCodePwd(eccGoodsRecordDO.getRedeemCodePwd());
        resp.setCreateTime(LocalDateTimeUtil.of(eccGoodsRecordDO.getCreateTime()));
        return resp;

    }
}
