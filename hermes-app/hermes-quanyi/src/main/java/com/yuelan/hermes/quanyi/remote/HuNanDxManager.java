package com.yuelan.hermes.quanyi.remote;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.yuelan.hermes.quanyi.common.pojo.properties.HuNanDxProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2025/6/20
 * @since 2025/6/20
 * <p>
 * 湖南电信
 */
@Slf4j
@Service
@AllArgsConstructor
public class HuNanDxManager {

    private static final String QUERY_METHOD = "qry.order.QryBpsOrderInfo";
    private final HuNanDxProperties huNanDxProperties;

    /**
     * 订单查询
     * docs/随销卡/1-订单同步接口汇总（加入CPS规范、选号流程）20210705.docx-查询BPS业务订单详情
     */
    public ApiWrapper<BpsOrderInfoResponse> orderQuery(String bpsOrderId) {
        BpsOrderInfoRequest request = new BpsOrderInfoRequest();
        request.setAccess_token(huNanDxProperties.getAccessToken());
        request.setMethod(QUERY_METHOD);
        request.setVersion("1.0");
        BpsOrderInfoRequest.Content content = new BpsOrderInfoRequest.Content();
        content.setOutId(bpsOrderId);
        request.setContent(content);

        HttpRequest req = HttpRequest.post(huNanDxProperties.API_URL)
                .body(JSONObject.toJSONString(request));
        log.info("【湖南电信】订单查询请求参数: {}", req);
        String response = req.execute().body();
        log.info("【湖南电信】订单查询响应结果: {}", response);
        // 使用TypeReference处理嵌套泛型
        return JSON.parseObject(
                response,
                new TypeReference<ApiWrapper<BpsOrderInfoResponse>>() {
                }
        );
    }


    @Data
    public static class BpsOrderInfoRequest {
        /**
         * 访问令牌（必填）
         */
        private String access_token;

        /**
         * 接口方法名（必填）
         * 示例：qry.order.QryBpsOrderInfo
         */
        private String method;

        /**
         * API 版本号（必填）
         * 示例：1.0
         */
        private String version;

        /**
         * 请求内容主体（必填）
         */
        private Content content;

        @Data
        public static class Content {
            /**
             * 外部订单ID（必填）
             * 示例：100320250630141442397537
             */
            private String outId;
        }
    }

    @Data
    public static class ApiWrapper<T> {

        /**
         * 业务响应内容（对应你现有的 BpsOrderInfoResponse）
         */
        private T result;

        /**
         * 系统级响应消息
         */
        private String res_message;

        /**
         * 系统级响应码
         * 00000 表示成功
         */
        private String res_code;

        /**
         * 判断系统调用是否成功
         */
        public boolean isSystemSuccess() {
            return "00000".equals(res_code);
        }

        /**
         * 获取业务响应数据（需先检查isSystemSuccess）
         */
        public T getBusinessResponse() {
            return result;
        }
    }


    @Data
    public static class BpsOrderInfoResponse {

        /**
         * 响应状态码
         * <p>
         * 0 表示成功，非 0 表示失败。具体错误码参考接口文档附录A。
         */
        private String code;

        /**
         * 响应数据主体
         * <p>
         * 当 code 为 0 时，此字段包含有效业务数据。
         */
        private RespData data;


        public boolean isApiSuccess() {
            return "0".equals(code);
        }

        /**
         * 响应数据内部类（严格对应 JSON 的 data 字段结构）
         */
        @Data
        public static class RespData {
            /**
             * 生产前外呼单详细信息
             * <p>
             * 包含外呼状态、生产状态等核心字段
             */
            private BusOppOrder busOppOrder;

            /**
             * 主业务订单信息
             * <p>
             * 包含订单状态、物流信息、激活状态等完整业务数据
             */
            private ServiceOrder serviceOrder;

            /**
             * 换号业务订单信息（字段结构与 serviceOrder 完全一致）
             * <p>
             * 注意：字段名 hhserviceOrder 需与 JSON 严格保持一致
             */
            private ServiceOrder hhserviceOrder;

            /**
             * 激活前充值标记
             * <p>
             * 1 - 已充值<br>
             * 其他值 - 未充值
             */
            private String isInvest;
        }

        /**
         * 生产前外呼单数据结构
         */
        @Data
        public static class BusOppOrder {
            /**
             * 外呼单处理状态
             * <p>
             * 0 - 待处理<br>
             * 1 - 处理中<br>
             * 2 - 处理完成<br>
             * 3 - 待分配
             */
            private String tbBusOppOrderState;

            /**
             * 外呼单生产状态
             * <p>
             * 0 - 待确认<br>
             * 1 - 生产中<br>
             * 2 - 已作废<br>
             * 3 - 同步失败<br>
             * 4 - 待下单<br>
             * 5 - 待转业务单<br>
             * 8 - 业务单挂起
             */
            private String tbBusOppOrderThState;

            /**
             * 外呼单备注描述
             * <p>
             * 由客服人员填写的处理说明
             */
            private String tbBusOppRemark;

            /**
             * 外呼执行次数
             * <p>
             * 记录对该号码的外呼尝试次数
             */
            private String tbBusOppDoCount;

            /**
             * 最近外呼时间
             * <p>
             * 格式：yyyy-MM-dd HH:mm:ss
             */
            private String shoutTime;

            /**
             * 外呼历史备注
             * <p>
             * 包含所有历史操作记录的完整备注
             */
            private String tbBusOppHistory;
        }

        /**
         * 业务订单数据结构
         */
        @Data
        public static class ServiceOrder {
            /**
             * 订单当前状态
             * <p>
             * pending - 待处理<br>
             * processing - 处理中<br>
             * completed - 处理完成<br>
             * ORDER_CANCEL - 已作废
             */
            private String serviceOrderState;

            /**
             * 订单创建时间
             * <p>
             * 格式：yyyy-MM-dd HH:mm:ss
             */
            private String serviceOrderCreateTime;

            /**
             * 订单处理备注
             * <p>
             * 业务人员填写的处理说明
             */
            private String serviceOrderRemark;

            /**
             * 订单作废原因
             * <p>
             * 当状态为 ORDER_CANCEL 时有效
             */
            private String serviceOrderReason;

            /**
             * 物流运单号
             * <p>
             * 快递公司对应的物流单号
             */
            private String serviceOrderLogisCode;

            /**
             * 用户充值档次
             * <p>
             * 0 - 0元档<br>
             * 010 - (0,10)元档<br>
             * 1030 - [10,30)元档<br>
             * 3050 - [30,50)元档<br>
             * 5070 - [50,70)元档<br>
             * 70100 - [70,100)元档<br>
             * 100 - 100元及以上档
             */
            private String rechargeLevel;

            /**
             * 存费送费业务标识
             * <p>
             * 1 - 参与存费送费（有效）<br>
             * 0 - 未参与<br>
             * 2 - 曾参与但已失效
             */
            private String paySku;

            /**
             * 号码欠费状态
             * <p>
             * 1 - 正常<br>
             * 0 - 欠费<br>
             * -1 - 预拆机<br>
             * -2 - 实拆机
             */
            private String prodStatusNormal;

            /**
             * 物流公司编码
             * <p>
             * 1 - EMS<br>
             * 2 - 顺丰快递<br>
             * 3 - 京东物流（面对面）<br>
             * 4 - 顺丰（面对面）<br>
             * 5 - 集团补卡（卡速达）
             */
            private String serviceOrderCarrier;

            /**
             * CRM系统状态
             * <p>
             * pending - 待同步<br>
             * CHARGE_SUCCESS - 收费成功<br>
             * IS_COMPLETED - 已竣工<br>
             * WRITE_EXCEPTION - 写卡异常<br>
             * CHARGE_EXCEPTION - 收费异常<br>
             * WRITE_SUCCESS - 写卡成功<br>
             * WRITE_FAIL - 写卡失败<br>
             * NO_COMPLETED - 未竣工<br>
             * PIPEI_SUCCESS - 集团配卡成功
             */
            private String serviceOrderCrmStatus;

            /**
             * 物流状态码
             * <p>
             * 00 - 已揽件<br>
             * 10 - 已签收<br>
             * 20 - 拒收退回<br>
             * 30 - 派送中<br>
             * 40 - 签收中
             */
            private String serviceOrderLogisState;

            /**
             * 号码激活时间 时间戳毫秒
             */
            private Long serviceOrderActivationTime;

            /**
             * 激活状态
             * <p>
             * 0 - 待激活<br>
             * 2 - 促激活<br>
             * AC002 - 激活成功<br>
             * AC001 - 激活失败<br>
             * IT001 - 激活异常<br>
             * 4 - 小程序/人工审核外呼
             */
            private String serviceOrderActivationStatus;

            /**
             * 业务订单编号
             * <p>
             * 系统生成的唯一订单标识
             */
            private String serviceOrderOutOrderId;

            /**
             * 预占用的号码
             * <p>
             * 业务办理时预留的手机号码
             */
            private String serviceOrderCusAccPhone;

            /**
             * CPS1扩展字段
             * <p>
             * 保留用于业务扩展
             */
            private String cps1;

            /**
             * 外呼结果记录
             * <p>
             * 每次外呼结果的详细记录
             */
            private String tbBusOppRgRemark;

            /**
             * 客户主动取消原因
             * <p>
             * 记录客户要求取消订单的原因
             */
            private String tbBusOppHistory;

            /**
             * CRM系统单号
             * <p>
             * 激活时对应的CRM工单号
             */
            private String serviceOrderCrmNo;
        }
    }
}
