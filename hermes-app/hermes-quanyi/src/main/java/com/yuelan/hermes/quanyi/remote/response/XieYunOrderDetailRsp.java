package com.yuelan.hermes.quanyi.remote.response;

import lombok.Data;

/**
 * <AUTHOR> 2024/12/10
 * @since 2024/12/10
 */
@Data
public class XieYunOrderDetailRsp {
    /**
     * 运营商订单号
     */
    private String orderId;

    /**
     * 订单状态
     * 0 订单生成
     * 1 订单正在运行
     * 2 订单成功结束
     * 3 订单异常取消
     * 4 号卡开户流程中间状态
     * 6 订单取消中
     */
    private String orderStatus;

    /**
     * 物 流 公 司 ID
     * （（LOGISTICS_COMPAN））
     */
    private String logisticsId;

    /**
     * 物流公司名称
     */
    private String logisticsName;

    /**
     * 物流单号
     */
    private String logisticsNum;

    /**
     * 订单所在环节
     * end           已完成    orderStauts=‘2’ ，代表该订单已完 --领卡成功？       orderStauts=‘3’ ，代表该订单已取消订单成功（完结）--退单
     * perrorEnd    办理失败  orderStauts=‘3’ ，代表订单下单失败（完结） --领卡失败
     * processing   处理中  orderStauts=‘4’ ，代表该订单正在配送途中（暂时不可取消订单）--发货中         orderStauts=‘2’ ，代表该订单已激活成功（完结）--激活成功
     * rejected   已拒签 orderStauts=‘4’ ，代表该订单已配送到达且已拒签（可取消订单）
     * package    待发货  orderStauts=‘4’ ，代表该订单下单成功（等待发货）  --发货中
     */
    private String orderActId;

    /**
     * 订单当前环节状态描述
     */
    private String orderActName;
}
