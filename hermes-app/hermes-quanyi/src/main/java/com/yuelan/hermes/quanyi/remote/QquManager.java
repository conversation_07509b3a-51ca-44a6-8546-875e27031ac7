package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Lists;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.common.pojo.properties.QiquProperties;
import com.yuelan.hermes.quanyi.common.util.QquUtil;
import com.yuelan.hermes.quanyi.remote.request.QquBaseReq;
import com.yuelan.hermes.quanyi.remote.request.QquOrderQueryReq;
import com.yuelan.hermes.quanyi.remote.request.QquRechargeReq;
import com.yuelan.hermes.quanyi.remote.response.QiquBaseRsp;
import com.yuelan.hermes.quanyi.remote.response.QquAmountRsp;
import com.yuelan.hermes.quanyi.remote.response.QquOrderQueryRsp;
import com.yuelan.hermes.quanyi.remote.response.QquRechargeRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

@Slf4j
@Component
public class QquManager {
    //话费慢充充值下单
    private static final String RECHARGE_SLOW = "/api/rechargeslow/server";
    //话费快充充值下单
    private static final String RECHARGE_FAST = "/api/recharge/server";
    //账户余额查询
    private static final String AMOUNT_BALANCE = "/api/amount/server";
    //订单查询
    private static final String QUERY_ORDER = "/api/order/searchpay";

    @Autowired
    private QiquProperties qiquProperties;


    /**
     * 话费快充充值下单
     */
    public QquRechargeRsp fastOrder(String orderNo, String mobile, BigDecimal amount) {
        if (!AppConstants.isReal()) {
            QquRechargeRsp qquRechargeRsp = new QquRechargeRsp();
            qquRechargeRsp.setNumber(IdUtil.getSnowflakeNextIdStr());
            qquRechargeRsp.setOrderId(orderNo);
            qquRechargeRsp.setAmount(amount);
            qquRechargeRsp.setMobile(mobile);
            qquRechargeRsp.setPhoneType(RandomUtil.randomEle(new String[]{"移动", "电信", "联通"}));
            return qquRechargeRsp;
        }
        QquRechargeReq qquRechargeReq = new QquRechargeReq();
        qquRechargeReq.setMobile(mobile);
        qquRechargeReq.setOrderId(orderNo);
        qquRechargeReq.setAmount(amount);
        qquRechargeReq.setNotifyUrl(qiquProperties.getCallBackUrl());
        return doPost(RECHARGE_FAST, qquRechargeReq, QquRechargeRsp.class);
    }

    /**
     * 话费慢充充值下单
     */
    public QquRechargeRsp slowOrder(String orderNo, String mobile, BigDecimal amount) {
        if (!AppConstants.isReal()) {
            QquRechargeRsp qquRechargeRsp = new QquRechargeRsp();
            qquRechargeRsp.setNumber(IdUtil.getSnowflakeNextIdStr());
            qquRechargeRsp.setOrderId(orderNo);
            qquRechargeRsp.setAmount(amount);
            qquRechargeRsp.setMobile(mobile);
            qquRechargeRsp.setPhoneType(RandomUtil.randomEle(new String[]{"移动", "电信", "联通"}));
            return qquRechargeRsp;
        }
        QquRechargeReq qquRechargeReq = new QquRechargeReq();
        qquRechargeReq.setMobile(mobile);
        qquRechargeReq.setOrderId(orderNo);
        qquRechargeReq.setAmount(amount);
        qquRechargeReq.setNotifyUrl(qiquProperties.getCallBackUrl());
        return doPost(RECHARGE_SLOW, qquRechargeReq, QquRechargeRsp.class);
    }

    /**
     * 充值订单查询
     */
    public QquOrderQueryRsp queryOrder(String supplierOrderNo) {
        QquOrderQueryReq req = new QquOrderQueryReq();
        req.setNumber(supplierOrderNo);
        return doPost(QUERY_ORDER, req, QquOrderQueryRsp.class);
    }

    /**
     * 账户余额查询
     */
    public QquAmountRsp accountBalance() {
        return doPost(AMOUNT_BALANCE, new QquBaseReq(), QquAmountRsp.class);
    }

    /**
     * 发送请求
     */
    private <T> T doPost(String url, QquBaseReq req, Class<T> clazz) {
        req.setAppId(qiquProperties.getAppid());
        req.setTimestamp(System.currentTimeMillis());

        Map<String, Object> beanToMap = BeanUtil.beanToMap(req);
        String sign = QquUtil.signMD5(qiquProperties.getAppsecret(), beanToMap, Lists.newArrayList("sign"));

        beanToMap.put("sign", sign);

        String host = qiquProperties.getHost();

        String result = HttpUtil.post(host + url, beanToMap, 10000);
        log.info("奇趣请求成功,url:{},params:{},result:{}", url, beanToMap, result);
        QiquBaseRsp<T> qiquBaseRsp = JSON.parseObject(result, new TypeReference<QiquBaseRsp<T>>(clazz) {
        });
        qiquBaseRsp.checkResult();
        return qiquBaseRsp.getData();
    }
}
