package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.YearMonth;
import java.util.Objects;

@Data
@EqualsAndHashCode(callSuper = true)
public class GamingProductItemReq extends GamingProductOptReq {

    @NotNull(message = "请选择商品")
    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "生效月份yyyy-MM ，该月第一天")
    @NotNull(message = "请选择生效月份")
    private YearMonth effectiveMonth;

    @Schema(description = "过期月份yyyy-MM,含该月最后一天")
    private YearMonth expireMonth;

    public void checkReq() {
        if (Objects.isNull(effectiveMonth)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "生效时间必填");
        }
        if (Objects.nonNull(expireMonth) && expireMonth.isBefore(effectiveMonth)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "过期时间不能早于生效时间");
        }
    }

}
