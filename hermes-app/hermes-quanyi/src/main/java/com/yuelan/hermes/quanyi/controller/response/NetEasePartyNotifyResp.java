package com.yuelan.hermes.quanyi.controller.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> 2025/2/8
 * @since 2025/2/8
 */
@Data
@Accessors(chain = true)
public class NetEasePartyNotifyResp {
    /**
     * 200成功 不在通知给我们
     * 500其他错误，基础通知
     */
    private Integer code;

    private String msg;

    public NetEasePartyNotifyResp setSuccess() {
        this.code = 200;
        this.msg = "success";
        return this;
    }

    public NetEasePartyNotifyResp setFail(String failMsg) {
        this.code = 500;
        this.msg = failMsg;
        return this;
    }

}
