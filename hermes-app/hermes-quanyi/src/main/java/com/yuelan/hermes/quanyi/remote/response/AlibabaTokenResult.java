package com.yuelan.hermes.quanyi.remote.response;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR> 2024/5/8 下午8:49
 */
@Data
public class AlibabaTokenResult {
    @JSONField(name = "w1_expires_in")
    private Long w1ExpiresIn;

    @JSONField(name = "refresh_token_valid_time")
    private Long refreshTokenValidTime;

    @JSONField(name = "taobao_user_nick")
    private String taoBaoUserNick;

    @JSONField(name = "re_expires_in")
    private Long reExpiresIn;

    @JSONField(name = "expire_time")
    private Long expireTime;

    @JSONField(name = "token_type")
    private String tokenType;

    @JSONField(name = "access_token")
    private String accessToken;

    @JSONField(name = "taobao_open_uid")
    private String taoBaoOpenUid;

    @JSONField(name = "w1_valid")
    private Long w1Valid;

    @JSONField(name = "refresh_token")
    private String refreshToken;

    @JSONField(name = "w2_expires_in")
    private Long w2ExpiresIn;

    @JSONField(name = "w2_valid")
    private Long w2Valid;

    @JSONField(name = "r1_expires_in")
    private Long r1ExpiresIn;

    @JSONField(name = "r2_expires_in")
    private Long r2ExpiresIn;

    @JSONField(name = "r2_valid")
    private Long r2Valid;

    @JSONField(name = "r1_valid")
    private Long r1Valid;

    @JSONField(name = "taobao_user_id")
    private String taoBaoUserId;


}
