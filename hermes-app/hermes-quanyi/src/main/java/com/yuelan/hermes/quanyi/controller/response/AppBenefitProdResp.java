package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.fastjson2.JSONArray;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.interfaces.PayChannelPkgInter;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/2 19:12
 */
@Data
public class AppBenefitProdResp {


    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long prodId;

    /**
     * 产品名字
     */
    @Schema(description = "产品名字")
    private String prodName;

    /**
     * 产品编码
     */
    @Schema(description = "产品编码")
    private String prodCode;

    /**
     * 销售价格
     */
    @Schema(description = "销售价格")
    private BigDecimal price;

    /**
     * 支付通道id
     */
    @Schema(description = "支付通道id")
    private Integer payChannelId;

    /**
     * 支付通道包id
     */
    @Schema(description = "支付通道包id")
    private Integer payChannelPkgId;

    /**
     * 背景色代码
     */
    @Schema(description = "背景色代码")
    private String bgColorCode;

    /**
     * 首页图
     */
    @Schema(description = "首页图")
    private String homepage;

    /**
     * 宣传组图
     */
    @Schema(description = "宣传组图")
    private List<String> imgs;


    /**
     * 订阅按钮图
     */
    @Schema(description = "订阅按钮图")
    private String subButton;

    /**
     * 订阅成功手机号
     */
    @Schema(description = "订阅成功手机号")
    private String subSuccessTel;

    /**
     * 兑换链接
     */
    @Schema(description = "兑换链接")
    private String redeemUrl;

    /**
     * 兑换次数限制（多合一会员可以兑换几个）
     */
    @Schema(description = "兑换次数限制")
    private Integer redeemLimit;

    @Schema(defaultValue = "支付渠道业务id")
    private String payChannelBizId;

    /**
     * 是否显示二确弹窗
     */
    @Schema(defaultValue = "是否显示二确弹窗")
    private Integer showPopover;

    /**
     * 二确介绍图
     */
    @Schema(defaultValue = "二确介绍图")
    private String popBgPath;

    /**
     * 二确确认键
     */
    @Schema(defaultValue = "二确确认键")
    private String popBtnPath;

    /**
     * 二确取消键
     */
    @Schema(defaultValue = "二确取消键")
    private String popCancelPath;

    /**
     * 是否显示客服电话
     */
    @Schema(defaultValue = "是否显示客服电话")
    private Integer showHelpTel;

    /**
     * 是否显示订购结果页
     */
    @Schema(defaultValue = "是否显示订购结果页")
    private Integer showOrderResult;

    public static AppBenefitProdResp buildResp(BenefitProductDO productDO) {
        if (productDO == null) {
            return null;
        }
        AppBenefitProdResp resp = new AppBenefitProdResp();
        resp.setProdId(productDO.getProdId());
        resp.setProdName(productDO.getProdName());
        resp.setPrice(productDO.getPrice());
        resp.setPayChannelId(productDO.getPayChannelId());
        resp.setBgColorCode(productDO.getBgColorCode());
        resp.setHomepage(productDO.getHomepage());
        if (Objects.nonNull(productDO.getImgs())) {
            resp.setImgs(JSONArray.parseArray(productDO.getImgs(), String.class));
        }
        resp.setSubButton(productDO.getSubButton());
        resp.setSubSuccessTel(productDO.getSubSuccessTel());
        resp.setRedeemUrl(productDO.getRedeemUrl());
        resp.setRedeemLimit(productDO.getRedeemLimit());
        resp.setPayChannelPkgId(productDO.getPayChannelPkgId());
        resp.setProdCode(productDO.getProdCode());

        List<PayChannelPkgInter> pkgInterList = Objects.requireNonNull(BenefitPayChannelEnum.of(productDO.getPayChannelId())).getPkgInterList();
        Integer payChannelPkgId = productDO.getPayChannelPkgId();
        String bizId = pkgInterList.stream().filter(x -> x.getChannelPkgId().equals(payChannelPkgId)).findFirst().map(PayChannelPkgInter::getChannelBizId).orElse(null);

        resp.setPayChannelBizId(bizId);

        resp.setShowHelpTel(productDO.getShowHelpTel());
        resp.setShowOrderResult(productDO.getShowOrderResult());
        resp.setShowPopover(productDO.getShowPopover());
        resp.setPopBgPath(productDO.getPopBgPath());
        resp.setPopBtnPath(productDO.getPopBtnPath());
        resp.setPopCancelPath(productDO.getPopCancelPath());
        return resp;
    }
}
