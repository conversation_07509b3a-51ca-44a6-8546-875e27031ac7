package com.yuelan.hermes.quanyi.controller.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuelan.core.validator.constraints.EnumLimit;
import com.yuelan.hermes.commons.enums.DeliveryTypeEnum;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GamingOrderItemListReq extends PageRequest {

    @Schema(description = "订单号（产品订单号/商户订单号/明细订单号/供应商订单号）")
    private String orderSearch;

    @Schema(description = "商户订单号")
    private String outOrderNo;

    @Schema(description = "明细订单号")
    private String itemNo;

    @Schema(description = "供应商订单号")
    private String supplierOrderNo;

    @Schema(description = "产品订单号")
    private String orderNo;

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "商品ID列表")
    private List<Long> goodsIds;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "产品ID列表")
    private List<Long> productIds;

    @Schema(description = "供应商类型")
    private Integer supplierType;

    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;

    @Schema(description = "发放方式：1-直充，2-兑换码")
    @EnumLimit(message = "发放方式不合法", enumInterface = DeliveryTypeEnum.class)
    private Integer deliveryType;

    @Schema(description = "总订单ID")
    private Long orderId;

    @Schema(description = "兑换码")
    private String redeemCode;

    @Schema(description = "明细订单状态：0处理中1交易成功2交易失败3订单异常")
    private Integer orderStatus;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "预下单状态 0-预下单默认状态 1-预下单成功 2-预下单失败")
    private Integer preOrderStatus;

    @Schema(description = "权益领取状态 0-待领取 1-领取成功 2-领取失败")
    private Integer obtainStatus;

    @Schema(description = "明细时间-开始")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeStart;

    @Schema(description = "明细时间-结束")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeEnd;

    @Schema(description = "领取时间-开始")
    private LocalDateTime obtainTimeStart;

    @Schema(description = "领取时间-结束")
    private LocalDateTime obtainTimeEnd;

    private Long startId;

}
