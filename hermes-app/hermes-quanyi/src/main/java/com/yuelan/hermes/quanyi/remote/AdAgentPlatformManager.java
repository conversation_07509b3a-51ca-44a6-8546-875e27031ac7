package com.yuelan.hermes.quanyi.remote;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yuelan.boot.constant.AppConstants;
import com.yuelan.hermes.quanyi.remote.request.OrderChangeStatusReq;
import com.yuelan.hermes.quanyi.remote.request.OrderSyncReq;
import com.yuelan.hermes.quanyi.remote.request.SkuPageSaveOrUpdateReq;
import com.yuelan.hermes.quanyi.remote.request.SkuSaveOrUpdateReq;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR> 2024/11/25
 * @since 2024/11/25
 */
@Slf4j
@Service
public class AdAgentPlatformManager {

    private static final String TEST_DOMAIN = "https://ad-api-test.hzyuelan.com";
    private static final String PROD_DOMAIN = "https://ad-api.hzyuelan.com";


    private static final String SYNC_URL = "/api/nc/order/init";
    private static final String STATUS_URL = "/api/nc/order/changeStatus";

    @Autowired
    ObjectMapper objectMapper;

    public void userOrderCreateNotify(OrderSyncReq req) {
        try {
            log.info("syncOrder req:{}", req);
            String jsonReq = objectMapper.writeValueAsString(req);
            String domain = AppConstants.isReal() ? PROD_DOMAIN : TEST_DOMAIN;
            HttpResponse response = HttpUtil.createPost(domain + SYNC_URL)
                    .body(jsonReq)
                    .execute();
            if (!response.isOk()) {
                log.error("syncOrder error, response code:{} body:{}", response.getStatus(), response.body());
                return;
            }
            JSONObject resp = JSONObject.parseObject(response.body());
            Integer code = resp.getInteger("code");
            if (!Objects.equals(200, code)) {
                log.error("syncOrder error, response:{}", resp);
                return;
            }
            log.info("syncOrder success, response:{}", resp);
        } catch (Exception e) {
            log.error("syncOrder error", e);
        }
    }

    public void orderStatusChangeNotify(OrderChangeStatusReq req) {
        try {
            log.info("syncOrderStatus req:{}", req);
            String jsonReq = objectMapper.writeValueAsString(req);
            String domain = AppConstants.isReal() ? PROD_DOMAIN : TEST_DOMAIN;
            HttpResponse response = HttpUtil.createPost(domain + STATUS_URL)
                    .body(jsonReq)
                    .execute();
            if (!response.isOk()) {
                log.error("syncOrderStatus error, response code:{} body:{}", response.getStatus(), response.body());
                return;
            }
            JSONObject resp = JSONObject.parseObject(response.body());
            Integer code = resp.getInteger("code");
            if (!Objects.equals(200, code)) {
                log.error("syncOrderStatus error, response:{}", resp);
                return;
            }
            log.info("syncOrderStatus success, response:{}", resp);
        } catch (Exception e) {
            log.error("syncOrderStatus error", e);
        }
    }

    public void productSync(SkuSaveOrUpdateReq req) {
        try {
            log.info("syncProduct req:{}", req);
            String jsonReq = objectMapper.writeValueAsString(req);
            String domain = AppConstants.isReal() ? PROD_DOMAIN : TEST_DOMAIN;
            HttpResponse response = HttpUtil.createPost(domain + "/api/nc/sku/saveOrUpdate")
                    .body(jsonReq)
                    .execute();
            if (!response.isOk()) {
                log.error("syncProduct error, response code:{} body:{}", response.getStatus(), response.body());
                return;
            }
            JSONObject resp = JSONObject.parseObject(response.body());
            Integer code = resp.getInteger("code");
            if (!Objects.equals(200, code)) {
                log.error("syncProduct error, response:{}", resp);
                throw BizException.create(BaseErrorCodeEnum.SYS_ERROR, "同步至投放平台失败");
            }
            log.info("syncProduct success, response:{}", resp);
        } catch (Exception e) {
            log.error("syncProduct error", e);
            throw BizException.create(BaseErrorCodeEnum.SYS_ERROR, "同步至投放平台失败");
        }
    }

    public void syncProductPage(SkuPageSaveOrUpdateReq req) {
        try {
            log.info("syncProductPage req:{}", req);
            String jsonReq = objectMapper.writeValueAsString(req);
            String domain = AppConstants.isReal() ? PROD_DOMAIN : TEST_DOMAIN;
            HttpResponse response = HttpUtil.createPost(domain + "/api/nc/sku/saveOrUpdatePage")
                    .body(jsonReq)
                    .execute();
            if (!response.isOk()) {
                log.error("syncProductPage error, response code:{} body:{}", response.getStatus(), response.body());
                return;
            }
            JSONObject resp = JSONObject.parseObject(response.body());
            Integer code = resp.getInteger("code");
            if (!Objects.equals(200, code)) {
                log.error("syncProductPage error, response:{}", resp);
                throw BizException.create(BaseErrorCodeEnum.SYS_ERROR, "同步至投放平台失败");
            }
            log.info("syncProductPage success, response:{}", resp);
        } catch (Exception e) {
            log.error("syncProductPage error", e);
            throw BizException.create(BaseErrorCodeEnum.SYS_ERROR, "同步至投放平台失败");
        }
    }
}
