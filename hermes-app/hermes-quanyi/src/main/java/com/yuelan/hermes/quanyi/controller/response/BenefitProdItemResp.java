package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductItemDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> 2024/4/10 下午3:57
 * <p>
 * 权益包 关联商品信息
 */
@Data
public class BenefitProdItemResp {

    @Schema(description = "关联id")
    private Long itemId;

    @Schema(description = "所属权益包产品id")
    private Long prodId;

    @Schema(description = "排序序号，从小到大")
    private Integer sort;

    @Schema(description = "商品详情")
    private BenefitGoodsResp goods;


    public static BenefitProdItemResp buildResp(BenefitProductItemDO itemDO, BenefitGoodsDO goodsDO) {
        if (itemDO == null) {
            return null;
        }
        BenefitProdItemResp resp = new BenefitProdItemResp();
        resp.setItemId(itemDO.getProdItemId());
        resp.setProdId(itemDO.getProdId());
        resp.setSort(itemDO.getSort());
        resp.setGoods(BenefitGoodsResp.buildResp(goodsDO));
        return resp;
    }

}
