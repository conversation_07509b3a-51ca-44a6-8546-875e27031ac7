package com.yuelan.hermes.quanyi.controller.open;

import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.biz.service.MgOrderService;
import com.yuelan.hermes.quanyi.controller.request.MiguFunCreateOrderReq;
import com.yuelan.hermes.quanyi.controller.response.MiguFunCreateOrderRsp;
import com.yuelan.hermes.quanyi.mq.producer.ProducerHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@Tag(name = "咪咕互娱API")
@RequestMapping("/mg")
@RestController
public class MgOrderController {
    @Autowired
    private MgOrderService mgOrderService;
    @Autowired
    private ProducerHelper producerHelper;

    @Operation(summary = "下单")
    @PostMapping(value = "/order/create")
    public MiguFunCreateOrderRsp createOrder(@Valid @RequestBody MiguFunCreateOrderReq req, @RequestHeader("sign") String sign) {
        req.setSign(sign);
        if (log.isDebugEnabled()) {
            log.info("咪咕互娱下单:{}", JSON.toJSONString(req));
        }
        try {
            return mgOrderService.lockAndCreateOrder(req);
        } catch (Exception e) {
            log.error("保存出错出错,插入MQ", e);
            // 入消息队列
            producerHelper.gameOrderReqMsg(req);
            return MiguFunCreateOrderRsp.success();
        }

    }

}
