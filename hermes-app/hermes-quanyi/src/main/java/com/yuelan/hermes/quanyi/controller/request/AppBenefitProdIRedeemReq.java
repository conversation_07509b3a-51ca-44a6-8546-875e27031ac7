package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/4/10 下午5:17
 */
@Data
public class AppBenefitProdIRedeemReq {

    @Schema(description = "商品编码")
    @NotEmpty(message = "商品编码不能为空")
    private String prodCode;


    @Schema(description = "商品id")
    @NotNull(message = "商品id不能为空")
    private Long goodsId;
}
