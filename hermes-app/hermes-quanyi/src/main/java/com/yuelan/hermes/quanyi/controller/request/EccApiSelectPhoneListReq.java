package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/7/22 下午7:27
 */
@Data
public class EccApiSelectPhoneListReq {

    @NotNull(message = "[期望返回号码数量]不能为空")
    @Min(value = 1, message = "[期望返回号码数量]数量只能在1-100区间")
    @Max(value = 100, message = "[期望返回号码数量]数量只能在1-100区间")
    @Schema(title = "期望返回号码数量", description = "数量只能在1-100区间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer selectNums;


    @NotEmpty(message = "[电商卡编号]不能为空")
    @Schema(title = "电商卡编号", description = "固定参数我方提供", requiredMode = Schema.RequiredMode.REQUIRED)
    private String eccProdCode;

    @Schema(title = "尾号搜索", description = "非必传，直接调用运营商接口查询尾号，2-4位", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String phoneLast4;

    @Schema(title = "号码归属地-省份编码", description = "非必传，不传返回随机归属地", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String provinceCode;

    @Schema(title = "号码归属地-城市编码", description = "非必传，不传返回随机归属地", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String cityCode;


    public EccSelectPoolPhoneListReq convertPoolReq() {
        EccSelectPoolPhoneListReq req = new EccSelectPoolPhoneListReq();
        req.setSelectNum(this.selectNums);
        req.setEccProdCode(this.eccProdCode);
        req.setProvinceCode(this.provinceCode);
        req.setCityCode(this.cityCode);
        return req;
    }

    public EccSelectPhoneListByPhoneLast4Req convertPhoneLast4SearchReq() {
        EccSelectPhoneListByPhoneLast4Req req = new EccSelectPhoneListByPhoneLast4Req();
        req.setSelectNum(this.selectNums);
        req.setEccProdCode(this.eccProdCode);
        req.setPhoneLast4(this.phoneLast4);
        req.setProvinceCode(this.provinceCode);
        req.setCityCode(this.cityCode);
        return req;
    }

}
