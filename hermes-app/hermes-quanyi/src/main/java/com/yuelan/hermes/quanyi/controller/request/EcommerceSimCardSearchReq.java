package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR> 2024/4/26 下午5:17
 */
@Data
public class EcommerceSimCardSearchReq {


    @Schema(description = "尾号2-4位数字")
    private String phoneLast4;


    @Schema(description = "号码类型，支持AAAAA、AAAA、ABCDE、ABCD、AAA、AABB、ABAB、ABC、AA，从末尾匹配")
    private String codeTypeCode;


    @Schema(description = "号段,比如186")
    private String numNet;

    @Schema(description = "一次选号数量，只有10和100 这2个选项")
    private Integer selectNum;

    private String provinceCode;

    private String cityCode;
}
