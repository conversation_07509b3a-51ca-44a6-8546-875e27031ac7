package com.yuelan.hermes.quanyi.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
public class OrderVirtualRsp {

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "第三方订单号")
    private String outOrderNo;

    @Schema(description = "用户手机号")
    private String phone;

    @Schema(description = "充值账号")
    private String userAccount;

    @Schema(description = "充值类型")
    private Integer goodsType;

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "SKU")
    private Long skuId;

    @Schema(description = "SKU编号")
    private String skuNo;

    @Schema(description = "SKU名称")
    private String skuName;

    @Schema(description = "SKU图片")
    private String skuImage;

    @Schema(description = "集采客户")
    private String buyer;

    @Schema(description = "订单数量")
    private Long quantity;

    @Schema(description = "订单金额")
    private BigDecimal totalAmount;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "回调状态")
    private Integer notifyStatus;

    @Schema(description = "订单时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderCreateTime;
}
