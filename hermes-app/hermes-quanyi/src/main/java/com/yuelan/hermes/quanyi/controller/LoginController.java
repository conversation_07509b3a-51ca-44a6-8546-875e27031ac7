package com.yuelan.hermes.quanyi.controller;

import cn.dev33.satoken.stp.SaTokenInfo;
import com.yuelan.hermes.quanyi.biz.service.LoginService;
import com.yuelan.hermes.quanyi.config.satoken.context.AdminContext;
import com.yuelan.hermes.quanyi.controller.request.AdminLoginReq;
import com.yuelan.hermes.quanyi.controller.request.AdminLoginSmsCodeReq;
import com.yuelan.hermes.quanyi.controller.response.AdminUserInfoRsp;
import com.yuelan.hermes.quanyi.controller.response.PicCaptchaRsp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(name = "管理员登录")
@RestController
public class LoginController extends AdminBaseController {

    @Autowired
    private LoginService loginService;

    @Operation(summary = "获取图形验证码")
    @GetMapping("/admin/picCaptcha")
    public BizResult<PicCaptchaRsp> getPicCaptcha(@RequestParam String username) {
        PicCaptchaRsp result = loginService.getPicCaptcha(username);
        return BizResult.create(result);
    }

    @Operation(summary = "获取登录短信验证码", description = "图形验证码验证失败时，请重新获取新的图像验证码")
    @PostMapping("/admin/loginSmsCode")
    public BizResult<Void> loginSmsCode(@Valid @RequestBody AdminLoginSmsCodeReq req) {
        loginService.reqSmsCode(req);
        return BizResult.ok();
    }

    @Operation(summary = "获取随机码")
    @GetMapping("/admin/loginKey")
    public BizResult<String> generateKey() {
        String key = loginService.generateKey();
        return BizResult.create(key);
    }

    @Operation(summary = "用户登录")
    @PostMapping("/admin/login")
    public BizResult<SaTokenInfo> login(@Valid @RequestBody AdminLoginReq loginReq) {
        SaTokenInfo tokenInfo = loginService.login(loginReq);
        return BizResult.create(tokenInfo);
    }

    @Operation(summary = "获取登录用户信息")
    @GetMapping("/a/userinfo")
    public BizResult<AdminUserInfoRsp> getUserInfo() {
        AdminContext loginUser = this.getLoginUser();
        AdminUserInfoRsp userInfo = loginService.getUserInfo(loginUser);
        return BizResult.create(userInfo);
    }

    @Operation(summary = "用户登出")
    @GetMapping("/a/logout")
    public BizResult<Boolean> logout() {
        boolean result = loginService.logout();
        return BizResult.create(result);
    }

}
