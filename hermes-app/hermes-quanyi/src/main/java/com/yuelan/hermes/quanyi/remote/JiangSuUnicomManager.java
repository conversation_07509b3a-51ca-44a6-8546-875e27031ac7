package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.commons.enums.SmsCodeType;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.common.constant.RedisKeys;
import com.yuelan.hermes.quanyi.common.enums.BenefitPayChannelEnum;
import com.yuelan.hermes.quanyi.common.enums.JSUnicomWoPayPkgEnum;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitUnicomPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.JiangSuUnicomProperties;
import com.yuelan.hermes.quanyi.common.util.AesEncryptUtils;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.controller.response.jsunicom.*;
import com.yuelan.plugins.redisson.util.RedisUtils;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Deprecated
public class JiangSuUnicomManager {


    public static final String TAG = "[江苏联通]";
    // 云供应商下发验证码
    public static final String GET_SMS_CODE = "/oldUserApi/outVsed/getSmsCode";
    // 校验验证码
    public static final String VERIFYCAPTCHA = "/oldUserApi/outVsed/verifyCaptcha";
    // 客户运营_产品推荐接口
    public static final String PRODUCT_RECOMMEND = "/oldUserApi/outVsed/productRecommend";
    // 客户运营_业务办理下单接口
    public static final String PRODUCT_ORDER = "/oldUserApi/outVsed/productOrder";
    // 客户运营_查询最新cbss升级信息
    public static final String UPGRADE_INFO = "/oldUserApi/outVsed/upgradeInfo";
    // 客户运营_订单详情
    public static final String PRODUCT_ORDER_DETAIL = "/oldUserApi/outVsed/productOrderDetail";

    private static final String SUCCESS_CODE = "0000";
    private static final int TIME_OUT = 10000;

    @Autowired
    private JiangSuUnicomProperties jiangSuUnicomProperties;

    @Resource
    private BenefitOrderLogService benefitOrderLogService;


    /**
     * 点击发送验证码
     */
    public boolean orderSendSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        String mobile = reqParams.getMobile();
        if (StringUtils.isEmpty(mobile)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }

        JSUnicomWoPayPkgEnum pkgEnum = JSUnicomWoPayPkgEnum.of(productDO.getPayChannelPkgId());
        // 获取投放渠道
        String channel = productDO.getDistributionChannel();
        if (StringUtils.isEmpty(channel)) {
            log.info(TAG + "该权益包产品没有配置渠道.手机号:{},权益包产品id:{}", reqParams.getMobile(), productDO.getProdId());
            return false;
        }
        String[] split = channel.split(StrUtil.DASHED);
        String commId = split[1];
        // 获取运营商推荐的商品
        JiangSuUnicomProductResp remoteProductResp = this.productRecommend(mobile, pkgEnum);
        if (remoteProductResp == null) {
            throw BizException.create(BaseErrorCodeEnum.NOT_PERMISSION, "暂时无法订购");
        }
        List<JiangSuUnicomProductResp.CommInfoDTO> commInfo = remoteProductResp.getCOMM_INFO();
        JiangSuUnicomProductResp.CommInfoDTO commInfoDTO = commInfo.stream().filter(x -> x.getCOMM_ID().equals(Long.valueOf(commId))).findFirst().orElse(null);
        if (commInfoDTO == null) {
            log.info(TAG + "运营商未推荐该产品。手机号:{},权益包产品code:{},运营商产品id:{}", reqParams.getMobile(), productDO.getProdCode(), commId);
            throw BizException.create(BizErrorCodeEnum.PROD_NOT_EXIST, "未找到相符推荐产品");
        }
        remoteProductResp.setCOMM_INFO(Lists.newArrayList(commInfoDTO));

        String businessId = remoteProductResp.getBUSINESS_ID();
        // 运营商下发验证码
        boolean sendSmsCode = this.sendSmsCode(mobile, businessId, commId, pkgEnum);
        if (!sendSmsCode) {
            return false;
        }
        // 放入Redis中
        String key = RedisKeys.getJiangSuUnicomProductKey(mobile);
        RedisUtils.setCacheObject(key, remoteProductResp, Duration.ofMinutes(15));

        return true;
    }

    /**
     * 申请订单
     */
    public BenefitPayResultBO createOrder(UserBenefitOrderReq req, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        BenefitUnicomPayResultBO payResultBO = new BenefitUnicomPayResultBO();
        payResultBO.setSuccess(false);
        String mobile = req.getMobile();

        JSUnicomWoPayPkgEnum pkgEnum = JSUnicomWoPayPkgEnum.of(productDO.getPayChannelPkgId());

        // 获取运营商推荐的商品
        String key = RedisKeys.getJiangSuUnicomProductKey(mobile);
        JiangSuUnicomProductResp productResp = RedisUtils.getCacheObject(key);
        if (productResp == null) {
            // 缓存中没有产品信息,说明缓存过期,中间间隔的时间过久
            payResultBO.setMessage("请重新发送验证码");
            return payResultBO;
        }
        String businessId = productResp.getBUSINESS_ID();
        JiangSuUnicomProductResp.CommInfoDTO commInfoDTO = productResp.getCOMM_INFO().stream().findFirst().orElse(null);
        if (commInfoDTO == null) {
            log.info(TAG + "运营商商品缓存没有数据.手机号:{},redisKey:{}", req.getMobile(), key);
            payResultBO.setMessage("订阅失败,请稍后重试");
            return payResultBO;
        }
        String strategyId = commInfoDTO.getSTRATEGY_ID();
        Long commId = commInfoDTO.getCOMM_ID();
        // 远程校验验证码
        boolean smsCodeFlag = this.checkSmsCode(mobile, businessId, req.getSmsCode(), pkgEnum);
        if (!smsCodeFlag) {
            log.info(TAG + "运营商验证码校验失败.手机号:{}", req.getMobile());
            payResultBO.setMessage("订阅失败,请稍后重试");
            return payResultBO;
        }

        // 开始下单
        JiangSuUnicomOrderResp orderResp = this.productOrder(orderDO.getOrderNo(), mobile, req.getSmsCode(), businessId, commId, strategyId, pkgEnum);
        if (orderResp == null) {
            log.info(TAG + "运营商下单失败.手机号:{}", req.getMobile());
            payResultBO.setMessage("订阅失败,请稍后重试");
            return payResultBO;
        }
        orderDO.setPreorderContent(JSON.toJSONString(orderResp));
        if (StringUtils.isNotBlank(orderResp.getOutOrderId())) {
            // 预下单,订单号存入扩展字段中
            orderDO.setExtraData(new JSONObject() {{
                put("outOrderId", orderResp.getOutOrderId());
            }}.toString());
            orderDO.setPayStatus(PayStatusEnum.DEFAULT.getCode());
            orderDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
            payResultBO.setSuccess(true);
        } else {
            // 正式下单
            String respCode = orderResp.getRSP_CODE();
            if (SUCCESS_CODE.equals(respCode)) {
                payResultBO.setSuccess(true);
                orderDO.setOutOrderNo(orderResp.getRSP_DATA().getORDER_ID());
                orderDO.setPayStatus(PayStatusEnum.DEFAULT.getCode());
                orderDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
            }
        }
        return payResultBO;
    }

    /**
     * 运营商获取产品推荐
     */
    private JiangSuUnicomProductResp productRecommend(String mobile, JSUnicomWoPayPkgEnum pkgEnum) {
        if (StringUtils.isEmpty(mobile)) {
            return null;
        }
        JiangSuUnicomProperties.Config properties = jiangSuUnicomProperties.getProperties(pkgEnum);
        Map<String, Object> params = new HashMap<>();
        params.put("serialNumber", mobile);
        params.put("channelCode", properties.getChannelId());
        params.put("queryType", "1");
        try {
            Object object = this.sendRemoteRequest(PRODUCT_RECOMMEND, params, properties, new BenefitOrderLog.Args(BenefitOrderLog.Biz.PRODUCT, mobile));
            return JSON.parseObject(JSON.toJSONString(object), JiangSuUnicomProductResp.class);
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw (BizException) e;
            }
            log.info(TAG + "产品推荐接口失败", e);
            return null;
        }
    }

    /**
     * 运营商下发短信验证码
     */
    private boolean sendSmsCode(String mobile, String bussinessId, String commId, JSUnicomWoPayPkgEnum pkgEnum) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        JiangSuUnicomProperties.Config properties = jiangSuUnicomProperties.getProperties(pkgEnum);
        Map<String, Object> params = new HashMap<>();
        params.put("serialNumber", mobile);
        params.put("commId", commId);
        params.put("channelCode", properties.getChannelId());
        params.put("bussinessId", bussinessId);
        params.put("entranName", "老用户专区");
        try {
            Object object = this.sendRemoteRequest(GET_SMS_CODE, params, properties, new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_SEND, mobile));
            if (object == null) {
                return false;
            }
            JiangSuUnicomSendSmsCodeResp resp = JSON.parseObject(JSON.toJSONString(object), JiangSuUnicomSendSmsCodeResp.class);
            String key = resp.getKey();
            // 存入缓存,
            String smsCodeKey = RedisKeys.getSmsCodeKey(mobile, SmsCodeType.BENEFIT_USER_ORDER);
            RedisUtils.setCacheObject(smsCodeKey, key, Duration.ofMinutes(10));
            return true;
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw (BizException) e;
            }
            log.info(TAG + "运营商下发短信验证码失败", e);
            return false;
        }
    }

    /**
     * 运营商校验验证码
     */
    private boolean checkSmsCode(String mobile, String bussinessId, String smsCode, JSUnicomWoPayPkgEnum pkgEnum) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        String key = RedisKeys.getSmsCodeKey(mobile, SmsCodeType.BENEFIT_USER_ORDER);
        String smsCodeKey = RedisUtils.getCacheObject(key);
        if (StringUtils.isEmpty(smsCodeKey)) {
            log.info(TAG + "缓存中未获取到有效的验证码key,");
            return false;
        }
        JiangSuUnicomProperties.Config properties = jiangSuUnicomProperties.getProperties(pkgEnum);
        Map<String, Object> params = new HashMap<>();
        params.put("serialNumber", mobile);
        params.put("channelCode", properties.getChannelId());
        params.put("bussinessId", bussinessId);
        params.put("smsCode", smsCode);
        params.put("key", smsCodeKey);
        try {

            Object object = this.sendRemoteRequest(VERIFYCAPTCHA, params, properties, new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_CHECK, mobile));
            return object != null;
            // 不报错,校验成功
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw (BizException) e;
            }
            log.info(TAG + "运营商校验验证码失败", e);
            return false;
        }
    }

    /**
     * 下单
     */
    private JiangSuUnicomOrderResp productOrder(String orderNo, String mobile, String smsCode, String bussinessId, Long commId, String strategyId, JSUnicomWoPayPkgEnum pkgEnum) {
        if (StringUtils.isEmpty(mobile)) {
            return null;
        }
        JiangSuUnicomProperties.Config properties = jiangSuUnicomProperties.getProperties(pkgEnum);
        Map<String, Object> params = new HashMap<>();
        params.put("serialNumber", mobile);
        params.put("channelCode", properties.getChannelId());
        params.put("bussinessId", bussinessId);
        params.put("smsCode", smsCode);
        params.put("modifyTag", "0");
        params.put("strategyId", strategyId);
        params.put("commId", commId);
        params.put("payTag", "9");
        params.put("feeMode", "0");
        params.put("priceVal", "0");
        try {
            Object object = this.sendRemoteRequest(PRODUCT_ORDER, params, properties, new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER, mobile, orderNo));
            return JSON.parseObject(JSON.toJSONString(object), JiangSuUnicomOrderResp.class);
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw (BizException) e;
            }
            log.info(TAG + "运营商业务办理下单接口失败", e);
            return null;
        }
    }

    /**
     * 订单详情查询
     *
     * @param orderId    订单id
     * @param outOrderId 外部订单id(如果为预下单的订单,传这个)
     */
    public JiangSuUnicomOrderDetailResp productOrderDetail(String orderId, String outOrderId, JSUnicomWoPayPkgEnum pkgEnum) {

        JiangSuUnicomProperties.Config properties = jiangSuUnicomProperties.getProperties(pkgEnum);
        Map<String, Object> params = new HashMap<>();
        params.put("channelCode", properties.getChannelId());
        if (StringUtils.isNotBlank(orderId)) {
            params.put("orderId", orderId);
        } else if (StringUtils.isNotBlank(outOrderId)) {
            params.put("outOrderId", outOrderId);
        }
        try {
            Object object = this.sendRemoteRequest(PRODUCT_ORDER_DETAIL, params, properties, new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER_QUERY));
            return JSON.parseObject(JSON.toJSONString(object), JiangSuUnicomOrderDetailResp.class);
        } catch (Exception e) {
            log.info(TAG + "运营商业务订单详情接口失败", e);
            return null;
        }
    }

    /**
     * 是否处于升级状态
     *
     * @return true-不升级 false-升级中
     */
    private boolean upgradeInfo(JSUnicomWoPayPkgEnum pkgEnum) {

        JiangSuUnicomProperties.Config properties = jiangSuUnicomProperties.getProperties(pkgEnum);
        Map<String, Object> params = new HashMap<>();
        params.put("channelCode", properties.getChannelId());
        try {
            Object object = this.sendRemoteRequest(UPGRADE_INFO, params, properties, new BenefitOrderLog.Args(BenefitOrderLog.Biz.UPGRADE_INFO));
            if (object == null) {
                return false;
            }
            JiangSuUniconBaseResp baseResp = (JiangSuUniconBaseResp) object;
            return "8888".equals(baseResp.getRespCode());
        } catch (Exception e) {
            log.info(TAG + "运营商查询最新cbss升级信息失败", e);
            return false;
        }
    }

    private Object sendRemoteRequest(String path, Map<String, Object> params, JiangSuUnicomProperties.Config properties, BenefitOrderLog.Args args) throws Exception {
        String url = properties.getHost() + path;
        String KEY = properties.getSecretKey();
        String encrypt = URLEncoder.encode(AesEncryptUtils.encrypt(JSON.toJSONString(params), KEY), "UTF-8");

        // 日志参数
        args.setReqParams(JSON.toJSONString(params));

        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url)
                .header("APPID", properties.getAppId())
                .contentType("application/json")
                .body(encrypt)
                .timeout(TIME_OUT)
                .log(BenefitPayChannelEnum.JS_UNICOM, args);

        log.info(TAG + "请求前:params:{},{}", params, requestWrapper.getHttpRequest());
        try {
            HttpResponse response = benefitOrderLogService.http(requestWrapper);
            log.info(TAG + "请求后:params:{},{}", params, response);
            if (response.isOk()) {
                String decrypt = AesEncryptUtils.decrypt(URLDecoder.decode(response.body(), "UTF-8"), KEY);
                JiangSuUniconBaseResp baseResp = JSON.parseObject(decrypt, JiangSuUniconBaseResp.class);
                if (path.equals(UPGRADE_INFO)) {
                    return baseResp;
                }
                if (!SUCCESS_CODE.equals(baseResp.getRespCode())) {
                    log.info(TAG + "url:{},返回数据:{}", url, JSON.toJSONString(baseResp));
                    throw BizException.create(BizResult.error(baseResp.getRespCode(), baseResp.getRespDesc()));
                }
                Object data = baseResp.getData();
                if (data != null) {
                    log.info(TAG + "返回数据:{}", JSON.toJSONString(data));
                    return data;
                }
                return "";
            }
            return null;
        } catch (Exception e) {
            log.info(TAG + "接口:{} 请求参数:{}", url, JSON.toJSONString(params), e);
            throw e;
        }
    }

    public static void main(String[] args) throws Exception {

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("APPID", "hzdm");
        String url = "https://weixin2.10010js.com/oldUserApi/outVsed/productRecommend";
        Map<String, Object> params = new HashMap<>();
        params.put("serialNumber", "13182215944");
        params.put("channelCode", "JS-HZDM");
        params.put("queryType", "1");

        String KEY = "txFZ7FANsCJl87Kt";
        String encrypt = URLEncoder.encode(AesEncryptUtils.encrypt(JSON.toJSONString(params), KEY), "UTF-8");

        HttpRequest request = HttpRequest.post("https://weixin2.10010js.com/oldUserApi/outVsed/productRecommend");
        request.body(encrypt);
        request.header("APPID", "hzdm");
        request.contentType("application/json");
        HttpResponse execute = request.execute();


        //HttpEntity requestBody = new HttpEntity(encrypt, headers);
        //
        //RestTemplate restTemplate = new RestTemplate();
        //String result = restTemplate.postForObject(url, requestBody, String.class);
        System.out.println(execute.getStatus());
        System.out.println("解密前:" + execute.body());
        String decrypt = AesEncryptUtils.decrypt(URLDecoder.decode(execute.body(), "UTF-8"), KEY);
        System.out.println("解密后：" + decrypt);

    }


}
