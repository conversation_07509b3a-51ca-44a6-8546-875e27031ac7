package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.quanyi.biz.manager.AdManager;
import com.yuelan.hermes.quanyi.biz.service.AdChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.biz.service.BenefitPlatformService;
import com.yuelan.hermes.quanyi.common.enums.*;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitUnicomPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.AdChannelDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.NxCmccProperties;
import com.yuelan.hermes.quanyi.common.util.NXSignUtil;
import com.yuelan.hermes.quanyi.common.util.ningxia.OrderTool;
import com.yuelan.hermes.quanyi.common.util.ningxia.RSATool;
import com.yuelan.hermes.quanyi.common.util.ningxia.SM4Encryption;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.controller.request.nxcmcc.NxCmccReq;
import com.yuelan.hermes.quanyi.controller.response.nxcmcc.NxCmccRes;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @version V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NxCmcc2Manager {

    public static final String TAG = "[宁夏移动]";
    private static final String RETURN_MSG = "success";
    private static final String TRUE = "true";

    // 预校验接口
    private static final String CHECK_SC_AUTH = "/nxcmcc_ttp/scapi/checkScAuth";
    // 短信验证码下发
    private static final String GET_SMS_CODE = "/nxcmcc_ttp/scapi/sendVerifyCode";
    // 业务办理
    private static final String ORDER = "/nxcmcc_ttp/scapi/saveScOrder";
    private static final int TIME_OUT = 10000;
    private static final String RSA_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC+LdVhfuF8vng5skphxLf0YGItnBkjq9DmGy4ejLG0oHoOT8HNmRLB4y9Wxh4X9euH+viJMK+w3L3lv8hSE6G636PFACaiWpfQ5c+jOR5ib84hfEznLIpiCIdclUZgLeKm5TQuYGoJzZYy+bho38mOSj+/g1LTgElo0C1QWW1JdwIDAQAB";
    private static final String PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoqEjypIPonuu4Wy5TU+ZeCH0l4Hgwvc8wQFVoNAE5TOZuvznyyJkPmpC881pWLhPHF5Be47g11KqBi+c4+2mrEqzrUxOjoLdizjNuovJEhdp9/cUU2+d8SMtb9ma5GXSwTFde85hJQ3a2AzL6MCq6feHFnz1YYWH4hc0D6dwcUotH2qp9IE+xbwd5yc2y3OA6Kf2/xQ1G2fnUyi7I26XDiJhliOLoTvq2OCwL+EAsvNOJ25cBzL3IDPqtaFKsnPd4nUiBpK8FlPJfhS6yeuIheGn6m6yTg/+a+ada3GPjlQneN96wHvjU6LVuwXwablm5gwKKxjkC6nTqe8Y+AXfQwIDAQAB";
    private static final String PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCxf0nDo3hCleDFvsmWEEP/NFeN+aBUlV2z4VAlnrjTSDsXZHVgLlT1UmZt01fNYHLegxlufwj/gZ3v9kq7alrmZHOMRYBStIC6TOVcdfpGvo8ruJTiDzTSJdoX3WCJVXbtOjv/KYltWFVVE1SfYfngQhQ9GjK05tzlZVON5TdljF22s0wgEdn+cYhnrX5jXkhm74EgjKQo7OCHTvVrLsTAXFsNfTvMsUQkQWetF7neeL5WPxjXpKlK72HscFx6fXzaYPjN3udYGvR/GPiTCP5Oq3fFPvo4mqAE439vbu7Ty/woZ8tehPmLg66W17dk5yLXtBvCGOqwdQQBQoXrcrhNAgMBAAECggEANstEMsR6oheRqYoBwjUoQJbgdoA6yhHIOPkT3GE4zmSsdUa6Y2Q6LvdujnXhU5H0NBPFnJJT7UiOwJ7IKD4nbJtS/lkQSbfWS2zkm1wCVKeMHCOhtn2aQUuVHavmGs9K5OYG0nj4p0xPH3HVvFQrJttPUyOMxsLEK37RweqiZNLhpNmU8FFJHDig3rrB079xNH+gSXTtaF+LJuAgSHw+I984V5kd+dmI2SWFjANmNvu9mHGc/8d4V0mMRtyiMZ6/PWKBFjBxE6PrYSaTiuI0SYBU9vD6c/ljoglFEdjS6tV+Rdj3FAEwVaHUQXlXraq7u24Oh6tUp91diTDp+5VlMwKBgQDnlvzsM7E0nfglvImYBPIrZaVn1AYCtfdDHJNsqNYJY28qzeo4feZ4vjNC8PIyRYYZj+beYv8na+StC4TPkPcFjGXHZQD3DKm8C1R41TveA82UeUwrDQLbHlvZS+cBNmGpRDgSUNc/Ro3cAQrUDU+EHAK6njIoF82YwPje/RRYzwKBgQDENLbjzL2oH2Ggk+aBE8T18CCHQdF7/0Mv5TCgxi/G8iOGwA1tuXR7dSis7ksR4HQZ2WrN8Lb+M56UzzGTC8Q3kSy7YZ9YNfgh28Le3WgcXgQwyJFKMEU0FouByKEa+dAE1wyNpVcsUwQpFLUuArNokvBC/yUHyIqAJDKOouksIwKBgQCwGJ4/WNsRAGFVCHSzlGc0WnkYZx8e5pCahlsPT55YyzQXnzXPOOSLwUynRSFU2oqqW0KYrTwJFMBwWIbncfpzTJuDr6i5MfbKlu7nZnS8T7X9hPJG9gDK7t22cEG6KUi/dbERNElHwvVRabV7IpfpA4eLsYi/9cGOuoVCwbu7/wKBgBnMOJPhMrqtTZ/YcoFcQRR5Oz2tiMa9/G6KuwcYp+WHpuYr0Fgx/z/QWsXKGzLBiWJwtNtZICZyURKN353O+UC5JOGKZ3IovnRtOcUl5W99aR2k8X2ItUCxCizJXW13lUNdpzfkmn6od66TG3NuRXqlf1O/ynWoh5VrrP4WK4RzAoGAcyf8BCg4J/Km764/vP0kmbgwu6t/7tkB/ODFk40ywUoBEX0TDoNy2WxF2d+vazZIbCjI3ytxbTF/Y6/AGdNXK9yk6TF5Cei87hY2Z9N+V2s4tnhjT9LMdbvfDLKm9iM0e1gg2hc7JSl4r7+4KF26q2VS3toOyxhwbzcTC5NnMzY=";

    @Autowired
    private NxCmccProperties nxCmccProperties;
    @Resource
    @Lazy
    private BenefitPlatformService benefitPlatformService;

    @Resource
    private AdChannelDOService adChannelDOService;
    @Resource
    private AdManager adManager;
    @Resource
    private BenefitOrderLogService benefitOrderLogService;
    @Resource
    private BenefitOrderDOService benefitOrderDOService;


    /**
     * 订阅下单
     *
     * @param req       请求参数
     * @param productDO 商品信息
     * @param orderDO   下单信息
     */
    public BenefitPayResultBO createOrder(UserBenefitOrderReq req, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        String mobile = req.getMobile();
        String smsCode = req.getSmsCode();

        BenefitUnicomPayResultBO payResultBO = new BenefitUnicomPayResultBO();
        payResultBO.setSuccess(false);
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(smsCode)) {
            log.info(TAG + "手机号或者验证码为空");
            payResultBO.setMessage("暂时无法订阅");
        }
        String prodId = this.getProdId(productDO);
        if (StringUtils.isEmpty(prodId)) {
            log.info(TAG + "业务产品编码为空");
            payResultBO.setMessage("暂时无法订阅");
        }
        // 生成宁夏订单号
        String outOrderNo = OrderTool.generateOrderNumber();
        orderDO.setOutOrderNo(outOrderNo);
        NxCmccRes res = this.productOrder(mobile, smsCode, outOrderNo, prodId);
        if (TRUE.equals(res.getSuccess())) {
            payResultBO.setSuccess(true);
            // 更新订单状态
            orderDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
            // 用户支付成功事件
        } else {
            payResultBO.setMessage(res.getMessage());
            orderDO.setPayStatus(PayStatusEnum.FAIL.getCode());
        }
        return payResultBO;
    }

    public NxCmccRes orderNotify(NxCmccReq req) {
        NxCmccRes res = new NxCmccRes();
        BenefitOrderDO orderDO = benefitOrderDOService.getByOutOrderNo(req.getOrderId());
        if (Objects.isNull(orderDO)) {
            res.setCode("1");
            res.setMessage("该订单查询不到");
            log.error(TAG + "订单通知，该订单查询不到。req:{}", JSON.toJSONString(req));
            return res;
        }
        String encryptMobile = getEncryptMobile(orderDO.getPhone());
        if (!Objects.equals(encryptMobile, req.getMobile())) {
            res.setCode("1");
            res.setMessage("手机号码不匹配");
            log.error(TAG + "订单通知，手机号码不匹配。orderDO:{},req:{}", JSON.toJSONString(orderDO), JSON.toJSONString(req));
            return res;
        }
        if ("1".equals(req.getStatus())) {
            orderDO.setPayStatus(PayStatusEnum.SUCCESS.getCode());
            // 用户支付成功事件
            sendPaySuccessEvent(orderDO);
        } else {
            orderDO.setPayStatus(PayStatusEnum.FAIL.getCode());
        }
        orderDO.setPayNotifyContent(JSON.toJSONString(req));
        orderDO.setPayNotifyTime(LocalDateTime.now());
        benefitOrderDOService.updateById(orderDO);
        benefitPlatformService.notifyMediaOrderResultAsync(orderDO, req.getRetMsg());
        res.setCode("0");
        res.setMessage(RETURN_MSG);
        return res;
    }

    /**
     * 业务受理
     *
     * @param mobile     手机号
     * @param randCode   验证码
     * @param outOrderNo 宁夏订单号
     */
    private NxCmccRes productOrder(String mobile, String randCode, String outOrderNo, String prodId) {
        String encryptMobile = this.getEncryptMobile(mobile);
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mobile", encryptMobile);
        params.put("productId", prodId);
        params.put("orderId", outOrderNo);
        params.put("smsCode", randCode);
        NxCmccRes res = this.sendRemoteRequest(ORDER, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER, mobile, outOrderNo));
        if (res == null) {
            throw BizException.create(BizErrorCodeEnum.ORDER_UNIFIED_ERROR);
        }
        return res;
    }


    /**
     * 运营商下发短信验证码
     */
    private boolean sendSmsCode(String mobile, String encryptMobile, String prodId) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mobile", encryptMobile);
        params.put("productId", prodId);
        NxCmccRes res = this.sendRemoteRequest(GET_SMS_CODE, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_SEND, mobile));
        if (res == null) {
            return false;
        }
        if (TRUE.equals(res.getSuccess())) {
            return true;
        } else {
            log.info(TAG + "短信验证码下发失败.手机号:{} 失败原因:{}", mobile, JSON.toJSONString(res));
            throw BizException.create(BizErrorCodeEnum.SMS_SEND_ERROR, res.getMessage());
        }
    }

    /**
     * 预校验接口
     *
     * @param mobile
     * @param encryptMobile
     * @param prodId
     */
    private void checkScAuth(String mobile, String encryptMobile, String prodId) {
        if (StringUtils.isEmpty(encryptMobile)) {
            return;
        }
        TreeMap<String, Object> params = new TreeMap<>();
        params.put("mobile", encryptMobile);
        params.put("productId", prodId);
        NxCmccRes res = this.sendRemoteRequest(CHECK_SC_AUTH, params, null);
        if (res == null) {
            return;
        }
        if (!TRUE.equals(res.getSuccess())) {
            log.info(TAG + "预校验失败.手机号:{} 失败原因:{}", mobile, JSON.toJSONString(res));
            throw BizException.create(BizErrorCodeEnum.SMS_SEND_ERROR, res.getMessage());
        }
    }

    /**
     * 获取业务id
     *
     * @param productDO 产品对象
     * @return 业务id
     */
    private String getProdId(BenefitProductDO productDO) {
        NxWoPayPkgEnum payPkgEnum = NxWoPayPkgEnum.of(productDO.getPayChannelPkgId());
        if (payPkgEnum == null) {
            return null;
        }
        return payPkgEnum.getProdId();
    }

    /**
     * 发送验证码短信
     *
     * @param reqParams 请求参数
     * @param productDO 商品信息
     */
    public boolean orderSendSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        String mobile = reqParams.getMobile();
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        String prodId = this.getProdId(productDO);
        NxWoPayPkgEnum payPkgEnum = NxWoPayPkgEnum.of(productDO.getPayChannelPkgId());
        if (payPkgEnum != null && payPkgEnum.isCrack()) {
            if (!saveData(payPkgEnum, mobile)) {
                return false;
            }
        }
        if (StringUtils.isEmpty(prodId)) {
            return false;
        }
        String encryptMobile = this.getEncryptMobile(mobile);
        this.checkScAuth(mobile, encryptMobile, prodId);
        return this.sendSmsCode(mobile, encryptMobile, prodId);
    }

    /**
     * 模拟js上报埋点数据-发送验证码前
     *
     * @param pkgEnum 产品枚举
     * @param mobile  手机号
     */
    public boolean saveData(NxWoPayPkgEnum pkgEnum, String mobile) {
        JSONObject data = new JSONObject() {{
            put("appId", nxCmccProperties.getAppId());
            put("applicationName", pkgEnum.getPkgId());
            put("title", pkgEnum.getProdId());
            // 下单页面
            put("url", "https://h5.hzyuelan.com/benefit/landingPage/iyiTss3S?channelId=22");
            // 为什么是空不知道
            put("phoneNo", "");
            // 真正手机号码
            put("params", mobile);
        }};
        String dataStr = data.toJSONString();
        String encryptedData = encryption(dataStr, 70, "_CMCC_");
        HttpRequest get = HttpRequest.get("https://www.nx.10086.cn/nxcmcc_ttp/api/pv/save?data=" + encryptedData);
        // 不确认如下header是否被校验 模拟了再说
        get.header("Accept", "*/*");
        get.header("Accept-Language", "zh-CN,zh;q=0.9");
        get.header("Origin", "https://h5.hzyuelan.com");
        get.header("Referer", "https://h5.hzyuelan.com/");
        get.header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36");
        log.info("模拟埋点数据上报:{}", get);

        HttpResponse response = get.execute();
        log.info("埋点数据上报结果:{}", response);
        //{
        //   "success": true,
        //   "code": 0,
        //   "message": "",
        //   "now": 1744016983
        // }
        JSONObject respJson = JSON.parseObject(response.body());
        Boolean success = respJson.getBoolean("success");
        String code = respJson.getString("code");

        if (!Boolean.TRUE.equals(success) || !"0".equals(code)) {
            log.error(TAG + "{}埋点数据上传失败:{}", mobile, respJson);
            return false;
        }
        return true;
    }

    /**
     * 发送远程接口
     *
     * @param path   接口路径
     * @param params 参数
     * @return 发送结果
     */
    private NxCmccRes sendRemoteRequest(String path, TreeMap<String, Object> params, BenefitOrderLog.Args args) {
        String url = nxCmccProperties.getHost() + path;
        String stamp = String.valueOf(Instant.now().getEpochSecond());

        Map<String, String> headers = new HashMap<>();
        headers.put("version", "1.0.0");
        headers.put("appId", nxCmccProperties.getAppId());
        headers.put("seqId", UUID.randomUUID().toString().replaceAll("-", ""));
        headers.put("timestamp", stamp);
        headers.put("appKey", nxCmccProperties.getAppKey());

        String sign = getSign(params, headers);
        headers.put("signature", sign);
        // "appkey"要参与拼接，但系统参数里不传递
        headers.remove("appKey");

        String jsonBody = JSON.toJSONString(params);
//        String sign = NXSignUtil.sign(params, nxCmccProperties.getAppKey());
        String encryptBody = SM4Encryption.encrypt(jsonBody);
        HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url)
                .headers(headers)
                .contentType("application/json")
                .body(encryptBody)
                .timeout(TIME_OUT)
                .log(BenefitPayChannelEnum.NX_CMCC, args);

        try {
            log.info(TAG + "平台请求运营商之前:{}", requestWrapper.getHttpRequest());
            HttpResponse response = benefitOrderLogService.http(requestWrapper);
            log.info(TAG + "平台请求运营商之后:{}", response);
            if (response.isOk()) {
                String jsonStr = SM4Encryption.decrypt(response.body());
                return JSON.parseObject(jsonStr, NxCmccRes.class);
            }
            return null;
        } catch (Exception e) {
            log.info(TAG + "接口:{} 请求参数:{}", url, JSON.toJSONString(params), e);
            return null;
        }
    }

    /**
     * 支付成功广告追踪转化事件上报
     */
    private void sendPaySuccessEvent(BenefitOrderDO orderDO) {
        if (orderDO == null || orderDO.getAdChannelId() == null || StringUtils.isEmpty(orderDO.getAdExt())) {
            return;
        }
        JSONObject adExt = JSONObject.parseObject(orderDO.getAdExt());
        AdChannelDO adChannel = adChannelDOService.getByModuleAndAdChannelId(SysModuleEnum.BENEFIT, orderDO.getAdChannelId());
        AdChannelCodeEnum adChannelCodeEnum = AdChannelCodeEnum.of(adChannel.getAdChannelCode());
        BenefitTrackingEventEnum paySuccessTrackingEventEnum = BenefitTrackingEventEnum.PAY_SUCCESS;
        adManager.adCallBackHandlerCall(adChannelCodeEnum, adExt, paySuccessTrackingEventEnum, Boolean.FALSE);
    }

    private String getEncryptMobile(String mobile) {
        try {
            return RSATool.encryptWithPublicKey(mobile, PUBLIC_KEY);
        } catch (Exception e) {
            log.info(TAG + "手机号加密失败:{}", mobile);
            throw BizException.create(BaseErrorCodeEnum.UNKNOWN_ERROR, "加密失败");
        }
    }

    private String getSign(Map<String, Object> params, Map<String, String> headers) {
        try {
            Map<String, String> mapforSign = NXSignUtil.paraObjectFilter(params);
            String paramStr = NXSignUtil.createLinkString(mapforSign);
            String headStr = NXSignUtil.createLinkString(headers);
            String str = paramStr + "&" + headStr;
            log.info("签名拼接串：{}", str);
            return RSATool.sign(str, PRIVATE_KEY);
        } catch (Exception e) {
            log.info(TAG + "参数加密失败:{}", JSON.toJSONString(params));
            throw BizException.create(BaseErrorCodeEnum.UNKNOWN_ERROR, "加密失败");
        }
    }

    public String encryption(String data, int length, String connect) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < data.length(); i += length) {
            String chunk = myRsa(data.substring(i, Math.min(i + length, data.length())));
            if (result.length() != 0) {
                result.append(connect);
            }
            result.append(chunk);
        }
        return URLEncodeUtil.encodeAll(result.toString());
    }


    public String myRsa(String content) {
        RSA encryptor = new RSA(null, RSA_KEY);
        return Base64.getEncoder().encodeToString(encryptor.encrypt(content.getBytes(StandardCharsets.UTF_8), KeyType.PublicKey));
    }

}
