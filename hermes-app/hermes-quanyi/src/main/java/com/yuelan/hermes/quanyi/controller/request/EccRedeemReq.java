package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/5/14 下午7:50
 */
@Data
public class EccRedeemReq {

    @NotEmpty(message = "缺少权益包code")
    @Schema(description = "权益包code")
    private String prodCode;

    @NotNull(message = "确实兑换的商品id")
    @Schema(description = "商品id")
    private Long goodsId;

}
