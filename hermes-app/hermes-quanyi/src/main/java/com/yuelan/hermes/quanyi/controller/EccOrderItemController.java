package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.EccOrderItemDOService;
import com.yuelan.hermes.quanyi.controller.request.EccOrderItemListReq;
import com.yuelan.hermes.quanyi.controller.response.EccOrderItemResp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> 2024/5/3 下午11:10
 */
@Validated
@RestController
@Tag(name = "电商卡/后台api/权益包子订单")
@RequiredArgsConstructor
@RequestMapping("/a/ecc/orderItem")
public class EccOrderItemController {

    private final EccOrderItemDOService eccOrderItemDOService;

    @Operation(summary = "查询子订单明细")
    @PostMapping("/listByOrderId")
    public BizResult<List<EccOrderItemResp>> listByOrderId(@RequestBody @Validated EccOrderItemListReq req) {
        return BizResult.create(eccOrderItemDOService.listByOrderId(req));
    }


}
