package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.UpOffStatusEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsDO;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/2 19:41
 */
@Data
public class EccGoodsUpdateStatusReq {

    @NotNull(message = "商品id不能为空")
    @Schema(description = "商品id")
    private Long goodsId;


    @NotNull(message = "上下架状态不能为空")
    @Schema(description = "上下架状态：0-下架；1-上架")
    private Integer goodsStatus;

    public Wrapper<EccGoodsDO> buildUpdateWrapper() {
        return Wrappers.lambdaUpdate(EccGoodsDO.class)
                .eq(EccGoodsDO::getGoodsId, goodsId)
                .set(EccGoodsDO::getGoodsStatus, goodsStatus);

    }

    public void checkReq() {
        UpOffStatusEnum statusEnum = LocalEnumUtils.findByCode(UpOffStatusEnum.class, goodsStatus);
        if (Objects.isNull(statusEnum)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "上下架状态值不合法");
        }
    }
}
