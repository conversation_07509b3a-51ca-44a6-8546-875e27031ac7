package com.yuelan.hermes.quanyi.controller.response;

import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2025/5/22
 * @since 2025/5/22
 */
@Data
@AutoMapper(target = EccProductPageResp.class)
public class AppEccProductPageResp {


    /**
     * 首屏
     */
    @Schema(description = "首屏")
    private String indexImg;

    /**
     * 下单按键图
     */
    @Schema(description = "下单按键图")
    private String orderButtonImg;

    /**
     * 详情图
     */
    @Schema(description = "详情图")
    private String pricingDetailImg;

    /**
     * 产品介绍图
     */
    @Schema(description = "产品介绍图")
    private String productDetailImg;


    @Schema(description = "授权牌图片")
    private String licenseImg;

    /**
     * 附加扩张图
     */
    @Schema(description = "附加扩张图")
    private List<String> extraImg;

    /**
     * 可折叠图
     */
    @Schema(description = "可折叠图")
    private List<String> collapseImg;

    @Schema(description = "背景颜色")
    private String bgColor;

}
