package com.yuelan.hermes.quanyi.controller.request;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/6/18 下午5:26
 */
@Data
public class PageEventUploadReq {
    @NotNull(message = "广告渠道代码不能为空")
    @Schema(description = "广告渠道代码")
    private String adChannelCode;


    @NotNull(message = "页面事件code不能为空")
    @Schema(description = "页面事件code")
    private Integer eventCode;

    @Schema(description = "扩展json")
    private JSONObject adExt;
}
