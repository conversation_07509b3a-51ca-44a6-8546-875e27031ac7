package com.yuelan.hermes.quanyi.controller.benefit;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.service.BenefitPlatformService;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.controller.request.benefitplatform.BenefitPlatformBaseReq;
import com.yuelan.hermes.quanyi.controller.request.benefitplatform.BenefitPlatformSmsCodeReq;
import com.yuelan.hermes.quanyi.controller.request.benefitplatform.BenefitPlatformUnicomReadReq;
import com.yuelan.hermes.quanyi.controller.response.benefitplatform.BenefitPlatformOrderRes;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/7/19
 * @description:
 */

@Tag(name = "权益对接平台")
@Slf4j
@Validated
@RestController
@RequestMapping("/benefit/platform")
@RequiredArgsConstructor
public class APPBenefitPlatformController {

    @Resource
    private BenefitPlatformService benefitPlatformService;

    @SaIgnore
    @Operation(summary = "请求运营商发送短信验证码", description = "下单前请求")
    @PostMapping("/order/sendSmsCode")
    public BizResult<BenefitPayResultBO> sendSmsCode(@Validated @RequestBody BenefitPlatformBaseReq req) {
        BenefitPayResultBO resultBO = benefitPlatformService.sendSmsCode(req);
        return BizResult.create(resultBO);
    }

    @SaIgnore
    @Operation(summary = "用户下单")
    @PostMapping("/order")
    public BizResult<BenefitPlatformOrderRes> order(@Validated @RequestBody BenefitPlatformSmsCodeReq req) {
        BenefitPlatformOrderRes res = benefitPlatformService.createOrder(req);
        return BizResult.create(res);
    }

    @SaIgnore
    @Operation(summary = "联通沃阅读微信支付订购")
    @PostMapping("/unicom/read/wxOrder")
    public BizResult<BenefitPlatformOrderRes> unicomReadOrder(@Validated @RequestBody BenefitPlatformUnicomReadReq req) {
        BenefitPlatformOrderRes res = benefitPlatformService.unicomReadOrder(req);
        return BizResult.create(res);
    }
}
