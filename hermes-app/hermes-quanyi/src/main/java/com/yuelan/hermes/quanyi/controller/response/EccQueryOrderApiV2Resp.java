package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.yuelan.hermes.commons.enums.ZopOrderStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.NcOrderStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.SimCardStatusEnum;
import com.yuelan.hermes.quanyi.common.enums.ZopStateEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import com.yuelan.hermes.quanyi.common.util.ZopOrderFailReasonUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> 2024/7/18 下午3:18
 * 新版通用号卡回调
 */
@Data
public class EccQueryOrderApiV2Resp {

    @Schema(description = "我方订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "接入方下单时候订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelOrderNo;

    @Schema(description = "身份证号码，脱敏后的身份证号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String idCard;

    @Schema(description = "身份证名字", requiredMode = Schema.RequiredMode.REQUIRED)
    private String idCardName;

    @Schema(description = "收货手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contactPhone;

    @Schema(description = "手机号码（用户选择/随机选择）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String phone;

    @Schema(title = "运营商订单号", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String spOrderNo;

    @Schema(title = "订单状态：1-审核中 2-领卡失败 3-领卡成功(运营商受理成功) 4-退单", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer orderStatus;

    @Schema(title = "失败原因", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String failReason;

    @Schema(title = "sim卡状态：0-待激活 1-激活 2-停机 3-销户", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer cardStatus;

    @Schema(title = "物流状态：0-待发货 1-发货 2-签收 3-拒收", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer expressStatus;

    @Schema(title = "首冲充值金额", description = "单位分", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private Integer firstRecharge;

    @Schema(title = "订单创建时间", description = "格式：2024-01-01 08:00:00", requiredMode = Schema.RequiredMode.REQUIRED)
    private String createTime;

    @Schema(title = "订单更新时间", description = "格式：2024-01-01 08:00:00", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String updateTime;


    public static EccQueryOrderApiV2Resp buildResp(EccNcOrderDO orderDO) {
        if (Objects.isNull(orderDO)) {
            return null;
        }
        EccQueryOrderApiV2Resp resp = new EccQueryOrderApiV2Resp();
        resp.setOrderNo(orderDO.getOrderNo());
        resp.setChannelOrderNo(orderDO.getChannelOrderNo());
        resp.setIdCard(orderDO.getIdCard());
        // 脱敏
        if (Objects.nonNull(orderDO.getIdCard())) {
            resp.setIdCard(DesensitizedUtil.idCardNum(orderDO.getIdCard(), 5, 2));
        }
        resp.setIdCardName(orderDO.getIdCardName());
        resp.setContactPhone(orderDO.getContactPhone());
        resp.setPhone(orderDO.getPhone());
        resp.setFailReason(orderDO.getFailReason());
        resp.setSpOrderNo(orderDO.getSpOrderNo());
        resp.setFirstRecharge(orderDO.getFirstChargeAmount());
        resp.setOrderStatus(orderDO.getOrderStatus());
        resp.setCardStatus(orderDO.getCardStatus());
        resp.setExpressStatus(orderDO.getExpressStatus());
        resp.setCreateTime(LocalDateTimeUtil.format(orderDO.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
        resp.setUpdateTime(LocalDateTimeUtil.format(orderDO.getUpdateTime(), DatePattern.NORM_DATETIME_PATTERN));
        return resp;
    }

    public static EccQueryOrderApiV2Resp buildResp(EccZopOrderDO orderDO) {
        if (Objects.isNull(orderDO)) {
            return null;
        }
        EccQueryOrderApiV2Resp resp = new EccQueryOrderApiV2Resp();
        resp.setOrderNo(orderDO.getOrderNo());
        resp.setChannelOrderNo(orderDO.getChannelOrderNo());
        resp.setIdCard(orderDO.getIdCard());
        // 脱敏
        if (Objects.nonNull(orderDO.getIdCard())) {
            resp.setIdCard(DesensitizedUtil.idCardNum(orderDO.getIdCard(), 5, 2));
        }
        resp.setIdCardName(orderDO.getIdCardName());
        resp.setContactPhone(orderDO.getContactPhone());
        resp.setPhone(orderDO.getPhone());
        resp.setSpOrderNo(orderDO.getSyncOrderNo());

        ZopOrderStatusEnum orderSyncStatus = ZopOrderStatusEnum.of(orderDO.getOrderSyncStatus());

        if (ZopOrderStatusEnum.SUCCESS.equals(orderSyncStatus)) {
            resp.setOrderStatus(NcOrderStatusEnum.GET_CARD_SUCCESS.getCode());
            resp.setCardStatus(SimCardStatusEnum.WAIT_ACTIVE.getCode());
        } else {
            resp.setOrderStatus(NcOrderStatusEnum.GET_CARD_FAIL.getCode());
            resp.setFailReason(ZopOrderFailReasonUtil.getFailReason(orderDO));
        }

        resp.setFirstRecharge(orderDO.getFirstRecharge());
        ZopStateEnum zopState = ZopStateEnum.getByCode(orderDO.getZopOrderState());
        if (Objects.nonNull(zopState)) {
            if (ZopStateEnum.DELIVER.equals(zopState)) {
                resp.setCardStatus(SimCardStatusEnum.WAIT_ACTIVE.getCode());
            }
            if (ZopStateEnum.ACTIVE.equals(zopState)) {
                resp.setCardStatus(SimCardStatusEnum.ACTIVE.getCode());
            }
            if (ZopStateEnum.CANCEL_ACTIVE.equals(zopState)) {
                resp.setCardStatus(SimCardStatusEnum.CANCEL.getCode());
            }
        }
        resp.setExpressStatus(null);
        resp.setCreateTime(DateUtil.formatDateTime(orderDO.getCreateTime()));
        if (orderDO.getUpdateTime() != null) {
            resp.setUpdateTime(DateUtil.formatDateTime(orderDO.getUpdateTime()));
        }
        return resp;
    }


}
