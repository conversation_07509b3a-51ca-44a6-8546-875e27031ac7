package com.yuelan.hermes.quanyi.controller.request;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * 抖音小程序广告回调请求
 * @link <a href="https://event-manager.oceanengine.com/docs/8650/api_docs">文档地址</a>
 */

@Data
public class DouYinAppletAdCallBackReq {

    @JSONField(name = "event_type")
    private String eventType;

    private Context context;

    private long timestamp;

    @Data
    @Accessors(chain = true)
    public static class Context {
        private Ad ad;
    }

    @Data
    @Accessors(chain = true)
    public static class Ad {
        private String callback;
    }


}
