package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccChannelDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR> 2024/5/2 上午10:44
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class EccChannelListReq extends PageRequest {

    /**
     * 主键
     */
    @Schema(description = "渠道id")
    private Long channelId;

    /**
     * 渠道名字
     */
    @Schema(description = "渠道名字")
    private String channelName;


    public Wrapper<EccChannelDO> buildQueryWrapper() {
        return Wrappers.lambdaQuery(EccChannelDO.class)
                .eq(channelId != null, EccChannelDO::getChannelId, channelId)
                .like(channelName != null, EccChannelDO::getChannelName, channelName)
                .orderByDesc(EccChannelDO::getChannelId);
    }
}
