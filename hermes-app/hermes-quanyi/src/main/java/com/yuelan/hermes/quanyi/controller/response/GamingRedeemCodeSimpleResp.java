package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.GamingRedeemCodeDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR> 2024/8/10 下午1:45
 */
@Data
public class GamingRedeemCodeSimpleResp {

    @Schema(description = "兑换码")
    private String redeemCodeKey;

    @Schema(description = "兑换码对应密码")
    private String redeemCodePwd;

    @Schema(description = "过期日期")
    private LocalDate expireDate;

    public static GamingRedeemCodeSimpleResp buildResp(GamingRedeemCodeDO gamingRedeemCodeDO) {
        if (gamingRedeemCodeDO == null) {
            return null;
        }
        GamingRedeemCodeSimpleResp resp = new GamingRedeemCodeSimpleResp();
        resp.setRedeemCodePwd(gamingRedeemCodeDO.getRedeemCodePwd());
        resp.setRedeemCodeKey(gamingRedeemCodeDO.getRedeemCodeKey());
        resp.setExpireDate(gamingRedeemCodeDO.getExpireDate());
        return resp;
    }
}
