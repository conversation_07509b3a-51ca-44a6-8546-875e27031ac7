package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsRecordDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2024/5/10 下午5:25
 */
@Data
public class RedeemableDetailsResp {

    @Schema(description = "权益商品领取详情")
    private List<UserRedeemDetail> list;

    public static UserRedeemDetail buildDetail(EccGoodsDO goodsDO, EccGoodsRecordDO eccGoodsRecordDO) {
        UserRedeemDetail userRedeemDetail = new UserRedeemDetail();
        userRedeemDetail.setGoodsId(goodsDO.getGoodsId());
        userRedeemDetail.setGoodsName(goodsDO.getGoodsName());
        userRedeemDetail.setRedeemed(eccGoodsRecordDO != null);
        userRedeemDetail.setRedeemType(goodsDO.getRedeemType());
        userRedeemDetail.setRedeemCode(eccGoodsRecordDO == null ? null : eccGoodsRecordDO.getRedeemCodeKey());
        userRedeemDetail.setRedeemPwd(eccGoodsRecordDO == null ? null : eccGoodsRecordDO.getRedeemCodePwd());
        return userRedeemDetail;

    }

    @Data
    public static class UserRedeemDetail {
        @Schema(description = "权益商品id")
        private Long goodsId;

        @Schema(description = "权益商品名称")
        private String goodsName;

        @Schema(title = "兑换方式：1-直充无库存，2-cdKey(兑换码)")
        private Integer redeemType;

        @Schema(description = "是否领取过了")
        private Boolean redeemed;

        @Schema(description = "兑换码")
        private String redeemCode;

        @Schema(description = "兑换密码")
        private String redeemPwd;

    }
}
