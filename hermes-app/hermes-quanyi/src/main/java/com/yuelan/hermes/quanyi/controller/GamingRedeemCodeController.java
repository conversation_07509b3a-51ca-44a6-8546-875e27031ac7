package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.GamingRedeemCodeDOService;
import com.yuelan.hermes.quanyi.controller.request.GamingRedeemCodePageReq;
import com.yuelan.hermes.quanyi.controller.response.GamingRedeemCodeResp;
import com.yuelan.hermes.quanyi.controller.response.ImportResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> 2024/7/11 下午7:33
 */
@Validated
@RestController
@Tag(name = "电竞卡/后台api/兑换码")
@RequiredArgsConstructor
@RequestMapping("/a/gaming/redeemCode")
public class GamingRedeemCodeController {

    private final GamingRedeemCodeDOService gamingRedeemCodeDOService;

    @Operation(summary = "分页列表")
    @PostMapping("/page")
    public BizResult<PageData<GamingRedeemCodeResp>> page(@RequestBody @Validated GamingRedeemCodePageReq req) {
        return BizResult.create(gamingRedeemCodeDOService.goodsRedeemCodePage(req));
    }

    @Operation(summary = "下载兑换码导入模板")
    @GetMapping(value = "/download/template", produces = "application/octet-stream")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        gamingRedeemCodeDOService.downloadTemplate(response);
    }

    @Operation(summary = "导入兑换码", description = "注意接口超时时间要设置久一点")
    @PostMapping(value = "/import/{goodsId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BizResult<ImportResp> importRedeemCode(@PathVariable Long goodsId, @RequestParam("file") MultipartFile file) throws IOException {
        return BizResult.create(gamingRedeemCodeDOService.importRedeemCode(goodsId, file));
    }


}
