package com.yuelan.hermes.quanyi.controller.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yuelan.hermes.commons.excel.GoodsVirtualTypeConverter;
import com.yuelan.hermes.commons.excel.OrderStatusEnumConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@Data
@ContentRowHeight(15)
@HeadRowHeight(20)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class OrderOfflineItemRsp {
    /**
     * 订单明细ID
     */
    @ExcelIgnore
    private Long itemId;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "订单时间")
    @Schema(description = "下单时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderCreateTime;

    @ColumnWidth(28)
    @ExcelProperty(value = "订单编号")
    @Schema(description = "订单编号")
    private String orderNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "用户手机号")
    @Schema(description = "用户手机号")
    private String phone;

    @ColumnWidth(20)
    @ExcelProperty(value = "充值账号")
    @Schema(description = "充值账号")
    private String userAccount;

    @ColumnWidth(15)
    @ExcelProperty(value = "充值类型", converter = GoodsVirtualTypeConverter.class)
    @Schema(description = "充值类型")
    private Integer goodsType;

    @ColumnWidth(20)
    @ExcelProperty(value = "商品名称")
    @Schema(description = "虚拟商品名称")
    private String goodsName;

    @ColumnWidth(20)
    @ExcelProperty(value = "SKU名称")
    @Schema(description = "虚拟商品SKU名称")
    private String skuName;

    @ColumnWidth(15)
    @ExcelProperty(value = "SKU编号")
    @Schema(description = "虚拟商品SKU编号")
    private String skuNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "供应商")
    @Schema(description = "供应商")
    private String supplier;

    @ColumnWidth(28)
    @ExcelProperty(value = "供应商订单号")
    @Schema(description = "供应商订单号")
    private String supplierOrderNo;

    @ColumnWidth(20)
    @ExcelProperty(value = "集采客户")
    @Schema(description = "集采客户名称")
    private String buyer;

    @ColumnWidth(15)
    @ExcelProperty(value = "采购价")
    @Schema(description = "采购价")
    private BigDecimal purchasePrice;

    @ColumnWidth(15)
    @ExcelProperty(value = "销售价")
    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @ColumnWidth(15)
    @ExcelProperty(value = "订单状态", converter = OrderStatusEnumConverter.class)
    @Schema(description = "订单状态")
    private Integer orderStatus;

    @ColumnWidth(20)
    @ExcelProperty(value = "卡号")
    @JsonIgnore
    private String voucherCode;

    @ColumnWidth(20)
    @ExcelProperty(value = "卡密")
    @JsonIgnore
    private String voucherPassword;

    @ColumnWidth(20)
    @ExcelProperty(value = "短链接")
    @JsonIgnore
    private String qrCodeUrl;

    @ColumnWidth(20)
    @ExcelProperty(value = "券SN")
    @JsonIgnore
    private String snCode;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "有效开始时间")
    @JsonIgnore
    private Date startDate;

    @ColumnWidth(20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "有效结束时间")
    @JsonIgnore
    private Date endDate;

    @ColumnWidth(25)
    @ExcelProperty(value = "备注")
    private String remark;

}
