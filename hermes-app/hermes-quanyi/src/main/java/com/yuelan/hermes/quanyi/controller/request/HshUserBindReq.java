package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.RegexMatch;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/7/29 下午2:31
 */
@Data
public class HshUserBindReq {

    @Schema(description = "手机号码")
    @NotEmpty(message = "手机号码不能为空")
    @RegexMatch(regex = "^1[3-9]\\d{9}$", message = "手机号格式错误")
    private String phone;

    @Schema(description = "验证码")
    @NotEmpty(message = "验证码不能为空")
    private String smsCode;

    @Schema(description = "临时token")
    @NotEmpty(message = "临时token不能为空")
    private String tempToken;

}
