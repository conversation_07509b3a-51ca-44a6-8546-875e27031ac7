package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 2024/8/5 下午5:56
 */
@Data
public class HshGamingOrderBenefitResp {

    @Schema(title = "订单id")
    private Long orderId;

    @Schema(title = "订单时间")
    private Date orderTime;

    @Schema(title = "产品名称")
    private String productName;

    @Schema(title = "权益列表，包含直发、兑换码全部权益")
    private List<HshGamingOrderItemResp> orderItems;

    public static HshGamingOrderBenefitResp buildResp(GamingOrderDO gamingOrderDO, List<GamingOrderItemDO> gamingOrderItems) {
        if (gamingOrderDO == null) {
            return null;
        }
        HshGamingOrderBenefitResp resp = new HshGamingOrderBenefitResp();
        resp.setOrderId(gamingOrderDO.getOrderId());
        resp.setProductName(gamingOrderDO.getProductName());
        resp.setOrderTime(gamingOrderDO.getCreateTime());
        resp.setOrderItems(new ArrayList<>());
        for (GamingOrderItemDO gamingOrderItem : gamingOrderItems) {
            resp.getOrderItems().add(HshGamingOrderItemResp.buildResp(gamingOrderItem));
        }
        return resp;
    }

    @Data
    public static class HshGamingOrderItemResp {
        @Schema(title = "子订单id")
        private Long itemId;

        @Schema(title = "商品名字")
        private String goodsName;

        @Schema(title = "权益发放类型：1-直充，2-兑换码")
        private Integer deliveryType;

        @Schema(title = "预下单状态")
        private Integer preOrderStatus;

        @Schema(title = "权益领取状态")
        private Integer obtainStatus;

        @Schema(title = "兑换码")
        private String redeemCode;

        @Schema(title = "兑换码过期时间")
        private LocalDate redeemCodeExpireDate;

        public static HshGamingOrderItemResp buildResp(GamingOrderItemDO gamingOrderItemDO) {
            HshGamingOrderItemResp resp = new HshGamingOrderItemResp();
            resp.setItemId(gamingOrderItemDO.getId());
            resp.setGoodsName(gamingOrderItemDO.getGoodsName());
            resp.setDeliveryType(gamingOrderItemDO.getDeliveryType());
            resp.setPreOrderStatus(gamingOrderItemDO.getPreorderStatus());
            resp.setObtainStatus(gamingOrderItemDO.getObtainStatus());
            resp.setRedeemCode(gamingOrderItemDO.getRedeemCode());
            resp.setRedeemCodeExpireDate(gamingOrderItemDO.getRedeemCodeExpireDate());
            return resp;
        }

    }

}
