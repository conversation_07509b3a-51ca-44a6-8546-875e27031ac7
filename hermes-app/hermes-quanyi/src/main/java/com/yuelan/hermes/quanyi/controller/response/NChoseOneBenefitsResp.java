package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2025/4/25
 * @since 2025/4/25
 * N-选一权益
 */
@Data
public class NChoseOneBenefitsResp {

    @Schema(description = "选发权益最多选几个")
    private Integer maxSelectable;

    @Schema(description = "剩余可选权益数量")
    public Integer remainCount;

    @Schema(description = "N选一权益列表")
    public List<UserBenefitItemStatusResp> benefits;


}
