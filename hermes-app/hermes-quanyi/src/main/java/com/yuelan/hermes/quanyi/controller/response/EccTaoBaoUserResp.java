package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccTaoBaoUserDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> 2024/5/3 下午2:20
 */
@Data
public class EccTaoBaoUserResp {
    @Schema(description = "我方用户id")
    private Long userId;
    /**
     * 绑定的手机号码
     */
    @Schema(description = "绑定的手机号码")
    private String bindPhone;

    @Schema(description = "淘宝账号名字（非昵称）")
    private String taobaoAccount;
    /**
     * 淘宝账号对应openUid加密
     */
    @Schema(description = "淘宝OpenId")
    private String taobaoOpenUid;


    @Schema(description = "绑定手机号码时间")
    private LocalDateTime bindTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;


    public static EccTaoBaoUserResp buildResp(EccTaoBaoUserDO eccTaoBaoUserDO) {
        if (eccTaoBaoUserDO == null) {
            return null;
        }
        EccTaoBaoUserResp resp = new EccTaoBaoUserResp();
        resp.setUserId(eccTaoBaoUserDO.getUserId());
        resp.setBindPhone(eccTaoBaoUserDO.getBindPhone());
        resp.setTaobaoAccount(eccTaoBaoUserDO.getTaobaoUserNick());
        resp.setTaobaoOpenUid(eccTaoBaoUserDO.getTaobaoOpenUid());
        resp.setBindTime(eccTaoBaoUserDO.getBindTime());
        resp.setCreateTime(eccTaoBaoUserDO.getCreateTime());
        return resp;
    }
}
