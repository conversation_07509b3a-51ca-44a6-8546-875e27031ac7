package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.result.entity.KeyValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 电竞卡商品管理
 */
@Data
public class GamingGoodsRsp {

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "商品主图")
    private String goodsImg;

    @Schema(description = "上下架状态0下架1上架")
    private KeyValue<Integer, String> status;

    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "采购价")
    private BigDecimal purchasePrice;

    @Schema(description = "供应商类型")
    private KeyValue<Integer, String> supplierType;

    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;

    @Schema(title = "权益发放类型：1-直充，2-兑换码")
    private Integer deliveryType;

    /**
     * 有效库存
     */
    @Schema(description = "有效库存")
    private Long availableStock;

    /**
     * 临期库存 60天
     */
    @Schema(description = " 临期库存 60天")
    private Long nearExpiryStock;

    /**
     * 过期库存
     */
    @Schema(description = "过期库存")
    private Long expiredStock;

    /**
     * 是否已经使用
     */
    @Schema(description = "usedStock")
    private Long usedStock;

}