package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 虚拟商品管理
 *
 * @TableName goods_virtual
 */
@Data
public class GoodsVirtualReq {

    @Schema(description = "商品ID")
    private Long id;

    @NotBlank(message = "商品名称不能为空")
    @Schema(description = "商品名称")
    private String name;

    @Schema(description = "商品类型1卡密2直冲")
    private Integer type;

    @Schema(description = "上游供应商")
    private Integer supplier;

    @Schema(description = "第三方商品编号")
    private String thirdGoodsNo;

    @Schema(description = "状态0下架1上架")
    private Integer status;

    @Size(min = 1, max = 9, message = "商品图片不得大于九张")
    @Schema(description = "商品图片")
    private List<String> images;

    @Schema(description = "内容")
    private String content;

    @NotEmpty(message = "商品规格不能为空")
    @Schema(description = "商品规格")
    private List<GoodsVirtualSkuReq> skuList;


}
