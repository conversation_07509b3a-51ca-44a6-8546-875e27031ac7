package com.yuelan.hermes.quanyi.controller.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOrderDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/5/3 下午2:20
 */
@Data
public class EccOrderResp {


    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 姓名
     */
    @Schema(description = "姓名")
    private String name;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;

    /**
     * 权益包id
     */
    @Schema(description = "权益包id")
    private Long prodId;

    /**
     * 权益包名字
     */
    @Schema(description = "权益包名字")
    private String prodName;


    @Schema(description = "渠道类型:1-内部渠道,2-外部渠道")
    private Integer channelType;

    /**
     * 投放渠道id
     */
    @Schema(description = "投放渠道id")
    private Long channelId;


    /**
     * 投放推广渠道
     */
    @Schema(description = "投放推广渠道")
    private String channelName;
    /**
     * 话费余额
     */
    @Schema(description = "话费余额")
    private BigDecimal balance;
    /**
     * 归属地
     */
    @Schema(description = "归属地")
    private String phoneArea;

    /**
     * 用户在网状态：0-销户；1-在网
     */
    @Schema(description = "用户在网状态")
    private Integer userNetStatus;

    /**
     * 是否可兑换（excel导入）：0-不可兑换；1-可兑换
     */
    @Schema(description = "是否可兑换")
    private Integer redeemable;
    /**
     * 月份
     */
    @Schema(description = "月份")
    private String month;

    /**
     * 总兑换次数（商品数量）
     */
    @Schema(description = "总兑换次数")
    private Integer redeemLimit;

    /**
     * 剩余可领取次数
     */
    @Schema(description = "剩余可领取次数")
    private Integer redeemRemain;

    /**
     * 订单状态: 0-处理中（可兑换的默认状态） 1-交易成功（全部领取成功） 2-交易失败（不可兑换的默认状态） 3-订单异常（部分领取失败）
     */
    @Schema(description = "订单状态")
    private Integer orderStatus;

    /**
     * 激活时间
     */
    @Schema(description = "激活时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activationTime;
    /**
     * 不可兑换原因
     */
    @Schema(description = "不可兑换原因")
    private String unavailableReason;
    /**
     * 最后停机时间
     */
    @Schema(description = "最后停机时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime serviceSuspensionTime;
    /**
     * 销户时间
     */
    @Schema(description = "销户时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime closureTime;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;


    public static EccOrderResp buildResp(EccOrderDO eccOrderDO) {
        if (eccOrderDO == null) {
            return null;
        }
        EccOrderResp resp = new EccOrderResp();
        resp.setOrderId(eccOrderDO.getOrderId());
        resp.setName(eccOrderDO.getName());
        resp.setPhone(eccOrderDO.getPhone());
        resp.setProdId(eccOrderDO.getProdId());
        resp.setProdName(eccOrderDO.getProdName());
        resp.setChannelType(eccOrderDO.getChannelType());
        resp.setChannelId(eccOrderDO.getChannelId());
        resp.setChannelName(eccOrderDO.getChannelName());
        resp.setBalance(eccOrderDO.getBalance());
        resp.setPhoneArea(eccOrderDO.getPhoneArea());
        resp.setUserNetStatus(eccOrderDO.getUserNetStatus());
        resp.setRedeemable(eccOrderDO.getRedeemable());
        resp.setMonth(eccOrderDO.getMonth());
        resp.setRedeemLimit(eccOrderDO.getRedeemLimit());
        resp.setRedeemRemain(eccOrderDO.getRedeemRemain());
        resp.setOrderStatus(eccOrderDO.getOrderStatus());
        resp.setActivationTime(eccOrderDO.getActivationTime());
        resp.setUnavailableReason(eccOrderDO.getUnavailableReason());
        resp.setServiceSuspensionTime(eccOrderDO.getServiceSuspensionTime());
        resp.setClosureTime(eccOrderDO.getClosureTime());
        resp.setRemark(eccOrderDO.getRemark());
        return resp;
    }
}
