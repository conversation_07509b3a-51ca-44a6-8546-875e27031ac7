package com.yuelan.hermes.quanyi.controller.gaming;

/**
 * <AUTHOR> 2025/1/17
 * @since 2025/1/17
 */

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.session.SaSession;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.biz.manager.NetEasePartyManager;
import com.yuelan.hermes.quanyi.biz.manager.TournamentsManager;
import com.yuelan.hermes.quanyi.common.pojo.bo.HshUserSessionUser;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO;
import com.yuelan.hermes.quanyi.config.satoken.StpHshUserUtil;
import com.yuelan.hermes.quanyi.controller.request.TournamentsSignupReq;
import com.yuelan.hermes.quanyi.controller.response.GamingBenefitDetailResp;
import com.yuelan.hermes.quanyi.controller.response.GamingOrderItemSimpleRsp;
import com.yuelan.hermes.quanyi.mapper.GamingOrderItemMapper;
import com.yuelan.hermes.quanyi.remote.NetEaseManager;
import com.yuelan.hermes.quanyi.remote.response.zop.NetEaseGameRoleInfoResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/7/29 下午8:00
 * 用户领取电竞卡权益我们调用网易接口下发奖励-蛋仔派对。（之前的全街领取界面是由网易开发的）
 */
@Slf4j
@Validated
@RestController
@Tag(name = "电竞卡-蛋仔派对接口")
@RequiredArgsConstructor
@SaCheckLogin(type = StpHshUserUtil.TYPE)
@RequestMapping("/party")
public class NetEasePartyGameController {

    private final NetEaseManager netEaseManager;
    private final NetEasePartyManager netEasePartyManager;
    private final GamingOrderItemMapper gamingOrderItemMapper;
    private final TournamentsManager tournamentsManager;

    @Operation(summary = "当月所有权益明细")
    @PostMapping("/benefitDetail")
    public BizResult<GamingBenefitDetailResp> benefitDetail() {
        GamingBenefitDetailResp detail = new GamingBenefitDetailResp();
        Long userId = StpHshUserUtil.getLoginIdAsLong();

        String roleId = netEasePartyManager.queryBindRoleId(userId);
        detail.setRoleId(roleId);

        HshUserSessionUser sessionUser = StpHshUserUtil.getSession().getModel(SaSession.USER, HshUserSessionUser.class);
        String phone = sessionUser.getPhone();

        // 本月订单 - 只查询网易蛋仔派对供应商的订单
        LocalDateTime start = LocalDate.now().withDayOfMonth(1).atStartOfDay();
        LocalDateTime end = LocalDateTime.now();
        List<GamingOrderItemDO> items = gamingOrderItemMapper.findByPhoneAndTimeAndSupplierType(phone, start, end, SupplierEnum.NET_EASE_PARTY.getCode());
        List<GamingOrderItemSimpleRsp> simpleRspList = items.stream().map(GamingOrderItemSimpleRsp::buildRsp).collect(Collectors.toList());
        detail.setItems(simpleRspList);

        return BizResult.create(detail);
    }

    /**
     * 登录后用户，查询游戏内角色信息
     */
    @Operation(summary = "查询游戏内角色信息")
    @PostMapping("/getRoleInfo/{roleId}")
    public BizResult<NetEaseGameRoleInfoResp.RoleInfo> getRoleInfo(@PathVariable @NotEmpty(message = "角色id不能为空") String roleId) {
        String gameId = NetEasePartyManager.GAME_ID;
        try {
            NetEaseGameRoleInfoResp resp = netEaseManager.getRoleInfo(gameId, roleId);
            if (resp.bizSuccess()) {
                return BizResult.create(resp.getData());
            }
        } catch (Exception e) {
            log.error("查询角色信息失败", e);
        }
        throw BizException.create(BaseErrorCodeEnum.SYSTEM_BUSY, "查询角色信息失败");
    }

    /**
     * 绑定并且，确认发放到角色（双向绑定 一个用户只能绑定一个蛋仔派对的橘色   只兑换当月订单）
     */
    @Operation(summary = "绑定并且确认兑换到角色")
    @GetMapping("/exchange")
    @Parameters({
            @Parameter(name = "itemId", description = "订单id"),
            @Parameter(name = "roleId", description = "角色id，未绑定角色时候必传")
    })
    public BizResult<Void> exchange(@Param("itemId") String itemId, @Param("roleId") String roleId) {
        HshUserSessionUser sessionUser = StpHshUserUtil.getSession().getModel(SaSession.USER, HshUserSessionUser.class);
        netEasePartyManager.exchangeBenefit(sessionUser, itemId, roleId);
        return BizResult.ok();
    }

    /**
     * 比赛报名
     * 活动时间就是3.27最后一天
     */
    @SaIgnore
    @Operation(summary = "比赛报名")
    @PostMapping("/tournaments/signup")
    public BizResult<Void> tournamentsSignup(@RequestBody TournamentsSignupReq req) {
        tournamentsManager.partTournamentsSign(req);
        return BizResult.ok();
    }

}
