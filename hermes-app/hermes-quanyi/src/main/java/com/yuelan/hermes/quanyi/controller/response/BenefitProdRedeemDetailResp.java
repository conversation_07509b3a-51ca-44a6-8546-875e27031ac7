package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> 2024/4/10 下午3:57
 * <p>
 * 权益包领取详情
 */
@Data
public class BenefitProdRedeemDetailResp {

    @Schema(description = "已经选择领取权益商品")
    private List<SimpleProdItem> obtainedProdItems;

    @Schema(description = "剩余可选择领取的数量")
    private Integer surplusNum;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SimpleProdItem {
        private String goodsName;
        private Long goodsId;
        @Schema(description = "道具下发情况，预下单状态：0-下发中，1-已下发，2-发放失败")
        private Integer orderStatus;
    }
}
