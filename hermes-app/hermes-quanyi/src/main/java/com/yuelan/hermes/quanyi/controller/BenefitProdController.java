package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.core.validator.validate.QueryGroup;
import com.yuelan.hermes.quanyi.biz.service.BenefitProductDOService;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.controller.request.BenefitDistributionUrlCopyReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitProdListReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitProdSaveReq;
import com.yuelan.hermes.quanyi.controller.request.BenefitProdUpdateStatusReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitPayChannelResp;
import com.yuelan.hermes.quanyi.controller.response.BenefitProdResp;
import com.yuelan.hermes.quanyi.controller.response.DistributionUrlResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> 2024/4/2 15:46
 */
@Validated
@RestController
@Tag(name = "权益N选1/后台接口/权益产品/权益包")
@RequiredArgsConstructor
@RequestMapping("/a/benefit/product")
public class BenefitProdController {

    private final BenefitProductDOService benefitProductDOService;

    @Operation(summary = "权益包详情")
    @GetMapping("/detail/{productId}")
    public BizResult<BenefitProdResp> detail(@PathVariable Long productId) {
        BenefitProductDO productDO = benefitProductDOService.getById(productId);
        return BizResult.create(BenefitProdResp.buildResp(productDO));
    }

    @Operation(summary = "权益包列表")
    @PostMapping("/list")
    public BizResult<PageData<BenefitProdResp>> list(@Validated(QueryGroup.class) @RequestBody BenefitProdListReq req) {
        return BizResult.create(benefitProductDOService.list(req));
    }

    @Log(title = "新增权益包", type = OperationType.INSERT)
    @Operation(summary = "新增权益包")
    @PostMapping("/save")
    public BizResult<Void> save(@Validated(AddGroup.class) @RequestBody BenefitProdSaveReq req) {
        benefitProductDOService.save(req);
        return BizResult.ok();
    }

    @Log(title = "编辑权益包", type = OperationType.UPDATE)
    @Operation(summary = "编辑权益包")
    @PostMapping("/edit")
    public BizResult<Void> edit(@Validated(EditGroup.class) @RequestBody BenefitProdSaveReq req) {
        benefitProductDOService.update(req);
        return BizResult.ok();
    }

    @Log(title = "权益包上架/下架", type = OperationType.UPDATE)
    @Operation(summary = "权益包上架/下架")
    @PostMapping("/updateStatus")
    public BizResult<Void> updateStatus(@RequestBody @Validated BenefitProdUpdateStatusReq req) {
        req.checkReq();
        benefitProductDOService.updateStatus(req);
        return BizResult.ok();
    }

    @Operation(summary = "查询权益包支持的支付通道")
    @GetMapping("/payChannel")
    public BizResult<List<BenefitPayChannelResp>> getPayChannel() {
        return BizResult.create(benefitProductDOService.getPayChannel());
    }


    @Operation(summary = "复制投放链接")
    @PostMapping("/copyDistributionUrl")
    public BizResult<DistributionUrlResp> copyDistributionUrl(@RequestBody @Validated BenefitDistributionUrlCopyReq req) {
        return BizResult.create(benefitProductDOService.copyDistributionUrl(req));
    }


}
