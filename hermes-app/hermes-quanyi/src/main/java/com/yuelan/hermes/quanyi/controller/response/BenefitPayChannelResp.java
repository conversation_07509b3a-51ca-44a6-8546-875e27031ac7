package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> 2024/4/19 下午2:36
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BenefitPayChannelResp {

    @Schema(description = "支付通道id")
    private Integer payChannelId;

    @Schema(description = "支付通道名称")
    private String payChannelName;

    @Schema(description = "渠道下多个包")
    private List<PayChannelPkgResp> channelPkgList;

}
