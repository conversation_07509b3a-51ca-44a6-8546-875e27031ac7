package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2025/4/24
 * @since 2025/4/24
 * <p>
 * 每个权益状态响应
 */
@Data
public class UserBenefitItemStatusResp {

    @Schema(description = "发货类型 1-直充 2-兑换码")
    public Integer deliveryType;
    @Schema(description = "领取状态：0-未领取 1-下发中 2-已经领")
    public Integer getStatus;
    @Schema(description = "兑换码")
    public String redeemCode;
    @Schema(description = "兑换卡密")
    public String redeemCodePwd;
    @Schema(description = "兑换码过期时间")
    public LocalDateTime redeemCodeExpiredTime;
    @Schema(description = "权益id")
    private Long benefitItemId;
    @Schema(description = "权益名称")
    private String benefitItemName;
    @Schema(description = "权益icon")
    private String benefitItemIcon;


}
