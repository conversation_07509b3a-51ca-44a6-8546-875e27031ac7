package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BenefitsPackageListReq extends PageRequest {

    @Schema(description = "权益包id")
    private Long packageId;

    @Schema(description = "权益包名字")
    private String packageName;

    @Schema(description = "权益包编码")
    private String packageCode;

    @Schema(description = "状态：0-下架，1-上架")
    private Integer status;

    public Wrapper<BenefitsPackageDO> buildQueryWrapper() {
        return Wrappers.lambdaQuery(BenefitsPackageDO.class)
                .eq(Objects.nonNull(packageId), BenefitsPackageDO::getPackageId, packageId)
                .eq(StrUtil.isNotBlank(packageName), BenefitsPackageDO::getPackageName, packageName)
                .eq(StrUtil.isNotBlank(packageCode), BenefitsPackageDO::getPackageCode, packageCode)
                .eq(Objects.nonNull(status), BenefitsPackageDO::getStatus, status)
//                .and(StrUtil.isNotBlank(queryPackage), wrapper -> wrapper
//                        .like(BenefitsPackageDO::getPackageId, queryPackage)
//                        .or()
//                        .like(BenefitsPackageDO::getPackageName, queryPackage))
                .orderBy(Objects.equals(getOrderBy(), "createTime"), isAsc(), BenefitsPackageDO::getCreateTime)
                .orderByDesc(Objects.isNull(getOrderBy()), BenefitsPackageDO::getPackageId);
    }
}
