package com.yuelan.hermes.quanyi.controller.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
public class MchAccountRechargeReq {

    @NotBlank(message = "请选择充值商户")
    @Schema(description = "商户号")
    private String mchId;

    @Schema(description = "充值金额")
    private BigDecimal amount;

    @Schema(description = "备注")
    private String remark;
}
