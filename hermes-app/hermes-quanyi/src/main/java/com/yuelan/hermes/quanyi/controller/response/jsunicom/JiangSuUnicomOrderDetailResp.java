package com.yuelan.hermes.quanyi.controller.response.jsunicom;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/6/25
 * @description:
 */


@NoArgsConstructor
@Data
public class JiangSuUnicomOrderDetailResp {


    /**
     * 接口操作应答描述
     */
    private String RESP_DESC;
    /**
     * 订单号
     */
    private String ORDER_ID;
    /**
     * 订单状态编码值:
     * 00(订单成功)
     * 01(订单失败)
     * 02(订购中)
     * 当做订单订购结果编码。
     */
    private String MAIN_ORDER_STATE;

    private RespDaTaDTO RESP_DATA;
    /**
     * 接口操作应答编码，0000查询成功，2222查询失败，3333为订购中或是全客内部处理失败
     */
    private String RESP_CODE;

    @NoArgsConstructor
    @Data
    public static class RespDaTaDTO {

        /**
         * 订单支付状态:0未支付；1已支付；9无需支付。
         */
        private String iS_PAY;
        /**
         * 返回订单中心订单号
         */
        private String ORDER_ID;
        /**
         * ORDER_LINE
         */
        private List<OrderLineDTO> ORDER_LINE;
        /**
         * 如果为订单冻结模式将会返回该字段，
         * 0-订单冻结中,
         * 2-订单推送全客,
         * 3-用户冷静期退订
         * 4-下单成功
         * 5-下单失败
         * 4或5状态会返回具体信息
         */
        private String orderStatus;

        /**
         * 订单状态:
         * 00-已入库
         * 90-竣工
         * 98-订单已归档
         */
        private String ORDER_STATE;
        /**
         * 订购时间
         */
        private String CREATE_DATE;

        /**
         * ORDERLINEDTO
         */
        @NoArgsConstructor
        @Data
        public static class OrderLineDTO {
            /**
             * 是否是主业务单标识，0的时候是主业务单
             */
            private String MAIN_TAG;
            /**
             * 主业务单(MAIN_TAG=0)的PRODUCE_ORDER_STATE状态：
             * 0=成功 ；
             * 1或Z=失败；
             * -1=订购中
             */
            private String PRODUCE_ORDER_STATE;
            /**
             * 订单中心返回的业务单错误信息，透传字段，如有疑问，请咨询订单中心或向订单中心提沃工单
             */
            private String ERROR_MSG;
        }
    }
}
