package com.yuelan.hermes.quanyi.controller.gaming;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.commons.enums.SmsCodeType;
import com.yuelan.hermes.quanyi.biz.service.SmsCodeService;
import com.yuelan.hermes.quanyi.biz.service.TempGamingRedeemCodeDOService;
import com.yuelan.hermes.quanyi.common.pojo.domain.TempGamingRedeemCodeDO;
import com.yuelan.hermes.quanyi.controller.request.temp.GamingReqSmsReq;
import com.yuelan.hermes.quanyi.controller.request.temp.GamingVerifySmsCodeReq;
import com.yuelan.hermes.quanyi.controller.response.temp.TempGamingRedeemCodeResp;
import com.yuelan.hermes.quanyi.mapper.GamingOrderMapper;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2024/7/29 下午8:00
 */
@SaIgnore
@Validated
@RestController
@Tag(name = "电竞卡/临时需求")
@RequiredArgsConstructor
@RequestMapping("/eSportCard")
public class GamingUserController {

    private final SmsCodeService smsCodeService;

    private final GamingOrderMapper gamingOrderMapper;

    private final TempGamingRedeemCodeDOService tempGamingRedeemCodeDOService;

    @Operation(summary = "获取短信验证码")
    @PostMapping("/reqSmsCode")
    public BizResult<Void> reqSmsCode(@Validated @RequestBody GamingReqSmsReq req){
        String phone = req.getPhone();
        int count = gamingOrderMapper.selectByPhoneCount(phone);
        if(count == 0 ){
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "未查询到电竞卡订单，不允许发生验证码");
        }
        smsCodeService.reqCaptcha(phone, SmsCodeType.GAMING_SMS_CODE);
        return BizResult.ok();
    }

    @Operation(summary = "验证验证码并且领取")
    @PostMapping("/verifySmsCode")
    public BizResult<TempGamingRedeemCodeResp> verifyAndGetRedeemCode(@Validated @RequestBody GamingVerifySmsCodeReq req) {
        String phone = req.getPhone();
        smsCodeService.verifyCaptcha(phone, req.getSmsCode(), SmsCodeType.GAMING_SMS_CODE);
        TempGamingRedeemCodeDO notUsedRedeemCode = tempGamingRedeemCodeDOService.getNotUsedRedeemCode(phone);
        TempGamingRedeemCodeResp redeemCodeResp = new TempGamingRedeemCodeResp();
        redeemCodeResp.setRedeemCode(notUsedRedeemCode.getRedeemCode());
        return BizResult.create(redeemCodeResp);
    }



}
