package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderItemDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/4/19 下午3:52
 */
@Data
public class BenefitOrderItemResp {

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long itemId;
    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;
    /**
     * 关联总订单号
     */
    @Schema(description = "关联总订单号")
    private String orderNo;
    /**
     * 明细订单号
     */
    @Schema(description = "明细订单号")
    private String itemNo;
    /**
     * 支付通道
     */
    @Schema(description = "支付通道")
    private String payChannel;
    /**
     * 推广渠道
     */
    @Schema(description = "推广渠道")
    private String distributionChannel;
    /**
     * 关联权益包id
     */
    @Schema(description = "关联权益包id")
    private Long prodId;
    /**
     * 关联权益商品id
     */
    @Schema(description = "关联权益商品id")
    private Long goodsId;
    /**
     * 商品名
     */
    @Schema(description = "商品名")
    private String goodsName;
    /**
     * 供应商类型
     */
    @Schema(description = "供应商类型")
    private Integer supplierType;
    /**
     * 供应商
     */
    @Schema(description = "供应商")
    private String supplier;
    /**
     * 供应商商品编号
     */
    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;
    /**
     * 供应商订单号
     */
    @Schema(description = "供应商订单号")
    private String supplierOrderNo;
    /**
     * 销售价
     */
    @Schema(description = "销售价")
    private BigDecimal price;
    /**
     * 成本价
     */
    @Schema(description = "成本价")
    private BigDecimal costPrice;
    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
    private LocalDateTime payTime;
    /**
     * 预下单状态，预下单状态：0-默认状态，1-下单成功，2-下单失败
     */
    @Schema(description = "预下单状态，预下单状态：0-默认状态，1-下单成功，2-下单失败")
    private Integer preorderStatus;
    /**
     * 预计下单响应内容
     */
    @Schema(description = "预计下单响应内容")
    private String preorderContent;
    /**
     * 预下单时间
     */
    @Schema(description = "预下单时间")
    private LocalDateTime preorderTime;
    /**
     * 回调时间
     */
    @Schema(description = "回调时间")
    private LocalDateTime callBackTime;
    /**
     * 下单回调内容
     */
    @Schema(description = "下单回调内容")
    private String callBackContent;
    /**
     * 订单状态:0-处理中 1-交易成功 2-交易失败 3-订单异常
     */
    @Schema(description = "订单状态:0-处理中 1-交易成功 2-交易失败 3-订单异常")
    private Integer orderStatus;

    @Schema(description = "兑换发起时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    /**
     * 外部渠道名字
     */
    @Schema(description = "外部渠道名字")
    private String outChannelName;

    @Schema(description = "包名")
    private String packageName;


    public static BenefitOrderItemResp buildResp(BenefitOrderItemDO benefitOrderItemDO) {
        if (benefitOrderItemDO == null) {
            return null;
        }
        BenefitOrderItemResp resp = new BenefitOrderItemResp();
        resp.setItemId(benefitOrderItemDO.getItemId());
        resp.setPhone(benefitOrderItemDO.getPhone());
        resp.setOrderNo(benefitOrderItemDO.getOrderNo());
        resp.setItemNo(benefitOrderItemDO.getItemNo());
        resp.setProdId(benefitOrderItemDO.getProdId());
        resp.setGoodsId(benefitOrderItemDO.getGoodsId());
        resp.setGoodsName(benefitOrderItemDO.getGoodsName());
        resp.setSupplierType(benefitOrderItemDO.getSupplierType());
        resp.setSupplier(benefitOrderItemDO.getSupplier());
        resp.setSupplierGoodsNo(benefitOrderItemDO.getSupplierGoodsNo());
        resp.setSupplierOrderNo(benefitOrderItemDO.getSupplierOrderNo());
        resp.setPrice(benefitOrderItemDO.getPrice());
        resp.setCostPrice(benefitOrderItemDO.getCostPrice());
        resp.setPayTime(benefitOrderItemDO.getPayTime());
        resp.setPreorderStatus(benefitOrderItemDO.getPreorderStatus());
        resp.setPreorderContent(benefitOrderItemDO.getPreorderContent());
        resp.setPreorderTime(benefitOrderItemDO.getPreorderTime());
        resp.setCallBackTime(benefitOrderItemDO.getCallBackTime());
        resp.setCallBackContent(benefitOrderItemDO.getCallBackContent());
        resp.setOrderStatus(benefitOrderItemDO.getOrderStatus());
        resp.setCreateTime(LocalDateTimeUtil.of(benefitOrderItemDO.getCreateTime()));
        resp.setUpdateTime(LocalDateTimeUtil.of(benefitOrderItemDO.getUpdateTime()));
        BenefitOrderDO orderDO = benefitOrderItemDO.getOrderDO();
        if (orderDO != null) {
            resp.setPayChannel(orderDO.getPayChannel());
            resp.setDistributionChannel(orderDO.getDistributionChannel());
            resp.setOutChannelName(orderDO.getOutChannelName());
            resp.setPackageName(orderDO.getPackageName());
        }
        return resp;
    }
}
