package com.yuelan.hermes.quanyi.controller.request;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class BenefitItemListReq extends PageRequest {

    @Schema(description = "权益id")
    private Long benefitItemId;

    @Schema(description = "模块：1-对应原权益包下的商品库")
    private Integer module;

    @Schema(description = "对应权益商品库的商品id")
    private Long targetId;

    @Schema(description = "权益名字")
    private String benefitName;

    @Schema(description = "唯一编码")
    private String benefitCode;

    @Schema(description = "权益类型：1-账户直接冲 2-兑换码")
    private Integer benefitType;

    @Schema(description = "供应商 枚举id")
    private Integer supplierId;

    @Schema(description = "供应商商品编码")
    private String supplierGoodsCode;

    @Schema(description = "供应商名称")
    private Integer supplierName;

    @Schema(description = "状态：0-下架 1-上架")
    private Integer status;

    public Wrapper<BenefitItemDO> buildQueryWrapper() {
        return Wrappers.lambdaQuery(BenefitItemDO.class)
                .eq(Objects.nonNull(benefitItemId), BenefitItemDO::getBenefitItemId, benefitItemId)
                .eq(Objects.nonNull(module), BenefitItemDO::getModule, module)
                .eq(Objects.nonNull(targetId), BenefitItemDO::getTargetId, targetId)
                .like(StrUtil.isNotBlank(benefitName), BenefitItemDO::getBenefitName, benefitName)
                .eq(StrUtil.isNotBlank(benefitCode), BenefitItemDO::getBenefitCode, benefitCode)
                .eq(Objects.nonNull(benefitType), BenefitItemDO::getDeliveryType, benefitType)
                .eq(Objects.nonNull(supplierId), BenefitItemDO::getSupplierId, supplierId)
                .like(Objects.nonNull(supplierName), BenefitItemDO::getSupplierName, supplierName)
                .eq(Objects.nonNull(status), BenefitItemDO::getStatus, status)
                .orderBy(Objects.equals(getOrderBy(), "createTime"), isAsc(), BenefitItemDO::getCreateTime)
                .orderByDesc(Objects.isNull(getOrderBy()), BenefitItemDO::getBenefitItemId);
    }
}
