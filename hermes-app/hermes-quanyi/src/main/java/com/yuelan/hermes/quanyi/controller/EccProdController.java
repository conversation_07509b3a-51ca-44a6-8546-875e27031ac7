package com.yuelan.hermes.quanyi.controller;

import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.core.validator.validate.QueryGroup;
import com.yuelan.hermes.quanyi.biz.service.EccProductDOService;
import com.yuelan.hermes.quanyi.biz.service.EccProductPageService;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductPageDO;
import com.yuelan.hermes.quanyi.controller.request.*;
import com.yuelan.hermes.quanyi.controller.response.*;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BaseListResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/4/2 15:46
 */
@Validated
@RestController
@Tag(name = "电商卡/后台api/权益包产品")
@RequiredArgsConstructor
@RequestMapping("/a/ecc/product")
public class EccProdController {

    private final EccProductDOService eccProductDOService;
    private final EccProductPageService eccProductPageService;

    @Operation(summary = "电商卡权益包详情")
    @GetMapping("/detail/{productId}")
    public BizResult<EccProdResp> detail(@PathVariable Long productId) {
        EccProductDO productDO = eccProductDOService.getById(productId);
        return BizResult.create(EccProdResp.buildResp(productDO));
    }

    @Operation(summary = "电商卡权益包列表")
    @PostMapping("/list")
    public BizResult<PageData<EccProdResp>> list(@Validated(QueryGroup.class) @RequestBody EccProdListReq req) {
        return BizResult.create(eccProductDOService.page(req));
    }

    @Operation(summary = "电商卡权益包选择框选项集合")
    @GetMapping("/selectOptions")
    public BizResult<EccProdSelectOptionsResp> selectOptions() {
        return BizResult.create(eccProductDOService.selectOptions());
    }


    @Operation(summary = "新增电商卡权益包")
    @PostMapping("/save")
    public BizResult<Void> save(@Validated(AddGroup.class) @RequestBody EccProdSaveReq req) {
        eccProductDOService.save(req);
        return BizResult.ok();
    }

    @Operation(summary = "编辑电商卡权益包")
    @PostMapping("/edit")
    public BizResult<Void> edit(@Validated(EditGroup.class) @RequestBody EccProdSaveReq req) {
        eccProductDOService.update(req);
        return BizResult.ok();
    }

    @Log(title = "电商卡权益包上架/下架", type = OperationType.UPDATE)
    @Operation(summary = "电商卡权益包上架/下架")
    @PostMapping("/updateStatus")
    public BizResult<Void> updateStatus(@RequestBody @Validated EccProdUpdateStatusReq req) {
        eccProductDOService.updateStatus(req);
        return BizResult.ok();
    }

    @Operation(summary = "复制投放链接")
    @PostMapping("/copyDistributionUrl")
    public BizResult<DistributionUrlResp> copyDistributionUrl(@RequestBody @Validated EccDistributionUrlCopyReq req) {
        return BizResult.create(eccProductDOService.copyDistributionUrl(req));
    }

    @Operation(summary = "电商卡卡种类选项")
    @GetMapping("/spProdOptions")
    public BizResult<EccSpProdOptionsResp> cardTypeOptions() {
        return BizResult.create(eccProductDOService.prodOptions());
    }

    @Operation(summary = "获取运营商归属省市")
    @GetMapping("/spProdRegion/{spProdId}")
    @Parameter(name = "spProdId", description = "运营商卡种ID", required = true)
    public BizResult<List<EccPostAreaResp>> spProdRegion(@PathVariable Integer spProdId) {
        return BizResult.create(eccProductDOService.spProdRegion(spProdId));
    }

    @Operation(summary = "号卡落地页配置新增/编辑")
    @PostMapping("/pageConfig")
    public BizResult<Void> landingPageConfig(@RequestBody @Validated EccProductPageReq req) {
        eccProductPageService.saveOrUpdate(req);
        return BizResult.ok();
    }

    @Operation(summary = "启用落地页配置")
    @GetMapping("/enablePageConfig/{pageId}")
    public BizResult<Void> enablePageConfig(@Validated @PathVariable Long pageId) {
        eccProductPageService.enablePageConfig(pageId);
        return BizResult.ok();
    }

    @Operation(summary = "号卡落地页配置详情")
    @PostMapping("/pageConfig/{productId}")
    public BizResult<BaseListResp<EccProductPageResp>> landingPageConfig(@PathVariable Long productId) {
        List<EccProductPageDO> pageDOList = eccProductPageService.getByProductId(productId);
        List<EccProductPageResp> pageResp = pageDOList.stream().
                map(EccProductPageResp::build)
                .collect(Collectors.toList());
        return BizResult.create(new BaseListResp<>(pageResp));
    }
}
