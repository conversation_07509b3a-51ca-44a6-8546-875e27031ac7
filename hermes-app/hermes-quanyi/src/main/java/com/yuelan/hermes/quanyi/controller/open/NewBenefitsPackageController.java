package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.yuelan.hermes.quanyi.biz.manager.NewBenefitsOrderManager;
import com.yuelan.hermes.quanyi.controller.request.ChannelBenefitPackageOrderReq;
import com.yuelan.hermes.quanyi.controller.response.PackageOrderResp;
import com.yuelan.result.entity.BizResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2025/5/8
 * @since 2025/5/8
 */
@SaIgnore
@Slf4j
@Validated
@Tag(name = "权益组合包")
@RequestMapping("/package")
@RestController
@RequiredArgsConstructor
public class NewBenefitsPackageController {

    private final NewBenefitsOrderManager newBenefitsOrderManager;

    @Operation(summary = "权益包下单")
    @ApiResponse(responseCode = "200", description = "code=0,成功返回数据，其他失败,msg为错误信息")
    @PostMapping("/order/create")
    public BizResult<PackageOrderResp> orderCreate(@RequestBody @Validated ChannelBenefitPackageOrderReq req) {
        return BizResult.create(newBenefitsOrderManager.createBenefitOrder(req));
    }

}
