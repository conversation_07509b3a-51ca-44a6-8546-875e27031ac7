package com.yuelan.hermes.quanyi.controller.response;

import lombok.Data;

@Data
public class RsResult {
    /**
     * 调用成功
     */
    private static final String SUCCESS = "0000";
    /**
     * 处理中
     */
    private static final String ING = "0001";

    /**
     * 返回码
     */
    private String code;
    /**
     * 返回信息
     */
    private String message;
    /**
     * 返回参数
     */
    private String data;

    public static RsResult success(String data) {
        RsResult rsResult = new RsResult();
        rsResult.setCode(SUCCESS);
        rsResult.setData(data);
        return rsResult;
    }

    public static RsResult ing(String data) {
        RsResult rsResult = new RsResult();
        rsResult.setCode(ING);
        rsResult.setData(data);
        return rsResult;
    }

    public static RsResult error(String code, String message) {
        RsResult rsResult = new RsResult();
        rsResult.setCode(code);
        rsResult.setMessage(message);
        return rsResult;
    }

}
