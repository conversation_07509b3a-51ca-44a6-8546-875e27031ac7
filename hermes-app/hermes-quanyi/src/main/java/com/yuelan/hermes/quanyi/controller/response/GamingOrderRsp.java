package com.yuelan.hermes.quanyi.controller.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 电竞卡订单
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GamingOrderRsp extends GamingOrderListRsp {

    @Schema(description = "附加数据")
    private String attach;

    @Schema(description = "短信发送失败response")
    private String smsSendFailResponse;

}