package com.yuelan.hermes.quanyi.remote;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yuelan.boot.utils.HttpServletRequestUtil;
import com.yuelan.hermes.commons.enums.BizNoPrefixEnum;
import com.yuelan.hermes.commons.enums.OrderStatusEnum;
import com.yuelan.hermes.commons.enums.PayStatusEnum;
import com.yuelan.hermes.commons.util.ExecutorServiceUtils;
import com.yuelan.hermes.quanyi.biz.manager.AdManager;
import com.yuelan.hermes.quanyi.biz.service.AdChannelDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderDOService;
import com.yuelan.hermes.quanyi.biz.service.BenefitOrderLogService;
import com.yuelan.hermes.quanyi.biz.service.BenefitPlatformService;
import com.yuelan.hermes.quanyi.common.enums.*;
import com.yuelan.hermes.quanyi.common.enums.error.BizErrorCodeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitOrderLog;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitUnicomPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.bo.HttpRequestWrapper;
import com.yuelan.hermes.quanyi.common.pojo.domain.AdChannelDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.LiaoNingCmccProperties;
import com.yuelan.hermes.quanyi.common.util.LiaoNingydUtil;
import com.yuelan.hermes.quanyi.common.util.LocalBizNoPlusUtils;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.controller.response.LiaoNingydRes;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.exception.BizException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LiaoNingCmccManager {

    public static final String TAG = "[辽宁移动]";
    private static final String SUCCESS_CODE = "0";
    // 发送二次确认短信
    private static final String GET_SMS_CODE = "/boss/reverse/sendCodeMsgNew";
    // 业务办理
    private static final String ORDER = "/boss/reverse/sendReverse";
    private static final int TIME_OUT = 10000;
    @Autowired
    private LiaoNingCmccProperties liaoNingCmccProperties;
    @Resource
    private AdChannelDOService adChannelDOService;
    @Resource

    private AdManager adManager;
    @Resource
    private BenefitOrderDOService benefitOrderDOService;
    @Resource
    @Lazy
    private BenefitPlatformService benefitPlatformService;
    @Resource
    private BenefitOrderLogService benefitOrderLogService;

    /**
     * 发送验证码短信
     *
     * @param reqParams 请求参数
     * @param productDO 商品信息
     */
    public boolean orderSendSmsCode(UserBenefitOrderReq reqParams, BenefitProductDO productDO) {
        String mobile = reqParams.getMobile();
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        LiaoNingPayPkgEnum pgkEnum = this.getPgkEnum(productDO);
        return this.sendSmsCode(mobile, pgkEnum);
    }

    /**
     * 下单
     *
     * @param req       请求参数
     * @param productDO 商品信息
     * @param orderDO   下单信息
     */
    public BenefitPayResultBO createOrder(UserBenefitOrderReq req, BenefitProductDO productDO, BenefitOrderDO orderDO) {
        String mobile = req.getMobile();
        String smsCode = req.getSmsCode();

        BenefitUnicomPayResultBO payResultBO = new BenefitUnicomPayResultBO();
        payResultBO.setSuccess(false);
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(smsCode)) {
            log.info(TAG + "手机号或者验证码为空");
            payResultBO.setMessage("暂时无法订阅");
        }
        LiaoNingPayPkgEnum pgkEnum = this.getPgkEnum(productDO);

        LiaoNingydRes res = this.productOrder(mobile, smsCode, pgkEnum, orderDO.getOrderNo());
        if (SUCCESS_CODE.equals(res.getCode())) {
            payResultBO.setSuccess(true);
            // 更新订单状态
            orderDO.setOrderStatus(OrderStatusEnum.PROCESSING.getCode());
            orderDO.setOutOrderNo(res.getData());
        } else {
            payResultBO.setMessage(res.getData());
            orderDO.setPayStatus(PayStatusEnum.FAIL.getCode());
        }
        orderDO.setPayNotifyContent(JSON.toJSONString(res));
        orderDO.setPayNotifyTime(LocalDateTime.now());
        return payResultBO;
    }

    /**
     * 业务受理
     *
     * @param mobile   手机号
     * @param randCode 验证码
     */
    private LiaoNingydRes productOrder(String mobile, String randCode, LiaoNingPayPkgEnum pkgEnum, String orderNo) {

        HashMap<String, Object> params = new HashMap<>();
        params.put("mobile", mobile);
        params.put("code", randCode);
        params.put("billId", liaoNingCmccProperties.getProperties(pkgEnum).getBillId());
        params.put("prodId", liaoNingCmccProperties.getProperties(pkgEnum).getProdId());
        return this.sendRemoteRequest(ORDER, params, new BenefitOrderLog.Args(BenefitOrderLog.Biz.ORDER, mobile, orderNo));
    }

    /**
     * 运营商下发短信验证码
     */
    private boolean sendSmsCode(String mobile, LiaoNingPayPkgEnum pkgEnum) {
        if (StringUtils.isEmpty(mobile)) {
            return false;
        }
        String ip = ServletUtil.getClientIP(HttpServletRequestUtil.getCurrentRequest());
        HashMap<String, Object> params = new HashMap<>();
        params.put("mobile", mobile);
        params.put("id", liaoNingCmccProperties.getProperties(pkgEnum).getSmsConfigId());
        params.put("ipAddress", ip);

        BenefitOrderLog.Args args = new BenefitOrderLog.Args(BenefitOrderLog.Biz.SMS_SEND, mobile);
        args.ip(ip, "ipAddress");
        LiaoNingydRes res = this.sendRemoteRequest(GET_SMS_CODE, params, args);
        if (res == null) {
            return false;
        }
        if (res.getCode().equals(SUCCESS_CODE)) {
            return true;
        } else {
            throw BizException.create(BizResult.error(res.getCode(), res.getData()));
        }
    }

    /**
     * 获取业务id
     *
     * @param productDO 产品对象
     * @return 业务id
     */
    private LiaoNingPayPkgEnum getPgkEnum(BenefitProductDO productDO) {
        LiaoNingPayPkgEnum payPkgEnum = LiaoNingPayPkgEnum.of(productDO.getPayChannelPkgId());
        if (payPkgEnum == null) {
            throw BizException.create(BizErrorCodeEnum.PROD_PAYPKG_ERROR);
        }
        return payPkgEnum;
    }

    /**
     * 发送远程接口
     *
     * @param path   接口路径
     * @param params 参数
     * @return 发送结果
     */
    private LiaoNingydRes sendRemoteRequest(String path, HashMap<String, Object> params, BenefitOrderLog.Args args) {
        String url = liaoNingCmccProperties.getDomain() + path;
        try {
            String sign = LiaoNingydUtil.getSign(params, liaoNingCmccProperties.getAppCode());
            String jsonBody = LiaoNingydUtil.encrypt(liaoNingCmccProperties.getKey(), JSON.toJSONString(params));

            // 日志参数
            args.setReqParams(JSON.toJSONString(params));

            HttpRequestWrapper requestWrapper = HttpRequestWrapper.post(url)
                    .header("appCode", liaoNingCmccProperties.getAppCode())
                    .header("sign", sign)
                    .contentType(ContentType.TEXT_PLAIN.getValue())
                    .body(jsonBody)
                    .timeout(TIME_OUT)
                    .log(BenefitPayChannelEnum.LN_CMCC, args);

            log.info(TAG + "平台请求运营商之前.params:{},req:{}", params, requestWrapper.getHttpRequest());
            HttpResponse response = benefitOrderLogService.http(requestWrapper);
            log.info(TAG + "平台请求运营商之后.params:{},res:{}", params, response);
            if (response.getStatus() == 200) {
                return JSON.parseObject(response.body(), LiaoNingydRes.class);
            }
            return null;
        } catch (Exception e) {
            log.info(TAG + "接口:{} 请求参数:{}", url, JSON.toJSONString(params), e);
            return null;
        }
    }

    /**
     * 支付成功广告追踪转化事件上报
     */
    private void sendPaySuccessEvent(BenefitOrderDO orderDO) {
        if (orderDO == null || orderDO.getAdChannelId() == null || StringUtils.isEmpty(orderDO.getAdExt())) {
            return;
        }
        JSONObject adExt = JSONObject.parseObject(orderDO.getAdExt());
        AdChannelDO adChannel = adChannelDOService.getByModuleAndAdChannelId(SysModuleEnum.BENEFIT, orderDO.getAdChannelId());
        AdChannelCodeEnum adChannelCodeEnum = AdChannelCodeEnum.of(adChannel.getAdChannelCode());
        BenefitTrackingEventEnum paySuccessTrackingEventEnum = BenefitTrackingEventEnum.PAY_SUCCESS;
        adManager.adCallBackHandlerCall(adChannelCodeEnum, adExt, paySuccessTrackingEventEnum, Boolean.FALSE);
    }

    public Map<String, String> orderNotify(Map<String, String> params) {
        int type = NumberUtil.parseInt(params.get("type"));
        Map<String, String> resultMap = new HashMap<>();
        if (type == 1) {
            // 订购成功
            String orderCode = params.get("orderCode");
            BenefitOrderDO orderDO = benefitOrderDOService.getOne(new LambdaQueryWrapper<BenefitOrderDO>()
                    .eq(BenefitOrderDO::getOutOrderNo, orderCode)
                    .eq(BenefitOrderDO::getPayChannelId, BenefitPayChannelEnum.LN_CMCC.getCode())
                    .eq(BenefitOrderDO::getPayStatus, PayStatusEnum.DEFAULT.getCode())
                    .orderByDesc(BenefitOrderDO::getOrderId)
                    .last("limit 1"));
            if (orderDO == null) {
                resultMap.put("code", "-2");
                resultMap.put("message", "未查找到有效的待支付订单");
                return resultMap;
            }
            orderDO.setPayStatus(PayStatusEnum.SUCCESS.getCode());
            benefitOrderDOService.updateById(orderDO);
            // 用户支付成功事件
            sendPaySuccessEvent(orderDO);
            ExecutorServiceUtils.execute(() -> {
                try {
                    // 通知媒体
                    benefitPlatformService.notifyMediaOrderResult(orderDO, "成功");
                } catch (Exception e) {
                    log.error("通知媒体失败。订单id:{}", orderDO.getOrderId(), e);
                }
            });
            resultMap.put("code", "0");
            resultMap.put("message", "请求成功");
        } else if (type == 2) {
            // 退订
            String mobile = params.get("mobile");
            String unsubscribeTime = params.get("unsubscribeTime");

            // 查询一条该手机号对µ应的最新的支付成功的订购记录
            BenefitOrderDO oldOrderDO = benefitOrderDOService.getOne(new LambdaQueryWrapper<BenefitOrderDO>()
                    .eq(BenefitOrderDO::getPhone, mobile)
                    .eq(BenefitOrderDO::getPayChannelId, BenefitPayChannelEnum.LN_CMCC.getCode())
                    .eq(BenefitOrderDO::getPayStatus, PayStatusEnum.SUCCESS.getCode())
                    .orderByDesc(BenefitOrderDO::getOrderId)
                    .last("limit 1"));
            if (oldOrderDO == null) {
                log.info("查找订单失败.入参:{}", JSON.toJSON(params));
                resultMap.put("code", "-1");
                resultMap.put("message", "通过手机号未查找到有效的订单");
                return resultMap;
            }
            BenefitOrderDO benefitOrderDO = new BenefitOrderDO();
            benefitOrderDO.setOrderNo(LocalBizNoPlusUtils.genBillNo(BizNoPrefixEnum.ORDER_BENEFIT.getPrefix()));
            benefitOrderDO.setOutOrderNo(oldOrderDO.getOutOrderNo());
            benefitOrderDO.setDistributionChannel(oldOrderDO.getDistributionChannel());
            benefitOrderDO.setChannelId(oldOrderDO.getChannelId());
            benefitOrderDO.setChannelName(oldOrderDO.getChannelName());
            benefitOrderDO.setAdChannelId(oldOrderDO.getAdChannelId());
            benefitOrderDO.setAdChannelName(oldOrderDO.getAdChannelName());
            benefitOrderDO.setAdExt(oldOrderDO.getAdExt());
            benefitOrderDO.setPayChannelId(oldOrderDO.getPayChannelId());
            benefitOrderDO.setPayChannel(oldOrderDO.getPayChannel());
            benefitOrderDO.setPayChannelPkgId(oldOrderDO.getPayChannelPkgId());
            benefitOrderDO.setPayChannelPkgName(oldOrderDO.getPayChannelPkgName());
            benefitOrderDO.setPhone(oldOrderDO.getPhone());
            benefitOrderDO.setProdId(oldOrderDO.getProdId());
            benefitOrderDO.setProdName(oldOrderDO.getProdName());
            benefitOrderDO.setRedeemLimit(oldOrderDO.getRedeemLimit());
            benefitOrderDO.setRedeemRemain(oldOrderDO.getRedeemRemain());
            benefitOrderDO.setCycleType(oldOrderDO.getCycleType());
            benefitOrderDO.setCycleRedeemLimit(oldOrderDO.getCycleRedeemLimit());
            benefitOrderDO.setOrderAmount(oldOrderDO.getOrderAmount());
            benefitOrderDO.setPayStatus(PayStatusEnum.UNSUBSCRIBE.getCode());
            benefitOrderDO.setPayNotifyContent(JSON.toJSONString(params));
            benefitOrderDO.setPayNotifyTime(DateUtil.date().toLocalDateTime());
            benefitOrderDO.setOrderStatus(oldOrderDO.getOrderStatus());
            benefitOrderDO.setPreorderStatus(oldOrderDO.getPreorderStatus());
            benefitOrderDO.setOutChannelId(oldOrderDO.getOutChannelId());
            benefitOrderDO.setOutChannelName(oldOrderDO.getOutChannelName());
            benefitOrderDO.setCreateTime(DateUtil.parse(unsubscribeTime, "yyyy-MM-dd HH:mm:ss"));
            benefitOrderDOService.save(benefitOrderDO);

            resultMap.put("code", "0");
            resultMap.put("message", "请求成功");
        }

        return resultMap;
    }
}
