package com.yuelan.hermes.quanyi.controller.request;

import lombok.Data;

/**
 * 订单信息实体类
 */
@Data
public class HuNanDxOrderStatusChangeReq {
    /**
     * 身份证号码 文档里面没有
     */
    private String cardNo;

    /**
     * 业务订单号（必填）
     */
    private String webOrder;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 激活状态 0 待激活,2 促激活 ，AC002 激活成功 ，AC001 激活失败,IT001 激活异常，4小程序/人审外呼
     */
    private String otherStatus;

    /**
     * 物流单号
     */
    private String sendNo;

    /**
     * 物流公司名称
     * 示例：顺丰、中通
     */
    private String logisticsName;

    /**
     * 物流状态
     * 示例：已发货/运输中/已签收
     */
    private String logisticsStatus;

    /**
     * CPS1 扩展字段
     */
    private String cp1;

    /**
     * 是否换号（仅靓号宽带送场景使用）
     * 格式：更换后的号码
     */
    private String isAccNumber;

    /**
     * 订单状态描述或作废原因
     */
    private String orderStatusDesc;

    /**
     * 激活前是否充值
     * 1-已充值, 其他值-未充值
     */
    private String isInvest;
}