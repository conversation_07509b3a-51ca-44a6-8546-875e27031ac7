package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.hermes.commons.enums.UpOffStatusEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * <AUTHOR> 2024/4/2 19:41
 */
@Data
public class BenefitProdUpdateStatusReq {

    @NotNull(message = "权益产品包id不能为空")
    @Schema(description = "权益产品包")
    private Long productId;


    @NotNull(message = "上下架状态不能为空")
    @Schema(description = "上下架状态：0-下架；1-上架")
    private Integer prodStatus;

    public static Wrapper<BenefitProductDO> buildUpdateWrapper(BenefitProdUpdateStatusReq req) {
        return Wrappers.lambdaUpdate(BenefitProductDO.class)
                .eq(BenefitProductDO::getProdId, req.getProductId())
                .set(BenefitProductDO::getProdStatus, req.getProdStatus());

    }

    public void checkReq() {
        UpOffStatusEnum statusEnum = LocalEnumUtils.findByCode(UpOffStatusEnum.class, prodStatus);
        if (Objects.isNull(statusEnum)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "上下架状态值不合法");
        }
    }
}
