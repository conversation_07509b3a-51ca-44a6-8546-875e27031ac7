package com.yuelan.hermes.quanyi.controller.response.benefitplatform;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/7/19
 * @description:
 */

@Data
public class BenefitPlatformOrderRes {

    @Schema(description = "是否成功")
    private Boolean success;

    @Schema(description = "成功")
    private String message;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "下单时间")
    private Long orderTimestamp;

    @Schema(description = "支付跳转链接")
    private String payUrl;

}
