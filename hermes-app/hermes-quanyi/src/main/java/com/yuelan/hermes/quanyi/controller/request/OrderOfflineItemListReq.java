package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class OrderOfflineItemListReq extends PageRequest {

    @NotBlank(message = "订单编号不能为空")
    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "订单明细单号")
    private String itemNo;

    @Schema(description = "供应商订单号")
    private String supplierOrderNo;

    @Schema(description = "用户手机号")
    private String phone;

    @Schema(description = "用户充值账号")
    private String userAccount;

    @Schema(description = "卡号")
    private String voucherCode;

    @Schema(description = "卡密")
    private String voucherPassword;

    @Schema(description = "短链接，二维码短链接或者条码短链接")
    private String qrCodeUrl;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "短信状态")
    private Integer smsStatus;
}