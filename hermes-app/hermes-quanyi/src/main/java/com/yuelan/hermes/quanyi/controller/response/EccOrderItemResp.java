package com.yuelan.hermes.quanyi.controller.response;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOrderItemDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/5/3 下午2:20
 */
@Data
public class EccOrderItemResp {

    @Schema(description = "主键")
    private Long itemId;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "关联总订单号")
    private Long orderId;

    @Schema(description = "明细订单号")
    private String itemNo;

    @Schema(description = "电商卡用户id")
    private Long eccUserId;

    @Schema(description = "关联权益商品id")
    private Long goodsId;

    @Schema(description = "商品名")
    private String goodsName;

    @Schema(description = "兑换码id")
    private Long redeemCodeId;

    @Schema(description = "预下单状态，预下单状态：0-默认状态，1-下单成功，2-下单失败")
    private Integer preorderStatus;

    @Schema(description = "预计下单响应内容")
    private String preorderContent;

    @Schema(description = "预下单时间")
    private LocalDateTime preorderTime;

    @Schema(description = "订单状态:0-处理中 1-交易成功 2-交易失败 3-订单异常")
    private Integer orderStatus;

    @Schema(description = "异常原因")
    private String abnormalReason;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;


    public static EccOrderItemResp buildResp(EccOrderItemDO orderItemDO) {
        if (orderItemDO == null) {
            return null;
        }
        EccOrderItemResp resp = new EccOrderItemResp();
        resp.setItemId(orderItemDO.getItemId());
        resp.setPhone(orderItemDO.getPhone());
        resp.setOrderId(orderItemDO.getOrderId());
        resp.setItemNo(orderItemDO.getItemNo());
        resp.setEccUserId(orderItemDO.getEccUserId());
        resp.setGoodsId(orderItemDO.getGoodsId());
        resp.setGoodsName(orderItemDO.getGoodsName());
        resp.setRedeemCodeId(orderItemDO.getRedeemCodeId());
        resp.setPreorderStatus(orderItemDO.getPreorderStatus());
        resp.setPreorderContent(orderItemDO.getPreorderContent());
        resp.setPreorderTime(orderItemDO.getPreorderTime());
        resp.setOrderStatus(orderItemDO.getOrderStatus());
        resp.setAbnormalReason(orderItemDO.getAbnormalReason());
        resp.setRemark(orderItemDO.getRemark());
        resp.setCreateTime(LocalDateTimeUtil.of(orderItemDO.getCreateTime()));
        return resp;
    }
}
