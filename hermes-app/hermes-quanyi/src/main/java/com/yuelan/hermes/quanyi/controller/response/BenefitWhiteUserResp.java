package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitWhiteUserDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class BenefitWhiteUserResp {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "手机号")
    private String phone;
    /**
     * 产品id
     */
    @Schema(description = "产品id")
    private Long prodId;
    /**
     * 产品名字
     */
    @Schema(description = "产品名字")
    private String prodName;
    /**
     * 状态
     */
    @Schema(description = "状态,0-无效 1-有效")
    private Integer status;

    public static BenefitWhiteUserResp buildResp(BenefitWhiteUserDO whiteUserDO) {
        if (whiteUserDO == null) {
            return null;
        }
        BenefitWhiteUserResp resp = new BenefitWhiteUserResp();
        resp.setId(whiteUserDO.getId());
        resp.setPhone(whiteUserDO.getPhone());
        resp.setProdId(whiteUserDO.getProdId());
        resp.setProdName(whiteUserDO.getProdName());
        resp.setStatus(whiteUserDO.getStatus());
        return resp;
    }
}
