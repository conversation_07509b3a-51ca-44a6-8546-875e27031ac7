package com.yuelan.hermes.quanyi.controller.open;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.biz.handler.impl.KaSaiNumCardServerImpl;
import com.yuelan.hermes.quanyi.remote.kassai.request.KaSaiMessagePushReq;
import com.yuelan.hermes.quanyi.remote.kassai.response.KsBaseResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> 2025/7/10
 * @since 2025/7/10
 * <p>
 * 卡赛回调控制器
 */
@SaIgnore
@Slf4j
@Tag(name = "卡赛号卡回调")
@RequestMapping("/kasai")
@RestController
@RequiredArgsConstructor
public class KaSaiController {

    private final KaSaiNumCardServerImpl kaSaiNumCardServer;

    @Operation(summary = "卡赛订单状态回调通知")
    @PostMapping("/notify")
    public KsBaseResponse<Void> notify(@RequestBody KaSaiMessagePushReq req) {
        log.info("KaSaiController notify: {}", JSONObject.toJSONString(req));
        try {
            return kaSaiNumCardServer.dealCallback(req);
        } catch (Exception e) {
            log.error("KaSaiController notify error", e);
            KsBaseResponse<Void> response = new KsBaseResponse<>();
            response.setCode(500);
            response.setMessage("处理异常");
            return response;
        }
    }
}
