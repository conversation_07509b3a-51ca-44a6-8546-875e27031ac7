package com.yuelan.hermes.quanyi.controller.request;

import com.alibaba.fastjson2.JSONObject;
import com.yuelan.core.validator.constraints.RegexMatch;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.annotation.Nullable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/4/8 09:52
 */
@Data
public class UserBenefitOrderReq {

    @Schema(description = "手机号码")
    @NotEmpty(message = "手机号码不能为空")
    @RegexMatch(regex = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String mobile;


    @Schema(description = "产品编码")
    @NotEmpty(message = "产品编码不能为空")
    private String prodCode;


    @Schema(description = "渠道id")
    @NotNull(message = "渠道id不能为空")
    private Long channelId;

    @Schema(description = "广告渠道编码")
    private String adChannelCode;

    @Schema(description = "广告扩展参数")
    private JSONObject adExt;

    @Schema(description = "验证码")
    @Nullable
    private String smsCode;

    @Schema(description = "支付成功后回跳url")
    private String retUrl;

    @Schema(description = "订购类型 1-连续订购 0-单次订购")
    private String subscriptionType;

}
