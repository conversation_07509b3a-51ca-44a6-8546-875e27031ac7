package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class BenefitItemResp {

    @Schema(description = "权益id")
    private Long benefitItemId;

    @Schema(description = "权益名字")
    private String benefitName;

    @Schema(description = "唯一编码")
    private String benefitCode;

    @Schema(description = "权益类型：1-账户直接冲 2-兑换码")
    private Integer benefitType;

    @Schema(description = "产品图")
    private String itemImg;

    @Schema(description = "成本价（单位元）")
    private BigDecimal costPrice;

    @Schema(description = "销售价格（单位元）")
    private BigDecimal sellingPrice;

    @Schema(description = "供应商 枚举id")
    private Integer supplierId;

    @Schema(description = "供应商名字")
    private String supplierName;

    @Schema(description = "供应商商品配置JSON")
    private String supplierGoodsParam;

    @Schema(description = "状态：0-下架 1-上架")
    private Integer status;

    public static BenefitItemResp buildResp(BenefitItemDO itemDO) {
        BenefitItemResp resp = new BenefitItemResp();
        resp.setBenefitItemId(itemDO.getBenefitItemId());
        resp.setBenefitName(itemDO.getBenefitName());
        resp.setBenefitCode(itemDO.getBenefitCode());
        resp.setBenefitType(itemDO.getDeliveryType());
        resp.setItemImg(itemDO.getItemImg());
        resp.setCostPrice(BigDecimal.valueOf(itemDO.getCostPrice() / 100));
        resp.setSellingPrice(BigDecimal.valueOf(itemDO.getSellingPrice() / 100));
        resp.setSupplierId(itemDO.getSupplierId());
        resp.setSupplierName(itemDO.getSupplierName());
        resp.setSupplierGoodsParam(itemDO.getSupplierGoodsParam());
        resp.setStatus(itemDO.getStatus());
        return resp;
    }

}
