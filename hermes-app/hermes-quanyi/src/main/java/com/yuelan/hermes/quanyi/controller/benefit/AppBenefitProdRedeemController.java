package com.yuelan.hermes.quanyi.controller.benefit;

import com.yuelan.hermes.quanyi.biz.handler.CacheHandler;
import com.yuelan.hermes.quanyi.biz.service.BenefitAppUserService;
import com.yuelan.hermes.quanyi.config.satoken.StpBenefitUserUtil;
import com.yuelan.hermes.quanyi.controller.request.AppBenefitProdIRedeemDetailReq;
import com.yuelan.hermes.quanyi.controller.request.AppBenefitProdIRedeemReq;
import com.yuelan.hermes.quanyi.controller.response.BenefitProdRedeemDetailResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Tag(name = "权益N选1/客户端接口/权益包兑换api")
@Slf4j
@Validated
@RestController
@RequestMapping("/b/benefit")
@RequiredArgsConstructor
public class AppBenefitProdRedeemController {

    private final BenefitAppUserService benefitAppUserService;
    private final CacheHandler cacheHandler;

    /**
     * 权益包领取详情
     *
     * @return 返回还可以领取几个权益, 以及已经领取的权益有哪些
     */
    @Operation(summary = "权益包兑换详情", description = "返回还可以兑换几个权益 以及已经兑换的权益有哪些")
    @PostMapping("/prod/redeem/detail")
    public BizResult<BenefitProdRedeemDetailResp> getUserProdRedeemDetail(@Validated @RequestBody AppBenefitProdIRedeemDetailReq req) {
        long userId = StpBenefitUserUtil.getLoginIdAsLong();
        return BizResult.create(benefitAppUserService.userRedeemDetail(userId, req));
    }

    @Operation(summary = "权益包兑换", description = "兑换权益")
    @PostMapping("/prod/redeem")
    public BizResult<Void> prodRedeem(@Validated @RequestBody AppBenefitProdIRedeemReq req) {
        long userId = StpBenefitUserUtil.getLoginIdAsLong();
        RLock rLock = cacheHandler.getBenefitProdRedeemLock(userId);
        try {
            if (Objects.isNull(rLock)) {
                throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR, "操作过于频繁，稍后再试");
            }
            benefitAppUserService.redeemProdItems(userId, req);
        } finally {
            // 释放锁
            if (Objects.nonNull(rLock) && rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
        return BizResult.ok();
    }


}
