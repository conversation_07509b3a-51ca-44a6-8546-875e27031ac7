package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.core.validator.constraints.EnumLimit;
import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.commons.enums.SupplierEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR> 2024/4/2 16:24
 */
@Data
public class BenefitGoodsSaveReq {

    /**
     * 商品id
     */
    @NotNull(message = "商品id不能为空", groups = {EditGroup.class})
    @Schema(description = "商品id")
    private Long goodsId;

    /**
     * 商品名
     */
    @NotEmpty(message = "商品名不能为空", groups = {AddGroup.class})
    @Schema(description = "商品名")
    private String goodsName;

    /**
     * 供应商编号/ID
     */
    @NotNull(message = "供应商类型不能为空", groups = {AddGroup.class})
    @EnumLimit(message = "供应商不合法", enumInterface = SupplierEnum.class, groups = {AddGroup.class})
    @Schema(description = "供应商类型")
    private Integer supplierType;

    /**
     * 供应商名字
     */
    @NotEmpty(message = "供应商商品编号不能为空", groups = {AddGroup.class})
    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;

    /**
     * 产品图
     */
    @NotEmpty(message = "产品图不能为空", groups = {AddGroup.class})
    @Schema(description = "产品图")
    private String goodsImg;

    /**
     * 成本价格
     */
    @NotNull(message = "成本价格不能为空", groups = {AddGroup.class})
    @Schema(description = "成本价格")
    private BigDecimal costPrice;

    /**
     * 售价
     */
    @NotNull(message = "售价不能为空", groups = {AddGroup.class})
    @Schema(description = "售价")
    private BigDecimal price;


    public static BenefitGoodsDO convert(BenefitGoodsSaveReq req) {
        BenefitGoodsDO benefitGoodsDO = new BenefitGoodsDO();
        benefitGoodsDO.setGoodsId(req.getGoodsId());
        benefitGoodsDO.setGoodsName(req.getGoodsName());
        benefitGoodsDO.setSupplierGoodsNo(req.getSupplierGoodsNo());
        benefitGoodsDO.setSupplierType(req.getSupplierType());
        SupplierEnum supplierEnum = LocalEnumUtils.findByCode(SupplierEnum.class, req.getSupplierType());
        assert supplierEnum != null;
        benefitGoodsDO.setSupplierName(supplierEnum.getDesc());
        benefitGoodsDO.setGoodsImg(req.getGoodsImg());
        benefitGoodsDO.setCostPrice(req.getCostPrice());
        benefitGoodsDO.setPrice(req.getPrice());
        return benefitGoodsDO;
    }


}
