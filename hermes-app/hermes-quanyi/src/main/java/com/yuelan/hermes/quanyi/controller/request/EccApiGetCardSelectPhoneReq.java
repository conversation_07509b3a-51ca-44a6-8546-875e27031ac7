package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.RegexMatch;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR> 2024/7/22 下午7:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EccApiGetCardSelectPhoneReq extends EccGetCardApiReq {

    @Schema(description = "手机号(选号接口返回参数)")
    @RegexMatch(regex = "^1[3-9]\\d{9}$", message = "手机号格式错误")
    @NotEmpty(message = "手机号不能为空")
    private String phone;

    @NotEmpty(message = "手机号码归属地-省份代码不能为空")
    @Schema(description = "手机号码归属地-省份代码(选号接口返回参数)")
    private String provinceCode;

    @NotEmpty(message = "手机号码归属地-城市代码不能为空")
    @Schema(description = "手机号码归属地-城市代码(选号接口返回参数)")
    private String cityCode;


    @Override
    public void reqCheck() {
        super.reqCheck();
    }

    @Override
    public EccBaseGetCardReq convert(EccOuterChannelDO dbChannelDO) {
        EccOuterChannelGetCardSelectPhoneReq req = new EccOuterChannelGetCardSelectPhoneReq();
        req.setIdCardName(this.getIdCardName());
        req.setIdCardNo(idCardNo);
        req.setContactPhone(contactPhone);
        req.setPostProvinceCode(postProvinceCode);
        req.setPostProvince(postProvince);
        req.setPostCityCode(postCityCode);
        req.setPostCity(postCity);
        req.setPostDistrictCode(postDistrictCode);
        req.setPostDistrict(postDistrict);
        req.setAddress(address);
        req.setEccProdCode(eccProdCode);
        req.setPageUrl(pageUrl);
        req.setChannelId(dbChannelDO.getOuterChannelId());
        req.setChannelOrderNo(channelOrderNo);
        req.setPhone(phone);
        req.setProvinceCode(provinceCode);
        req.setCityCode(cityCode);
        req.setCallbackUrl(callbackUrl);
        return req;
    }

}
