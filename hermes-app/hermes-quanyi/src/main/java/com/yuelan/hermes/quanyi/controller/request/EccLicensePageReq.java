package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccLicenseDO;
import com.yuelan.result.entity.PageRequest;
import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2025/5/21
 * @since 2025/5/21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMapper(target = EccLicenseDO.class)
public class EccLicensePageReq extends PageRequest {
    /**
     * 资源id
     */
    @Schema(description = "授权牌id")
    private Long licenseId;

    /**
     * 授权牌名称
     */
    @Schema(description = "授权牌名称")
    private String licenseName;

    /**
     * 运营商
     */
    @Schema(description = "运营商")
    private Integer operator;

    /**
     * 资源 uri （不包含域名）
     */
    @Schema(description = "资源 uri （不包含域名）")
    private String uri;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间-开始")
    private LocalDateTime updateTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间-结束")
    private LocalDateTime updateTimeEnd;

    public Wrapper<EccLicenseDO> buildQueryWrapper() {
        return Wrappers.<EccLicenseDO>lambdaQuery()
                .eq(licenseId != null, EccLicenseDO::getLicenseId, licenseId)
                .like(licenseName != null, EccLicenseDO::getLicenseName, licenseName)
                .eq(operator != null, EccLicenseDO::getOperator, operator)
                .like(uri != null, EccLicenseDO::getUri, uri)
                .ge(createTimeStart != null, EccLicenseDO::getCreateTime, createTimeStart)
                .le(createTimeEnd != null, EccLicenseDO::getCreateTime, createTimeEnd)
                .ge(updateTime != null, EccLicenseDO::getUpdateTime, updateTime)
                .le(updateTimeEnd != null, EccLicenseDO::getUpdateTime, updateTimeEnd)
                .orderByDesc(EccLicenseDO::getLicenseId);
    }


}
