package com.yuelan.hermes.quanyi.controller.request.third;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.ThirdChannelOrderDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ThirdChannelOrderListReq extends PageRequest {

    @Schema(description = "主键ID")
    private Long thirdOrderId;

    /**
     * 我方订单号
     */
    @Schema(description = "产品订单号")
    private String orderNo;

    /**
     * 第三方同步订单号
     */
    @Schema(description = "第三方同步订单号")
    private String thirdOrderNo;

    /**
     * 支付渠道ID
     */
    @Schema(description = "支付渠道ID")
    private Integer payChannelId;

    /**
     * 渠道包唯一ID
     */
    @Schema(description = "渠道包唯一ID")
    private Integer uniqueChannelPkgId;

    /**
     * 渠道ID
     */
    @Schema(description = "渠道ID")
    private Long channelId;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String channelName;

    /**
     * 包名
     */
    @Schema(description = "包名")
    private String packageName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

    /**
     * 支付状态，1支付成功2支付失败
     */
    @Schema(description = "支付状态，1支付成功2支付失败")
    private Integer payStatus;

    /**
     * 支付消息
     */
    @Schema(description = "支付消息")
    private String payMessage;


    /**
     * 下单时间-开始
     */
    @Schema(description = "下单时间-开始")
    private LocalDateTime orderTimeStart;

    /**
     * 下单时间-结束
     */
    @Schema(description = "下单时间-结束")
    private LocalDateTime orderTimeEnd;

    /**
     * 创建时间-开始
     */
    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间-结束
     */
    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    public LambdaQueryWrapper<ThirdChannelOrderDO> buildQueryWrapper(Long startId, boolean isAsc) {
        return Wrappers.lambdaQuery(ThirdChannelOrderDO.class)
                .gt(startId != null && startId > 0, ThirdChannelOrderDO::getThirdOrderId, startId)
                .eq(StrUtil.isNotBlank(orderNo), ThirdChannelOrderDO::getOrderNo, orderNo)
                .eq(StrUtil.isNotBlank(thirdOrderNo), ThirdChannelOrderDO::getThirdOrderNo, thirdOrderNo)
                .eq(Objects.nonNull(payChannelId), ThirdChannelOrderDO::getPayChannelId, payChannelId)
                .eq(Objects.nonNull(uniqueChannelPkgId), ThirdChannelOrderDO::getUniqueChannelPkgId, uniqueChannelPkgId)
                .eq(Objects.nonNull(channelId), ThirdChannelOrderDO::getChannelId, channelId)
                .like(StrUtil.isNotBlank(channelName), ThirdChannelOrderDO::getChannelName, channelName)
                .like(StrUtil.isNotBlank(packageName), ThirdChannelOrderDO::getPackageName, packageName)
                .like(StrUtil.isNotBlank(phone), ThirdChannelOrderDO::getPhone, phone)
                .eq(Objects.nonNull(payStatus), ThirdChannelOrderDO::getPayStatus, payStatus)
                .like(StrUtil.isNotBlank(payMessage), ThirdChannelOrderDO::getPayMessage, payMessage)
                .ge(Objects.nonNull(orderTimeStart), ThirdChannelOrderDO::getOrderTime, orderTimeStart)
                .le(Objects.nonNull(orderTimeEnd), ThirdChannelOrderDO::getOrderTime, orderTimeEnd)
                .ge(Objects.nonNull(createTimeStart), ThirdChannelOrderDO::getCreateTime, createTimeStart)
                .le(Objects.nonNull(createTimeEnd), ThirdChannelOrderDO::getCreateTime, createTimeEnd)
                .orderBy(true, isAsc, ThirdChannelOrderDO::getThirdOrderId);
    }

}
