package com.yuelan.hermes.quanyi.controller.request;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yuelan.hermes.quanyi.common.pojo.domain.FileExportTaskDO;
import com.yuelan.result.entity.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR> 2024/4/2 16:24
 */

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FileExportTaskReq extends PageRequest {

    @Schema(description = "任务id")
    private Long taskId;

    public Wrapper<FileExportTaskDO> buildQueryWrapper(long adminId) {
        return Wrappers.lambdaQuery(FileExportTaskDO.class)
                .eq(adminId > 0, FileExportTaskDO::getAdminId, adminId)
                .eq(taskId != null, FileExportTaskDO::getTaskId, taskId)
                .orderByDesc(FileExportTaskDO::getTaskId);
    }


}
