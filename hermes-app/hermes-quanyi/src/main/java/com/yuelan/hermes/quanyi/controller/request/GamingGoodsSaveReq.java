package com.yuelan.hermes.quanyi.controller.request;

import com.yuelan.core.validator.constraints.EnumLimit;
import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.hermes.commons.enums.DeliveryTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 电竞卡商品管理
 */
@Data
public class GamingGoodsSaveReq {

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "商品主图")
    private String goodsImg;

    @Schema(description = "发放方式：1-直充，2-兑换码")
    @NotNull(message = "请选择发放方式", groups = AddGroup.class)
    @EnumLimit(message = "发放方式不合法", enumInterface = DeliveryTypeEnum.class)
    private Integer deliveryType;

    @Schema(description = "库存阈值")
    @Min(value = 0, message = "库存阈值不能小于0")
    private Integer stockAlert;

    @Schema(description = "供应商类型")
    private Integer supplierType;

    @Schema(description = "供应商商品编号")
    private String supplierGoodsNo;

    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "市场价")
    private BigDecimal marketPrice;

    @Schema(description = "采购价")
    private BigDecimal purchasePrice;


}