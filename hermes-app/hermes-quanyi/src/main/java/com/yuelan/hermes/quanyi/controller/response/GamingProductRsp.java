package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.result.entity.KeyValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 电竞卡产品管理
 */
@Data
public class GamingProductRsp {

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "产品主图")
    private String productImg;

    @Schema(description = "销售价")
    private BigDecimal salePrice;

    @Schema(description = "上下架状态0下架1上架")
    private KeyValue<Integer, String> status;

    @Schema(description = "产品类型1点播2权益")
    private KeyValue<Integer, String> type;

    @Schema(description = "咪咕合作模式：1-老平台 2-新平台")
    private KeyValue<Integer, String> mgModel;

}