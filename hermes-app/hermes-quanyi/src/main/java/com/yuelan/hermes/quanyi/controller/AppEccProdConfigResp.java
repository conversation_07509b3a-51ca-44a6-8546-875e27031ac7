package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.controller.response.AppEccProductPageResp;
import com.yuelan.hermes.quanyi.controller.response.EccPostAreaResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2025/5/28
 * @since 2025/5/28
 */
@Data
public class AppEccProdConfigResp {

    @Schema(description = "产品id")
    private Long prodId;

    @Schema(description = "产品名字")
    private String prodName;

    @Schema(description = "运营商：1-移动 2-联通 3-电信 4-广电")
    private Integer operator;

    @Schema(description = "是不是 zop 的产品")
    private boolean isZopProduct;

    @Schema(description = "归属地类型 0-全国 1-分省 2-分市")
    private Integer regionType;

    @Schema(description = "归属地列表")
    private List<EccPostAreaResp> regionList;

    @Schema(description = "落地页配置")
    private AppEccProductPageResp pageConfig;

}
