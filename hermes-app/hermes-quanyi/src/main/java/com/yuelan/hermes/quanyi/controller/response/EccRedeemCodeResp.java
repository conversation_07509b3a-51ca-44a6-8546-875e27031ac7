package com.yuelan.hermes.quanyi.controller.response;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccRedeemCodeDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR> 2024/5/3 下午2:20
 */
@Data
public class EccRedeemCodeResp {

    /**
     * 主键
     */
    @Schema(description = "兑换码id")
    private Long codeId;

    /**
     * 关联商品id
     */
    @Schema(description = "商品id")
    private Long goodsId;

    @Schema(description = "商品名称")
    private String goodsName;

    /**
     * 供应商唯一编号
     */
    @Schema(description = "供应商唯一编号")
    private Integer supplierId;

    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 兑换码
     */
    @Schema(description = "兑换码")
    private String redeemCodeKey;

    /**
     * 兑换码对应密码
     */
    @Schema(description = "兑换码对应密码")
    private String redeemCodePwd;

    /**
     * 兑换码对应密码
     */
    @Schema(description = "过期时间（包含）")
    private LocalDate expireTime;

    /**
     * 兑换是否已经使用：0-未使用；1-已使用
     */
    @Schema(description = "兑换是否已经使用：0-未使用；1-已使用")
    private Integer redeemCodeStatus;


    public static EccRedeemCodeResp buildResp(EccRedeemCodeDO redeemCodeDO) {
        if (redeemCodeDO == null) {
            return null;
        }
        EccRedeemCodeResp resp = new EccRedeemCodeResp();
        resp.setCodeId(redeemCodeDO.getCodeId());
        resp.setGoodsId(redeemCodeDO.getGoodsId());
        resp.setGoodsName(redeemCodeDO.getGoodsName());
        resp.setSupplierId(redeemCodeDO.getSupplierId());
        resp.setSupplierName(redeemCodeDO.getSupplierName());
        resp.setRedeemCodeKey(redeemCodeDO.getRedeemCodeKey());
        resp.setRedeemCodePwd(redeemCodeDO.getRedeemCodePwd());
        resp.setExpireTime(redeemCodeDO.getExpireTime());
        resp.setRedeemCodeStatus(redeemCodeDO.getRedeemCodeStatus());
        return resp;

    }
}
