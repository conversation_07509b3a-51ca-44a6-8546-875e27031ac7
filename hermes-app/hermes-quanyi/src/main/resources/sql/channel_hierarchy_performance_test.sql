-- 渠道层级查询性能测试 SQL
-- 用于直接在数据库中测试递归 CTE 的性能

-- 1. 测试查询渠道的所有上级渠道链
-- 替换 @channelId 为实际的渠道ID
SET @channelId = 1;

SELECT 
  '=== 上级渠道链查询测试 ===' as test_name,
  @channelId as target_channel_id;

WITH RECURSIVE parent_chain AS (
  -- 基础查询：获取当前渠道的直接父级
  SELECT 
    p.outer_channel_id, p.channel_name, p.api_key, p.api_secret, p.is_disabled, 
    p.ip_whitelist, p.zop_referrer_code, p.parent_channel_id, p.channel_level, p.deduction_rate, 
    p.create_time, p.update_time, p.deleted,
    1 as level
  FROM ecc_outer_channel p
  INNER JOIN ecc_outer_channel c ON c.parent_channel_id = p.outer_channel_id
  WHERE c.outer_channel_id = @channelId AND p.deleted = 0
  
  UNION ALL
  
  -- 递归查询：获取上级的上级
  SELECT 
    p.outer_channel_id, p.channel_name, p.api_key, p.api_secret, p.is_disabled,
    p.ip_whitelist, p.zop_referrer_code, p.parent_channel_id, p.channel_level, p.deduction_rate,
    p.create_time, p.update_time, p.deleted,
    pc.level + 1
  FROM ecc_outer_channel p
  INNER JOIN parent_chain pc ON pc.parent_channel_id = p.outer_channel_id
  WHERE p.deleted = 0 AND pc.level < 10  -- 防止无限递归，最多10层
)
SELECT 
  outer_channel_id, channel_name, parent_channel_id, channel_level, level as hierarchy_level
FROM parent_chain
ORDER BY level ASC;  -- 按层级从低到高排序

-- 2. 测试查询渠道的所有下级渠道
-- 替换 @parentChannelId 为实际的父渠道ID
SET @parentChannelId = 1;

SELECT 
  '=== 下级渠道查询测试 ===' as test_name,
  @parentChannelId as parent_channel_id;

WITH RECURSIVE descendant_tree AS (
  -- 基础查询：获取直接子渠道
  SELECT 
    outer_channel_id, channel_name, api_key, api_secret, is_disabled,
    ip_whitelist, zop_referrer_code, parent_channel_id, channel_level, deduction_rate,
    create_time, update_time, deleted,
    1 as level
  FROM ecc_outer_channel
  WHERE parent_channel_id = @parentChannelId AND deleted = 0
  
  UNION ALL
  
  -- 递归查询：获取子级的子级
  SELECT 
    c.outer_channel_id, c.channel_name, c.api_key, c.api_secret, c.is_disabled,
    c.ip_whitelist, c.zop_referrer_code, c.parent_channel_id, c.channel_level, c.deduction_rate,
    c.create_time, c.update_time, c.deleted,
    dt.level + 1
  FROM ecc_outer_channel c
  INNER JOIN descendant_tree dt ON dt.outer_channel_id = c.parent_channel_id
  WHERE c.deleted = 0 AND dt.level < 10  -- 防止无限递归，最多10层
)
SELECT 
  outer_channel_id, channel_name, parent_channel_id, channel_level, level as hierarchy_level
FROM descendant_tree
ORDER BY level ASC, outer_channel_id ASC;

-- 3. 性能对比：传统方式 vs 递归CTE
-- 传统方式（模拟N+1查询）
SELECT '=== 传统查询方式性能对比 ===' as comparison_test;

-- 查询某个渠道的直接子渠道（第一层）
SELECT 
  outer_channel_id, channel_name, parent_channel_id, channel_level,
  '第1层' as hierarchy_level
FROM ecc_outer_channel 
WHERE parent_channel_id = @parentChannelId AND deleted = 0;

-- 4. 检查数据完整性
SELECT '=== 数据完整性检查 ===' as integrity_check;

-- 检查是否存在循环引用
WITH RECURSIVE cycle_check AS (
  SELECT 
    outer_channel_id, 
    parent_channel_id, 
    channel_name,
    CAST(outer_channel_id AS CHAR(1000)) as path,
    1 as level
  FROM ecc_outer_channel 
  WHERE parent_channel_id IS NOT NULL AND deleted = 0
  
  UNION ALL
  
  SELECT 
    c.outer_channel_id,
    c.parent_channel_id,
    c.channel_name,
    CONCAT(cc.path, '->', c.outer_channel_id) as path,
    cc.level + 1
  FROM ecc_outer_channel c
  INNER JOIN cycle_check cc ON cc.parent_channel_id = c.outer_channel_id
  WHERE c.deleted = 0 
    AND cc.level < 20  -- 限制递归深度
    AND FIND_IN_SET(c.outer_channel_id, REPLACE(cc.path, '->', ',')) = 0  -- 检查是否已访问
)
SELECT 
  outer_channel_id,
  channel_name,
  path,
  level,
  CASE 
    WHEN level > 10 THEN '可能存在深层嵌套或循环'
    ELSE '正常'
  END as status
FROM cycle_check
WHERE level > 5  -- 只显示层级较深的记录
ORDER BY level DESC, outer_channel_id;

-- 5. 索引使用情况检查
SELECT '=== 索引使用情况检查 ===' as index_check;

EXPLAIN 
WITH RECURSIVE descendant_tree AS (
  SELECT 
    outer_channel_id, parent_channel_id, channel_name, channel_level,
    1 as level
  FROM ecc_outer_channel
  WHERE parent_channel_id = @parentChannelId AND deleted = 0
  
  UNION ALL
  
  SELECT 
    c.outer_channel_id, c.parent_channel_id, c.channel_name, c.channel_level,
    dt.level + 1
  FROM ecc_outer_channel c
  INNER JOIN descendant_tree dt ON dt.outer_channel_id = c.parent_channel_id
  WHERE c.deleted = 0 AND dt.level < 10
)
SELECT COUNT(*) FROM descendant_tree;
