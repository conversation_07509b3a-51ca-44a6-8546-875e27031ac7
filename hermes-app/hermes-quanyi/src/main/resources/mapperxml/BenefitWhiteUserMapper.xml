<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitWhiteUserMapper">
    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitWhiteUserDO">
        <id column="id" property="id"/>
        <result column="phone" property="phone"/>
        <result column="prod_id" property="prodId"/>
        <result column="prod_name" property="prodName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="status" property="status"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , phone, prod_id,prod_name, create_time, update_time, deleted, `status`
    </sql>
</mapper>