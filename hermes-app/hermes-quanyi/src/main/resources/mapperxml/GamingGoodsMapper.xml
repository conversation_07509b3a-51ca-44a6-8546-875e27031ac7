<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GamingGoodsMapper">

    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.GamingGoodsDO">
        <!--@mbg.generated-->
        <!--@Table gaming_goods-->
        <id column="goods_id" jdbcType="BIGINT" property="goodsId" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="goods_img" jdbcType="VARCHAR" property="goodsImg" />
        <result column="delivery_type" jdbcType="TINYINT" property="deliveryType" />
        <result column="stock_alert" jdbcType="INTEGER" property="stockAlert" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="sale_price" jdbcType="DECIMAL" property="salePrice" />
        <result column="market_price" jdbcType="DECIMAL" property="marketPrice" />
        <result column="purchase_price" jdbcType="DECIMAL" property="purchasePrice" />
        <result column="supplier_type" jdbcType="TINYINT" property="supplierType" />
        <result column="supplier_goods_no" jdbcType="VARCHAR" property="supplierGoodsNo" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="deleted" jdbcType="TINYINT" property="deleted" />
    </resultMap>

    <resultMap id="GamingGoodsAndStockMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.GamingGoodsDO" extends="BaseResultMap">
      <association property="stock" javaType="com.yuelan.hermes.quanyi.common.pojo.bo.GamingGoodsStockDTO">
          <result column="available_stock" jdbcType="BIGINT" property="availableStock" />
          <result column="near_expiry_stock" jdbcType="BIGINT" property="nearExpiryStock" />
          <result column="expired_stock" jdbcType="BIGINT" property="expiredStock" />
          <result column="used_stock" jdbcType="BIGINT" property="usedStock" />
      </association>
    </resultMap>


    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        goods_id, goods_name, goods_img, delivery_type, stock_alert, `status`, sale_price, market_price,
        purchase_price, supplier_type, supplier_goods_no, create_time, update_time, deleted
    </sql>

    <sql id="Join_Column_List">
        <!--@mbg.generated-->
        gg.goods_id,gg.goods_name,gg.goods_img,gg.delivery_type,gg.stock_alert,gg.`status`,gg.sale_price,gg.market_price,gg.purchase_price,
        gg.supplier_type,gg.supplier_goods_no,gg.create_time,gg.update_time,gg.deleted
    </sql>

    <insert id="batchInsert" keyColumn="goods_id" keyProperty="goodsId" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into gaming_goods
        (goods_name, goods_img, delivery_type, `status`, sale_price, market_price, purchase_price, supplier_type,
        supplier_goods_no, create_time, update_time, deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.goodsName}, #{item.goodsImg}, #{item.deliveryType}, #{item.status}, #{item.salePrice}, #{item.marketPrice},
            #{item.purchasePrice}, #{item.supplierType}, #{item.supplierGoodsNo}, #{item.createTime},
            #{item.updateTime}, #{item.deleted})
        </foreach>
    </insert>

    <select id="search" resultType="com.yuelan.hermes.quanyi.controller.response.GamingGoodsSearchRsp">
        SELECT goods_id, goods_name
        FROM
        ( SELECT
        <include refid="Base_Column_List"/>
        FROM gaming_goods
        <where>
            <if test="goodsId != null">goods_id = #{goodsId}</if>
            <if test="goodsName != null and goodsName != ''">
                or `goods_name` like concat('%', #{goodsName}, '%')
            </if>
        </where>
        LIMIT 50
        ) t
        WHERE t.deleted = 0
    </select>

    <select id="findByProductId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingGoodsDO">
        select t2.goods_id, t2.goods_name, t2.goods_img, t2.`status`, t2.sale_price, t2.market_price, t2.purchase_price,
        t2.supplier_type, t2.supplier_goods_no, t2.create_time, t2.update_time, t2.deleted
        from gaming_product_item t1
        left join gaming_goods t2 on t1.goods_id=t2.goods_id and t2.deleted = 0
        where t1.product_id =#{productId}
        and t1.deleted = 0
    </select>

    <sql id="GamingGoodsReqWhere">
        <where>
            <if test="req.goodsId != null">and goods_id = #{req.goodsId}</if>
            <if test="req.goodsName != null and req.goodsName != ''">and goods_name like concat('%', #{req.goodsName}, '%')</if>
            <if test="req.status != null">and status = #{req.status}</if>
            <if test="req.supplierType != null">and supplier_type = #{req.supplierType}</if>
            <if test="req.supplierGoodsNo != null and req.supplierGoodsNo != ''">
                and supplier_goods_no = #{req.supplierGoodsNo}
            </if>
            <if test="req.deliveryType !=null">
                and delivery_type = #{req.deliveryType}
            </if>
            and deleted = 0
        </where>
    </sql>

    <sql id="JoinGamingGoodsReqWhere">
        <where>
            <if test="req.goodsId != null">and gg.goods_id = #{req.goodsId}</if>
            <if test="req.goodsName != null and req.goodsName != ''">and gg.goods_name like concat('%', #{req.goodsName}, '%')</if>
            <if test="req.status != null">and gg.status = #{req.status}</if>
            <if test="req.supplierType != null">and gg.supplier_type = #{req.supplierType}</if>
            <if test="req.supplierGoodsNo != null and req.supplierGoodsNo != ''">
                and gg.supplier_goods_no = #{req.supplierGoodsNo}
            </if>
            <if test="req.deliveryType !=null">
                and gg.delivery_type = #{req.deliveryType}
            </if>
            and gg.deleted = 0
        </where>
    </sql>

    <select id="pageByGamingGoodsReq" resultMap="GamingGoodsAndStockMap">
        select
        <include refid="Join_Column_List"/>
        , CASE WHEN gg.delivery_type = 2
                  THEN IFNULL(gc.available_stock, 0)
            END AS available_stock
        , CASE WHEN gg.delivery_type = 2
                  THEN IFNULL(gc.near_expiry_stock, 0)
            END AS near_expiry_stock
        , CASE WHEN gg.delivery_type = 2
                 THEN IFNULL(gc.expired_stock, 0)
            END AS expired_stock
        , CASE WHEN gg.delivery_type = 2
                  THEN IFNULL(gc.used_stock, 0)
            END AS used_stock
        from gaming_goods gg
                 LEFT JOIN(SELECT goods_id,
                                  COUNT(CASE WHEN used = 0 AND expire_date &gt;= CURRENT_DATE THEN 1 END)                                   AS available_stock,
                                  COUNT(CASE
                                            WHEN used = 0 AND
                                                    expire_date BETWEEN CURRENT_DATE AND DATE_ADD(CURRENT_DATE, INTERVAL 30 DAY)
                                                THEN 1 END)                                                            AS near_expiry_stock,
                                  COUNT(CASE WHEN used = 0 AND expire_date &lt; CURRENT_DATE THEN 1 END) AS expired_stock,
                                  COUNT(CASE WHEN used = 1 THEN 1 END)                                   AS used_stock
                           FROM gaming_redeem_code
                           WHERE deleted = 0
                           GROUP BY goods_id) gc ON gg.goods_id = gc.goods_id
        <include refid="JoinGamingGoodsReqWhere"/>
        <if test="req.stockAlertStatus != null and req.stockAlertStatus == 1 ">
            and available_stock is not null
            and gg.stock_alert is not null
            and available_stock &lt;= gg.stock_alert
        </if>
        order by gg.goods_id desc
        limit #{req.offset},#{req.size}
    </select>

    <select id="countByGamingGoodsReq" resultType="java.lang.Long">
        select
            count(1)
        from gaming_goods gg
                 LEFT JOIN(SELECT goods_id,
                                  COUNT(CASE WHEN used = 0 AND expire_date &gt;= CURRENT_DATE  THEN 1 END)                                   AS available_stock,
                                  COUNT(CASE
                                            WHEN used = 0 AND
                                                    expire_date BETWEEN CURRENT_DATE AND DATE_ADD(CURRENT_DATE, INTERVAL 30 DAY)
                                                THEN 1 END)                                                            AS near_expiry_stock,
                                  COUNT(CASE WHEN used = 0 AND expire_date &lt; CURRENT_DATE THEN 1 END) AS expired_stock,
                                  COUNT(CASE WHEN used = 1 THEN 1 END)                                   AS used_stock
                           FROM gaming_redeem_code
                           WHERE deleted = 0
                           GROUP BY goods_id) gc ON gg.goods_id = gc.goods_id
        <include refid="JoinGamingGoodsReqWhere"/>
        <if test="req.stockAlertStatus != null and req.stockAlertStatus == 1 ">
            and available_stock is not null
            and gg.stock_alert is not null
            and available_stock &lt;= gg.stock_alert
        </if>
    </select>

    <select id="findByGoodsId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingGoodsDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_goods
        where goods_id = #{goodsId}
        and deleted = 0
    </select>

    <update id="removeById">
        update gaming_goods
        set deleted = 1,
        update_time = now()
        where goods_id = #{goodsId}
        and deleted = 0
    </update>

    <update id="updateStatus">
        update gaming_goods
        set `status` = #{status},
        update_time = now()
        where goods_id = #{goodsId}
        and deleted = 0
    </update>

    <select id="listOptions" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingGoodsDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_goods gg
        <include refid="JoinGamingGoodsReqWhere"/>
        and deleted = 0
    </select>
</mapper>