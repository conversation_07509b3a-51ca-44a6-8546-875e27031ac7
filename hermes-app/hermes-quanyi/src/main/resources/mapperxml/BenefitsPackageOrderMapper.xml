<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitsPackageOrderMapper">
    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageOrderDO">
        <!--@mbg.generated-->
        <!--@Table benefits_package_order-->
        <id column="package_order_id" jdbcType="BIGINT" property="packageOrderId" />
        <result column="extension_id" jdbcType="BIGINT" property="extensionId" />
        <result column="package_id" jdbcType="BIGINT" property="packageId" />
        <result column="package_name" jdbcType="VARCHAR" property="packageName" />
        <result column="package_code" jdbcType="VARCHAR" property="packageCode" />
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="selling_price" jdbcType="INTEGER" property="sellingPrice" />
        <result column="optional_ids" jdbcType="VARCHAR" property="optionalIds" />
        <result column="optional_count" jdbcType="INTEGER" property="optionalCount" />
        <result column="optional_dispatch_count" jdbcType="INTEGER" property="optionalDispatchCount" />
        <result column="instant_ids" jdbcType="VARCHAR" property="instantIds" />
        <result column="instant_count" jdbcType="INTEGER" property="instantCount" />
        <result column="instant_dispatch_count" jdbcType="INTEGER" property="instantDispatchCount" />
        <result column="max_selectable" jdbcType="INTEGER" property="maxSelectable" />
        <result column="dispatch_error_count" jdbcType="INTEGER" property="dispatchErrorCount" />
        <result column="redemption_deadline" jdbcType="TIMESTAMP" property="redemptionDeadline" />
        <result column="is_refunded" jdbcType="TINYINT" property="isRefunded" />
        <result column="refunded_time" jdbcType="TIMESTAMP" property="refundedTime" />
        <result column="is_valid" jdbcType="TINYINT" property="isValid" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        package_order_id, extension_id, package_id, package_name, package_code, mobile, selling_price,
        optional_ids, optional_count, optional_dispatch_count, instant_ids, instant_count,
        instant_dispatch_count, max_selectable, dispatch_error_count, redemption_deadline,
        is_refunded, refunded_time, is_valid, create_time, update_time
    </sql>

    <update id="updateDispatchInfoById">
        UPDATE benefits_package_order
        <set>
            <if test="dispatchErrorCountIncrement != null and dispatchErrorCountIncrement != 0">
                dispatch_error_count = dispatch_error_count + #{dispatchErrorCountIncrement},
            </if>
            <if test="instantDispatchCountIncrement != null and instantDispatchCountIncrement != 0">
                instant_dispatch_count = instant_dispatch_count + #{instantDispatchCountIncrement},
            </if>
            <if test="optionalDispatchCountIncrement != null and optionalDispatchCountIncrement != 0">
                optional_dispatch_count = optional_dispatch_count + #{optionalDispatchCountIncrement},
            </if>
        </set>
        WHERE package_order_id = #{packageOrderId}
    </update>

    <update id="updateRefundStatusByIds">
        UPDATE benefits_package_order
        <set>
            is_refunded = 1,
            refunded_time = NOW()
        </set>
        WHERE package_order_id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_refunded = 0 AND is_valid = 1 and redemption_deadline > NOW()
    </update>
</mapper>