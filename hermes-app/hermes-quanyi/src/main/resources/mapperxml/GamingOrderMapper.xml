<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GamingOrderMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        order_id, order_no, out_order_no, merchant_id, merchant_name, out_user_id, phone,
        product_id, product_code, product_name, order_amount, order_status, sms_send_status,
        sms_send_response, notify_status, obtain_status_notify, monthly_type, attach, out_order_time,
        create_time, update_time, deleted
    </sql>
    <insert id="batchInsert" keyColumn="order_id" keyProperty="orderId" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into gaming_order
        (order_no, out_order_no, merchant_id, merchant_name, out_user_id, phone,
        product_id, product_code, product_name, order_amount, order_status, sms_send_status,
        sms_send_response, notify_status, obtain_status_notify, monthly_type, attach, out_order_time,
        create_time, update_time, deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.orderNo}, #{item.outOrderNo}, #{item.merchantId}, #{item.merchantName}, #{item.outUserId}, #{item.phone},
            #{item.productId}, #{item.productCode}, #{item.productName}, #{item.orderAmount},#{item.orderStatus},#{item.smsSendStatus},
            #{item.smsSendResponse}, #{item.notifyStatus},#{item.obtainStatusNotify},#{item.monthlyType}, #{item.attach},  #{item.outOrderTime}、
            ,#{item.createTime}, #{item.updateTime}, #{item.deleted})
        </foreach>
    </insert>

    <select id="findByMerchantOrder" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_order
        where merchant_id =#{merchantId}
        and out_order_no =#{outOrderNo}
        and deleted = 0
    </select>

    <sql id="GamingOrderListReqWhere">
        <where>
            <if test="req.orderNo != null and req.orderNo != ''">and order_no = #{req.orderNo}</if>
            <if test="req.orderIds != null">
                and order_id in
                <foreach close=")" collection="req.orderIds" item="orderId" open="(" separator=",">
                    #{orderId}
                </foreach>
            </if>
            <if test="req.productIds != null">
                and product_id in
                <foreach close=")" collection="req.productIds" item="productId" open="(" separator=",">
                    #{productId}
                </foreach>
            </if>
            <if test="req.merchantId != null">and merchant_id = #{req.merchantId}</if>
            <if test="req.outOrderNo != null and req.outOrderNo != ''">and out_order_no = #{req.outOrderNo}</if>
            <if test="req.phone != null and req.phone != ''">and phone = #{req.phone}</if>
            <if test="req.productId != null">and product_id = #{req.productId}</if>
            <if test="req.orderStatus != null">and order_status = #{req.orderStatus}</if>
            <if test="req.notifyStatus != null">and notify_status = #{req.notifyStatus}</if>
            <if test="req.monthlyType != null">and monthly_type = #{req.monthlyType}</if>
            <if test="req.orderStartTime != null">and create_time <![CDATA[ >= ]]> #{req.orderStartTime}</if>
            <if test="req.orderEndTime != null">and create_time <![CDATA[ <= ]]> #{req.orderEndTime}</if>
            <if test="req.updateStartTime != null">and update_time <![CDATA[ >= ]]> #{req.updateStartTime}</if>
            <if test="req.updateEndTime != null">and update_time <![CDATA[ <= ]]> #{req.updateEndTime}</if>
            <if test="req.startId !=null "> and order_id <![CDATA[ > ]]>  #{req.startId} </if>
            and deleted = 0
        </where>
    </sql>

    <select id="pageByGamingOrderListReq" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_order
        <include refid="GamingOrderListReqWhere"/>
        <if test="req.asc != null and req.asc == true">
            order by order_id
        </if>
       <if test="req.asc == null or req.asc == false">
            order by order_id desc
        </if>
        limit #{req.offset},#{req.size}
    </select>

    <select id="countByGamingOrderListReq" resultType="java.lang.Long">
        select count(*)
        from gaming_order
        <include refid="GamingOrderListReqWhere"/>
    </select>

    <select id="cursorByGamingOrderListReq" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_order
        <include refid="GamingOrderListReqWhere"/>
        <if test="maxId != null">and order_id <![CDATA[ > ]]> #{maxId}</if>
        order by order_id
        limit 500
    </select>

    <select id="findByOrderNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_order
        where order_no=#{orderNo}
        and deleted = 0
    </select>

    <select id="findByOrderId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_order
        where order_id=#{orderId}
        and deleted = 0
    </select>

    <update id="updateStatus">
        update gaming_order
        set order_status = #{status},
        update_time = now()
        where order_id=#{orderId}
        and deleted = 0
    </update>

    <update id="updateNotifyStatus">
        update gaming_order
        set notify_status = #{notifyStatus},
        update_time = now()
        where order_id=#{orderId}
        and deleted = 0
    </update>

    <select id="gamingOrderCountByPhone" resultType="int">
        select count(1)
        from gaming_order
        where phone = #{phone}
        and deleted = 0
    </select>

    <select id="listByPhone" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_order
        where phone = #{phone}
        AND create_time <![CDATA[ >= ]]> #{start}
        AND create_time <![CDATA[ <= ]]> #{end}
        and deleted = 0
    </select>
</mapper>