<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GamingRedeemCodeDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.GamingRedeemCodeDO">
    <!--@mbg.generated-->
    <!--@Table gaming_redeem_code-->
    <id column="code_id" jdbcType="BIGINT" property="codeId" />
    <result column="goods_id" jdbcType="BIGINT" property="goodsId" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="item_no" jdbcType="VARCHAR" property="itemNo" />
    <result column="redeem_code_key" jdbcType="VARCHAR" property="redeemCodeKey" />
    <result column="redeem_code_pwd" jdbcType="VARCHAR" property="redeemCodePwd" />
    <result column="expire_date" jdbcType="DATE" property="expireDate" />
    <result column="used" jdbcType="INTEGER" property="used" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    code_id, goods_id, supplier_id, item_no, redeem_code_key, redeem_code_pwd, expire_date,
    used, create_time, update_time, deleted
  </sql>

    <update id="updateOneUsedByGoodsIdAndValidity">
      UPDATE gaming_redeem_code
      SET used = 1,
      item_no            = #{itemNo},
      update_time        = now()
      WHERE used = 0
      AND goods_id = #{goodsId}
      AND (expire_date &gt;= CURDATE() OR expire_date IS NULL)
      ORDER BY expire_date IS NULL, expire_date
      LIMIT 1
    </update>

  <insert id="ignoreUniqueBatchInsert">
    insert ignore into gaming_redeem_code
    (batch_id, goods_id, supplier_id, item_no, redeem_code_key, redeem_code_pwd, expire_date, create_time)
    values
    <foreach collection="list" item="item" separator="," >
      (#{item.batchId}, #{item.goodsId}, #{item.supplierId}, #{item.itemNo}, #{item.redeemCodeKey}, #{item.redeemCodePwd}, #{item.expireDate}, now())
    </foreach>
  </insert>
</mapper>