<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.MerchantAccountMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, merchant_id, balance, total_in, total_out, create_time, update_time, deleted
    </sql>

    <select id="findByMerchantId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.MerchantAccountDO">
        select
        <include refid="Base_Column_List"/>
        from merchant_account
        where merchant_id =#{merchantId}
        and deleted =0
    </select>

    <update id="deduct">
        <selectKey keyColumn="balance" keyProperty="balance" order="AFTER" resultType="java.math.BigDecimal">
            SELECT (SELECT balance FROM merchant_account WHERE merchant_id = #{merchantId}) account_balance
            from DUAL
        </selectKey>
        update merchant_account
        set balance = balance - #{balance},
        total_out = total_out + #{balance},
        update_time = now()
        where merchant_id = #{merchantId}
        and balance &gt;= #{balance}
        and deleted = 0
    </update>

    <update id="refund">
        <selectKey keyColumn="balance" keyProperty="balance" order="AFTER" resultType="java.math.BigDecimal">
            SELECT (SELECT balance FROM merchant_account WHERE merchant_id = #{merchantId}) account_balance
            from DUAL
        </selectKey>
        update merchant_account
        set balance = balance + #{balance},
        total_out = total_out - #{balance},
        update_time = now()
        where merchant_id = #{merchantId}
        and deleted = 0
    </update>

    <update id="pay">
        <selectKey keyColumn="balance" keyProperty="balance" order="AFTER" resultType="java.math.BigDecimal">
            SELECT (SELECT balance FROM merchant_account WHERE merchant_id = #{merchantId}) account_balance
            from DUAL
        </selectKey>
        update merchant_account
        set balance = balance + #{balance},
        total_in = total_in + #{balance},
        update_time = now()
        where merchant_id = #{merchantId}
        and deleted = 0
    </update>

    <sql id="MchAccountListReqWhere">
        <where>
            <if test="merchantId != null">t1.id = #{merchantId}</if>
            and t1.deleted =0
        </where>
    </sql>

    <select id="pageByMchAccountListReq" resultType="com.yuelan.hermes.quanyi.controller.response.MchAccountListRsp">
        select
        t1.id as merchantId,
        t1.mch_id as mchId,
        t1.name as mchName,
        t2.balance as amountBalance,
        t2.total_in as totalIn,
        t2.total_out as totalOut,
        t1.create_time as createTime
        from merchant t1
        left join merchant_account t2 on t1.id = t2.merchant_id
        <include refid="MchAccountListReqWhere"/>
        order by t1.id desc
        limit #{offset},#{size}
    </select>

    <select id="countByMchAccountListReq" resultType="java.lang.Long">
        select count(*)
        from merchant t1
        left join merchant_account t2 on t1.id = t2.merchant_id
        <include refid="MchAccountListReqWhere"/>
    </select>
</mapper>