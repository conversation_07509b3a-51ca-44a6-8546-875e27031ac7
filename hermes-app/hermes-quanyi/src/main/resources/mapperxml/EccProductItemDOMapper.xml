<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccProductItemDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccProductItemDO">
    <!--@mbg.generated-->
    <!--@Table ecc_product_item-->
    <id column="prod_item_id" jdbcType="BIGINT" property="prodItemId" />
    <result column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="goods_id" jdbcType="BIGINT" property="goodsId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    prod_item_id, prod_id, goods_id, create_time, update_time, deleted
  </sql>
</mapper>