<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.OrderMobileMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, order_no, out_trade_no, item_no, merchant_id, merchant_name, mch_id, mobile,
        sp, recharge_amount, goods_id, goods_name, sku_id, sku_no, sku_name, order_amount,
        order_status, has_slow, finish_time, notify_url, notify_status, voucher, create_time,
        update_time, deleted
    </sql>

    <select id="findByOrderNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderMobileDO">
        select
        <include refid="Base_Column_List"/>
        from order_mobile
        where order_no = #{orderNo}
        and deleted = 0
    </select>

    <select id="findByRechargerOrderDetailReq" resultType="com.yuelan.hermes.quanyi.controller.response.RechargerOrderDetailRsp">
        select order_no,out_trade_no,sku_no,mobile,recharge_amount as amount,finish_time,order_status as status
        from order_mobile
        where mch_id = #{mchId}
        <if test="orderNo != null and orderNo != ''">and order_no = #{orderNo}</if>
        <if test="outTradeNo != null and outTradeNo != ''">and out_trade_no = #{outTradeNo}</if>
        and deleted = 0
        limit 1
    </select>

    <select id="findByOutTradeNoAndMchId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderMobileDO">
        select
        <include refid="Base_Column_List"/>
        from order_mobile
        where out_trade_no = #{outTradeNo}
        and mch_id = #{mchId}
        and deleted = 0
    </select>

    <select id="countByOrderMobileListReq" resultType="java.lang.Long">
        select count(*)
        from order_mobile
        <include refid="OrderMobileListReqWhere"/>
    </select>

    <select id="pageByOrderMobileListReq" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderMobileDO">
        select
        <include refid="Base_Column_List"/>
        from order_mobile
        <include refid="OrderMobileListReqWhere"/>
        order by id desc
        limit #{req.offset},#{req.size}
    </select>
    <sql id="OrderMobileListReqWhere">
        <where>
            <if test="req.orderIds != null and req.orderIds.size() &gt; 0">
                id in
                <foreach close=")" collection="req.orderIds" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.orderNo != null and req.orderNo != ''">and order_no = #{req.orderNo}</if>
            <if test="req.outTradeNo != null and req.outTradeNo != ''">
                and out_trade_no = #{req.outTradeNo}
            </if>
            <if test="req.merchantId != null">and merchant_id = #{req.merchantId}</if>
            <if test="req.sp != null">and sp = #{req.sp}</if>
            <if test="req.mobile != null and req.mobile != ''">and mobile =#{req.mobile}</if>
            <if test="req.skuNo != null and req.skuNo != ''">and sku_no =#{req.skuNo}</if>
            <if test="req.orderStartTime != null">
                and create_time <![CDATA[ >= ]]> #{req.orderStartTime}
            </if>
            <if test="req.orderEndTime != null">
                and create_time <![CDATA[ <= ]]> #{req.orderEndTime}
            </if>
            <if test="req.orderStatus != null">and order_status =#{req.orderStatus}</if>
            <if test="req.hasSlow != null">and has_slow = #{req.hasSlow}</if>
            <if test="req.notifyStatus != null">and notify_status = #{req.notifyStatus}</if>
            and deleted = 0
        </where>
    </sql>

    <select id="cursorByOrderMobileListReq" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderMobileDO">
        select
        <include refid="Base_Column_List"/>
        from order_mobile
        <include refid="OrderMobileListReqWhere"/>
        <if test="maxId != null">and id <![CDATA[ > ]]> #{maxId}</if>
        order by id
        limit 500
    </select>

    <update id="updateItemNo">
        update order_mobile
        set item_no =#{itemNo},
        <if test="orderStatus != null">order_status =#{orderStatus},</if>
        <if test="orderStatus == 2">finish_time = now(),</if>
        update_time =now()
        where id = #{orderId}
    </update>

    <update id="updateNotifyStatus">
        update order_mobile
        set notify_status =#{notifyStatus},
        update_time =now()
        where id = #{orderId}
    </update>

    <update id="updateStatus">
        update order_mobile
        <set>
            order_status =#{orderStatus},
            <if test="finishTime != null">finish_time=#{finishTime},</if>
            <if test="voucher != null and voucher != ''">voucher=#{voucher},</if>
            <if test="sp != null">sp=#{sp},</if>
            update_time =now()
        </set>
        where id = #{orderId}
    </update>

    <select id="existByAbnormal" resultType="java.lang.Integer">
        select 1
        from order_mobile
        where order_status = 3
          and deleted = 0 limit 1
    </select>

</mapper>