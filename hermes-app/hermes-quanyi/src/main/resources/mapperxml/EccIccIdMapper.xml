<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccIccIdMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccIccIdDO">
    <!--@mbg.generated-->
    <!--@Table ecc_iccid-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="iccid" jdbcType="VARCHAR" property="iccId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="batch_number" jdbcType="VARCHAR" property="batchId" />
    <result column="validity_period" jdbcType="DATE" property="validityPeriod" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, iccid, channel_id, product_id, batch_number, validity_period, create_time, update_time
  </sql>
</mapper>
