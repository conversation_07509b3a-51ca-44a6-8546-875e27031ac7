<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GoodsVirtualStockMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, sku_id, real_amount, sell_amount, disable_amount, wait_expires_amount, expires_amount,
        warn_amount, create_time, update_time, deleted
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into goods_virtual_stock
        (sku_id, real_amount, sell_amount, disable_amount, wait_expires_amount, expires_amount,warn_amount,
        create_time, update_time, deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.skuId}, #{item.realAmount}, #{item.sellAmount}, #{item.disableAmount}, #{item.waitExpiresAmount},
            #{item.expiresAmount}, #{item.warnAmount}, #{item.createTime}, #{item.updateTime}, #{item.deleted})
        </foreach>
    </insert>


    <update id="updateRealAmount">
        update goods_virtual_stock
        set real_amount = real_amount + #{realAmount},
        update_time = now()
        where sku_id = #{skuId}
    </update>

    <select id="findBySkuIdIn" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualStockDO">
        select
        <include refid="Base_Column_List"/>
        from goods_virtual_stock
        where sku_id in
        <foreach close=")" collection="skuIds" item="skuId" open="(" separator=",">
            #{skuId}
        </foreach>
    </select>

    <select id="findBySkuId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualStockDO">
        select
        <include refid="Base_Column_List"/>
        from goods_virtual_stock
        where sku_id =#{skuId}
    </select>

    <update id="deduction">
        update goods_virtual_stock
        set real_amount = real_amount - #{amount},
        sell_amount = sell_amount + #{amount},
        update_time = now()
        where sku_id = #{skuId}
        and real_amount-disable_amount-expires_amount  <![CDATA[ >= ]]> #{amount}
    </update>

    <select id="cursorByStockAlarm" resultType="java.lang.Long">
        select sku_id
        from goods_virtual_stock
        <where>
            <if test="minId != null">id <![CDATA[ > ]]>#{minId}</if>
            and warn_amount &gt; 0
            and deleted = 0
        </where>
        limit 1000
    </select>

    <update id="updateStatisticsAmountBySkuId">
        update goods_virtual_stock
        <set>
            <if test="realAmount != null">real_amount = #{realAmount},</if>
            <if test="realAmount != null">sell_amount = #{sellAmount},</if>
            <if test="realAmount != null">expires_amount = #{expiresAmount},</if>
            <if test="waitExpiresAmount != null">wait_expires_amount = #{waitExpiresAmount},</if>
            <if test="disableAmount != null">disable_amount = #{disableAmount},</if>
            update_time = now()
        </set>
        where sku_id = #{skuId}
    </update>

    <update id="updateDisableAmount">
        update goods_virtual_stock
        <set>
            disable_amount = disable_amount+#{disableAmount},
            update_time = now()
        </set>
        where sku_id = #{skuId}
    </update>
</mapper>