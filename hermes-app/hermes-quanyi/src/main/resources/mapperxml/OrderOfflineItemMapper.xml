<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.OrderOfflineItemMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, order_no, item_no, supplier, supplier_order_no, purchase_price, phone, user_account,
        voucher_code, voucher_password, sn_code, qr_code_url, shop_url, start_date, end_date,
        order_status, sms_status, send_time, remark, create_time, update_time, deleted
    </sql>

    <sql id="Data_Column_List">
        <include refid="Base_Column_List"/>,ext_data
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into order_offline_item
        (order_no, item_no, supplier, supplier_order_no, purchase_price, phone, user_account,
        voucher_code, voucher_password, sn_code, qr_code_url, shop_url, start_date, end_date,
        order_status, sms_status, send_time, ext_data, remark, create_time, update_time,
        deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.orderNo}, #{item.itemNo}, #{item.supplier}, #{item.supplierOrderNo}, #{item.purchasePrice},
            #{item.phone}, #{item.userAccount}, #{item.voucherCode}, #{item.voucherPassword},
            #{item.snCode}, #{item.qrCodeUrl}, #{item.shopUrl}, #{item.startDate}, #{item.endDate},
            #{item.orderStatus}, #{item.smsStatus}, #{item.sendTime}, #{item.extData}, #{item.remark},
            #{item.createTime}, #{item.updateTime}, #{item.deleted})
        </foreach>
    </insert>


    <sql id="OrderOfflineItemRsp_Column_List">
        i.id                as itemId,
        o.create_time       as orderCreateTime,
        i.order_no          as orderNo,
        i.phone             as phone,
        i.user_account      as userAccount,
        o.goods_type        as goodsType,
        o.goods_name        as goodsName,
        o.sku_name          as skuName,
        o.sku_no            as skuNo,
        i.supplier          as supplier,
        i.supplier_order_no as supplierOrderNo,
        o.buyer             as buyer,
        i.purchase_price    as purchasePrice,
        o.price             as salePrice,
        i.order_status      as orderStatus,
        i.voucher_code      as voucherCode,
        i.voucher_password  as voucherPassword,
        i.qr_code_url       as qrCodeUrl,
        i.sn_code           as snCode,
        i.start_date        as startDate,
        i.end_date          as endDate,
        i.remark            as remark
    </sql>


    <select id="cursorByOrderOfflineListReq" resultType="com.yuelan.hermes.quanyi.controller.response.OrderOfflineItemRsp">
        select
        <include refid="OrderOfflineItemRsp_Column_List"/>
        FROM order_offline_item i
        LEFT JOIN order_offline o ON i.order_no = o.order_no
        <where>
            <if test="maxId != null">and i.id <![CDATA[ > ]]> #{maxId}</if>
            <if test="req.orderNo != null and req.orderNo != ''">and i.order_no = #{req.orderNo}</if>
            and i.deleted = 0
            and o.deleted = 0
        </where>
        order by i.id
        limit 500
    </select>

    <select id="cursorByOrderOfflineBatchExportReq" resultType="com.yuelan.hermes.quanyi.controller.response.OrderOfflineItemRsp">
        select
        <include refid="OrderOfflineItemRsp_Column_List"/>
        FROM order_offline o
        LEFT JOIN order_offline_item i ON i.order_no = o.order_no and i.deleted = 0
        <where>
            <if test="maxId != null">and i.id <![CDATA[ > ]]> #{maxId}</if>
            <if test="req.orderIds != null and req.orderIds.size() &gt; 0">
                and o.id in
                <foreach close=")" collection="req.orderIds" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.orderNo != null and req.orderNo != ''">and o.order_no = #{req.orderNo}</if>
            <if test="req.goodsId != null">and o.goods_id = #{req.goodsId}</if>
            <if test="req.skuNo != null and req.skuNo != ''">and o.sku_no= #{req.skuNo}</if>
            <if test="req.buyer != null and req.buyer != ''">and o.buyer = #{req.buyer}</if>
            <if test="req.orderStartTime != null">and o.create_time <![CDATA[ >= ]]> #{req.orderStartTime}</if>
            <if test="req.orderEndTime != null">and o.create_time <![CDATA[ <= ]]> #{req.orderEndTime}</if>
            <if test="req.orderStatus != null">and o.order_status = #{req.orderStatus}</if>
            <if test="req.goodsType != null">and o.goods_type = #{req.goodsType}</if>
            and o.deleted = 0
        </where>
        order by i.id
        limit 500
    </select>

    <sql id="OrderOfflineItemListReqWhere">
        <where>
            order_no = #{orderNo}
            <if test="itemNo != null and itemNo != ''">and item_no = #{itemNo}</if>
            <if test="supplierOrderNo != null and supplierOrderNo != ''">
                and supplier_order_no = #{supplierOrderNo}
            </if>
            <if test="phone != null and phone != ''">and phone = #{phone}</if>
            <if test="userAccount != null and userAccount != ''">and user_account = #{userAccount}</if>
            <if test="voucherCode != null and voucherCode != ''">and voucher_code = #{voucherCode}</if>
            <if test="voucherPassword != null and voucherPassword != ''">
                and voucher_password = #{voucherPassword}
            </if>
            <if test="qrCodeUrl != null and qrCodeUrl != ''">and qr_code_url = #{qrCodeUrl}</if>
            <if test="orderStatus != null">and order_status = #{orderStatus}</if>
            <if test="smsStatus != null">and sms_status = #{smsStatus}</if>
            and deleted = 0
        </where>
    </sql>

    <select id="pageByOrderOfflineItemListReq" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderOfflineItemDO">
        select
        <include refid="Base_Column_List"/>
        FROM order_offline_item
        <include refid="OrderOfflineItemListReqWhere"/>
        limit #{offset},#{size}
    </select>

    <select id="countByOrderOfflineItemListReq" resultType="java.lang.Long">
        select count(*)
        FROM order_offline_item
        <include refid="OrderOfflineItemListReqWhere"/>
    </select>

    <select id="findByOrderNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderOfflineItemDO">
        select
        <include refid="Base_Column_List"/>
        FROM order_offline_item
        where order_no =#{orderNo}
        and deleted = 0
    </select>

    <update id="updateSupplierOrderNo">
        update order_offline_item
        set supplier_order_no = #{supplierOrderNo},
        update_time = now()
        where id = #{id}
        and supplier_order_no = ''
    </update>

    <select id="findByItemNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderOfflineItemDO">
        select
        <include refid="Base_Column_List"/>
        FROM order_offline_item
        where item_no =#{itemNo}
        and deleted = 0
        limit 1
    </select>

    <update id="updateItemDO">
        update order_offline_item
        <set>
            <if test="voucherCode != null and voucherCode != ''">voucher_code = #{voucherCode},</if>
            <if test="voucherPassword != null and voucherPassword != ''">voucher_password = #{voucherPassword},</if>
            <if test="snCode != null and snCode != ''">sn_code = #{snCode},</if>
            <if test="qrCodeUrl != null and qrCodeUrl != ''">qr_code_url = #{qrCodeUrl},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="extData != null and extData != ''">ext_data = #{extData},</if>
            update_time = now()
        </set>
        where id = #{id}
    </update>

    <update id="updateSms">
        update order_offline_item
        set sms_status = #{sms_status},
        send_time = #{sendTime},
        update_time = now()
        where id = #{id}
    </update>
</mapper>