<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccAreaDOMapper">

    <resultMap id="AreaListMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO">
        <id column="id_a" property="id"/>
        <result column="area_name_a" property="areaName"/>
        <result column="parent_id_a" property="parentId"/>
        <result column="num_code_a" property="numCode"/>
        <result column="post_code_a" property="postCode"/>
        <result column="level_a" property="level"/>
        <collection property="subAreaList" ofType="com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO">
            <id column="id_b" property="id"/>
            <result column="area_name_b" property="areaName"/>
            <result column="parent_id_b" property="parentId"/>
            <result column="num_code_b" property="numCode"/>
            <result column="post_code_b" property="postCode"/>
            <result column="level_b" property="level"/>
            <collection property="subAreaList" ofType="com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO">
                <id column="id_c" property="id"/>
                <result column="area_name_c" property="areaName"/>
                <result column="parent_id_c" property="parentId"/>
                <result column="num_code_c" property="numCode"/>
                <result column="post_code_c" property="postCode"/>
                <result column="level_c" property="level"/>
            </collection>
        </collection>
    </resultMap>

    <select id="listArea" parameterType="com.yuelan.hermes.quanyi.controller.request.EccAreaReq"
            resultMap="AreaListMap">
        select *,
        c.id id_c,
        c.area_name area_name_c,
        c.parent_id parent_id_c,
        c.num_code num_code_c,
        c.post_code post_code_c,
        c.level level_c
        from (select a.id id_a,
        a.area_name area_name_a,
        a.parent_id parent_id_a,
        a.num_code num_code_a,
        a.post_code post_code_a,
        a.level level_a,
        b.id id_b,
        b.area_name area_name_b,
        b.parent_id parent_id_b,
        b.num_code num_code_b,
        b.post_code post_code_b,
        b.level level_b
        from ecc_area a
        left join ecc_area b on a.parent_id = 0 and a.id = b.parent_id
        <where>
            and a.operator = #{operator}
            <if test="req.provinceId != null">
                and a.id = #{req.provinceId}
            </if>
            <if test="req.postProvinceCode != null and req.postProvinceCode != ''">
                and a.post_code = #{req.postProvinceCode}
            </if>
            <if test="req.postProvince != null and req.postProvince != ''">
                and a.area_name = #{req.postProvince}
            </if>
            <if test="req.postCityCode != null and req.postCityCode != ''">
                and b.post_code = #{req.postCityCode}
            </if>
            <if test="req.postCity != null and req.postCity != ''">
                and b.area_name = #{req.postCity}
            </if>

        </where>
        ) ab
        left join ecc_area c on ab.id_b = c.parent_id
        <where>
            <if test="req.postDistrictCode != null and req.postDistrictCode != ''">
                and c.post_code = #{req.postDistrictCode}
            </if>
            <if test="req.postDistrict != null and req.postDistrict != ''">
                and c.area_name = #{req.postDistrict}
            </if>
        </where>
        order by ab.id_a, ab.id_b, c.id
    </select>

    <select id="listProvince" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO">
        select *
        from ecc_area
        where operator = #{operator}
          AND level = 1
        order by id
    </select>

    <select id="listProvinceAndCity" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO">
        select *
        from ecc_area
        where operator = #{operator} and
        level in (1, 2)
        order by id
    </select>

    <select id="getOneAreaByNumCode" resultMap="AreaListMap">
        SELECT
            a.id AS id_a,
            a.area_name AS area_name_a,
            a.parent_id AS parent_id_a,
            a.num_code AS num_code_a,
            a.post_code AS post_code_a,
            a.level AS level_a,
            b.id AS id_b,
            b.area_name AS area_name_b,
            b.parent_id AS parent_id_b,
            b.num_code AS num_code_b,
            b.post_code AS post_code_b,
            b.level AS level_b
        FROM
            ecc_area a
                LEFT JOIN ecc_area b ON b.parent_id = a.id
        WHERE
            a.operator = #{operator}
          and a.num_code = #{provinceCode}
          and b.num_code =#{cityCode}
    </select>

    <select id="getOneAreaByName" resultMap="AreaListMap">
        SELECT
        a.id AS id_a,
        a.area_name AS area_name_a,
        a.parent_id AS parent_id_a,
        a.num_code AS num_code_a,
        a.post_code AS post_code_a,
        a.level AS level_a,
        b.id AS id_b,
        b.area_name AS area_name_b,
        b.parent_id AS parent_id_b,
        b.num_code AS num_code_b,
        b.post_code AS post_code_b,
        b.level AS level_b
        FROM
        ecc_area a
        LEFT JOIN ecc_area b ON b.parent_id = a.id
        WHERE
        a.operator = #{operator}
        and a.area_name = #{provinceName}
        and b.area_name =#{cityName}
    </select>
</mapper>