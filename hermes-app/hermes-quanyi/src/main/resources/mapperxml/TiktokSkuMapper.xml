<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.TiktokSkuMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.TiktokSkuDO">
    <!--@mbg.generated-->
    <!--@Table sl_tiktok_sku-->
    <id column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="ecc_prod_id" jdbcType="BIGINT" property="eccProdId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="images" jdbcType="VARCHAR" property="images" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="tag_group_id" jdbcType="VARCHAR" property="tagGroupId" />
    <result column="entry_schema" jdbcType="VARCHAR" property="entrySchema" />
    <result column="sku_attr" jdbcType="VARCHAR" property="skuAttr" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    sku_id, ecc_prod_id, title, price, images, `type`, tag_group_id, entry_schema, sku_attr, 
    create_time, update_time, deleted
  </sql>
</mapper>