<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccAggregatePageProductMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccAggregatePageProductDO">
    <!--@mbg.generated-->
    <!--@Table ecc_aggregate_page_product-->
    <id column="page_product_id" jdbcType="BIGINT" property="pageProductId" />
    <result column="aggregate_page_id" jdbcType="BIGINT" property="aggregatePageId" />
    <result column="sort" jdbcType="TINYINT" property="sort" />
    <result column="province_ad_code" jdbcType="VARCHAR" property="provinceAdCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="page_default" jdbcType="INTEGER" property="pageDefault" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    page_product_id, aggregate_page_id, sort, province_ad_code, province_name, page_default,
    product_id, create_time, update_time
  </sql>
</mapper>