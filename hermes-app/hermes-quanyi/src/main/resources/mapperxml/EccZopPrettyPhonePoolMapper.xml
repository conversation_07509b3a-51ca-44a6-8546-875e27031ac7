<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccZopPrettyPhonePoolMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccZopPrettyPhonePool">
    <!--@mbg.generated-->
    <!--@Table ecc_zop_pretty_phone_pool-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sp_prod_id" jdbcType="INTEGER" property="spProdId" />
    <result column="sp_goods_id" jdbcType="VARCHAR" property="spGoodsId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="quality_score" jdbcType="INTEGER" property="qualityScore" />
    <result column="petty_tag" jdbcType="VARCHAR" property="pettyTag" />
    <result column="used" jdbcType="TINYINT" property="used" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sp_prod_id, sp_goods_id, phone, province_code,province, city_code,city, quality_score, petty_tag,
    used, create_time, update_time
  </sql>

  <insert id="saveOrUpdateTime">
      INSERT INTO ecc_zop_pretty_phone_pool
      (sp_prod_id, sp_goods_id, phone, province_code, province, city_code, city, quality_score, petty_tag, used, create_time, update_time)
      VALUES
      <foreach collection="list" item="item" separator=",">
          (
          #{item.spProdId,jdbcType=INTEGER},
          #{item.spGoodsId,jdbcType=VARCHAR},
          #{item.phone,jdbcType=VARCHAR},
          #{item.provinceCode,jdbcType=VARCHAR},
          #{item.province,jdbcType=VARCHAR},
          #{item.cityCode,jdbcType=VARCHAR},
          #{item.city,jdbcType=VARCHAR},
          #{item.qualityScore,jdbcType=INTEGER},
          #{item.pettyTag,jdbcType=VARCHAR},
          #{item.used,jdbcType=TINYINT},
          DEFAULT,
          DEFAULT
          )
      </foreach>
      ON DUPLICATE KEY UPDATE
      update_time = NOW()  -- 冲突时仅更新此字段
  </insert>

  <delete id="deleteByScoreRankAbove">
    DELETE ecc_zop_pretty_phone_pool
    FROM ecc_zop_pretty_phone_pool
           JOIN (SELECT id
                 FROM (SELECT id, ROW_NUMBER() OVER (ORDER BY quality_score DESC, id DESC) AS row_num
                       FROM ecc_zop_pretty_phone_pool
                       WHERE sp_prod_id = #{spProdId}
                         AND sp_goods_id = #{spGoodsId}) AS ranked
                 WHERE ranked.row_num > #{maxScoreRank}) AS to_delete
                ON ecc_zop_pretty_phone_pool.id = to_delete.id
  </delete>

  <update id="batchUpdateScoreAndTag">
    <foreach collection="list" item="item" separator=";">
      update ecc_zop_pretty_phone_pool
      set quality_score = #{item.qualityScore,jdbcType=INTEGER},
        petty_tag = #{item.pettyTag,jdbcType=VARCHAR},
        update_time = now()
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectTagGroup" resultType="com.yuelan.hermes.quanyi.common.pojo.bo.TagGroupBO">
    SELECT json_element as tag, COUNT(*) as phoneQuantity
    FROM ecc_zop_pretty_phone_pool,
         JSON_TABLE(
                 petty_tag,
                 '$[*]' COLUMNS (json_element VARCHAR(255) PATH '$')
         ) AS json_data
    WHERE sp_prod_id = #{spProdId}
        AND sp_goods_id = #{spGoodsId}
        <if test="provinceCode != null">
            AND province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND city_code = #{cityCode}
        </if>
    GROUP BY json_element
  </select>

  <select id="pageByTag" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.EccZopPrettyPhonePool">
    SELECT
      <include refid="Base_Column_List"/>
    FROM ecc_zop_pretty_phone_pool
    <where>
        <if test="tag != null">
            AND  JSON_CONTAINS(petty_tag, '"${tag}"')
        </if>
        AND sp_prod_id = #{spProdId}
        AND sp_goods_id = #{spGoodsId}
        <if test="provinceCode != null">
            AND province_code = #{provinceCode}
        </if>
        <if test="cityCode != null">
            AND city_code = #{cityCode}
        </if>

    </where>
    ORDER BY quality_score DESC,id DESC

  </select>

  <delete id="removeExpirePrettyPhonePool">
    DELETE FROM ecc_zop_pretty_phone_pool
    WHERE COALESCE(update_time, create_time) &lt; DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
  </delete>
</mapper>