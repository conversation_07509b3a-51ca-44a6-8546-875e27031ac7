<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.OrderOnlineMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, order_no, out_order_no, purchaser_type, mch_id, mch_name, phone, user_account,
        quantity, total_amount, goods_type, goods_id, goods_name, sku_id, sku_no, sku_name,
        sku_image, order_status, notify_status, remark, create_time, update_time, deleted
    </sql>

    <select id="findByOrderNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderOnlineDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM order_online
        where order_no =#{orderNo}
        and deleted = 0
    </select>

    <update id="updateOrderStatus">
        update order_online
        set order_status = #{status},
        <if test="remark != null and remark != ''">remark = #{remark},</if>
        update_time = now()
        where order_no = #{orderNo}
    </update>

    <select id="findByOutOrderNoAndPurchaserType" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderOnlineDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM order_online
        where out_order_no = #{outOrderNo}
        and purchaser_type = #{purchaserType}
        limit 1
    </select>

    <update id="updateNotifyStatus">
        update order_online
        set notify_status =#{notifyStatus},
        update_time =now()
        where id = #{orderId}
    </update>

    <select id="countByOrderVirtualListReq" resultType="java.lang.Long">
        select count(*)
        from order_online
        <include refid="OrderVirtualListReqWhere"/>
    </select>

    <select id="pageByOrderVirtualListReq" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderOnlineDO">
        select
        <include refid="Base_Column_List"/>
        FROM order_online
        <include refid="OrderVirtualListReqWhere"/>
        order by id desc
        limit #{req.offset},#{req.size}
    </select>

    <sql id="OrderVirtualListReqWhere">
        <where>
            <if test="req.orderNos != null and req.orderNos.size() &gt; 0">
                and order_no in
                <foreach close=")" collection="req.orderNos" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.orderNo != null and req.orderNo != ''">and order_no = #{req.orderNo}</if>
            <if test="req.outOrderNo != null and req.outOrderNo != ''">and out_order_no = #{req.outOrderNo}</if>
            <if test="req.goodsId != null">and goods_id = #{req.goodsId}</if>
            <if test="req.skuNo != null and req.skuNo != ''">and sku_no= #{req.skuNo}</if>
            <if test="req.phone != null and req.phone != ''">and phone = #{req.phone}</if>
            <if test="req.userAccount != null and req.userAccount != ''">
                and user_account= #{req.userAccount}
            </if>
            <if test="req.orderStatus != null">and order_status = #{req.orderStatus}</if>
            <if test="req.notifyStatus != null">and notify_status = #{req.notifyStatus}</if>
            <if test="req.goodsType != null">and goods_type = #{req.goodsType}</if>
            <if test="req.orderStartTime != null">and create_time <![CDATA[ >= ]]> #{req.orderStartTime}</if>
            <if test="req.orderEndTime != null">and create_time <![CDATA[ <= ]]> #{req.orderEndTime}</if>
            <if test="req.mchId != null and req.mchId != ''">and mch_id = #{req.mchId}</if>
            and deleted = 0
        </where>
    </sql>
</mapper>