<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccOuterChannelDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO">
    <!--@mbg.generated-->
    <!--@Table ecc_outer_channel-->
    <id column="outer_channel_id" jdbcType="BIGINT" property="outerChannelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="api_key" jdbcType="VARCHAR" property="apiKey" />
    <result column="api_secret" jdbcType="VARCHAR" property="apiSecret" />
    <result column="is_disabled" jdbcType="INTEGER" property="isDisabled" />
    <result column="ip_whitelist" jdbcType="VARCHAR" property="ipWhitelist" />
    <result column="zop_referrer_code" jdbcType="VARCHAR" property="zopReferrerCode" />
    <result column="parent_channel_id" jdbcType="BIGINT" property="parentChannelId" />
    <result column="channel_level" jdbcType="TINYINT" property="channelLevel" />
    <result column="deduction_rate" jdbcType="TINYINT" property="deductionRate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    outer_channel_id, channel_name, api_key, api_secret, is_disabled, ip_whitelist, zop_referrer_code,
    parent_channel_id, channel_level, deduction_rate, create_time, update_time,
    deleted
  </sql>

  <sql id="Left_Join_Base_Column_List">
          <!--@mbg.generated-->
    eoc.outer_channel_id,eoc.channel_name,eoc.api_key,eoc.api_secret,eoc.is_disabled,eoc.ip_whitelist,eoc.zop_referrer_code,
    eoc.parent_channel_id,eoc.channel_level,eoc.deduction_rate,eoc.create_time,eoc.update_time,eoc.deleted
  </sql>

  <!-- 使用递归CTE查询渠道的所有上级渠道链 -->
  <!-- 最多 10层 防止无限递归-->
  <select id="selectParentChannelChain" resultMap="BaseResultMap">
    WITH RECURSIVE parent_chain AS (
    SELECT
    <include refid="Left_Join_Base_Column_List"/>
    ,
      1 as level
    FROM ecc_outer_channel eoc
           INNER JOIN ecc_outer_channel c ON c.parent_channel_id = eoc.outer_channel_id
    WHERE c.outer_channel_id = #{channelId}
      AND eoc.deleted = 0

    UNION ALL

    SELECT
    <include refid="Left_Join_Base_Column_List"/>
    ,
      pc.level + 1
    FROM ecc_outer_channel eoc
           INNER JOIN parent_chain pc ON pc.parent_channel_id = eoc.outer_channel_id
    WHERE eoc.deleted = 0
      AND pc.level &lt; 10
    )
    SELECT
    <include refid="Base_Column_List"/>
    FROM parent_chain
    ORDER BY level ASC
  </select>

  <!-- 使用递归CTE查询渠道的所有下级渠道 -->
  <!-- 防止无限递归，最多10层-->
  <select id="selectAllDescendantChannels" resultMap="BaseResultMap">
    WITH RECURSIVE descendant_tree AS (
    SELECT
    <include refid="Base_Column_List"/>
    ,
      1 as level
    FROM ecc_outer_channel
    WHERE parent_channel_id = #{parentChannelId}
      AND deleted = 0

    UNION ALL

    SELECT
    <include refid="Left_Join_Base_Column_List"/>
    ,
      dt.level + 1
    FROM ecc_outer_channel eoc
           INNER JOIN descendant_tree dt ON dt.outer_channel_id = eoc.parent_channel_id
    WHERE eoc.deleted = 0
      AND dt.level &lt; 10
    )
    SELECT
    <include refid="Base_Column_List"/>
    FROM descendant_tree
    ORDER BY level ASC, outer_channel_id ASC
  </select>
</mapper>