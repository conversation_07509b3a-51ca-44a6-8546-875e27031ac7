<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.FileExportTaskDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.FileExportTaskDO">
    <!--@mbg.generated-->
    <!--@Table file_export_task-->
    <id column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="task_code" jdbcType="VARCHAR" property="taskCode" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="eval_param" jdbcType="VARCHAR" property="evalParam" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="file_write_status" jdbcType="INTEGER" property="fileWriteStatus" />
    <result column="oss_upload_status" jdbcType="INTEGER" property="ossUploadStatus" />
    <result column="oss_url" jdbcType="VARCHAR" property="ossUrl" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    task_id, task_code, task_name, eval_param, task_status, file_name, file_path, file_write_status,
    oss_upload_status, oss_url, expire_time, create_time, update_time, deleted
  </sql>
</mapper>