<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccImeiMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiDO">
    <!--@mbg.generated-->
    <!--@Table ecc_imei-->
    <id column="imei_id" jdbcType="BIGINT" property="imeiId" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="batch_id" jdbcType="BIGINT" property="batchId" />
    <result column="device" jdbcType="VARCHAR" property="device" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    imei_id, imei, batch_id, device, channel_type, channel_id, product_id, delivery_time,
    create_time, update_time
  </sql>

  <insert id="saveBatchIgnore">
    INSERT INTO ecc_imei
      (<include refid="Base_Column_List" />)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.imeiId}, #{item.imei}, #{item.batchId}, #{item.device}, #{item.channelType},
      #{item.channelId}, #{item.productId}, #{item.deliveryTime}, now(), #{item.updateTime}
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    update_time = VALUES(update_time)
  </insert>
</mapper>