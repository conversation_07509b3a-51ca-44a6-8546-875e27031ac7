<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitOrderItemDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderItemDO">
    <!--@mbg.generated-->
    <!--@Table benefit_order_item-->
    <id column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="item_no" jdbcType="VARCHAR" property="itemNo" />
    <result column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="goods_id" jdbcType="BIGINT" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="supplier_type" jdbcType="TINYINT" property="supplierType" />
    <result column="supplier" jdbcType="VARCHAR" property="supplier" />
    <result column="supplier_goods_no" jdbcType="VARCHAR" property="supplierGoodsNo" />
    <result column="supplier_order_no" jdbcType="VARCHAR" property="supplierOrderNo" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="cost_price" jdbcType="DECIMAL" property="costPrice" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="preorder_status" jdbcType="TINYINT" property="preorderStatus" />
    <result column="preorder_content" jdbcType="VARCHAR" property="preorderContent" />
    <result column="preorder_time" jdbcType="TIMESTAMP" property="preorderTime" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>


  <resultMap id="BenefitOrderItemAndOrdeDOMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderItemDO">
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="boi.phone" jdbcType="VARCHAR" property="phone" />
    <result column="boi.order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="item_no" jdbcType="VARCHAR" property="itemNo" />
    <result column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="goods_id" jdbcType="BIGINT" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="supplier_type" jdbcType="TINYINT" property="supplierType" />
    <result column="supplier" jdbcType="VARCHAR" property="supplier" />
    <result column="supplier_goods_no" jdbcType="VARCHAR" property="supplierGoodsNo" />
    <result column="supplier_order_no" jdbcType="VARCHAR" property="supplierOrderNo" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="cost_price" jdbcType="DECIMAL" property="costPrice" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="boi.preorder_status" jdbcType="TINYINT" property="preorderStatus" />
    <result column="boi.preorder_content" jdbcType="VARCHAR" property="preorderContent" />
    <result column="preorder_time" jdbcType="TIMESTAMP" property="preorderTime" />
    <result column="boi.order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="boi.create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="boi.update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="boi.deleted" jdbcType="TINYINT" property="deleted" />
    <association property="orderDO" javaType="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO">
      <result column="order_id" jdbcType="BIGINT" property="orderId" />
      <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
      <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
      <result column="distribution_channel" jdbcType="VARCHAR" property="distributionChannel" />
      <result column="pay_channel_id" jdbcType="INTEGER" property="payChannelId" />
      <result column="pay_channel" jdbcType="VARCHAR" property="payChannel" />
      <result column="pay_channel_pkg_id" jdbcType="INTEGER" property="payChannelPkgId" />
      <result column="pay_channel_pkg_name" jdbcType="VARCHAR" property="payChannelPkgName" />
      <result column="bo.phone" jdbcType="VARCHAR" property="phone" />
      <result column="bo.prod_id" jdbcType="BIGINT" property="prodId" />
      <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
      <result column="redeem_limit" jdbcType="INTEGER" property="redeemLimit" />
      <result column="redeem_remain" jdbcType="INTEGER" property="redeemRemain" />
      <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
      <result column="bo.preorder_status" jdbcType="TINYINT" property="preorderStatus" />
      <result column="bo.preorder_content" jdbcType="VARCHAR" property="preorderContent" />
      <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
      <result column="pay_notify_content" jdbcType="VARCHAR" property="payNotifyContent" />
      <result column="pay_notify_time" jdbcType="TIMESTAMP" property="payNotifyTime" />
      <result column="bo.order_status" jdbcType="TINYINT" property="orderStatus" />
      <result column="extra_data" jdbcType="VARCHAR" property="extraData" />
      <result column="bo.create_time" jdbcType="TIMESTAMP" property="createTime" />
      <result column="bo.update_time" jdbcType="TIMESTAMP" property="updateTime" />
      <result column="bo.deleted" jdbcType="TINYINT" property="deleted" />
    </association>
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    item_id, phone, order_no, item_no, prod_id, goods_id, goods_name, supplier_type,
    supplier, supplier_goods_no, supplier_order_no, price, cost_price, pay_time, preorder_status,
    preorder_content, preorder_time, order_status, create_time, update_time, deleted
  </sql>

  <sql id="BenefitOrderItemDO_Join_Column_List">
    boi.item_id,boi.phone,boi.order_no,boi.item_no,boi.prod_id,boi.goods_id,boi.goods_name,boi.supplier_type,boi.supplier,
    boi.supplier_goods_no,boi.supplier_order_no,boi.price,boi.cost_price,boi.pay_time,boi.preorder_status,boi.preorder_content,
    boi.preorder_time,boi.order_status,boi.create_time,boi.update_time,boi.deleted
  </sql>

  <select id="selectOrderItemAndOrder" resultMap="BenefitOrderItemAndOrdeDOMap">
      select
      <include refid="BenefitOrderItemDO_Join_Column_List"/>
      ,
      <include refid="com.yuelan.hermes.quanyi.mapper.BenefitOrderDOMapper.BenefitOrderDO_Join_Column_List"/>
      from benefit_order_item boi
               left join benefit_order bo on boi.order_no = bo.order_no
      <where>
          <if test="req.itemId != null">
              and boi.item_id = #{req.itemId}
          </if>
          <if test="req.phone != null">
              and boi.phone = #{req.phone}
          </if>
          <if test="req.orderNo != null">
              and boi.order_no = #{req.orderNo}
          </if>
          <if test="req.itemNo != null">
              and boi.item_no = #{req.itemNo}
          </if>
          <if test="req.prodId != null">
              and boi.prod_id = #{req.prodId}
          </if>
          <if test="req.goodsId != null">
              and boi.goods_id = #{req.goodsId}
          </if>
          <if test="req.supplierType != null">
              and boi.supplier_type = #{req.supplierType}
          </if>
          <if test="req.supplierGoodsNo != null">
              and boi.supplier_goods_no = #{req.supplierGoodsNo}
          </if>
          <if test="req.preorderStatus != null">
              and boi.preorder_status = #{req.preorderStatus}
          </if>
          <if test="req.orderStatus != null">
              and boi.order_status = #{req.orderStatus}
          </if>
          <if test="req.createTimeStart != null">
              and boi.create_time &gt;= #{req.createTimeStart}
          </if>
          <if test="req.createTimeEnd != null">
              and boi.create_time &lt;= #{req.createTimeEnd}
          </if>
          order by boi.item_id desc
    </where>
  </select>
</mapper>