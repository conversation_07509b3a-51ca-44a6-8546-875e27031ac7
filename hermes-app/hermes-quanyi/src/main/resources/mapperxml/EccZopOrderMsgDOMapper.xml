<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccZopOrderMsgDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderMsgDO">
    <!--@mbg.generated-->
    <!--@Table ecc_zop_order_msg-->
    <id column="msg_id" jdbcType="BIGINT" property="msgId" />
    <result column="zop_msg_type" jdbcType="TINYINT" property="zopMsgType" />
    <result column="zop_order_id" jdbcType="TINYINT" property="zopOrderId" />
    <result column="zop_msg_id" jdbcType="VARCHAR" property="zopMsgId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="msg_time" jdbcType="TIMESTAMP" property="msgTime" />
    <result column="order_state" jdbcType="VARCHAR" property="orderState" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    msg_id, zop_msg_type,zop_order_id,zop_msg_id, order_no, phone, msg_time, order_state, create_time,
    update_time, deleted
  </sql>
</mapper>