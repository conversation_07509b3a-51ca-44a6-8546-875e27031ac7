<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitPlatformMapper">
    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitPlatformDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="platform_name" property="platformName"/>
        <result column="platform_code" property="platformCode"/>
        <result column="platform_secret" property="platformSecret"/>
        <result column="ip_white" property="ipWhite"/>
        <result column="status" property="status"/>
        <result column="call_back_url" property="callBackUrl"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , platform_name
        , platform_code
        , `status`
        , platform_secret
        , ip_white
        , call_back_url
        , create_time
        , update_time,
    deleted
    </sql>
</mapper>