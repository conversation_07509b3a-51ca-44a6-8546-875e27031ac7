<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.UserDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.UserDO">
    <!--@mbg.generated-->
    <!--@Table hsh_user-->
    <id column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
    <result column="reg_ip" jdbcType="VARCHAR" property="regIp" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    user_id, user_name, nick_name, user_phone, reg_ip, create_time, update_time
  </sql>
</mapper>