<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GoodsVirtualStockItemMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, batch_no, goods_id, sku_id, supplier, buyer, purchase_price, sale_price, purchase_date,
        delivery_time, voucher_code, voucher_password, sn_code, qr_code_url, shop_url, start_date,
        end_date, bill_no, `status`, `enable`, remark, create_time, update_time, deleted
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into goods_virtual_stock_item
        (batch_no, goods_id, sku_id, supplier, buyer, purchase_price, sale_price, purchase_date,
        delivery_time, voucher_code, voucher_password, sn_code, qr_code_url, shop_url,
        start_date, end_date, bill_no, `status`, `enable`, remark, create_time, update_time, deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.batchNo}, #{item.goodsId}, #{item.skuId}, #{item.supplier}, #{item.buyer},
            #{item.purchasePrice}, #{item.salePrice}, #{item.purchaseDate}, #{item.deliveryTime},
            #{item.voucherCode}, #{item.voucherPassword}, #{item.snCode}, #{item.qrCodeUrl},
            #{item.shopUrl}, #{item.startDate}, #{item.endDate}, #{item.billNo}, #{item.status},
            #{item.enable}, #{item.remark}, #{item.createTime}, #{item.updateTime}, #{item.deleted}
            )
        </foreach>
    </insert>


    <sql id="GoodsVirtualStockDetailRsp_Colum_List">
        i.id             as stockItemId,
        i.batch_no       as batchNo,
        i.goods_id       as goodsId,
        v.`name`         as goodsName,
        s.sku_name       as skuName,
        i.sku_id         as skuId,
        s.sku_no         as skuNo,
        i.supplier       as supplier,
        i.purchase_price as purchasePrice,
        i.purchase_date  as purchaseDate,
        i.create_time    as createTime,
        i.buyer          as buyer,
        i.sale_price     as salePrice,
        i.delivery_time  as deliveryTime,
        i.status         as status,
        i.remark         as remark
    </sql>
    <sql id="VoucherPasswordRsp_Colum_List">
        i.voucher_code as voucherCode,
        i.voucher_password as voucherPassword,
        i.qr_code_url as qrCodeUrl,
        i.start_date as startDate,
        i.end_date as endDate,
        i.enable as enable,
        <include refid="GoodsVirtualStockDetailRsp_Colum_List"/>
    </sql>

    <sql id="GoodsVirtualStockDetailReqWhere">
        <where>
            <if test="req.batchNo != null and req.batchNo != ''">and i.batch_no = #{req.batchNo}</if>
            <if test="req.skuNo != null and req.skuNo != ''">and s.sku_no = #{req.skuNo}</if>
            <if test="req.goodsId != null">and i.goods_id = #{req.goodsId}</if>
            <if test="req.goodsName != null and req.goodsName != ''">
                and v.`name` like concat('%', #{req.goodsName}, '%')
            </if>
            <if test="req.skuName != null and req.skuName != ''">
                and s.sku_name like concat('%', #{req.skuName}, '%')
            </if>
            <if test="req.supplier != null and req.supplier != ''">
                and i.supplier like concat('%', #{req.supplier}, '%')
            </if>
            <if test="req.voucherCode != null and req.voucherCode != ''">
                and i.voucher_code =#{req.voucherCode}
            </if>
            <if test="req.voucherPassword != null and req.voucherPassword != ''">
                and i.voucher_password =#{req.voucherPassword}
            </if>
            <if test="req.qrCodeUrl != null and req.qrCodeUrl != ''">
                and i.qr_code_url =#{req.qrCodeUrl}
            </if>
            <if test="req.status != null">and i.status =#{req.status}</if>
            <if test="req.enable != null">and i.enable =#{req.enable}</if>
            and i.deleted = 0
        </where>
    </sql>

    <select id="countByGoodsVirtualStockDetailReq" resultType="java.lang.Long">
        SELECT count(*)
        FROM goods_virtual_stock_item i
        LEFT JOIN goods_virtual v ON i.goods_id = v.id
        LEFT JOIN goods_virtual_sku s on i.sku_id = s.id
        <include refid="GoodsVirtualStockDetailReqWhere"/>
    </select>

    <select id="pageByGoodsVirtualStockDetailReq" resultType="com.yuelan.hermes.quanyi.controller.response.GoodsVirtualStockDetailRsp">
        SELECT
        <include refid="GoodsVirtualStockDetailRsp_Colum_List"/>
        FROM goods_virtual_stock_item i
        LEFT JOIN goods_virtual v ON i.goods_id = v.id
        LEFT JOIN goods_virtual_sku s on i.sku_id = s.id
        <include refid="GoodsVirtualStockDetailReqWhere"/>
        order by ${req.orderBy}
        limit #{req.offset},#{req.size}
    </select>

    <select id="cursorByGoodsVirtualStockDetailReq" resultType="com.yuelan.hermes.quanyi.controller.response.GoodsVirtualStockDetailRsp">
        SELECT
        <include refid="GoodsVirtualStockDetailRsp_Colum_List"/>
        FROM goods_virtual_stock_item i
        LEFT JOIN goods_virtual v ON i.goods_id = v.id
        LEFT JOIN goods_virtual_sku s on i.sku_id = s.id
        <include refid="GoodsVirtualStockDetailReqWhere"/>
        <if test="maxId != null">and i.id <![CDATA[ < ]]> #{maxId}</if>
        order by i.id desc
        limit 500
    </select>


    <select id="getVoucherPasswordCount" resultType="java.lang.Long">
        SELECT count(*)
        FROM goods_virtual_stock_item i
        LEFT JOIN goods_virtual v ON i.goods_id = v.id
        LEFT JOIN goods_virtual_sku s on i.sku_id = s.id
        <include refid="GoodsVirtualStockDetailReqWhere"/>
    </select>

    <select id="getVoucherPasswordList" resultType="com.yuelan.hermes.quanyi.controller.response.VoucherPasswordRsp">
        SELECT
        <include refid="VoucherPasswordRsp_Colum_List"/>
        FROM goods_virtual_stock_item i
        LEFT JOIN goods_virtual v ON i.goods_id = v.id
        LEFT JOIN goods_virtual_sku s on i.sku_id = s.id
        <include refid="GoodsVirtualStockDetailReqWhere"/>
        order by ${req.orderBy}
        limit #{req.offset},#{req.size}
    </select>

    <select id="statisticsExpiresAmountByGoodsVirtualStockDetailReq" resultType="java.lang.Integer">
        SELECT count(*)
        FROM goods_virtual_stock_item i
        LEFT JOIN goods_virtual v ON i.goods_id = v.id
        LEFT JOIN goods_virtual_sku s on i.sku_id = s.id
        <include refid="GoodsVirtualStockDetailReqWhere"/>
        and i.end_date  <![CDATA[ <= ]]>now()
        and i.`status` = 1
    </select>


    <select id="voucherPasswordListExport" resultType="com.yuelan.hermes.quanyi.controller.response.VoucherPasswordRsp">
        SELECT
        <include refid="VoucherPasswordRsp_Colum_List"/>
        FROM goods_virtual_stock_item i
        LEFT JOIN goods_virtual v ON i.goods_id = v.id
        LEFT JOIN goods_virtual_sku s on i.sku_id = s.id
        <include refid="GoodsVirtualStockDetailReqWhere"/>
        <if test="maxId != null">and i.id <![CDATA[ < ]]> #{maxId}</if>
        order by i.id desc
        limit 500
    </select>


    <select id="findOne" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualStockItemDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM goods_virtual_stock_item
        WHERE id = #{id}
        and deleted = 0
    </select>

    <update id="updateVoucherPasswordStatus">
        update goods_virtual_stock_item
        set enable = #{tarEnable},
        update_time = now()
        where id = #{id}
        and enable = #{orgEnable}
        and deleted = 0
    </update>

    <select id="checkVoucherCodePassword" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualStockItemDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM goods_virtual_stock_item
        <where>
            <if test="voucherCode != null">voucher_code = #{voucherCode}</if>
            <if test="voucherPassword != null">and voucher_password = #{voucherPassword}</if>
            <if test="qrCodeUrl != null"> and qr_code_url = #{qrCodeUrl}</if>
            and sku_id = #{skuId}
            and deleted = 0
        </where>
        limit 1
    </select>

    <update id="deduction">
        update goods_virtual_stock_item
        set `status` = 2,
        buyer = #{buyer},
        sale_price =#{salePrice},
        bill_no = #{billNo},
        delivery_time = now(),
        update_time=now()
        WHERE sku_id = #{skuId}
        AND end_date <![CDATA[ > ]]> now()
        AND `status` = 1
        AND `enable` = 1
        AND deleted = 0
        order by end_date
        limit #{limit}
    </update>

    <select id="findBySkuIdAndBillNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualStockItemDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM goods_virtual_stock_item
        WHERE sku_id = #{skuId}
        AND bill_no = #{billNo}
        AND `status` = 2
        AND deleted = 0
    </select>

    <select id="statisticsWaitExpiresAmountBySkuId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM goods_virtual_stock_item
        WHERE sku_id = #{skuId}
        and end_date  <![CDATA[ <= ]]>#{expireDate}
        and end_date &gt; now()
        and status = 1
        and deleted = 0
    </select>

    <select id="statisticsExpiresAmountBySkuId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM goods_virtual_stock_item
        WHERE sku_id = #{skuId}
        and end_date  <![CDATA[ <= ]]>now()
        and status = 1
        and deleted = 0
    </select>

    <select id="statisticsRealAmountBySkuId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM goods_virtual_stock_item
        WHERE sku_id = #{skuId}
        and status = 1
        and deleted = 0
    </select>

    <select id="statisticsSellAmountBySkuId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM goods_virtual_stock_item
        WHERE sku_id = #{skuId}
        and status = 2
        and deleted = 0
    </select>

    <select id="statisticsDisableAmountBySkuId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM goods_virtual_stock_item
        WHERE sku_id = #{skuId}
        and status = 1
        and enable = 0
        and deleted = 0
    </select>

    <sql id="InStockWhere">
        <where>
            id in
            <foreach close=")" collection="ids" item="id" open="(" separator=",">
                #{id}
            </foreach>
            AND `status` = 1
            AND deleted = 0
        </where>
    </sql>

    <select id="countInStockByIdIn" resultType="java.lang.Integer">
        SELECT count(*)
        FROM goods_virtual_stock_item
        <include refid="InStockWhere"/>
    </select>

    <update id="deleteInStockByIdIn">
        update goods_virtual_stock_item
        set `deleted` = 1,
        update_time=now()
        <include refid="InStockWhere"/>
    </update>


</mapper>