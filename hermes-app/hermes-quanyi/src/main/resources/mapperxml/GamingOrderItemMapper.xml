<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GamingOrderItemMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, item_no, order_id, goods_id, goods_name, send_type, supplier_type, delivery_type,
        redeem_code, redeem_code_expire_date, supplier_order_no, supplier_goods_no, ext_data,
        order_status, fail_code, fail_reason, product_id, phone, preorder_status, preorder_remark,
        obtain_status, obtain_time, obtain_remark, remark, create_time, update_time, deleted
    </sql>
    <sql id="Join_Column_List">
        <!--@mbg.generated-->
        goi.id,goi.item_no,goi.order_id,goi.goods_id,goi.goods_name,goi.send_type,goi.supplier_type,goi.delivery_type,
        goi.redeem_code,goi.redeem_code_expire_date,goi.supplier_order_no,goi.supplier_goods_no,goi.ext_data,goi.order_status,
        goi.fail_code,goi.fail_reason,goi.product_id,goi.phone,goi.preorder_status,goi.preorder_remark,goi.obtain_status,
        goi.obtain_time,goi.obtain_remark,goi.remark,goi.create_time,goi.update_time,goi.deleted
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into gaming_order_item
        ( item_no, order_id, goods_id, goods_name, send_type, supplier_type, delivery_type,
        redeem_code, redeem_code_expire_date, supplier_order_no, supplier_goods_no, ext_data,
        order_status, fail_code, fail_reason, product_id, phone, preorder_status, preorder_remark,
        obtain_status, obtain_time, obtain_remark, remark, create_time, update_time, deleted
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.itemNo}, #{item.orderId}, #{item.goodsId}, #{item.goodsName}, #{item.sendType},#{item.supplierType},#{item.deliveryType},
            #{item.redeemCode}, #{item.redeemCodeExpireDate}, #{item.supplierOrderNo}, #{item.supplierGoodsNo}, #{item.extData},
            #{item.orderStatus}, #{item.failCode}, #{item.failReason}, #{item.productId}, #{item.phone},#{item.preorderStatus}, #{item.preorderRemark},
            #{item.obtainStatus}, #{item.obtainTime}, #{item.obtainRemark},#{item.remark},#{item.createTime}, #{item.updateTime}, #{item.deleted})
        </foreach>
    </insert>

    <select id="findByOrderId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_order_item
        where order_id = #{orderId}
        and deleted=0
    </select>

    <select id="findOrderIdBySupplierOrderNo" resultType="java.lang.Long">
        select order_id
        from gaming_order_item
        where supplier_order_no = #{supplierOrderNo}
        and deleted=0
    </select>

    <select id="findByOrderIdIn" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_order_item
        where order_id in
        <foreach close=")" collection="orderIds" item="orderId" open="(" separator=",">
            #{orderId}
        </foreach>
        and deleted=0
    </select>

    <select id="findByItemNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_order_item
        where item_no = #{itemNo}
        and deleted=0
    </select>

    <update id="updateStatus">
        update gaming_order_item
        set order_status = #{tarStatus},
        <if test="remark != null and remark != ''">remark = #{remark},</if>
        update_time = now()
        where id =#{itemId}
        and order_status =#{orgStatus}
        and deleted=0
    </update>

    <select id="existByOrderIdAndStatus" resultType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from gaming_order_item
        where order_id = #{orderId}
        and order_status in
        <foreach collection="status" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted=0
        limit 1
    </select>

    <select id="findStatusByOrderId" resultType="java.lang.Integer">
        select order_status
        from gaming_order_item
        where order_id = #{orderId}
        and deleted=0
    </select>

    <update id="updateSupplierOrder">
        update gaming_order_item
        set order_status = #{orderStatus},
        <if test="supplierOrderNo != null and supplierOrderNo != ''">supplier_order_no = #{supplierOrderNo},</if>
        <if test="preorderStatus != null">preorder_status = #{preorderStatus},</if>
        <if test="preorderRemark != null and preorderRemark != ''">preorder_remark = #{preorderRemark},</if>
        update_time = now()
        where id = #{itemId}
            AND order_status = #{oldStatus}
        and deleted = 0
    </update>

    <select id="findStatusByOrderIdAndStatus" resultType="java.lang.Integer">
        select distinct order_status
        from gaming_order_item
        where order_id = #{orderId}
        and order_status in
        <foreach collection="status" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and deleted=0
    </select>

    <select id="countByReq" resultType="java.lang.Long">
        SELECT count(1)
        FROM gaming_order_item goi
        LEFT JOIN gaming_order go ON goi.order_id = go.order_id
        <include refid="orderItemReqWhere"/>
    </select>

    <select id="listByReq" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO">
        SELECT
        <include refid="Join_Column_List"/>
        ,go.product_name
        ,go.order_no
        ,go.out_order_no
        FROM gaming_order_item goi
        LEFT JOIN gaming_order go ON goi.order_id = go.order_id
        <include refid="orderItemReqWhere"/>
        <if test="req.asc != null and req.asc == true">
            ORDER BY goi.id asc
        </if>
        <if test="req.asc == null or req.asc == false">
            ORDER BY goi.id desc
        </if>
        limit #{req.offset},#{req.size}
    </select>

    <sql id="orderItemReqWhere">
        <where>
            <if test="req.orderSearch != null">
                AND (go.out_order_no = #{req.orderSearch} OR goi.supplier_order_no = #{req.orderSearch})
            </if>
            <if test="req.orderNo != null">
                AND go.order_no = #{req.orderNo}
            </if>
            <if test="req.itemNo != null">
                AND goi.item_no = #{req.itemNo}
            </if>
            <if test="req.goodsId != null">
                AND goi.goods_id = #{req.goodsId}
            </if>
            <if test="req.goodsIds != null and req.goodsIds.size() > 0">
                AND goi.goods_id  IN
                <foreach collection="req.goodsIds" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="req.productId != null">
                AND goi.product_id = #{req.productId}
            </if>
            <if test="req.productIds != null and req.productIds.size() > 0">
                AND goi.product_id  IN
                <foreach collection="req.productIds" item="itemId" open="(" separator="," close=")">
                    #{itemId}
                </foreach>
            </if>
            <if test="req.supplierType != null">
                AND goi.supplier_type = #{req.supplierType}
            </if>
            <if test="req.deliveryType != null">
                AND goi.delivery_type = #{req.deliveryType}
            </if>
            <if test="req.orderId != null">
                AND goi.order_id = #{req.orderId}
            </if>
            <if test="req.supplierGoodsNo != null and req.supplierGoodsNo != ''">
                AND goi.supplier_goods_no = #{req.supplierGoodsNo}
            </if>
            <if test="req.redeemCode != null">
                AND goi.redeem_code = #{req.redeemCode}
            </if>
            <if test="req.orderStatus != null">
                AND goi.order_status = #{req.orderStatus}
            </if>
            <if test="req.phone != null">
                AND goi.phone = #{req.phone}
            </if>
            <if test="req.preOrderStatus != null">
                AND goi.preorder_status = #{req.preOrderStatus}
            </if>
            <if test="req.obtainStatus != null">
                AND goi.obtain_status = #{req.obtainStatus}
            </if>
            <if test="req.createTimeStart != null">
                AND goi.create_time <![CDATA[ >= ]]> #{req.createTimeStart}
            </if>
            <if test="req.createTimeEnd != null">
                AND goi.create_time <![CDATA[ <= ]]> #{req.createTimeEnd}
            </if>
            <if test="req.obtainTimeStart != null">
                AND goi.obtain_time <![CDATA[ >= ]]> #{req.obtainTimeStart}
            </if>
            <if test="req.obtainTimeEnd != null">
                AND goi.obtain_time <![CDATA[ <= ]]> #{req.obtainTimeEnd}
            </if>
            <if test="req.startId != null">
                AND goi.id <![CDATA[ > ]]> #{req.startId}
            </if>
            and goi.deleted = 0
        </where>
    </sql>

</mapper>