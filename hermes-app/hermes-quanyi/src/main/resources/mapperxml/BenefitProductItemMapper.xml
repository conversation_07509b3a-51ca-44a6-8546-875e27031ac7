<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitProductItemMapper">
    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductItemDO">
        <!--@mbg.generated-->
        <!--@Table benefit_product_item-->
        <id column="prod_item_id" jdbcType="BIGINT" property="prodItemId" />
        <result column="prod_id" jdbcType="BIGINT" property="prodId" />
        <result column="goods_id" jdbcType="BIGINT" property="goodsId" />
        <result column="sort" jdbcType="INTEGER" property="sort" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="deleted" jdbcType="TINYINT" property="deleted" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        prod_item_id, prod_id, goods_id, sort, create_time, update_time, deleted
    </sql>

    <update id="reductionSortByDeleteSort">
        UPDATE benefit_product_item
             SET sort = sort -1
        WHERE prod_id = #{prodId}
            AND sort > #{deletedSort}
            AND deleted = 0
    </update>
</mapper>