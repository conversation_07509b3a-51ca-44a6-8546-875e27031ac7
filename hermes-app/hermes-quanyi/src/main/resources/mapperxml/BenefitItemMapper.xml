<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitItemMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO">
    <!--@mbg.generated-->
    <!--@Table benefit_item-->
    <id column="benefit_item_id" jdbcType="BIGINT" property="benefitItemId" />
    <result column="module" jdbcType="TINYINT" property="module" />
    <result column="target_id" jdbcType="BIGINT" property="targetId" />
    <result column="benefit_name" jdbcType="VARCHAR" property="benefitName" />
    <result column="benefit_code" jdbcType="VARCHAR" property="benefitCode" />
    <result column="delivery_type" jdbcType="TINYINT" property="deliveryType" />
    <result column="item_img" jdbcType="VARCHAR" property="itemImg" />
    <result column="cost_price" jdbcType="INTEGER" property="costPrice" />
    <result column="selling_price" jdbcType="INTEGER" property="sellingPrice" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_goods_param" jdbcType="VARCHAR" property="supplierGoodsParam" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    benefit_item_id, `module`, target_id, benefit_name, benefit_code, delivery_type,
    item_img, cost_price, selling_price, supplier_id, supplier_name, supplier_goods_param,
    `status`, create_time, update_time
  </sql>
</mapper>