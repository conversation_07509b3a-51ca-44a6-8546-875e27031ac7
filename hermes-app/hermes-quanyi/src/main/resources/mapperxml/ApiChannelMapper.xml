<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.ApiChannelMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.ApiChannel">
    <!--@mbg.generated-->
    <!--@Table api_channel-->
    <id column="api_channel_id" jdbcType="INTEGER" property="apiChannelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="api_key" jdbcType="VARCHAR" property="apiKey" />
    <result column="api_secret" jdbcType="VARCHAR" property="apiSecret" />
    <result column="ip_white_list" jdbcType="VARCHAR" property="ipWhiteList" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    api_channel_id, channel_name, api_key, api_secret, ip_white_list, create_time, update_time
  </sql>
</mapper>