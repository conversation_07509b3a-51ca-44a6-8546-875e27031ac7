<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitOrderLogDOMapper">

    <update id="insertOrUpdate" >
        INSERT INTO benefit_order_log (`order_no`, `pay_channel_id`, `phone`, `content`, `update_num`)
        VALUES (#{orderNo}, #{payChannelId}, #{phone}, #{content}, 1)
            ON DUPLICATE KEY
        UPDATE content=CONCAT(content,',',#{content}), update_num=update_num+1, update_time=NOW();
    </update>

</mapper>