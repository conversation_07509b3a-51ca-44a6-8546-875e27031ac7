<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccZopPhonePoolDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccZopPhonePoolDO">
    <!--@mbg.generated-->
    <!--@Table ecc_zop_phone_pool-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sp_prod_id" jdbcType="INTEGER" property="spProdId" />
    <result column="sp_goods_id" jdbcType="VARCHAR" property="spGoodsId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="quality_level" jdbcType="INTEGER" property="qualityLevel" />
    <result column="used" jdbcType="TINYINT" property="used" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sp_prod_id, sp_goods_id, phone, province_code,province, city_code, city,quality_level, used,
    create_time, update_time
  </sql>
  <insert id="saveBatchIgnore">
    insert ignore into ecc_zop_phone_pool (sp_prod_id,sp_goods_id, phone,province_code, province,city_code,city, quality_level, used, create_time, update_time) values
    <foreach collection="list" item="item" separator="," >
      (#{item.spProdId,jdbcType=INTEGER}, #{item.spGoodsId,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, #{item.provinceCode,jdbcType=VARCHAR},#{item.province,jdbcType=VARCHAR},#{item.cityCode,jdbcType=VARCHAR},#{item.city,jdbcType=VARCHAR},#{item.qualityLevel,jdbcType=INTEGER}, #{item.used,jdbcType=TINYINT}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>


  <delete id="deleteExceedPoolSize">
    DELETE
    FROM ecc_zop_phone_pool
    WHERE id &lt; #{minId}
      AND sp_prod_id = #{spProdId}
        AND sp_goods_id = #{spGoodsId}
        AND province_code = #{provinceCode}
        and city_code = #{cityCode}
  </delete>

  <select id="selectAvailablePhoneList" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.EccZopPhonePoolDO">
    SELECT
      <include refid="Base_Column_List" />
    FROM ecc_zop_phone_pool where sp_prod_id = #{spProdId}
    AND sp_goods_id = #{spGoodsId}
    and used = 0
    <if test="provinceCode != null">
      AND province_code = #{provinceCode}
    </if>
    <if test="cityCode != null">
      AND city_code = #{cityCode}
    </if>
    ORDER BY RAND()
    LIMIT #{selectCount}
  </select>

  <select id="fetchMaxIdFromTopNRecords" resultType="java.lang.Long">
    SELECT id FROM ecc_zop_phone_pool
    where sp_prod_id = #{spProdId}
      AND sp_goods_id = #{spGoodsId}
      AND province_code = #{provinceCode}
      and city_code = #{cityCode}
    LIMIT 1 OFFSET #{topN}
  </select>

  <select id="selectMinIdRecord" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List" />
    FROM ecc_zop_phone_pool
        where sp_prod_id = #{spProdId}
        AND sp_goods_id = #{spGoodsId}
    ORDER BY id ASC
    LIMIT 1
  </select>
</mapper>