<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccLicenseMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccLicenseDO">
    <!--@mbg.generated-->
    <!--@Table ecc_license-->
    <id column="license_id" jdbcType="BIGINT" property="licenseId" />
    <result column="license_name" jdbcType="VARCHAR" property="licenseName" />
    <result column="operator" jdbcType="TINYINT" property="operator" />
    <result column="uri" jdbcType="VARCHAR" property="uri" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    license_id, license_name, `operator`, uri, create_time, update_time, deleted
  </sql>
</mapper>