<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.MerchantAccountStatementMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, merchant_id, amount, balance, trade_type, biz_type, biz_no, remark, create_time,
        update_time, deleted
    </sql>

    <select id="findByBizNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.MerchantAccountStatementDO">
        select
        <include refid="Base_Column_List"/>
        from merchant_account_statement
        where biz_no =#{bizNo}
        and biz_type =#{bizType}
        and deleted = 0
    </select>

    <select id="pageByMchAccountStatementReq" resultType="com.yuelan.hermes.quanyi.controller.response.MchAccountStatementRsp">
        select
        <include refid="Base_Column_List"/>
        from merchant_account_statement
        <include refid="MchAccountStatementReqWhere"/>
        order by id desc
        limit #{offset},#{size}
    </select>

    <select id="countByMchAccountStatementReq" resultType="java.lang.Long">
        select count(*)
        from merchant_account_statement
        <include refid="MchAccountStatementReqWhere"/>
    </select>

    <sql id="MchAccountStatementReqWhere">
        <where>
            merchant_id = #{merchantId}
            <if test="bizNo != null and bizNo != ''">and biz_no =#{bizNo}</if>
            <if test="bizType != null">and biz_type =#{bizType}</if>
            <if test="tradeType != null">and trade_type =#{tradeType}</if>
            and deleted =0
        </where>
    </sql>
</mapper>