<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.HttpAsyncTaskMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.HttpAsyncTask">
    <!--@mbg.generated-->
    <!--@Table http_async_task-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="request_id" jdbcType="VARCHAR" property="requestId" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="headers" jdbcType="LONGVARCHAR" property="headers" />
    <result column="body" jdbcType="LONGVARCHAR" property="body" />
    <result column="content_type" jdbcType="VARCHAR" property="contentType" />
    <result column="source_system" jdbcType="VARCHAR" property="sourceSystem" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="business_id" jdbcType="VARCHAR" property="businessId" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="max_retry_count" jdbcType="INTEGER" property="maxRetryCount" />
    <result column="next_retry_time" jdbcType="TIMESTAMP" property="nextRetryTime" />
    <result column="retry_interval" jdbcType="INTEGER" property="retryInterval" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="last_response" jdbcType="LONGVARCHAR" property="lastResponse" />
    <result column="last_response_code" jdbcType="INTEGER" property="lastResponseCode" />
    <result column="success_strategy" jdbcType="VARCHAR" property="successStrategy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, request_id, `method`, url, headers, body, content_type, source_system, business_type, 
    business_id, retry_count, max_retry_count, next_retry_time, retry_interval, `status`, 
    last_response, last_response_code, success_strategy, create_time, update_time, remark
  </sql>
</mapper>