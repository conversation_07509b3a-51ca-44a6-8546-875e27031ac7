<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccGdOrderNotifyMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccGdOrderNotifyDO">
    <!--@mbg.generated-->
    <!--@Table ecc_gd_order_notify-->
    <id column="notify_id" jdbcType="BIGINT" property="notifyId" />
    <result column="notify_body" jdbcType="VARCHAR" property="notifyBody" />
    <result column="deal_success" jdbcType="INTEGER" property="dealSuccess" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    notify_id, notify_body, deal_success, fail_reason, create_time
  </sql>
</mapper>