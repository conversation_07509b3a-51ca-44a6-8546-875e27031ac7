<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.ThirdChannelOrderMapper">
    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.ThirdChannelOrderDO">
        <id column="third_order_id" jdbcType="BIGINT" property="thirdOrderId" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="third_order_no" jdbcType="VARCHAR" property="thirdOrderNo" />
        <result column="unique_channel_pkg_id" jdbcType="INTEGER" property="uniqueChannelPkgId" />
        <result column="channel_id" jdbcType="BIGINT" property="channelId" />
        <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
        <result column="package_name" jdbcType="VARCHAR" property="packageName" />
        <result column="phone" jdbcType="VARCHAR" property="phone" />
        <result column="pay_status" jdbcType="TINYINT" property="payStatus" />
        <result column="pay_message" jdbcType="VARCHAR" property="payMessage" />
        <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="deleted" jdbcType="TINYINT" property="deleted" />
    </resultMap>
    <sql id="Base_Column_List">
        third_order_id,
        order_no,
        third_order_no,
        unique_channel_pkg_id,
        channel_id,
        channel_name,
        package_name,
        phone,
        pay_status,
        pay_message,
        order_time,
        create_time,
        update_time,
        deleted
    </sql>

</mapper>