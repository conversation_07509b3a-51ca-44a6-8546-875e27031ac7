<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccProductDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO">
    <!--@mbg.generated-->
    <!--@Table ecc_product-->
    <id column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
    <result column="prod_code" jdbcType="VARCHAR" property="prodCode" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="distribution_url" jdbcType="VARCHAR" property="distributionUrl" />
    <result column="sp_prod_id" jdbcType="INTEGER" property="spProdId" />
    <result column="sp_prod_name" jdbcType="VARCHAR" property="spProdName" />
    <result column="sp_goods_id" jdbcType="VARCHAR" property="spGoodsId" />
    <result column="sp_goods_name" jdbcType="VARCHAR" property="spGoodsName" />
    <result column="prohibited_areas" jdbcType="VARCHAR" property="prohibitedAreas" />
    <result column="prod_status" jdbcType="TINYINT" property="prodStatus" />
    <result column="region_type" jdbcType="INTEGER" property="regionType" />
    <result column="region_list" jdbcType="LONGVARCHAR" property="regionList" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    prod_id, prod_name, prod_code, price, distribution_url, sp_prod_id, sp_prod_name,
    sp_goods_id, sp_goods_name, prohibited_areas, prod_status, region_type, region_list,
    create_time, update_time, deleted
  </sql>
  <select id="selectPageExpand" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    ,(select count(1) from ecc_product_item where prod_id = ecc_product.prod_id and deleted = 0) as goods_count
    FROM ecc_product
    ${ew.customSqlSegment}
    </select>
</mapper>