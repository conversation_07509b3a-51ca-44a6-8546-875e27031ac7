<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccGdOrderMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccGdOrderDO">
    <!--@mbg.generated-->
    <!--@Table ecc_gd_order-->
    <id column="gd_order_id" jdbcType="BIGINT" property="gdOrderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="original_order_no" jdbcType="VARCHAR" property="originalOrderNo" />
    <result column="sp_order_no" jdbcType="VARCHAR" property="spOrderNo" />
    <result column="transfer_operator" jdbcType="TINYINT" property="transferOperator" />
    <result column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="channel_type" jdbcType="INTEGER" property="channelType" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="channel_order_no" jdbcType="VARCHAR" property="channelOrderNo" />
    <result column="ad_channel_id" jdbcType="BIGINT" property="adChannelId" />
    <result column="ad_channel_name" jdbcType="VARCHAR" property="adChannelName" />
    <result column="ad_ext" jdbcType="VARCHAR" property="adExt" />
    <result column="page_url" jdbcType="VARCHAR" property="pageUrl" />
    <result column="id_card_name" jdbcType="VARCHAR" property="idCardName" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="gd_goods_id" jdbcType="VARCHAR" property="gdGoodsId" />
    <result column="post_province_code" jdbcType="VARCHAR" property="postProvinceCode" />
    <result column="post_province" jdbcType="VARCHAR" property="postProvince" />
    <result column="post_city_code" jdbcType="VARCHAR" property="postCityCode" />
    <result column="post_city" jdbcType="VARCHAR" property="postCity" />
    <result column="post_district_code" jdbcType="VARCHAR" property="postDistrictCode" />
    <result column="post_district" jdbcType="VARCHAR" property="postDistrict" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="select_type" jdbcType="TINYINT" property="selectType" />
    <result column="order_submit_resp" jdbcType="VARCHAR" property="orderSubmitResp" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="card_status" jdbcType="TINYINT" property="cardStatus" />
    <result column="express_status" jdbcType="TINYINT" property="expressStatus" />
    <result column="express_company" jdbcType="VARCHAR" property="expressCompany" />
    <result column="express_no" jdbcType="VARCHAR" property="expressNo" />
    <result column="activate_time" jdbcType="TIMESTAMP" property="activateTime" />
    <result column="stop_time" jdbcType="TIMESTAMP" property="stopTime" />
    <result column="close_time" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="first_charge_amount" jdbcType="INTEGER" property="firstChargeAmount" />
    <result column="first_charge_time" jdbcType="TIMESTAMP" property="firstChargeTime" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    gd_order_id, order_no, original_order_no, sp_order_no, transfer_operator, prod_id, 
    prod_name, channel_id, channel_type, channel_name, channel_order_no, ad_channel_id, 
    ad_channel_name, ad_ext, page_url, id_card_name, id_card, gd_goods_id, post_province_code, 
    post_province, post_city_code, post_city, post_district_code, post_district, address, 
    contact_phone, phone, province_code, province, city_code, city, select_type, order_submit_resp, 
    order_status, card_status, express_status, express_company, express_no, activate_time, 
    stop_time, close_time, first_charge_amount, first_charge_time, fail_reason, remark, 
    create_time, update_time, deleted
  </sql>
</mapper>