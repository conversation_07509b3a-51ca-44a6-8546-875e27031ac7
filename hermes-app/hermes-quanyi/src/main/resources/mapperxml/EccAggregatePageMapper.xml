<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccAggregatePageMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccAggregatePageDO">
    <!--@mbg.generated-->
    <!--@Table ecc_aggregate_page-->
    <id column="aggregate_page_name" jdbcType="BIGINT" property="aggregatePageName" />
    <result column="page_name" jdbcType="VARCHAR" property="pageName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    aggregate_page_name, page_name, create_time, update_time
  </sql>
</mapper>