<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitUserDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitUserDO">
    <!--@mbg.generated-->
    <!--@Table benefit_user-->
    <id column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="distribution_channel" jdbcType="VARCHAR" property="distributionChannel" />
    <result column="from_prod_id" jdbcType="BIGINT" property="fromProdId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    user_id, nick_name, phone, distribution_channel, from_prod_id, create_time, update_time,deleted
  </sql>
</mapper>