<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccImeiBindMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccImeiBindDO">
    <!--@mbg.generated-->
    <!--@Table ecc_imei_bind-->
    <id column="bind_id" jdbcType="BIGINT" property="bindId" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="icc_id" jdbcType="VARCHAR" property="iccId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="operator_order_no" jdbcType="VARCHAR" property="operatorOrderNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    bind_id, imei, icc_id, order_id, operator_order_no, create_time, update_time
  </sql>
</mapper>