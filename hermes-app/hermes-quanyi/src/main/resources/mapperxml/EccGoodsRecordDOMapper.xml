<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccGoodsRecordDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsRecordDO">
    <!--@mbg.generated-->
    <!--@Table ecc_goods_record-->
    <id column="record_id" jdbcType="BIGINT" property="recordId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="ecc_user_id" jdbcType="BIGINT" property="eccUserId" />
    <result column="ecc_item_no" jdbcType="VARCHAR" property="eccItemNo" />
    <result column="ecc_order_month" jdbcType="VARCHAR" property="eccOrderMonth" />
    <result column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
    <result column="goods_id" jdbcType="BIGINT" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="redeem_type" jdbcType="TINYINT" property="redeemType" />
    <result column="redeem_code_key" jdbcType="VARCHAR" property="redeemCodeKey" />
    <result column="redeem_code_pwd" jdbcType="VARCHAR" property="redeemCodePwd" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    record_id, phone, ecc_user_id, ecc_item_no, ecc_order_month, prod_id, prod_name,redeem_type,
    goods_id, goods_name, redeem_code_key, redeem_code_pwd, create_time, update_time, 
    deleted
  </sql>
</mapper>