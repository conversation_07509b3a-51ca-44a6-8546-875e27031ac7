<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitOrderDOMapper">
    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO">
        <!--@mbg.generated-->
        <!--@Table benefit_order-->
        <id column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo"/>
        <result column="distribution_channel" jdbcType="VARCHAR" property="distributionChannel"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="channel_name" jdbcType="VARCHAR" property="channelName"/>
        <result column="out_channel_id" jdbcType="BIGINT" property="outChannelId"/>
        <result column="out_channel_name" jdbcType="VARCHAR" property="outChannelName"/>
        <result column="ad_channel_id" jdbcType="BIGINT" property="adChannelId"/>
        <result column="ad_channel_name" jdbcType="VARCHAR" property="adChannelName"/>
        <result column="ad_ext" jdbcType="VARCHAR" property="adExt"/>
        <result column="pay_channel_id" jdbcType="INTEGER" property="payChannelId"/>
        <result column="pay_channel" jdbcType="VARCHAR" property="payChannel"/>
        <result column="pay_channel_pkg_id" jdbcType="INTEGER" property="payChannelPkgId"/>
        <result column="pay_channel_pkg_name" jdbcType="VARCHAR" property="payChannelPkgName"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="prod_id" jdbcType="BIGINT" property="prodId"/>
        <result column="prod_name" jdbcType="VARCHAR" property="prodName"/>
        <result column="redeem_limit" jdbcType="INTEGER" property="redeemLimit"/>
        <result column="redeem_remain" jdbcType="INTEGER" property="redeemRemain"/>
        <result column="cycle_type" jdbcType="TINYINT" property="cycleType"/>
        <result column="cycle_redeem_limit" jdbcType="INTEGER" property="cycleRedeemLimit"/>
        <result column="order_amount" jdbcType="DECIMAL" property="orderAmount"/>
        <result column="preorder_status" jdbcType="TINYINT" property="preorderStatus"/>
        <result column="preorder_content" jdbcType="VARCHAR" property="preorderContent"/>
        <result column="pay_status" jdbcType="TINYINT" property="payStatus"/>
        <result column="pay_notify_content" jdbcType="VARCHAR" property="payNotifyContent"/>
        <result column="pay_notify_time" jdbcType="TIMESTAMP" property="payNotifyTime"/>
        <result column="order_status" jdbcType="TINYINT" property="orderStatus"/>
        <result column="extra_data" jdbcType="VARCHAR" property="extraData"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="package_name" jdbcType="VARCHAR" property="packageName"/>
        <result column="city_adcode" jdbcType="INTEGER" property="cityAdcode"/>
        <result column="sms_code" jdbcType="VARCHAR" property="smsCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        order_id, order_no, out_order_no, distribution_channel, channel_id, channel_name,
        ad_channel_id, ad_channel_name, ad_ext, pay_channel_id, pay_channel, pay_channel_pkg_id,
        pay_channel_pkg_name, phone, prod_id, prod_name, redeem_limit, redeem_remain, cycle_type,
        cycle_redeem_limit, order_amount, preorder_status, preorder_content, pay_status,
        pay_notify_content, pay_notify_time, order_status, extra_data, create_time, update_time,
        deleted, out_channel_id, out_channel_name, package_name, city_adcode, sms_code
    </sql>

    <sql id="BenefitOrderDO_Join_Column_List">
        bo
        .
        order_id
        ,bo.order_no,bo.out_order_no,bo.distribution_channel,bo.channel_id,bo.channel_name,bo.ad_channel_id,
    bo.ad_channel_name,bo.ad_ext,bo.pay_channel_id,bo.pay_channel,bo.pay_channel_pkg_id,bo.pay_channel_pkg_name,
    bo.phone,bo.prod_id,bo.prod_name,bo.redeem_limit,bo.redeem_remain,bo.cycle_type,bo.cycle_redeem_limit,bo.order_amount,
    bo.preorder_status,bo.preorder_content,bo.pay_status,bo.pay_notify_content,bo.pay_notify_time,bo.order_status,
    bo.extra_data,bo.create_time,bo.update_time,bo.deleted,bo.out_channel_id,bo.out_channel_name,bo.package_name,
    bo.city_adcode, bo.sms_code
    </sql>


    <update id="decreaseOrderRemainNum">
        update benefit_order
        set redeem_remain = redeem_remain - 1
        where order_id = #{orderId}
          and redeem_remain > 0
    </update>

    <select id="joinAdInfoPage" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO">
        SELECT t.*, t1.provinceName, t1.cityName FROM (
        SELECT
        <include refid="Base_Column_List"/>
        FROM benefit_order
        ${ew.customSqlSegment}
        LIMIT #{offset}, #{limit}
        ) t LEFT JOIN area_code t1 ON t1.cityAdcode = t.city_adcode
    </select>

    <select id="pageCount" resultType="long">
        SELECT count(1)
        FROM benefit_order ${ew.customSqlSegment}
    </select>
</mapper>