<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccOrderDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccOrderDO">
    <!--@mbg.generated-->
    <!--@Table ecc_order-->
    <id column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
    <result column="phone_area" jdbcType="VARCHAR" property="phoneArea" />
    <result column="month" jdbcType="VARCHAR" property="month" />
    <result column="user_net_status" jdbcType="TINYINT" property="userNetStatus" />
    <result column="redeemable" jdbcType="TINYINT" property="redeemable" />
    <result column="unavailable_reason" jdbcType="VARCHAR" property="unavailableReason" />
    <result column="redeem_limit" jdbcType="INTEGER" property="redeemLimit" />
    <result column="redeem_remain" jdbcType="INTEGER" property="redeemRemain" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="activation_time" jdbcType="TIMESTAMP" property="activationTime" />
    <result column="service_suspension_time" jdbcType="TIMESTAMP" property="serviceSuspensionTime" />
    <result column="closure_time" jdbcType="TIMESTAMP" property="closureTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    order_id, order_no, `name`, phone, prod_id, prod_name, channel_id, channel_type, channel_name,
    balance, phone_area, `month`, user_net_status, redeemable, unavailable_reason, redeem_limit, 
    redeem_remain, order_status, activation_time, service_suspension_time, closure_time, 
    remark, create_time, update_time, deleted
  </sql>

  <update id="createMonthTableIfNotExists">
    CREATE TABLE IF NOT EXISTS ecc_order_${month} LIKE ecc_order
  </update>

  <update id="reduceRemainAndSetStatus">
    UPDATE ecc_order
    SET redeem_remain = redeem_remain - 1,
    order_status = CASE WHEN redeem_remain = 0 THEN 1 ELSE order_status END,
    update_time = NOW()
    WHERE order_id = #{orderId}
  </update>

  <select id="selectTableByTableName" resultType="java.lang.String">
    SHOW TABLES LIKE #{tableName}
  </select>
</mapper>