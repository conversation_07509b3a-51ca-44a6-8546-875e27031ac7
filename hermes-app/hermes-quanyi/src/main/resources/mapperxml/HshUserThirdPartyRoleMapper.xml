<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.HshUserThirdPartyRoleMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.HshUserThirdPartyRoleDO">
    <!--@mbg.generated-->
    <!--@Table hsh_user_third_party_role-->
    <result column="hsh_user_id" jdbcType="BIGINT" property="hshUserId" />
    <result column="supplier" jdbcType="INTEGER" property="supplier" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    hsh_user_id, supplier, role_id, create_time, update_time
  </sql>
</mapper>