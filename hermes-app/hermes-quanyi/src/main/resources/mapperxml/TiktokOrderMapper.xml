<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.TiktokOrderMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.TiktokOrderDO">
    <!--@mbg.generated-->
    <!--@Table sl_tiktok_order-->
    <id column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="biz_order_no" jdbcType="VARCHAR" property="bizOrderNo" />
    <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="sku_title" jdbcType="VARCHAR" property="skuTitle" />
    <result column="ecc_prod_id" jdbcType="BIGINT" property="eccProdId" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="price" jdbcType="INTEGER" property="price" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="pay_status" jdbcType="INTEGER" property="payStatus" />
    <result column="pay_way" jdbcType="TINYINT" property="payWay" />
    <result column="pay_way_order" jdbcType="VARCHAR" property="payWayOrder" />
    <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    order_id, order_no, biz_order_no, out_order_no, sku_id, sku_title, ecc_prod_id, open_id,
    price, quantity, order_time, pay_status, pay_way, pay_way_order, sync_time, create_time,
    update_time
  </sql>
</mapper>