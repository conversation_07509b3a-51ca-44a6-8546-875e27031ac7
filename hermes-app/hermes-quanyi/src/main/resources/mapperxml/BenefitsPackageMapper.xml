<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitsPackageMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageDO">
    <!--@mbg.generated-->
    <!--@Table benefits_package-->
    <result column="package_id" jdbcType="OTHER" property="packageId" />
    <result column="package_name" jdbcType="OTHER" property="packageName" />
    <result column="package_code" jdbcType="OTHER" property="packageCode" />
    <result column="redemption_period" jdbcType="OTHER" property="redemptionPeriod" />
    <result column="selling_price" jdbcType="OTHER" property="sellingPrice" />
    <result column="redemption_limit" jdbcType="OTHER" property="redemptionLimit" />
    <result column="status" jdbcType="OTHER" property="status" />
    <result column="create_time" jdbcType="OTHER" property="createTime" />
    <result column="update_time" jdbcType="OTHER" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    package_id, package_name, package_code, redemption_period, selling_price, redemption_limit,
    `status`, create_time, update_time
  </sql>
</mapper>