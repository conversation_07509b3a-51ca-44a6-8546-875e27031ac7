<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.AreaCodeMapper">
    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.AreaCodeDO">
        <id column="id" jdbcType="TINYINT" property="id" />
        <result column="provinceName" jdbcType="VARCHAR" property="provinceName" />
        <result column="cityName" jdbcType="VARCHAR" property="cityName" />
        <result column="provinceAdcode" jdbcType="TINYINT" property="provinceAdcode" />
        <result column="provinceCenter" jdbcType="VARCHAR" property="provinceCenter" />
        <result column="cityCode" jdbcType="VARCHAR" property="cityCode" />
        <result column="cityAdcode" jdbcType="TINYINT" property="cityAdcode" />
        <result column="cityCenter" jdbcType="VARCHAR" property="cityCenter" />
        <result column="districts" jdbcType="VARCHAR" property="districts" />
        <result column="isoCode" jdbcType="VARCHAR" property="isoCode" />
    </resultMap>
    <sql id="Base_Column_List">
        id, provinceName, cityName, provinceAdcode, provinceCenter, cityCode, cityAdcode, cityCenter, districts, isoCode
    </sql>
</mapper>