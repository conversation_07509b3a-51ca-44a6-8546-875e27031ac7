<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitOrderExtensionMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderExtensionDO">
    <!--@mbg.generated-->
    <!--@Table benefit_order_extension-->
    <id column="extension_id" jdbcType="BIGINT" property="extensionId" />
    <result column="channel_order_no" jdbcType="VARCHAR" property="channelOrderNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="mobile" jdbcType="INTEGER" property="mobile" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="pay_channel_id" jdbcType="INTEGER" property="payChannelId" />
    <result column="pay_channel_name" jdbcType="VARCHAR" property="payChannelName" />
    <result column="pay_pkg_id" jdbcType="BIGINT" property="payPkgId" />
    <result column="pay_pkg_name" jdbcType="VARCHAR" property="payPkgName" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    extension_id, channel_order_no, order_no, mobile, channel_id, channel_name, pay_channel_id,
    pay_channel_name, pay_pkg_id, pay_pkg_name, order_source, create_time, update_time
  </sql>
</mapper>