<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.AdminMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, username, `password`,role, nickname, phone, email, `status`, create_by, create_time,
        update_by, update_time, deleted
    </sql>

    <select id="findByUsername" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.AdminDO">
        select
        <include refid="Base_Column_List"/>
        from `admin`
        where username = #{username}
        limit 1
    </select>

    <select id="findOne" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.AdminDO">
        select
        <include refid="Base_Column_List"/>
        from `admin`
        where id = #{id}
    </select>

    <select id="countByAdminListReq" resultType="java.lang.Long">
        select count(*)
        from `admin`
        <include refid="AdminListReqWhere"/>
    </select>

    <select id="pageByAdminListReq" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.AdminDO">
        select
        <include refid="Base_Column_List"/>
        from `admin`
        <include refid="AdminListReqWhere"/>
        limit #{offset},#{size}
    </select>

    <sql id="AdminListReqWhere">
        <where>
            <if test="username != null and username != ''">and username = #{username}</if>
            <if test="nickname != null and nickname != ''">
                and nickname like concat(#{nickname}, '%')
            </if>
            <if test="phone != null and phone != ''">and phone like concat('%', #{phone}, '%')</if>
            <if test="email != null and email != ''">and email like concat('%', #{email}, '%')</if>
            <if test="status != null">and status = #{status}</if>
            and deleted = 0
        </where>
    </sql>
    <select id="disableOrEnableAdmin" resultType="int">
        update `admin`
        set status = #{tarStatus},
        update_by =#{updateBy},
        update_time=now()
        where id = #{adminId}
        and status = #{orgStatus}
    </select>

    <update id="modifyPassword">
        update `admin`
        set `password` = #{password},
        update_by = #{updateBy},
        update_time = now()
        where id = #{adminId}
    </update>
</mapper>