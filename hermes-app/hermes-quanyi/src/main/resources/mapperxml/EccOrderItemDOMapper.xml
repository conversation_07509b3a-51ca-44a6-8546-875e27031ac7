<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccOrderItemDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccOrderItemDO">
    <!--@mbg.generated-->
    <!--@Table ecc_order_item-->
    <id column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="item_no" jdbcType="VARCHAR" property="itemNo" />
    <result column="ecc_user_id" jdbcType="BIGINT" property="eccUserId" />
    <result column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
    <result column="goods_id" jdbcType="BIGINT" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_order_no" jdbcType="VARCHAR" property="supplierOrderNo" />
    <!--redeemType-->
    <result column="redeem_type" jdbcType="TINYINT" property="redeemType" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="cost_price" jdbcType="DECIMAL" property="costPrice" />
    <result column="redeem_code_id" jdbcType="BIGINT" property="redeemCodeId" />
    <result column="preorder_status" jdbcType="TINYINT" property="preorderStatus" />
    <result column="preorder_content" jdbcType="VARCHAR" property="preorderContent" />
    <result column="preorder_time" jdbcType="TIMESTAMP" property="preorderTime" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="abnormal_reason" jdbcType="VARCHAR" property="abnormalReason" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    item_id, phone, order_id, item_no, ecc_user_id, prod_id, prod_name, goods_id, goods_name, 
    supplier_id, supplier_name, supplier_order_no, redeem_type, price, cost_price, redeem_code_id,
    preorder_status, preorder_content, preorder_time, order_status, abnormal_reason, 
    remark, create_time, update_time, deleted
  </sql>

  <update id="createMonthTableIfNotExists">
    CREATE TABLE IF NOT EXISTS ecc_order_item_${month} LIKE ecc_order_item
  </update>
</mapper>