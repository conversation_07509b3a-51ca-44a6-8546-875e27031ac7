<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitChannelDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitChannelDO">
    <!--@mbg.generated-->
    <!--@Table benefit_channel-->
    <id column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    channel_id, channel_name, create_time, update_time, deleted
  </sql>
</mapper>