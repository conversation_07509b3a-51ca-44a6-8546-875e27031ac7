<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GoodsVirtualSkuMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, sku_no, sku_name, sku_image, goods_id, third_no, sale_price, market_price, purchase_price,
        face_amount, `status`, create_time, update_time, deleted
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into goods_virtual_sku
        (sku_no, sku_name, sku_image, goods_id, third_no, sale_price, market_price, purchase_price,
        face_amount, `status`, create_time, update_time, deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.skuNo}, #{item.skuName}, #{item.skuImage}, #{item.goodsId}, #{item.thirdNo},
            #{item.salePrice}, #{item.marketPrice}, #{item.purchasePrice}, #{item.faceAmount},
            #{item.status}, #{item.createTime}, #{item.updateTime}, #{item.deleted})
        </foreach>
    </insert>

    <sql id="goodsVirtualStockRsp_Column_List">
        s.id                             as skuId,
        s.sku_no                         as skuNo,
        v.id                             as goodsId,
        v.`name`                         as goodsName,
        s.sku_name                       as skuName,
        t.real_amount                    as realAmount,
        t.sell_amount                    as sellAmount,
        t.disable_amount                 as disableAmount,
        t.real_amount - t.expires_amount as effectiveAmount,
        t.wait_expires_amount            as waitExpiresAmount,
        t.expires_amount                 as expiresAmount,
        t.warn_amount                    as warnAmount
    </sql>

    <update id="deleteByIdIn">
        update goods_virtual_sku
        set deleted = 1,
        update_time = now()
        where id in
        <foreach close=")" collection="ids" item="id" open="(" separator=",">
            #{id}
        </foreach>
    </update>
    <select id="findByGoodsId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualSkuDO">
        select
        <include refid="Base_Column_List"/>
        from goods_virtual_sku
        where goods_id = #{goodsId}
        and deleted = 0
    </select>

    <sql id="GoodsVirtualStockReqWhere">
        <where>
            <if test="req.skuNo != null and req.skuNo != ''">and s.sku_no = #{req.skuNo}</if>
            <if test="req.skuId != null">and s.id = #{req.skuId}</if>
            <if test="req.goodsId != null">and s.goods_id = #{req.goodsId}</if>
            <if test="req.skuName != null and req.skuName != ''">
                and s.sku_name like concat('%', #{req.skuName} , '%')
            </if>
            and v.type = 1
            and s.deleted = 0
            and v.deleted = 0
        </where>
    </sql>

    <select id="countByGoodsVirtualStockReq" resultType="java.lang.Long">
        SELECT count(*)
        FROM goods_virtual_sku s
        LEFT JOIN goods_virtual v ON s.goods_id = v.id
        LEFT JOIN goods_virtual_stock t ON s.id = t.sku_id
        <include refid="GoodsVirtualStockReqWhere"/>
    </select>


    <select id="pageByGoodsVirtualStockReq" resultType="com.yuelan.hermes.quanyi.controller.response.GoodsVirtualStockRsp">
        SELECT
        <include refid="goodsVirtualStockRsp_Column_List"/>
        FROM goods_virtual_sku s
        LEFT JOIN goods_virtual v ON s.goods_id = v.id
        LEFT JOIN goods_virtual_stock t ON s.id = t.sku_id
        <include refid="GoodsVirtualStockReqWhere"/>
        order by ${req.orderBy}
        limit #{req.offset},#{req.size}
    </select>

    <select id="statisticsByGoodsVirtualStockReq" resultType="com.yuelan.hermes.quanyi.controller.response.GoodsVirtualStockStatisticsRsp">
        SELECT sum(real_amount) as realAmount,
        sum(sell_amount) as sellAmount,
        sum(wait_expires_amount) as waitExpiresAmount,
        sum(expires_amount) as expiresAmount,
        sum(disable_amount) as disableAmount
        FROM goods_virtual_sku s
        LEFT JOIN goods_virtual v ON s.goods_id = v.id
        LEFT JOIN goods_virtual_stock t ON s.id = t.sku_id
        <include refid="GoodsVirtualStockReqWhere"/>
    </select>

    <select id="findOne" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualSkuDO">
        select
        <include refid="Base_Column_List"/>
        from goods_virtual_sku
        where id = #{skuId}
        and deleted = 0
    </select>

    <update id="updateStatusById">
        update goods_virtual_sku
        set `status` = #{status},update_time = now()
        where goods_id=#{goodsId}
        and deleted = 0
    </update>

    <select id="cursorAll" resultType="java.lang.Long">
        select t1.id
        from goods_virtual_sku t1 left join goods_virtual t2 on t1.goods_id = t2.id
        <where>
            <if test="minId != null">t1.id <![CDATA[ > ]]>#{minId}</if>
            and t2.type = 1
            and t1.deleted = 0
            and t2.deleted = 0
        </where>
        limit 1000
    </select>

    <select id="findByGoodsIdIn" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualSkuDO">
        select
        <include refid="Base_Column_List"/>
        from goods_virtual_sku
        where goods_id in
        <foreach close=")" collection="goodsIds" item="goodsId" open="(" separator=",">
            #{goodsId}
        </foreach>
        and deleted = 0
    </select>

    <select id="findBySkuNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualSkuDO">
        select
        <include refid="Base_Column_List"/>
        from goods_virtual_sku
        where sku_no = #{skuNo}
        and deleted = 0
    </select>

    <select id="searchSku" resultType="com.yuelan.hermes.quanyi.controller.response.GoodsVirtualSkuSearchRsp">
        SELECT * FROM (
        SELECT s.id AS skuId,s.sku_no AS skuNo,s.sku_name AS skuName,g.id AS goodsId,g.name AS goodsName,g.type AS goodsType,s.deleted
        FROM goods_virtual_sku s left join goods_virtual g on s.goods_id = g.id
        <where>
            <if test="goodsId != null">s.goods_id = #{goodsId}</if>
            <if test="keyword != null and keyword != ''">
                and ( s.sku_no = #{keyword}
                or s.sku_name like concat('%', #{keyword}, '%')
                <if test="skuId != null">or s.id = #{skuId}</if>
                )
            </if>
        </where>
        LIMIT 50
        ) t
        WHERE t.deleted = 0
    </select>
</mapper>