<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.UserSubscriptionMapper">
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, phone, product_id, sub_status, first_sub_time, latest_renewal_time, first_order_no,
        renewal_order_nos, sub_count, create_time, update_time, deleted
    </sql>

    <select id="selectByPhoneAndProduct" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.UserSubDO">
        select
        <include refid="Base_Column_List"/>
        from user_sub
        where phone = #{phone}
        and product_id = #{productId}
        and deleted = 0
    </select>

    <update id="updateByPhoneAndProduct">
        update user_sub set update_time = #{updateTime}
        <if test="subStatus != null">, sub_status = #{subStatus}</if>
        <if test="firstSubTime != null">, first_sub_time = #{firstSubTime}</if>
        <if test="latestRenewalTime != null">, latest_renewal_time = #{latestRenewalTime}</if>
        <if test="firstOrderNo != null">, first_order_no = #{firstOrderNo}</if>
        <if test="renewalOrderNos != null and renewalOrderNos != ''">, renewal_order_nos = #{renewalOrderNos}</if>
        <if test="subCount != null">, sub_count = #{subCount}</if>
        where phone = #{phone} and product_id = #{productId} and deleted = 0
    </update>

    <update id="autoUpdateToNoSubStatus">
        update user_sub
        set sub_status = 0
        where sub_status = 1
          and date_add(latest_renewal_time, interval 2 month) <![CDATA[ < ]]> now()
          and deleted = 0
    </update>
</mapper>