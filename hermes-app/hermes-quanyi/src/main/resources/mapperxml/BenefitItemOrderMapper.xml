<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitItemOrderMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemOrderDO">
    <!--@mbg.generated-->
    <!--@Table benefit_item_order-->
    <id column="item_order_id" jdbcType="BIGINT" property="itemOrderId" />
    <result column="extension_id" jdbcType="BIGINT" property="extensionId" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="original_item_order_id" jdbcType="BIGINT" property="originalItemOrderId" />
    <result column="supplier_request_order" jdbcType="VARCHAR" property="supplierRequestOrder" />
    <result column="benefit_item_id" jdbcType="BIGINT" property="benefitItemId" />
    <result column="benefit_item_name" jdbcType="VARCHAR" property="benefitItemName" />
    <result column="dispatch_timing" jdbcType="TINYINT" property="dispatchTiming" />
    <result column="delivery_type" jdbcType="TINYINT" property="deliveryType" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_goods_param" jdbcType="VARCHAR" property="supplierGoodsParam" />
    <result column="supplier_order_no" jdbcType="VARCHAR" property="supplierOrderNo" />
    <result column="redeem_code_id" jdbcType="BIGINT" property="redeemCodeId" />
    <result column="redeem_code" jdbcType="VARCHAR" property="redeemCode" />
    <result column="redeem_code_pwd" jdbcType="VARCHAR" property="redeemCodePwd" />
    <result column="redeem_code_expire_time" jdbcType="TIMESTAMP" property="redeemCodeExpireTime" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="process_state" jdbcType="TINYINT" property="processState" />
    <result column="process_state_desc" jdbcType="VARCHAR" property="processStateDesc" />
    <result column="request" jdbcType="VARCHAR" property="request" />
    <result column="request_time" jdbcType="TIMESTAMP" property="requestTime" />
    <result column="response" jdbcType="VARCHAR" property="response" />
    <result column="redemption_status" jdbcType="TINYINT" property="redemptionStatus" />
    <result column="redemption_time" jdbcType="TIMESTAMP" property="redemptionTime" />
    <result column="callback_req" jdbcType="VARCHAR" property="callbackReq" />
    <result column="callback_time" jdbcType="TIMESTAMP" property="callbackTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    item_order_id, extension_id, order_type, original_item_order_id, supplier_request_order,
    benefit_item_id, benefit_item_name, dispatch_timing, delivery_type, mobile, supplier_id,
    supplier_name, supplier_goods_param, supplier_order_no, redeem_code_id, redeem_code,
    redeem_code_pwd, redeem_code_expire_time, order_status, process_state, process_state_desc,
    request, request_time, response, redemption_status, redemption_time, callback_req,
    callback_time, create_time, update_time
  </sql>
</mapper>