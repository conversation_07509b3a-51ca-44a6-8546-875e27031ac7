<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.TempGamingRedeemCodeDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.TempGamingRedeemCodeDO">
    <!--@mbg.generated-->
    <!--@Table temp_gaming_redeem_code-->
    <id column="code_id" jdbcType="BIGINT" property="codeId" />
    <result column="redeem_code" jdbcType="VARCHAR" property="redeemCode" />
    <result column="used" jdbcType="TINYINT" property="used" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="INTEGER" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    code_id, redeem_code, used, phone, create_time, update_time, deleted
  </sql>

  <update id="updateOneUsedByGoodsIdAndValidity">
    UPDATE temp_gaming_redeem_code
        SET used = 1,
        phone            = #{phone},
        update_time        = now()
        WHERE used = 0
        ORDER BY code_id
        LIMIT 1
  </update>
</mapper>