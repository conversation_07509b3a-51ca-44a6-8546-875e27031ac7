<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GoodsVirtualImgMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, goods_id, images, create_time, update_time, deleted
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into goods_virtual_img(goods_id, images, create_time, update_time, deleted)
        values (#{goodsId}, #{images}, #{createTime}, #{updateTime}, #{deleted})
        ON duplicate KEY UPDATE images = #{images},update_time=#{updateTime}
    </insert>

    <select id="findByGoodsId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualImgDO">
        select
        <include refid="Base_Column_List"/>
        from goods_virtual_img
        where goods_id = #{goodsId}
    </select>

    <update id="updateImg">
        update goods_virtual_img
        set images = #{imgJson},
        update_time = NOW()
        WHERE goods_id = #{goodsId}
    </update>

    <select id="findImgByGoodsId" resultType="java.lang.String">
        select images
        from goods_virtual_img
        where goods_id = #{goodsId}
    </select>
</mapper>