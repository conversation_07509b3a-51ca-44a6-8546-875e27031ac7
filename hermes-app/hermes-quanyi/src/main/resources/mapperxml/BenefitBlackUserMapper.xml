<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitBlackUserMapper">
    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitBlackUserDO">
        <id column="id" property="id"/>
        <result column="biz_type" property="bizType"/>
        <result column="biz_value" property="bizValue"/>
        <result column="pay_channel_id" property="payChannelId"/>
        <result column="pay_channel_pkg_id" property="payChannelPkgId"/>
        <result column="expire_time" property="expireTime"/>
        <result column="status" property="status"/>
        <result column="deleted" property="deleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , biz_type, biz_value, pay_channel_id,pay_channel_pkg_id, expire_time, deleted, create_time, update_time,status
    </sql>
</mapper>