<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.MerchantMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, mch_id, `name`, username, `password`, phone, email, `status`, create_by, create_time,
        update_by, update_time, deleted
    </sql>

    <select id="findAll" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.MerchantDO">
        select
        <include refid="Base_Column_List"/>
        from merchant
        where deleted = 0
    </select>


    <select id="search" resultType="com.yuelan.hermes.quanyi.controller.response.MchRsp">
        SELECT id as merchantId, name as merchantName, mch_id as mchId
        FROM merchant
        WHERE deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (
            mch_id = #{keyword}
            OR name like concat('%', #{keyword}, '%')
            )
        </if>
        limit 20
    </select>

    <select id="findByMchId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.MerchantDO">
        select
        <include refid="Base_Column_List"/>
        from merchant
        where mch_id =#{mchId}
        and deleted = 0
        limit 1

    </select>

    <select id="findOne" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.MerchantDO">
        select
        <include refid="Base_Column_List"/>
        from merchant
        where id =#{merchantId}
        and deleted = 0
        limit 1
    </select>

    <select id="countByMerchantListReq" resultType="long">
        select count(*)
        from merchant
        <include refid="MerchantListReqWhere"/>
    </select>

    <select id="pageByMerchantListReq" resultType="com.yuelan.hermes.quanyi.controller.response.MerchantRsp">
        select
        <include refid="Base_Column_List"/>
        from merchant
        <include refid="MerchantListReqWhere"/>
        order by id desc
        limit #{offset},#{size}
    </select>
    <sql id="MerchantListReqWhere">
        <where>
            <if test="id != null">id =#{id}</if>
            <if test="mchId != null and mchId != ''">and mch_id =#{mchId}</if>
            <if test="name != null and name != ''">and `name` like concat(#{name}, '%')</if>
            <if test="phone != null and phone != ''">and phone like concat(#{phone}, '%')</if>
            <if test="status != null">and `status` = #{status}</if>
            and deleted = 0
        </where>
    </sql>
</mapper>