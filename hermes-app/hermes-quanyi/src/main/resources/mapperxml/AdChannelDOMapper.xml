<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.AdChannelDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.AdChannelDO">
    <!--@mbg.generated-->
    <!--@Table ecc_ad_channel-->
    <id column="ad_channel_id" jdbcType="BIGINT" property="adChannelId" />
    <result column="ad_channel_name" jdbcType="VARCHAR" property="adChannelName" />
    <result column="ad_channel_code" jdbcType="VARCHAR" property="adChannelCode" />
    <result column="module_type" jdbcType="INTEGER" property="moduleType" />
    <result column="macro_parameters" jdbcType="VARCHAR" property="macroParameters" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ad_channel_id, ad_channel_name, ad_channel_code, module_type, macro_parameters, create_time,
    update_time, deleted
  </sql>
</mapper>