<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.MerchantAccessMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, merchant_id, algorithm, secret_key, public_key_path, private_key_path, third_public_key_path,
        create_time, update_time, deleted
    </sql>

    <select id="findByMerchantId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.MerchantAccessDO">
        select
        <include refid="Base_Column_List"/>
        from merchant_access
        where merchant_id = #{merchantId}
        and deleted = 0
    </select>

    <select id="findByMerchantIdIn" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.MerchantAccessDO">
        select
        <include refid="Base_Column_List"/>
        from merchant_access
        where merchant_id in
        <foreach close=")" collection="merchantIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and deleted = 0
    </select>



</mapper>