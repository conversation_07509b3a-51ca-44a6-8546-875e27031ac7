<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GoodsVirtualContentMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, goods_id, content, create_time, update_time, deleted
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into goods_virtual_content
        (goods_id, content, create_time, update_time, deleted)
        values (#{goodsId}, #{content}, #{createTime}, #{updateTime}, #{deleted})
        ON duplicate KEY UPDATE content = #{content},update_time=#{updateTime}
    </insert>

    <select id="findByGoodsId" resultType="java.lang.String">
        select content
        from goods_virtual_content
        where goods_id = #{goodsId}
    </select>

    <update id="updateByGoodsId">
        update goods_virtual_content
        set content = #{content},update_time = now()
        where goods_id = #{goodsId}
    </update>
</mapper>