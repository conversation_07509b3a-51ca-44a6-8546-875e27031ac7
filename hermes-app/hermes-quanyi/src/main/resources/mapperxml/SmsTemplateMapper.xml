<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.SmsTemplateMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, title, template_no, biz_type, channel, out_template_id, content, signature, create_time,
        update_time, deleted
    </sql>

    <select id="findByTemplateNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.SmsTemplateDO">
        select
        <include refid="Base_Column_List"/>
        from sms_template
        where template_no = #{templateNo}
        and deleted=0
    </select>

    <select id="findSmsTemplateRspByBizType" resultType="com.yuelan.hermes.quanyi.controller.response.SmsTemplateRsp">
        select template_no, title, content
        from sms_template
        where biz_type = #{bizType}
        and deleted = 0
    </select>
</mapper>