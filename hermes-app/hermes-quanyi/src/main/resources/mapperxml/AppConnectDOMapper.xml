<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.AppConnectDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.AppConnectDO">
    <!--@mbg.generated-->
    <!--@Table app_connect-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="social_type" jdbcType="TINYINT" property="socialType" />
    <result column="biz_user_id" jdbcType="VARCHAR" property="bizUserId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="biz_unionid" jdbcType="VARCHAR" property="bizUnionid" />
    <result column="biz_temp_session" jdbcType="VARCHAR" property="bizTempSession" />
    <result column="temp_token" jdbcType="VARCHAR" property="tempToken" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, social_type, biz_user_id, user_id, nick_name, image_url, biz_unionid, biz_temp_session, 
    temp_token, create_time, update_time, deleted
  </sql>
</mapper>