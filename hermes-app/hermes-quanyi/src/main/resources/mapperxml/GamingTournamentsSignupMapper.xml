<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GamingTournamentsSignupMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.GamingTournamentsSignup">
    <!--@mbg.generated-->
    <!--@Table gaming_tournaments_signup-->
    <id column="signup_id" jdbcType="BIGINT" property="signupId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="school" jdbcType="VARCHAR" property="school" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="rank" jdbcType="INTEGER" property="rank" />
    <result column="signup_time" jdbcType="TIMESTAMP" property="signupTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    signup_id, user_name, phone_number, school, role_name, role_id, `rank`, signup_time, 
    create_time, update_time
  </sql>
</mapper>