<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitGoodsMapper">
    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitGoodsDO">
        <!--@mbg.generated-->
        <!--@Table benefit_goods-->
        <id column="goods_id" jdbcType="BIGINT" property="goodsId"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="supplier_type" jdbcType="BIGINT" property="supplierType"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="goods_img" jdbcType="VARCHAR" property="goodsImg"/>
        <result column="cost_price" jdbcType="INTEGER" property="costPrice"/>
        <result column="price" jdbcType="INTEGER" property="price"/>
        <result column="goods_status" jdbcType="INTEGER" property="goodsStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        goods_id, goods_name, supplier_type, supplier_name, goods_img, cost_price, price, goods_status,
        create_time, update_time, deleted
    </sql>
</mapper>