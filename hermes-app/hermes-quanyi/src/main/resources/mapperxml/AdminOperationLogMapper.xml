<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.AdminOperationLogMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, admin_id, title, type_value, type_name, url, ip, request_data, response_data,
        `status`, error_msg, create_time, update_time, deleted
    </sql>

    <select id="findByAdminLogReq" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.AdminOperationLogDO">
        select
        <include refid="Base_Column_List"/>
        from `admin_operation_log`
        order by id desc
        limit #{offset},#{size}
    </select>

    <select id="countByAdminLogReq" resultType="java.lang.Long">
        select count(*)
        from `admin_operation_log`
    </select>
</mapper>