<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitProductDOMapper">
    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO">
        <!--@mbg.generated-->
        <!--@Table benefit_product-->
        <id column="prod_id" jdbcType="BIGINT" property="prodId" />
        <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
        <result column="prod_code" jdbcType="VARCHAR" property="prodCode" />
        <result column="distribution_channel" jdbcType="VARCHAR" property="distributionChannel" />
        <result column="price" jdbcType="DECIMAL" property="price" />
        <result column="pay_channel_id" jdbcType="INTEGER" property="payChannelId" />
        <result column="pay_channel_pkg_id" jdbcType="INTEGER" property="payChannelPkgId" />
        <result column="bg_color_code" jdbcType="VARCHAR" property="bgColorCode" />
        <result column="homepage" jdbcType="VARCHAR" property="homepage" />
        <result column="imgs" jdbcType="VARCHAR" property="imgs" />
        <result column="agreement_content" jdbcType="LONGVARCHAR" property="agreementContent" />
        <result column="distribution_url" jdbcType="VARCHAR" property="distributionUrl" />
        <result column="redeem_url" jdbcType="VARCHAR" property="redeemUrl" />
        <result column="redeem_limit" jdbcType="INTEGER" property="redeemLimit" />
        <result column="prod_status" jdbcType="INTEGER" property="prodStatus" />
        <result column="show_popover" jdbcType="TINYINT" property="showPopover" />
        <result column="pop_bg_path" jdbcType="VARCHAR" property="popBgPath" />
        <result column="pop_btn_path" jdbcType="VARCHAR" property="popBtnPath" />
        <result column="pop_cancel_path" jdbcType="VARCHAR" property="popCancelPath" />
        <result column="show_help_tel" jdbcType="TINYINT" property="showHelpTel" />
        <result column="show_order_result" jdbcType="TINYINT" property="showOrderResult" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="deleted" jdbcType="TINYINT" property="deleted" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        prod_id, prod_name, prod_code, distribution_channel, price, pay_channel_id, pay_channel_pkg_id,
        bg_color_code, homepage, imgs, agreement_content, distribution_url, redeem_url, redeem_limit,
        prod_status, show_popover, pop_bg_path, pop_btn_path, pop_cancel_path, show_help_tel, show_order_result
        create_time, update_time, deleted
    </sql>
</mapper>