<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.OrderMobileItemMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, item_no, order_no, supplier, supplier_type, supplier_goods_no, supplier_sku_no,
        supplier_order_no, supplier_price, remark, fail_reason, order_status, create_time, update_time,
        deleted
    </sql>

    <sql id="Data_Column_List">
        <include refid="Base_Column_List"/>,ext_data
    </sql>

    <select id="findByItemNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderMobileItemDO">
        select
        <include refid="Base_Column_List"/>
        from order_mobile_item
        where item_no =#{itemNo}
        and deleted = 0
    </select>

    <select id="findByOrderNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderMobileItemDO">
        select
        <include refid="Base_Column_List"/>
        from order_mobile_item
        where order_no =#{orderNo}
        and deleted = 0
    </select>


    <select id="findByItemNoIn" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderMobileItemDO">
        select
        <include refid="Base_Column_List"/>
        from order_mobile_item
        where item_no in
        <foreach close=")" collection="list" item="itemNo" open="(" separator=",">
            #{itemNo}
        </foreach>
        and deleted = 0
    </select>

    <update id="updateStatus">
        update order_mobile_item
        set order_status =#{orderStatus},
        <if test="extData != null and extData != ''">ext_data=#{extData},</if>
        update_time = now()
        where id = #{itemId}
    </update>
</mapper>