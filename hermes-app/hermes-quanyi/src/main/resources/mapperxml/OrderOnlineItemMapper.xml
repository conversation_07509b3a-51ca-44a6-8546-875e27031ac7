<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.OrderOnlineItemMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, item_no, order_no, supplier_type, supplier, supplier_goods_no, supplier_sku_no,
        supplier_order_no, purchase_price, sale_price, voucher_code, voucher_password, sn_code,
        qr_code_url, shop_url, start_date, end_date, use_status, use_name, use_phone, use_time,
        use_place, remark, order_status, create_time, update_time, deleted
    </sql>

    <sql id="Data_Column_List">
        <include refid="Base_Column_List"/>,ext_data
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into order_online_item
        (item_no, order_no, supplier_type, supplier, supplier_goods_no, supplier_sku_no,
        supplier_order_no, purchase_price, sale_price, voucher_code, voucher_password,
        sn_code, qr_code_url, shop_url, start_date, end_date, use_status, use_name, use_phone,
        use_time, use_place, remark, ext_data, order_status, create_time, update_time,
        deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.itemNo}, #{item.orderNo}, #{item.supplierType}, #{item.supplier}, #{item.supplierGoodsNo},
            #{item.supplierSkuNo}, #{item.supplierOrderNo}, #{item.purchasePrice}, #{item.salePrice},
            #{item.voucherCode}, #{item.voucherPassword}, #{item.snCode}, #{item.qrCodeUrl},
            #{item.shopUrl}, #{item.startDate}, #{item.endDate}, #{item.useStatus}, #{item.useName},
            #{item.usePhone}, #{item.useTime}, #{item.usePlace}, #{item.remark}, #{item.extData},
            #{item.orderStatus}, #{item.createTime}, #{item.updateTime}, #{item.deleted})
        </foreach>
        ON duplicate KEY UPDATE id=id
    </insert>


    <sql id="OrderVirtualRspWhere">
        o.create_time      AS orderCreateTime,
        o.order_no         AS orderNo,
        o.out_order_no     AS outOrderNo,
        o.phone            AS phone,
        o.user_account     AS userAccount,
        o.goods_name       AS goodsName,
        o.sku_name         AS skuName,
        o.sku_no           AS skuNo,
        i.supplier         AS supplier,
        o.mch_name         AS buyer,
        o.goods_type       AS goodsType,
        i.purchase_price   AS purchasePrice,
        i.sale_price       AS salePrice,
        o.quantity         AS quantity,
        o.order_status     AS orderStatus,
        i.voucher_code     AS voucherCode,
        i.voucher_password AS voucherPassword,
        i.qr_code_url      AS qrCodeUrl,
        i.sn_code          AS snCode,
        i.start_date       AS startDate,
        i.end_date         AS endDate
    </sql>

    <sql id="OrderVirtualListReqWhere">
        <where>
            <if test="req.orderNos != null and req.orderNos.size() > 0">
                and o.order_no in
                <foreach close=")" collection="req.orderNos" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.orderNo != null and req.orderNo != ''">and o.order_no = #{req.orderNo}</if>
            <if test="req.outOrderNo != null and req.outOrderNo != ''">and o.out_order_no = #{req.outOrderNo}</if>
            <if test="req.phone != null and req.phone != ''">and o.phone = #{req.phone}</if>
            <if test="req.userAccount != null and req.userAccount != ''">
                and o.user_account= #{req.userAccount}
            </if>
            <if test="req.orderStatus != null">and o.order_status = #{req.orderStatus}</if>
            <if test="req.notifyStatus != null">and o.notify_status = #{req.notifyStatus}</if>
            <if test="req.goodsType != null">and o.goods_type = #{req.goodsType}</if>
            <if test="req.voucherCode != null and req.voucherCode != ''">
                and i.voucher_code = #{req.voucherCode}
            </if>
            <if test="req.voucherPassword != null and req.voucherPassword != ''">
                and i.voucher_password = #{req.voucherPassword}
            </if>
            <if test="req.orderStartTime != null">and o.create_time <![CDATA[ >= ]]> #{req.orderStartTime}</if>
            <if test="req.orderEndTime != null">and o.create_time <![CDATA[ <= ]]> #{req.orderEndTime}</if>
            <if test="req.goodsId != null">and o.goods_id = #{req.goodsId}</if>
            <if test="req.skuNo != null and req.skuNo != ''">and o.sku_no= #{req.skuNo}</if>
            <if test="req.supplier != null and req.supplier != ''">
                and i.supplier = #{req.supplier}
            </if>
            <if test="req.mchId != null and req.mchId != ''">and o.mch_id = #{req.mchId}</if>
            and o.deleted = 0
        </where>
    </sql>
    <select id="countByOrderVirtualListReq" resultType="java.lang.Long">
        select count(*)
        FROM order_online o
        LEFT JOIN order_online_item i ON i.order_no = o.order_no and i.deleted = 0
        <include refid="OrderVirtualListReqWhere"/>
    </select>

    <select id="cursorByOrderVirtualListReq" resultType="java.lang.Long">
        select distinct o.id
        FROM order_online o
        LEFT JOIN order_online_item i ON i.order_no = o.order_no and i.deleted = 0
        <include refid="OrderVirtualListReqWhere"/>
        <if test="maxId != null">and o.id <![CDATA[ > ]]> #{maxId}</if>
        order by o.id
        limit 100
    </select>

    <select id="findByOrderIdIn" resultType="com.yuelan.hermes.quanyi.controller.response.OrderVirtualExportRsp">
        select
        <include refid="OrderVirtualRspWhere"/>
        FROM order_online o
        LEFT JOIN order_online_item i ON i.order_no = o.order_no and i.deleted = 0
        where o.id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findByOrderNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderOnlineItemDO">
        select
        <include refid="Base_Column_List"/>
        FROM order_online_item
        where order_no = #{orderNo}
        and deleted = 0
    </select>

    <select id="findOne" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderOnlineItemDO">
        select
        <include refid="Base_Column_List"/>
        FROM order_online_item
        where item_no = #{orderNo}
        and deleted = 0
    </select>


    <update id="updateStatus">
        update order_online_item
        set order_status = #{tarStatus},
        update_time = now()
        where item_no = #{itemNo}
    </update>

    <update id="updateOrderOnlineItemDO">
        update order_online_item
        <set>
            <if test="voucherCode != null and voucherCode != ''">voucher_code = #{voucherCode},</if>
            <if test="voucherPassword != null and voucherPassword != ''">
                voucher_password = #{voucherPassword},
            </if>
            <if test="qrCodeUrl != null and qrCodeUrl != ''">qr_code_url = #{qrCodeUrl},</if>
            <if test="snCode != null and snCode != ''">sn_code = #{snCode},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="useStatus != null">use_status = #{useStatus},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="extData != null and extData != ''">ext_data = #{extData},</if>
            update_time = now()
        </set>
        where id = #{id}
    </update>

    <update id="updateSupplierOrderNo">
        update order_online_item
        set supplier_order_no = #{supplierOrderNo},
        update_time = now()
        where id = #{id}
        and supplier_order_no = ''
    </update>

    <select id="findSupplierDistinct" resultType="java.lang.String">
        SELECT DISTINCT supplier
        FROM order_online_item
    </select>

    <select id="findByOrderVirtualListReq" resultType="java.lang.String">
        SELECT DISTINCT order_no
        FROM order_online_item
        <where>
            <if test="req.voucherCode != null and req.voucherCode != ''">
                and voucher_code = #{req.voucherCode}
            </if>
            <if test="req.voucherPassword != null and req.voucherPassword != ''">
                and voucher_password = #{req.voucherPassword}
            </if>
            and deleted = 0
        </where>
    </select>



</mapper>