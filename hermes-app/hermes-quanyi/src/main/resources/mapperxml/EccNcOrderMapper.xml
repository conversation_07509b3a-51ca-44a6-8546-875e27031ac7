<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccNcOrderMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccNcOrderDO">
    <!--@mbg.generated-->
    <!--@Table ecc_nc_order-->
    <id column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="channel_order_no" jdbcType="VARCHAR" property="channelOrderNo" />
    <result column="sp_order_no" jdbcType="VARCHAR" property="spOrderNo" />
    <result column="original_order_no" jdbcType="VARCHAR" property="originalOrderNo" />
    <result column="operator" jdbcType="INTEGER" property="operator" />
    <result column="prod_id" jdbcType="BIGINT" property="prodId" />
    <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="channel_type" jdbcType="TINYINT" property="channelType" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="ad_channel_id" jdbcType="BIGINT" property="adChannelId" />
    <result column="ad_channel_name" jdbcType="VARCHAR" property="adChannelName" />
    <result column="ad_ext" jdbcType="VARCHAR" property="adExt" />
    <result column="page_url" jdbcType="VARCHAR" property="pageUrl" />
    <result column="ad_agent_platform" jdbcType="TINYINT" property="adAgentPlatform" />
    <result column="id_card_name" jdbcType="VARCHAR" property="idCardName" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="sp_goods_id" jdbcType="VARCHAR" property="spGoodsId" />
    <result column="post_province_code" jdbcType="VARCHAR" property="postProvinceCode" />
    <result column="post_province" jdbcType="VARCHAR" property="postProvince" />
    <result column="post_city_code" jdbcType="VARCHAR" property="postCityCode" />
    <result column="post_city" jdbcType="VARCHAR" property="postCity" />
    <result column="post_district_code" jdbcType="VARCHAR" property="postDistrictCode" />
    <result column="post_district" jdbcType="VARCHAR" property="postDistrict" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="select_type" jdbcType="TINYINT" property="selectType" />
    <result column="req_card_time" jdbcType="TIMESTAMP" property="reqCardTime" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="card_status" jdbcType="TINYINT" property="cardStatus" />
    <result column="express_status" jdbcType="TINYINT" property="expressStatus" />
    <result column="express_company" jdbcType="VARCHAR" property="expressCompany" />
    <result column="express_no" jdbcType="VARCHAR" property="expressNo" />
    <result column="activate_time" jdbcType="TIMESTAMP" property="activateTime" />
    <result column="stop_time" jdbcType="TIMESTAMP" property="stopTime" />
    <result column="close_time" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="first_charge_amount" jdbcType="INTEGER" property="firstChargeAmount" />
    <result column="first_charge_time" jdbcType="TIMESTAMP" property="firstChargeTime" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="ext2" jdbcType="VARCHAR" property="ext2" />
    <result column="ext_json" jdbcType="VARCHAR" property="extJson" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    order_id, order_no, channel_order_no, sp_order_no, original_order_no, `operator`, 
    prod_id, prod_name, channel_id, channel_type, channel_name, ad_channel_id, ad_channel_name, 
    ad_ext, page_url, ad_agent_platform, id_card_name, id_card, sp_goods_id, post_province_code, 
    post_province, post_city_code, post_city, post_district_code, post_district, address, 
    contact_phone, phone, province_code, province, city_code, city, select_type, req_card_time, 
    order_status, card_status, express_status, express_company, express_no, activate_time, 
    stop_time, close_time, first_charge_amount, first_charge_time, fail_reason, ext, 
    ext2, ext_json, remark, create_time, update_time
  </sql>
</mapper>