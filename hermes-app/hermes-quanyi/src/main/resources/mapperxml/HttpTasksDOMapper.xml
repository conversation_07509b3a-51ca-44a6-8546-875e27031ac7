<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.HttpTasksDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.HttpTasksDO">
    <!--@mbg.generated-->
    <!--@Table http_tasks-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="content_type" jdbcType="TINYINT" property="contentType" />
    <result column="headers" jdbcType="VARCHAR" property="headers" />
    <result column="body" jdbcType="LONGVARCHAR" property="body" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="time_out" jdbcType="INTEGER" property="timeOut" />
    <result column="send_status" jdbcType="TINYINT" property="sendStatus" />
    <result column="accept_status" jdbcType="TINYINT" property="acceptStatus" />
    <result column="response_code" jdbcType="INTEGER" property="responseCode" />
    <result column="response_time" jdbcType="TIMESTAMP" property="responseTime" />
    <result column="response_body" jdbcType="VARCHAR" property="responseBody" />
    <result column="success_condition" jdbcType="VARCHAR" property="successCondition" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="max_retries" jdbcType="INTEGER" property="maxRetries" />
    <result column="scheduled_time" jdbcType="TIMESTAMP" property="scheduledTime" />
    <result column="retry_strategy" jdbcType="TINYINT" property="retryStrategy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, url, content_type, headers, body, send_time, time_out, send_status, accept_status, 
    response_code, response_time, response_body, success_condition, retry_count, max_retries, 
    scheduled_time, retry_strategy, create_time, update_time
  </sql>
</mapper>