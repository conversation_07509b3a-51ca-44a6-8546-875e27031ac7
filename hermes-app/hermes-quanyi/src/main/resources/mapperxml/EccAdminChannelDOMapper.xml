<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccAdminChannelDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccAdminChannelDO">
    <!--@mbg.generated-->
    <!--@Table ecc_admin_channel-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="admin_id" jdbcType="BIGINT" property="adminId" />
    <result column="admin_name" jdbcType="VARCHAR" property="adminName" />
    <result column="channel_ids" property="channelIds" typeHandler="com.yuelan.hermes.quanyi.biz.handler.typeHandler.ListLongTypeHandler" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, admin_id, admin_name, channel_ids, create_time, update_time, deleted
  </sql>
</mapper>