<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccTaoBaoUserDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccTaoBaoUserDO">
    <!--@mbg.generated-->
    <!--@Table ecc_tao_bao_user-->
    <id column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="bind_phone" jdbcType="VARCHAR" property="bindPhone" />
    <result column="taobao_user_id" jdbcType="VARCHAR" property="taobaoUserId" />
    <result column="taobao_user_nick" jdbcType="VARCHAR" property="taobaoUserNick" />
    <result column="taobao_open_uid" jdbcType="VARCHAR" property="taobaoOpenUid" />
    <result column="access_token" jdbcType="VARCHAR" property="accessToken" />
    <result column="expires_in" jdbcType="TIMESTAMP" property="expiresIn" />
    <result column="refresh_token" jdbcType="VARCHAR" property="refreshToken" />
    <result column="re_expires_in" jdbcType="TIMESTAMP" property="reExpiresIn" />
    <result column="bind_time" jdbcType="TIMESTAMP" property="bindTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    user_id, bind_phone, taobao_user_id, taobao_user_nick, taobao_open_uid, access_token, 
    expires_in, refresh_token, re_expires_in,bind_time, create_time, update_time, deleted
  </sql>
</mapper>