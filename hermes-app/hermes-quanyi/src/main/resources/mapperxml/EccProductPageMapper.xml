<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccProductPageMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccProductPageDO">
    <!--@mbg.generated-->
    <!--@Table ecc_product_page-->
    <id column="page_id" jdbcType="BIGINT" property="pageId" />
    <result column="product_id" jdbcType="BIGINT" property="productId" />
    <result column="index_img" jdbcType="VARCHAR" property="indexImg" />
    <result column="order_button_img" jdbcType="VARCHAR" property="orderButtonImg" />
    <result column="pricing_detail_img" jdbcType="VARCHAR" property="pricingDetailImg" />
    <result column="product_detail_img" jdbcType="VARCHAR" property="productDetailImg" />
    <result column="license_id" jdbcType="BIGINT" property="licenseId" />
    <result column="extra_img" jdbcType="VARCHAR" property="extraImg" />
    <result column="collapse_img" jdbcType="VARCHAR" property="collapseImg" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    page_id, product_id, index_img, order_button_img, pricing_detail_img, product_detail_img, 
    license_id, extra_img, collapse_img, create_time, update_time
  </sql>

  <update id="updateSetActivated">
    UPDATE ecc_product_page
    SET activated = IF(page_id = #{pageId}, 1, 0)
    WHERE product_id = #{prodId}
  </update>
</mapper>