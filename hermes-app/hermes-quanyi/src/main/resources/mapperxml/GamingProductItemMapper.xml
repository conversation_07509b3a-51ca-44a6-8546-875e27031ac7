<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GamingProductItemMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, product_id, goods_id, effective_date, expire_date, create_time, update_time, deleted
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into gaming_product_item
        (product_id, goods_id, effective_date, expire_date, create_time, update_time, deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productId}, #{item.goodsId}, #{item.effectiveDate}, #{item.expireDate}, #{item.createTime},
            #{item.updateTime}, #{item.deleted}
            )
        </foreach>
    </insert>

    <select id="existByGoodsId" resultType="java.lang.Long">
        select id
        from gaming_product_item
        where goods_id = #{goodsId}
        and deleted = 0
        limit 1
    </select>

    <update id="removeByProductId">
        update gaming_product_item
        set deleted = 1,
        update_time = now()
        where product_id = #{productId}
        and deleted = 0
    </update>

    <update id="removeById">
        update gaming_product_item
        set deleted = 1,
        update_time = now()
        where id = #{id}
        and deleted = 0
    </update>

    <select id="findByProductId" resultType="com.yuelan.hermes.quanyi.common.pojo.bo.GamingProductItemBO">
        select t1.id as itemId
             , t1.product_id
             , t2.goods_id
             , t2.goods_name
             , t2.goods_img
             , t2.`status`
             , t2.sale_price
             , t2.market_price
             , t2.purchase_price
             , t2.supplier_type
             , t2.supplier_goods_no
             , t2.create_time
             , t2.update_time
             , t2.deleted
             , t1.effective_date
             , t1.expire_date
             , t2.delivery_type
             , CASE
                   WHEN t2.delivery_type = 2
                       THEN IFNULL(gc.available_stock, 0)
            END      AS available_stock
             , CASE
                   WHEN t2.delivery_type = 2
                       THEN IFNULL(gc.near_expiry_stock, 0)
            END      AS near_expiry_stock
             , CASE
                   WHEN t2.delivery_type = 2
                       THEN IFNULL(gc.expired_stock, 0)
            END      AS expired_stock
             , CASE
                   WHEN t2.delivery_type = 2
                       THEN IFNULL(gc.used_stock, 0)
            END      AS used_stock
        from gaming_product_item t1
                 left join gaming_goods t2 on t1.goods_id = t2.goods_id and t2.deleted = 0
                 LEFT JOIN(SELECT goods_id,
                                  COUNT(CASE WHEN used = 0 AND expire_date &gt;= CURRENT_DATE THEN 1 END) AS available_stock,
                                  COUNT(CASE
                                            WHEN used = 0 AND
                                                 expire_date BETWEEN CURRENT_DATE AND DATE_ADD(CURRENT_DATE, INTERVAL 30 DAY)
                                                THEN 1 END)                                               AS near_expiry_stock,
                                  COUNT(CASE WHEN used = 0 AND expire_date &lt; CURRENT_DATE THEN 1 END)  AS expired_stock,
                                  COUNT(CASE WHEN used = 1 THEN 1 END)                                    AS used_stock
                           FROM gaming_redeem_code
                           WHERE deleted = 0
                           GROUP BY goods_id) gc ON t2.goods_id = gc.goods_id
        where t1.product_id = #{productId}
          and t1.deleted = 0
    </select>

    <select id="findById" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductItemDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_product_item
        where id = #{id}
        and deleted = 0
    </select>

    <select id="findByProductIdAndGoodsId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductItemDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_product_item
        where product_id = #{productId}
        and goods_id = #{goodsId}
        and deleted = 0
    </select>

    <select id="listByProductId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductItemDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_product_item
        where product_id = #{productId}
        and deleted = 0
    </select>
</mapper>