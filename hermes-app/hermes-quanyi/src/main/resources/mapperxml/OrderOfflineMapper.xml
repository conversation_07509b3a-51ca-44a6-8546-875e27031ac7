<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.OrderOfflineMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, order_no, buyer, goods_type, goods_id, goods_name, sku_id, sku_no, sku_name,
        sku_image, supplier_type, supplier_goods_no, supplier_sku_no, price, quantity, total_amount,
        order_status, order_model, sms_template, remark, create_by, create_time, update_time,
        deleted
    </sql>

    <sql id="OrderOfflineListReqWhere">
        <where>
            <if test="req.orderNo != null and req.orderNo != ''">and order_no = #{req.orderNo}</if>
            <if test="req.goodsId != null">and goods_id = #{req.goodsId}</if>
            <if test="req.skuNo != null and req.skuNo != ''">and sku_no= #{req.skuNo}</if>
            <if test="req.buyer != null and req.buyer != ''">and buyer = #{req.buyer}</if>
            <if test="req.orderStartTime != null">and create_time <![CDATA[ >= ]]> #{req.orderStartTime}</if>
            <if test="req.orderEndTime != null">and create_time <![CDATA[ <= ]]> #{req.orderEndTime}</if>
            <if test="req.orderStatus != null">and order_status = #{req.orderStatus}</if>
            <if test="req.goodsType != null">and goods_type = #{req.goodsType}</if>
            and deleted = 0
        </where>
    </sql>


    <select id="countByOrderOfflineListReq" resultType="java.lang.Long">
        select count(*)
        FROM order_offline
        <include refid="OrderOfflineListReqWhere"/>
    </select>

    <select id="pageByOrderOfflineListReq" resultType="com.yuelan.hermes.quanyi.controller.response.OrderOfflineRsp">
        select
        <include refid="Base_Column_List"/>
        FROM order_offline
        <include refid="OrderOfflineListReqWhere"/>
        order by id desc
        limit #{req.offset},#{req.size}
    </select>

    <select id="findBuyerDistinct" resultType="java.lang.String">
        SELECT DISTINCT buyer
        FROM order_offline
    </select>

    <select id="findByOrderNo" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.OrderOfflineDO">
        select
        <include refid="Base_Column_List"/>
        FROM order_offline
        where order_no = #{orderNo}
        and deleted = 0
        limit 1
    </select>

    <update id="updateOrderStatus">
        update order_offline
        set order_status = #{status},
        update_time = now()
        where order_no = #{orderNo}
    </update>

</mapper>