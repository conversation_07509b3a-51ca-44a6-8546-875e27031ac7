<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccChannelDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccChannelDO">
    <!--@mbg.generated-->
    <!--@Table ecc_channel-->
    <id column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="parent_channel_id" jdbcType="BIGINT" property="parentChannelId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    channel_id, channel_name, parent_channel_id, create_time, update_time, deleted
  </sql>

  <!-- 使用递归CTE查询渠道的所有下级渠道 -->
  <select id="selectAllDescendantChannels" resultMap="BaseResultMap">
    WITH RECURSIVE descendant_tree AS (
      -- 基础查询：获取直接子渠道
      SELECT
        channel_id, channel_name, parent_channel_id, create_time, update_time, deleted,
        1 as level
      FROM ecc_channel
      WHERE parent_channel_id = #{parentChannelId} AND deleted = 0

      UNION ALL

      -- 递归查询：获取子级的子级
      SELECT
        c.channel_id, c.channel_name, c.parent_channel_id, c.create_time, c.update_time, c.deleted,
        dt.level + 1
      FROM ecc_channel c
      INNER JOIN descendant_tree dt ON dt.channel_id = c.parent_channel_id
      WHERE c.deleted = 0 AND dt.level &lt; 10  -- 防止无限递归，最多10层
    )
    SELECT
      channel_id, channel_name, parent_channel_id, create_time, update_time, deleted
    FROM descendant_tree
    ORDER BY level ASC, channel_id ASC
  </select>
</mapper>