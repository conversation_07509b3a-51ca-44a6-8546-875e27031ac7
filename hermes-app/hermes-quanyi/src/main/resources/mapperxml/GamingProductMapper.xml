<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GamingProductMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        product_id, product_code, product_name, product_img, mg_mode, sale_price, `type`, `status`,
        create_time, update_time, deleted
    </sql>
    <insert id="batchInsert" keyColumn="product_id" keyProperty="productId" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into gaming_product
        (product_code, product_name, product_img, sale_price, mg_mode, `type`, `status`, create_time,
        update_time, deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.productCode}, #{item.productName}, #{item.productImg}, #{item.salePrice}, #{item.mgMode},
            #{item.type}, #{item.status}, #{item.createTime}, #{item.updateTime}, #{item.deleted}
            )
        </foreach>
    </insert>

    <select id="search" resultType="com.yuelan.hermes.quanyi.controller.response.ProductSearchRsp">
        SELECT product_id, product_code,product_name
        FROM
        ( SELECT
        <include refid="Base_Column_List"/>
        FROM gaming_product
        <where>
            <if test="productId != null">product_id= #{productId}</if>
            <if test="productCode != null and productCode != ''">or `product_code` = #{productCode}</if>
            <if test="productName != null and productName != ''">
                or `product_name` like concat('%', #{productName}, '%')
            </if>
        </where>
        LIMIT 50
        ) t
        WHERE t.deleted = 0
    </select>

    <select id="findByProductCode" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_product
        where product_code=#{productCode}
        and deleted = 0
    </select>

    <select id="findByProductId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_product
        where product_id=#{productId}
        and deleted = 0
    </select>

    <sql id="GamingProductListReqWhere">
        <where>
            <if test="req.productId != null">and product_id = #{req.productId}</if>
            <if test="req.productCode != null  and req.productCode != ''">and product_code = #{req.productCode}</if>
            <if test="req.productName != null and req.productName != ''">
                and product_name like concat('%', #{req.productName}, '%')
            </if>
            <if test="req.status != null">and status = #{req.status}</if>
            <if test="req.type != null">and type = #{req.type}</if>
            <if test="req.mgModel != null">and mg_mode = #{req.mgModel}</if>
            <if test="req.goodsId != null">
                and product_id in ( select product_id from gaming_product_item where goods_id = #{req.goodsId})
            </if>
            and deleted = 0
        </where>
    </sql>

    <select id="pageByGamingProductListReq" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_product
        <include refid="GamingProductListReqWhere"/>
        order by product_id desc
        limit #{req.offset},#{req.size}
    </select>

    <select id="countByGamingProductListReq" resultType="java.lang.Long">
        select count(*)
        from gaming_product
        <include refid="GamingProductListReqWhere"/>
    </select>

    <update id="updateStatus">
        update gaming_product
        set `status` = #{status},
        update_time = now()
        where product_id = #{productId}
        and deleted = 0
    </update>

    <update id="removeByProductId">
        update gaming_product
        set deleted = 1,
        update_time = now()
        where product_id = #{productId}
        and deleted = 0
    </update>

    <select id="findByMgModel" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_product
        where mg_mode = #{mgMode}
        and deleted = 0
    </select>

    <select id="findByProductType" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_product
        where `type` = #{type}
        and deleted = 0
    </select>

    <select id="listAll" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GamingProductDO">
        select
        <include refid="Base_Column_List"/>
        from gaming_product
        where deleted = 0
    </select>
</mapper>