<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.GoodsVirtualMapper">

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, `name`, image, `type`, `status`, min_sale_price, supplier, third_goods_no, create_time,
        update_time, deleted
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into goods_virtual
        (`name`, image, `type`, `status`, min_sale_price, supplier, third_goods_no, create_time,
        update_time, deleted)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.name}, #{item.image}, #{item.type}, #{item.status}, #{item.minSalePrice},
            #{item.supplier}, #{item.thirdGoodsNo}, #{item.createTime}, #{item.updateTime},
            #{item.deleted})
        </foreach>
    </insert>

    <update id="deleteById">
        update goods_virtual
        set deleted = 1
        where id=#{id}
    </update>

    <sql id="GoodsVirtualListReqWhere">
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="name != null and name != ''">and `name` like concat('%', #{name} , '%')</if>
            <if test="status != null">and `status`= #{status}</if>
            <if test="type != null">and `type` = #{type}</if>
            and deleted = 0
        </where>
    </sql>

    <select id="countByGoodsVirtualListReq" resultType="java.lang.Long">
        select count(*)
        from goods_virtual
        <include refid="GoodsVirtualListReqWhere"/>
    </select>

    <select id="pageByGoodsVirtualListReq" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualDO">
        select
        <include refid="Base_Column_List"/>
        from goods_virtual
        <include refid="GoodsVirtualListReqWhere"/>
        order by id desc
        limit #{offset},#{size}
    </select>

    <update id="updateStatusById">
        update goods_virtual
        set `status` = #{status}
        where id=#{id}
        and deleted = 0
    </update>

    <select id="findByGoodsNameLike" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualDO">
        select id
        from goods_virtual
        where `name` like concat('%', #{goodsName} , '%')
        and deleted = 0
    </select>

    <select id="findOne" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.GoodsVirtualDO">
        select
        <include refid="Base_Column_List"/>
        from goods_virtual
        where id =#{id}
        and deleted = 0
    </select>

    <select id="searchGoods" resultType="com.yuelan.hermes.quanyi.controller.response.GoodsVirtualSearchRsp">
        SELECT id as goodsId, name as goodsName, type as goodsType
        FROM
        ( SELECT * FROM goods_virtual
        <where>
            <if test="goodsId != null">id = #{goodsId}</if>
            <if test="goodsName != null and goodsName != ''">
                or `name` like concat('%', #{goodsName}, '%')
            </if>
        </where>
        LIMIT 50
        ) t
        WHERE t.deleted = 0
    </select>

</mapper>