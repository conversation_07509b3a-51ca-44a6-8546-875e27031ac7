<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccRedeemCodeDOMapper">
<resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccRedeemCodeDO">
    <!--@mbg.generated-->
    <!--@Table ecc_redeem_code-->
    <id column="code_id" jdbcType="BIGINT" property="codeId" />
    <result column="goods_id" jdbcType="BIGINT" property="goodsId" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="item_no" jdbcType="VARCHAR" property="itemNo" />
    <result column="redeem_code_key" jdbcType="VARCHAR" property="redeemCodeKey" />
    <result column="redeem_code_pwd" jdbcType="VARCHAR" property="redeemCodePwd" />
    <result column="expire_time" jdbcType="DATE" property="expireTime" />
    <result column="redeem_code_status" jdbcType="INTEGER" property="redeemCodeStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
</resultMap>
<sql id="Base_Column_List">
    <!--@mbg.generated-->
    code_id, goods_id, supplier_id, item_no, redeem_code_key, redeem_code_pwd, expire_time,
    redeem_code_status, create_time, update_time, deleted
</sql>
  <update id="updateOneUsedByGoodsIdAndValidity">
      UPDATE ecc_redeem_code
      SET redeem_code_status = 1,
          item_no            = #{itemNo},
          update_time        = now()
      WHERE redeem_code_status = 0
        AND goods_id = #{goodsId}
        AND (expire_time &gt;= CURDATE() OR expire_time IS NULL)
      ORDER BY expire_time IS NULL, expire_time
      LIMIT 1
  </update>
</mapper>