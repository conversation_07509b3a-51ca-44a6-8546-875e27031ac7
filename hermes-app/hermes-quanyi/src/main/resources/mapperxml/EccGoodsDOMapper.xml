<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccGoodsDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccGoodsDO">
    <!--@mbg.generated-->
    <!--@Table ecc_goods-->
    <id column="goods_id" jdbcType="BIGINT" property="goodsId" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="redeem_type" jdbcType="TINYINT" property="redeemType" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="cost_price" jdbcType="DECIMAL" property="costPrice" />
    <result column="goods_status" jdbcType="TINYINT" property="goodsStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <!--关联统计-->
    <result column="stock" jdbcType="TINYINT" property="stock" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    goods_id, goods_name, supplier_id, supplier_name, redeem_type, price, cost_price,
    goods_status, create_time, update_time, deleted
  </sql>

  <sql id="Join_Column_List">
    <!--@mbg.generated-->
    g.goods_id,g.goods_name,g.supplier_id,g.supplier_name,g.redeem_type,g.supplier_goods_no,g.par_value,g.price,g.cost_price,
    g.goods_status,g.create_time,g.update_time,g.deleted
  </sql>

  <select id="selectPageExpend" resultMap="BaseResultMap">
    SELECT
    <include refid="Join_Column_List"/>
   ,IF(g.redeem_type = 2, rc.stock, null) as stock
    FROM ecc_goods g
    LEFT JOIN (SELECT goods_id,
                      COUNT(*) as stock
               FROM ecc_redeem_code
               WHERE expire_time > now() and redeem_code_status = 0
                 AND deleted = 0
               GROUP BY goods_id
    ) rc  ON g.goods_id = rc.goods_id
    <where>
      <if test="req.goodsId != null">
        AND g.goods_id = #{req.goodsId}
      </if>
      <if test="req.goodsName != null">
        AND g.goods_name LIKE CONCAT('%', #{req.goodsName}, '%')
      </if>
      <if test="req.supplierId != null">
        AND g.supplier_id = #{req.supplierId}
      </if>
      <if test="req.supplierName != null">
        AND g.supplier_name LIKE CONCAT('%', #{req.supplierName}, '%')
      </if>
      <if test="req.redeemType != null">
        AND g.redeem_type = #{req.redeemType}
      </if>
      <if test="req.goodsStatus != null">
        AND g.goods_status = #{req.goodsStatus}
      </if>
      AND g.deleted = 0
    </where>
    ORDER BY goods_id DESC
  </select>

<select id="selectExpendAndStock" resultMap="BaseResultMap">
    SELECT
    <include refid="Join_Column_List"/>
    ,IF(g.redeem_type = 2, rc.stock, null) as stock
    FROM ecc_goods g
    LEFT JOIN (SELECT goods_id,
    COUNT(*) as stock
    FROM ecc_redeem_code
    WHERE expire_time > now()
    AND deleted = 0
    GROUP BY goods_id
    ) rc  ON g.goods_id = rc.goods_id
        WHERE  g.goods_id IN
        <foreach collection="ids" item="goodsId" open="(" separator="," close=")">
            #{goodsId}
        </foreach>
        AND g.deleted = 0
    ORDER BY goods_id DESC
</select>

    <select id="selectDetailById" resultMap="BaseResultMap">
        SELECT
        <include refid="Join_Column_List"/>
        ,IF(g.redeem_type = 2, rc.stock, null) as stock
        FROM ecc_goods g
                 LEFT JOIN (SELECT goods_id,
                                   COUNT(*) as stock
                            FROM ecc_redeem_code
                            WHERE expire_time > now()
                              AND deleted = 0
                            GROUP BY goods_id) rc ON g.goods_id = rc.goods_id
        WHERE g.goods_id = #{goodsId}
          AND g.deleted = 0
    </select>
</mapper>