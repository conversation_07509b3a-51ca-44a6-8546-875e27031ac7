<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccZopOrderDOMapper">
    <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO">
        <!--@mbg.generated-->
        <!--@Table ecc_zop_order-->
        <id column="zop_order_id" jdbcType="BIGINT" property="zopOrderId" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="original_order_no" jdbcType="VARCHAR" property="originalOrderNo" />
        <result column="zop_order_source" jdbcType="TINYINT" property="zopOrderSource" />
        <result column="prod_id" jdbcType="BIGINT" property="prodId" />
        <result column="prod_name" jdbcType="VARCHAR" property="prodName" />
        <result column="channel_id" jdbcType="BIGINT" property="channelId" />
        <result column="channel_type" jdbcType="INTEGER" property="channelType" />
        <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
        <result column="channel_order_no" jdbcType="VARCHAR" property="channelOrderNo" />
        <result column="ad_channel_id" jdbcType="BIGINT" property="adChannelId" />
        <result column="ad_channel_name" jdbcType="VARCHAR" property="adChannelName" />
        <result column="ad_ext" jdbcType="VARCHAR" property="adExt" />
        <result column="page_url" jdbcType="VARCHAR" property="pageUrl" />
        <result column="id_card_name" jdbcType="VARCHAR" property="idCardName" />
        <result column="id_card" jdbcType="VARCHAR" property="idCard" />
        <result column="zop_goods_id" jdbcType="VARCHAR" property="zopGoodsId" />
        <result column="post_province_code" jdbcType="VARCHAR" property="postProvinceCode" />
        <result column="post_province" jdbcType="VARCHAR" property="postProvince" />
        <result column="post_city_code" jdbcType="VARCHAR" property="postCityCode" />
        <result column="post_city" jdbcType="VARCHAR" property="postCity" />
        <result column="post_district_code" jdbcType="VARCHAR" property="postDistrictCode" />
        <result column="post_district" jdbcType="VARCHAR" property="postDistrict" />
        <result column="address" jdbcType="VARCHAR" property="address" />
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
        <result column="phone" jdbcType="VARCHAR" property="phone" />
        <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
        <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
        <result column="zop_referrer_code" jdbcType="VARCHAR" property="zopReferrerCode" />
        <result column="risk_check_status" jdbcType="TINYINT" property="riskCheckStatus" />
        <result column="risk_check_resp" jdbcType="VARCHAR" property="riskCheckResp" />
        <result column="pre_order_status" jdbcType="TINYINT" property="preOrderStatus" />
        <result column="pre_order_resp" jdbcType="VARCHAR" property="preOrderResp" />
        <result column="pre_order_token" jdbcType="VARCHAR" property="preOrderToken" />
        <result column="pre_order_no" jdbcType="VARCHAR" property="preOrderNo" />
        <result column="select_type" jdbcType="TINYINT" property="selectType" />
        <result column="select_num_status" jdbcType="TINYINT" property="selectNumStatus" />
        <result column="select_num_resp" jdbcType="VARCHAR" property="selectNumResp" />
        <result column="order_sync_status" jdbcType="TINYINT" property="orderSyncStatus" />
        <result column="order_sync_resp" jdbcType="VARCHAR" property="orderSyncResp" />
        <result column="sync_order_id" jdbcType="VARCHAR" property="syncOrderId" />
        <result column="sync_order_no" jdbcType="VARCHAR" property="syncOrderNo" />
        <result column="zop_order_state" jdbcType="VARCHAR" property="zopOrderState" />
        <result column="first_recharge" jdbcType="INTEGER" property="firstRecharge" />
        <result column="callback_url" jdbcType="VARCHAR" property="callbackUrl" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="deleted" jdbcType="TINYINT" property="deleted" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        zop_order_id,order_no,original_order_no,zop_order_source,prod_id,prod_name,channel_id,channel_type,channel_name,channel_order_no,
        ad_channel_id,ad_channel_name,ad_ext,page_url,id_card_name,id_card,zop_goods_id,post_province_code,post_province,
        post_city_code,post_city,post_district_code,post_district,address,contact_phone,phone,province_code,city_code,
        zop_referrer_code,risk_check_status,risk_check_resp,pre_order_status,pre_order_resp,pre_order_token,pre_order_no,
        select_type,select_num_status,select_num_resp,order_sync_status,order_sync_resp,sync_order_id,sync_order_no,
        zop_order_state,first_recharge,callback_url,remark,create_time,update_time,deleted
    </sql>

    <select id="orderDateTotalCount" resultType="java.lang.Long">
        select count(1) from (
        SELECT
        date(create_time) as getDate
        FROM `ecc_zop_order`
        <where>
            <if test="req.prodId != null">
                and prod_id = #{req.prodId}
            </if>
            <if test="req.channelId != null">
                and channel_id = #{req.channelId}
            </if>
            <if test="req.channelType != null">
                and channel_type = #{req.channelType}
            </if>
            <if test="req.startTime != null">
                and create_time &gt;= #{req.startTime}
            </if>
            <if test="req.endTime != null">
                and create_time &lt; #{req.endTime}
            </if>
            <if test="channelLimit != null and channelLimit.size() > 0">
                and channel_id in
                <foreach collection="channelLimit" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY getDate,prod_id,channel_id
        ) tmp
    </select>

    <select id="orderDateTotal" resultType="com.yuelan.hermes.quanyi.common.pojo.bo.EccZopOrderDateTotalBO">
        select
        get_date as getDate,
        prod_id as prodId,
        prod_name as prodName,
        channel_id as channelId,
        channel_name as channelName,
        channel_type as channelType,
        order_count as orderCount,
        success_sum as successTotal,
        success_sum/order_count as successRate,
        fail_sum as failTotal,
        fail_sum/order_count as failRate,
        send_sum + activate_sum + first_sum as sendTotal,
        activate_sum + first_sum as activateTotal,
        (activate_sum + first_sum)/success_sum as activateRate,
        first_sum as firstTotal,
        first_sum/success_sum as firstRate
        from (
        SELECT
        get_date,
        prod_id,
        prod_name,
        channel_id,
        channel_name,
        channel_type,
        count(1) as order_count,
        sum(case when order_sync_status = 1 then 1 else 0 end) as success_sum,
        sum(case when order_sync_status &lt;&gt; 1 then 1 else 0 end) as fail_sum,
        sum(case when zop_order_state = 'E0' then 1 else 0 end) as send_sum,
        sum(case when zop_order_state = '1' then 1 else 0 end) as activate_sum,
        sum(case when zop_order_state = '6' then 1 else 0 end) as first_sum
        FROM (
            SELECT DISTINCT
            date(create_time) as get_date,
            prod_id,
            prod_name,
            contact_phone,
            phone,
            channel_id,
            channel_name,
            channel_type,
            order_sync_status,
            zop_order_state
            FROM `ecc_zop_order`
            <where>
                <if test="req.prodId != null">
                    and prod_id = #{req.prodId}
                </if>
                <if test="req.channelId != null">
                    and channel_id = #{req.channelId}
                </if>
                <if test="req.channelType != null">
                    and channel_type = #{req.channelType}
                </if>
                <if test="req.startTime != null">
                    and create_time &gt;= #{req.startTime}
                </if>
                <if test="req.endTime != null">
                    and create_time &lt; #{req.endTime}
                </if>
                <if test="channelLimit != null and channelLimit.size() > 0">
                    and channel_id in
                    <foreach collection="channelLimit" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
        ) tmp
        GROUP BY get_date,prod_id,channel_id
        ) tmp2
        <choose>
            <when test="req.orderBy != null">
                order by ${req.orderBy}
                <choose>
                    <when test="req.asc">
                        asc
                    </when>
                    <otherwise>
                        desc
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by getDate desc
            </otherwise>
        </choose>
        limit #{req.offset},#{req.size}
    </select>

    <select id="orderTotal" resultType="com.yuelan.hermes.quanyi.common.pojo.bo.EccZopOrderDateTotalBO">
        select
        order_count as orderCount,
        success_sum as successTotal,
        success_sum/order_count as successRate,
        fail_sum as failTotal,
        fail_sum/order_count as failRate,
        send_sum + activate_sum + first_sum as sendTotal,
        activate_sum + first_sum as activateTotal,
        (activate_sum + first_sum)/success_sum as activateRate,
        first_sum as firstTotal,
        first_sum/success_sum as firstRate
        from (
        SELECT
        count(1) as order_count,
        sum(case when order_sync_status = 1 then 1 else 0 end) as success_sum,
        sum(case when order_sync_status &lt;&gt; 1 then 1 else 0 end) as fail_sum,
        sum(case when zop_order_state = 'E0' then 1 else 0 end) as send_sum,
        sum(case when zop_order_state = '1' then 1 else 0 end) as activate_sum,
        sum(case when zop_order_state = '6' then 1 else 0 end) as first_sum
        FROM (
            SELECT DISTINCT
            date(create_time) as get_date,
            prod_id,
            prod_name,
            contact_phone,
            phone,
            channel_id,
            channel_name,
            channel_type,
            order_sync_status,
            zop_order_state
            FROM `ecc_zop_order`
            <where>
                <if test="req.prodId != null">
                    and prod_id = #{req.prodId}
                </if>
                <if test="req.channelId != null">
                    and channel_id = #{req.channelId}
                </if>
                <if test="req.channelType != null">
                    and channel_type = #{req.channelType}
                </if>
                <if test="req.startTime != null">
                    and create_time &gt;= #{req.startTime}
                </if>
                <if test="req.endTime != null">
                    and create_time &lt; #{req.endTime}
                </if>
                <if test="channelLimit != null and channelLimit.size() > 0">
                    and channel_id in
                    <foreach collection="channelLimit" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </where>
            ) tmp
        ) tmp2
    </select>



    <select id="listInnerChannelOrderByFailReason" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO">
        SELECT  <include refid="Base_Column_List"/>
        FROM ecc_zop_order
        <where>
            and channel_type = 1
            and order_sync_status != 1
            and create_time &gt;= #{startTime}
            and create_time &lt;= #{endTime}
            and CONCAT_WS(',', risk_check_resp, pre_order_resp, select_num_resp, order_sync_resp) regexp #{keywordsReg}
        </where>
    </select>

    <select id="listExistOrderByChosePhoneAndContactPhone" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO">
        SELECT <include refid="Base_Column_List"/>
        FROM ecc_zop_order
        WHERE (phone,contact_phone,zop_order_source) IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.phone}, #{item.contactPhone},#{orderSource})
        </foreach>
    </select>
</mapper>