-- 渠道分销系统改进 - 数据库迁移脚本
-- 为EccOuterChannelDO添加层级管理和扣量相关字段

-- 1. 为外部渠道表添加层级管理字段
ALTER TABLE ecc_outer_channel 
ADD COLUMN parent_channel_id BIGINT COMMENT '上级渠道ID（用于建立渠道层级关系）',
ADD COLUMN deduction_rate INT DEFAULT 0 COMMENT '扣量比例（0-95，单位：%）',
ADD COLUMN admin_id BIGINT COMMENT '关联的管理员账号ID';

-- 2. 为通用号卡订单表添加扣量渠道字段
ALTER TABLE ecc_nc_order 
ADD COLUMN deduction_channel_id BIGINT COMMENT '扣量渠道ID（根据扣量计算结果确定）';

-- 3. 添加索引以提高查询性能
-- 外部渠道表索引
CREATE INDEX idx_ecc_outer_channel_parent_id ON ecc_outer_channel(parent_channel_id);
CREATE INDEX idx_ecc_outer_channel_admin_id ON ecc_outer_channel(admin_id);

-- 订单表索引
CREATE INDEX idx_ecc_nc_order_deduction_channel_id ON ecc_nc_order(deduction_channel_id);
CREATE INDEX idx_ecc_nc_order_channel_deduction ON ecc_nc_order(channel_id, deduction_channel_id);

-- 4. 添加外键约束（可选，根据实际需要决定是否启用）
-- ALTER TABLE ecc_outer_channel 
-- ADD CONSTRAINT fk_ecc_outer_channel_parent 
-- FOREIGN KEY (parent_channel_id) REFERENCES ecc_outer_channel(outer_channel_id);

-- ALTER TABLE ecc_outer_channel 
-- ADD CONSTRAINT fk_ecc_outer_channel_admin 
-- FOREIGN KEY (admin_id) REFERENCES admin(id);

-- ALTER TABLE ecc_nc_order 
-- ADD CONSTRAINT fk_ecc_nc_order_deduction_channel 
-- FOREIGN KEY (deduction_channel_id) REFERENCES ecc_outer_channel(outer_channel_id);

-- 5. 数据初始化
-- 将所有历史订单的deduction_channel_id设为NULL（按需求要求）
UPDATE ecc_nc_order SET deduction_channel_id = NULL WHERE deduction_channel_id IS NOT NULL;

-- 6. 添加约束检查
-- 扣量比例必须在0-95之间
ALTER TABLE ecc_outer_channel 
ADD CONSTRAINT chk_deduction_rate 
CHECK (deduction_rate >= 0 AND deduction_rate <= 95);
