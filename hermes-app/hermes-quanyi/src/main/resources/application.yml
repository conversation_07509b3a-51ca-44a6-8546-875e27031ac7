server:
  port: 21000
  shutdown: graceful
spring:
  application:
    name: hermes-quanyi
  lifecycle:
    timeout-per-shutdown-phase: 60s
  jackson:
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false
  main:
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
management:
  server:
    port: 21008
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: "*"
        exclude: shutdown
  endpoint:
    health:
      show-details: ALWAYS
  trace:
    http:
      enabled: true
mybatis-plus:
  type-handlers-package: com.yuelan.hermes.quanyi.biz.handler.typeHandler
  mapper-locations: classpath:mapperxml/*.xml
  global-config:
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
knife4j:
  enable: true
  basic:
    enable: true
    # Basic认证用户名
    username: apiAdmin
    # Basic认证密码
    password: 123@456
springdoc:
  group-configs:
    - group: 'default'
      packages-to-scan: com.yuelan.hermes.quanyi.controller
    - group: '电竞卡'
      paths-to-match: '/a/gaming/**'
      packages-to-scan: com.yuelan.hermes.quanyi.controller
# logback配置
log:
  path: ${user.home}/logs/${spring.application.name}

powerjob:
  worker:
    enabled: true
    port: 21001
    app-name: ${spring.application.name}
    server-address: **********:7700
    protocol: HTTP

# lock4j 注解锁配置
lock4j:
  acquire-timeout: 3000 #默认值3s，可不设置
  expire: 30000 #默认值30s，可不设置
  primary-executor: com.baomidou.lock.executor.RedissonLockExecutor #默认redisson>redisTemplate>zookeeper，可不设置
  lock-key-prefix: lock4j #锁key前缀, 默认值lock4j，可不设置

# 高德 ip定位 不区分环境 但是要 ip 加白名单才可以调用
gaode:
  key: 4ad3464740a1658f36488aaa4dd2c54d
