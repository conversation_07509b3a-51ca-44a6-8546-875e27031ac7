package com.yuelan.hermes.commons.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.common.util.MiguFunUtils;
import com.yuelan.hermes.quanyi.controller.request.MiguFunCreateOrderReq;
import com.yuelan.hermes.quanyi.remote.NetEaseManager;
import com.yuelan.hermes.quanyi.remote.request.NetEaseGrantBenefitsReq;
import com.yuelan.hermes.quanyi.remote.response.zop.NetEaseGameRoleInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR> 2025/2/12
 * @since 2025/2/12
 *
 * 出网ip=************** 才可以请求
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class EggPartyTest {

    @Resource
    private NetEaseManager netEaseManager;

    @Test
    public void queryRoleInfo() {
        String roleId = "0472956473";
        NetEaseGameRoleInfoResp roleInfo = netEaseManager.getRoleInfo("u5", roleId);
        log.info("查询角色信息结果:{}", JSONObject.toJSONString(roleInfo));
    }
    @Test
    public void grantBenefits() {
        NetEaseGrantBenefitsReq req = new NetEaseGrantBenefitsReq();
        //0001780000
        req.setUserId("0001800000");
        req.setOrderId(UUID.randomUUID().toString());
        // req.setOrderId("595660267870047724691368");
        req.setTel(Base64.encode("13819199748"));
        //u5.yidong3.cn 7元
        // u5.yidong4.cn 16元
        // u5.yidong5.cn 32元
        // req.setProductId("u5.yidong5.cn");
        // req.setSubItem("105484");

        // req.setProductId("u5.yidong4.cn");
        // req.setSubItem("105483");

        req.setProductId("u5.yidong3.cn");
        req.setSubItem("105482");
        req.setPeriod(LocalDateTimeUtil.format(LocalDateTimeUtil.now(), DatePattern.SIMPLE_MONTH_PATTERN));
        req.setAppChannel("netease.allysdk3rd.yydc.10086");
        //LocalDateTime -> 时间秒数
        ZoneOffset zoneOffset = ZoneOffset.of("+8");
        req.setExpiredTime(LocalDateTime.now().plusDays(1).toEpochSecond(zoneOffset));
        netEaseManager.grantBenefits(req);
    }

    public static void main(String[] args) {
        MiguFunCreateOrderReq req = new MiguFunCreateOrderReq();
        req.setCreateTime(System.currentTimeMillis());
        req.setOrderId(IdUtil.fastSimpleUUID());
        req.setPhoneNum(MiguFunUtils.encryptHex("19279096090", "qimht1gmcqcmi34h"));
        req.setProductCode("gCix3Rw4");
        req.setUserId("19279096090");
        req.setExtrInfo("{\"monthlyFlag\":\"0\"}");
        System.out.println(JSONObject.toJSONString(req));

        List<Object> params = Lists.newArrayList(req.getOrderId(), req.getUserId(), req.getCreateTime());
        String sign = MiguFunUtils.sign(params, "227fd8b7b39548");
        System.out.println(sign);



    }



}
