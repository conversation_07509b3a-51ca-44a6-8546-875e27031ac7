package com.yuelan.hermes.commons.util;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.biz.manager.GdCardByXieYunManager;
import com.yuelan.hermes.quanyi.biz.service.EccAreaService;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccGdOrderDO;
import com.yuelan.hermes.quanyi.controller.request.GdPhoneKeySearchReq;
import com.yuelan.hermes.quanyi.job.ecc.XieYunGdCardJob;
import com.yuelan.hermes.quanyi.remote.XieYunManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> 2024/12/16
 * @since 2024/12/16
 */
@Slf4j
@RunWith(SpringRunner.class)
// 指定测试环境
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class XieYunTest {

    @Autowired
    XieYunGdCardJob job;
    @Autowired
    private GdCardByXieYunManager gdCardByXieYunManager;
    @Autowired
    private XieYunManager xieYunManager;
    @Autowired
    private EccAreaService eccAreaService;

    @Test
    public void phoneSearch() {
        GdPhoneKeySearchReq req = new GdPhoneKeySearchReq();
        req.setCityCode("0571");
        req.setPhoneKey(null);
        System.out.println(gdCardByXieYunManager.searchPhone(req));
        // 19265801064
    }

    @Test
    public void getCard() {
        EccGdOrderDO eccGdOrderDO = new EccGdOrderDO();
        eccGdOrderDO.setPostCityCode("0571");
        eccGdOrderDO.setCityCode("0571");
        eccGdOrderDO.setPostCity("杭州市");
        eccGdOrderDO.setCity("杭州市");


        eccGdOrderDO.setPostProvinceCode("ZJ00");
        eccGdOrderDO.setProvinceCode("ZJ00");
        eccGdOrderDO.setPostProvince("浙江省");
        eccGdOrderDO.setProvince("浙江省");

        eccGdOrderDO.setPostDistrictCode("3411");
        eccGdOrderDO.setPostDistrict("西湖区");

        eccGdOrderDO.setAddress("文三路90号东部软件园科技大厦8楼801");

        eccGdOrderDO.setOrderNo(IdUtil.fastSimpleUUID());
        eccGdOrderDO.setPhone("19275704875");
        eccGdOrderDO.setContactPhone("13989458839");

        eccGdOrderDO.setIdCard("340222199110286629");
        eccGdOrderDO.setIdCardName("王兵");
        eccGdOrderDO.setGdGoodsId("XPe2DegoBbxo1LpH");


        gdCardByXieYunManager.getGdCard(eccGdOrderDO);
    }

    @Test
    public void queryDetail() {
        System.out.println(xieYunManager.queryOrderDetail("C20241217095405357935", "19275704875", "************"));
        // 下单后 (orderId=C20241217095405357935, orderStatus=4, logisticsId=JD, logisticsName=京东物流, logisticsNum=null, orderActId=package, orderActName=待发货)

        System.out.println(xieYunManager.queryFirstRechargeStatus("19275704875"));
    }

    @Test
    public void cancel() {
        // xieYunManager.cancelOrder("C20241216191505599379", "19265801064", "测试取消","0", "************");
        // xieYunManager.cancelOrder("C20241217092901061653", "19275704875", "测试取消","0", "286619431946");
        xieYunManager.cancelOrder("C20241221182119062337", "19224579694", "测试取消", "0", "204124760999");
    }

    @Test
    public void areaTest() {
        // xieYunManager.getAreaList("ZJ00");

        EccAreaDO location = eccAreaService.getOneAreaByCode(SpEnum.CBN, "ZJ00", "0571", null);
        System.out.println(JSONObject.toJSONString(location));
    }

    @Test
    public void jobTest() {
        job.xieYunGdCardNotEndJob(null);
    }
}
