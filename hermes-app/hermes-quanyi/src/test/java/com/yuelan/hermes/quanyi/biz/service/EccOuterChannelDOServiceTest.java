package com.yuelan.hermes.quanyi.biz.service;

import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.controller.request.EccOuterChannelListReq;
import com.yuelan.hermes.quanyi.controller.response.EccOuterChannelResp;
import com.yuelan.result.entity.PageData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * 外部渠道服务测试类
 *
 * <AUTHOR> Assistant
 * @since 2025-07-25
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class EccOuterChannelDOServiceTest {

    @Autowired
    private EccOuterChannelDOService eccOuterChannelDOService;

    /**
     * 测试分页查询 - 基础功能
     */
    @Test
    public void testPageListBasic() {
        log.info("开始测试外部渠道分页查询基础功能");
        
        EccOuterChannelListReq req = new EccOuterChannelListReq();
        req.setPage(1);
        req.setSize(100);

        PageData<EccOuterChannelResp> result = eccOuterChannelDOService.pageList(req);
        log.info(JSONObject.toJSONString(result));

    }

    /**
     * 测试分页查询 - 按渠道ID查询
     */
    @Test
    public void testPageListByChannelId() {
        log.info("开始测试按渠道ID查询");
        
        // 先获取一条数据作为测试数据
        EccOuterChannelListReq basicReq = new EccOuterChannelListReq();
        basicReq.setPage(1);
        basicReq.setSize(1);
        PageData<EccOuterChannelResp> basicResult = eccOuterChannelDOService.pageList(basicReq);
        
        if (!basicResult.getList().isEmpty()) {
            Long testChannelId = basicResult.getList().get(0).getOuterChannelId();
            
            EccOuterChannelListReq req = new EccOuterChannelListReq();
            req.setPage(1);
            req.setSize(10);
            req.setOuterChannelId(testChannelId);

            PageData<EccOuterChannelResp> result = eccOuterChannelDOService.pageList(req);
            
            assertNotNull("查询结果不应该为空", result);
            assertTrue("应该找到对应的渠道", result.getTotal() > 0);
            
            if (!result.getList().isEmpty()) {
                assertEquals("查询结果的渠道ID应该匹配", testChannelId, result.getList().get(0).getOuterChannelId());
                log.info("按渠道ID查询成功：ID={}, 名称={}", 
                        result.getList().get(0).getOuterChannelId(), result.getList().get(0).getChannelName());
            }
        } else {
            log.info("数据库中没有外部渠道数据，跳过按ID查询测试");
        }
    }

    /**
     * 测试分页查询 - 按渠道名称模糊查询
     */
    @Test
    public void testPageListByChannelName() {
        log.info("开始测试按渠道名称模糊查询");
        
        // 先获取一条数据作为测试数据
        EccOuterChannelListReq basicReq = new EccOuterChannelListReq();
        basicReq.setPage(1);
        basicReq.setSize(1);
        PageData<EccOuterChannelResp> basicResult = eccOuterChannelDOService.pageList(basicReq);
        
        if (!basicResult.getList().isEmpty()) {
            String testChannelName = basicResult.getList().get(0).getChannelName();
            
            // 使用渠道名称的一部分进行模糊查询
            String searchKeyword = testChannelName.length() > 2 ? testChannelName.substring(0, 2) : testChannelName;
            
            EccOuterChannelListReq req = new EccOuterChannelListReq();
            req.setPage(1);
            req.setSize(10);
            req.setChannelName(searchKeyword);

            PageData<EccOuterChannelResp> result = eccOuterChannelDOService.pageList(req);
            
            assertNotNull("查询结果不应该为空", result);
            log.info("按渠道名称模糊查询结果：关键词={}, 找到{}条记录", searchKeyword, result.getTotal());
            
            // 验证查询结果中包含关键词
            if (!result.getList().isEmpty()) {
                boolean foundMatch = result.getList().stream()
                        .anyMatch(channel -> channel.getChannelName().contains(searchKeyword));
                assertTrue("查询结果应该包含关键词", foundMatch);
            }
        } else {
            log.info("数据库中没有外部渠道数据，跳过按名称查询测试");
        }
    }

    /**
     * 测试分页查询 - 按API Key查询
     */
    @Test
    public void testPageListByApiKey() {
        log.info("开始测试按API Key查询");
        
        // 先获取一条数据作为测试数据
        EccOuterChannelListReq basicReq = new EccOuterChannelListReq();
        basicReq.setPage(1);
        basicReq.setSize(1);
        PageData<EccOuterChannelResp> basicResult = eccOuterChannelDOService.pageList(basicReq);
        
        if (!basicResult.getList().isEmpty()) {
            String testApiKey = basicResult.getList().get(0).getApiKey();
            
            EccOuterChannelListReq req = new EccOuterChannelListReq();
            req.setPage(1);
            req.setSize(10);
            req.setApiKey(testApiKey);

            PageData<EccOuterChannelResp> result = eccOuterChannelDOService.pageList(req);
            
            assertNotNull("查询结果不应该为空", result);
            assertTrue("应该找到对应的渠道", result.getTotal() > 0);
            
            if (!result.getList().isEmpty()) {
                assertEquals("查询结果的API Key应该匹配", testApiKey, result.getList().get(0).getApiKey());
                log.info("按API Key查询成功：API Key={}, 渠道名称={}", 
                        result.getList().get(0).getApiKey(), result.getList().get(0).getChannelName());
            }
        } else {
            log.info("数据库中没有外部渠道数据，跳过按API Key查询测试");
        }
    }

    /**
     * 测试分页查询 - 按上级渠道ID查询
     */
    @Test
    public void testPageListByParentChannelId() {
        log.info("开始测试按上级渠道ID查询");
        
        EccOuterChannelListReq req = new EccOuterChannelListReq();
        req.setPage(1);
        req.setSize(10);
        req.setParentChannelId(0L); // 查询一级渠道（上级渠道ID为0）

        PageData<EccOuterChannelResp> result = eccOuterChannelDOService.pageList(req);
        
        assertNotNull("查询结果不应该为空", result);
        log.info("查询一级渠道结果：找到{}条记录", result.getTotal());
        
        // 验证查询结果都是一级渠道
        if (!result.getList().isEmpty()) {
            boolean allFirstLevel = result.getList().stream()
                    .allMatch(channel -> Long.valueOf(0L).equals(channel.getParentChannelId()));
            assertTrue("查询结果应该都是一级渠道", allFirstLevel);
        }
    }

    /**
     * 测试分页查询 - 按渠道层级查询
     */
    @Test
    public void testPageListByChannelLevel() {
        log.info("开始测试按渠道层级查询");
        
        // 测试查询一级渠道
        EccOuterChannelListReq req1 = new EccOuterChannelListReq();
        req1.setPage(1);
        req1.setSize(10);
        req1.setChannelLevel(1);

        PageData<EccOuterChannelResp> result1 = eccOuterChannelDOService.pageList(req1);
        
        assertNotNull("一级渠道查询结果不应该为空", result1);
        log.info("一级渠道查询结果：找到{}条记录", result1.getTotal());
        
        // 测试查询二级渠道
        EccOuterChannelListReq req2 = new EccOuterChannelListReq();
        req2.setPage(1);
        req2.setSize(10);
        req2.setChannelLevel(2);

        PageData<EccOuterChannelResp> result2 = eccOuterChannelDOService.pageList(req2);
        
        assertNotNull("二级渠道查询结果不应该为空", result2);
        log.info("二级渠道查询结果：找到{}条记录", result2.getTotal());
    }

    /**
     * 测试分页查询 - 组合条件查询
     */
    @Test
    public void testPageListWithMultipleConditions() {
        log.info("开始测试组合条件查询");
        
        EccOuterChannelListReq req = new EccOuterChannelListReq();
        req.setPage(1);
        req.setSize(5);
        req.setChannelLevel(1); // 只查询一级渠道
        req.setParentChannelId(0L); // 上级渠道ID为0

        PageData<EccOuterChannelResp> result = eccOuterChannelDOService.pageList(req);
        
        assertNotNull("组合查询结果不应该为空", result);
        log.info("组合条件查询结果：找到{}条记录", result.getTotal());
        
        // 验证查询结果符合条件
        if (!result.getList().isEmpty()) {
            boolean allMatch = result.getList().stream()
                    .allMatch(channel -> 
                            Integer.valueOf(1).equals(channel.getChannelLevel()) && 
                            Long.valueOf(0L).equals(channel.getParentChannelId()));
            assertTrue("查询结果应该都符合组合条件", allMatch);
        }
    }

    /**
     * 测试分页查询 - 边界条件测试
     */
    @Test
    public void testPageListBoundaryConditions() {
        log.info("开始测试边界条件");
        
        // 测试第0页（应该被处理为第1页）
        EccOuterChannelListReq req1 = new EccOuterChannelListReq();
        req1.setPage(0);
        req1.setSize(10);

        PageData<EccOuterChannelResp> result1 = eccOuterChannelDOService.pageList(req1);
        assertNotNull("第0页查询结果不应该为空", result1);
        
        // 测试页大小为0（应该有默认处理）
        EccOuterChannelListReq req2 = new EccOuterChannelListReq();
        req2.setPage(1);
        req2.setSize(0);

        PageData<EccOuterChannelResp> result2 = eccOuterChannelDOService.pageList(req2);
        assertNotNull("页大小为0的查询结果不应该为空", result2);
        
        // 测试查询不存在的渠道ID
        EccOuterChannelListReq req3 = new EccOuterChannelListReq();
        req3.setPage(1);
        req3.setSize(10);
        req3.setOuterChannelId(-1L); // 不存在的ID

        PageData<EccOuterChannelResp> result3 = eccOuterChannelDOService.pageList(req3);
        assertNotNull("查询不存在ID的结果不应该为空", result3);
        assertEquals("查询不存在ID应该返回0条记录", Long.valueOf(0), result3.getTotal());
        
        log.info("边界条件测试完成");
    }
}
