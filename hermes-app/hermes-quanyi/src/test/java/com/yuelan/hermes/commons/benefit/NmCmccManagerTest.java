package com.yuelan.hermes.commons.benefit;

import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.common.pojo.properties.NmCmccProperties;
import com.yuelan.hermes.quanyi.remote.benefit.NmCmccManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * ClassName: NmCmccManagerTest
 * Package: com.yuelan.hermes.commons.benefit
 *
 * @Autor: whc
 * @Create: 2025/4/18 - 10:16
 * @Version: v1.0
 */

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class NmCmccManagerTest {

    @Resource
    private NmCmccManager nmCmccManager;

    @Test
    public void testGetRandomCode() throws Exception {

        NmCmccProperties properties = new NmCmccProperties();
        /*properties.setTokenIp("**************:18511");
        properties.setOppfIp("**************:11001");
        properties.setAppId("501146");
        properties.setAppKey("6b968fcf48a19c399090e99a52747c61");
        properties.setPublicKey("MGcwDQYJKoZIhvcNAQEBBQADVgAwUwJMAJC18y9ld9Ai4JB2o6PzSHJm1B\n" +
                "ZPrdTPIhsWoA1u9CLTY2ffMXQfJgMs+bnRsM+i/w1ErmBmXsUE19nTkFteaorrl77QQbUEqTTPgQIDAQAB");
        properties.setOpCode("SYS71110784");
        properties.setOpOrgId("71110145");
        properties.setChannelId("2001");*/

//        nmCmccManager = new NmCmccManager(properties, null);

        //nmCmccManager.getRandomCode("13947100001","300000363332","1");
        //nmCmccManager.checkRandomCode("13947100001","624768");
//        nmCmccManager.orderInfoBack("13947100001","250411328145275");
//        nmCmccManager.orderInfoQuery("13947100001", "2", LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN), LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.PURE_DATETIME_PATTERN));
//        nmCmccManager.goodsAccept("13947100001", "300000363332", "1", "0", "1");

    }
}
