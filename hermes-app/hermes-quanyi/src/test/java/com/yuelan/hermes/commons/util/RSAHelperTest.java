package com.yuelan.hermes.commons.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.common.util.RSAHelper;

import java.util.Objects;

public class RSAHelperTest {

    String privateKeyBase64 = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAM+Ml8KTR88pq5Ps\n" +
            "rbYEjIQuJe24O4xIDzXbve17NcB5JOgvu6O+WLqKl7d0nu9Z5TEwUPSu5SB11o2Q\n" +
            "KTtwYZqWx+C4PzCb6ECG5Gx9+r0srEDzzsQZQvkLv2zCktrpZT1KrLTE0WYnjpLJ\n" +
            "18HhEPQBMnkA3KwguC4HlKnXucnfAgMBAAECgYBlIO8tWtApi/dTW4aE4xCIs2e9\n" +
            "vgAd6Vqn9otDtL5LK3wlOmTmczAOrMsiE8wBej7nydAYeUOhnmlNjwnYMXkMlXSZ\n" +
            "igilUNGG02+ocxPbBQPFEtMY0acfXKIRIDW7y2axnfXq7fHJwTKPe6FgnPz+AK+A\n" +
            "E5w6saqvz/FnDhxt8QJBAP2lNOItb2y6FVX1dyxmAAHHj+x4zgWHMdakKk2amCp4\n" +
            "avbl5VVkEgx6p464w5Dush3vRTqaHMxocwcD61QRYkkCQQDRedZ479dAM4TNleaW\n" +
            "AHe8NxyDUGJo77X1EKGbHWDfLkMwuRIGFrnCO7d9NF08odQbxM+/hgCt87vpPB54\n" +
            "mUrnAkAn6fx1byLuoCXFXhg/01lC7m12j9gTxJ+SIElL5vz1CfgDHFBHbLTuY8rE\n" +
            "Scz0nKtIzKM/Qosvm2wmeXZpx0aJAkEApMJ68bZbjJC5BObBbfxCMRT75UDllnns\n" +
            "mSuSoKmT1xbRHFKEnatkQiSsJtt39kemyIydhpvEjw2lIEb8BIk1wwJBANinxzzc\n" +
            "NuGkSGOUh9xp6AiJxfZWke52zsmXX5FomULixlYdepYsBpJ0tVWpthy2Te6DmILX\n" +
            "WbZ9+SKqqH8rst4=";
    String thirdPublicKeyBase64 = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHPTxKy8y9MOGI2h52DuVvk0Rl\n" +
            "Ov2NjBTY88frHLk3qRYU24ymdGV4H++ta8DZiMJNa1OI1oAyYMVlC8OZaENWTnUP\n" +
            "J2Et76jnQ1zdtnm1fl75l863nYyJx+qJwohBTqIHGtQpLp6pZHLWLYg6B7UYkpna\n" +
            "7TOTjEpKmVop6qO2cQIDAQAB";

    //    @Test
    public void decryptAndVerify() {

        String params = "{\"rechargeTag\":\"JC220413882207\",\"accountType\":\"1\",\"accountNo\":\"***********\",\"num\":\"1\"," +
                "\"sipOrderNo\":\"202212021701U0061167\"} ";
        String data = "gkZK3iYaVxu7hb2oorsca68jIg83vV0KEZK6BQTWqYATsKUc1TK+Fvw1nujawTwsJl97GGvwdwJ0c5tgMNas4H2ieywR8JTYX9BJGw4lZAyY0eU6rhUt2UD8" +
                "/mvSm4pQpNvI8XStUW9ihrhs006HotkMUxBOsYqUAQRv2KPI+cwJP9O4TBhVMpDDOLgoXhXPcyMlSn5sU802XlsL6yD+W38Gzc2Bc10txWJ48z4trBpJLLew/HWFt2wOEZJNz5jbfQ" +
                "/1wn+FTY8aVGhKSbI9im7LiGdF88gH6IVss4JvWW24H6TCCSVvzn4jPznLd6Ykdi8rFP4yHg1zlu4gE5jp1A==";
        String sign = "Irf6A7vUwmxyoIeO/dC+WZyD2h78b1BSY++kU5OzaC32Jta3ONeR+c789bYF6bJh5+92dsmrWPssCO9mqHQIHTzOyHqInztCppyKoJ1HEzEtMre0hL29eXZCc0BEgZQPLw9MD8" +
                "/SKMdg4D8QvD+OtBQCnRJ/Xmxm+uBGAiyKLdM=";

        String decryptData = RSAHelper.decryptAndVerify(privateKeyBase64, thirdPublicKeyBase64, data, sign);

        JSONObject json1 = JSON.parseObject(params);
        JSONObject json2 = JSON.parseObject(decryptData);

        System.out.println(json1.toString());
        System.out.println(json2.toString());
        System.out.println(Objects.equals(json1, json2));
    }


}