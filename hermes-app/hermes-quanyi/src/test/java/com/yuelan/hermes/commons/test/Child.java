package com.yuelan.hermes.commons.test;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> 2024/7/8 下午6:44
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ColumnWidth(15)
public class Child {
    @ColumnWidth(100)
    @ExcelProperty("子对象名称")
    private String childName;
    @ColumnWidth(100)
    @ExcelProperty("子对象值")
    private String childValue;
}