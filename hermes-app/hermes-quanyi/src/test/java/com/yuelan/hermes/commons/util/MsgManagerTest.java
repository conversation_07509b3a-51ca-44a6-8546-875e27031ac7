package com.yuelan.hermes.commons.util;

import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.biz.manager.MsgManager;
import com.yuelan.hermes.quanyi.biz.service.EccZopOrderDOService;
import com.yuelan.hermes.quanyi.biz.service.EccZopOrderMsgDOService;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopOrderMsgDO;
import com.yuelan.result.entity.PageRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/7/23
 */
@SpringBootTest(classes = {QuanYiApplication.class})
@ActiveProfiles("local")
@RunWith(SpringRunner.class)
public class MsgManagerTest {

    @Autowired
    private EccZopOrderMsgDOService eccZopOrderMsgDOService;
    @Autowired
    private EccZopOrderDOService eccZopOrderDOService;
    @Autowired
    private MsgManager msgManager;

    @Test
    public void sendFirstCallbackMsg() {
        EccZopOrderMsgDO msgDO = new EccZopOrderMsgDO();
        msgDO.setZopMsgType(2);
        msgDO.setZopMsgId("111111");
        msgDO.setOrderNo("ZO24071815234901766148096");
        msgDO.setPhone("153012341234");
        msgDO.setMsgTime(LocalDateTime.now());
        msgDO.setOrderState("1");
        eccZopOrderMsgDOService.save(msgDO);

        EccZopOrderDO orderDO = eccZopOrderDOService.getById(97L);
        orderDO.setCallbackUrl("http://127.0.0.1:8080/");
        msgManager.sendFirstCallbackMsg(orderDO, msgDO);
    }

}
