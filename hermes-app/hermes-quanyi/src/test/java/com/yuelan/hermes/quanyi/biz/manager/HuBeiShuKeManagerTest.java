package com.yuelan.hermes.quanyi.biz.manager;

import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.remote.benefit.HuBeiShuKeManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> 2025/7/17
 * @since 2025/7/17
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class HuBeiShuKeManagerTest {

    @Autowired
    HuBeiShuKeManager huBeiShuKeManager;

    @Test
    public void getUrlTest(){

    }

}
