// package com.yuelan.hermes.quanyi.controller;
//
// import com.alibaba.excel.EasyExcel;
// import com.yuelan.hermes.quanyi.QuanYiApplication;
// import com.yuelan.hermes.quanyi.common.pojo.excel.EccIccIdImportExcel;
// import com.yuelan.hermes.quanyi.common.pojo.excel.EccIccidImportExcel;
// import lombok.extern.slf4j.Slf4j;
// import org.junit.Test;
// import org.junit.runner.RunWith;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.http.MediaType;
// import org.springframework.mock.web.MockMultipartFile;
// import org.springframework.test.context.junit4.SpringRunner;
// import org.springframework.test.web.servlet.MockMvc;
// import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
// import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
//
// import java.io.ByteArrayOutputStream;
// import java.io.IOException;
// import java.util.ArrayList;
// import java.util.List;
//
// /**
//  * ICCID控制器集成测试
//  *
//  * <AUTHOR> Assistant
//  * @since 2025-07-18
//  */
// @Slf4j
// @RunWith(SpringRunner.class)
// @SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
// @AutoConfigureWebMvc
// public class EccIccidControllerTest {
//
//     @Autowired
//     private MockMvc mockMvc;
//
//     /**
//      * 测试下载模板接口
//      */
//     @Test
//     public void testDownloadTemplate() throws Exception {
//         mockMvc.perform(MockMvcRequestBuilders.get("/a/ecc/iccid/download/template"))
//                 .andExpect(MockMvcResultMatchers.status().isOk())
//                 .andExpect(MockMvcResultMatchers.header().exists("Content-Disposition"));
//
//         log.info("模板下载测试通过");
//     }
//
//     /**
//      * 测试ICCID格式验证接口
//      */
//     @Test
//     public void testValidateIccid() throws Exception {
//         // 测试有效ICCID
//         mockMvc.perform(MockMvcRequestBuilders.get("/a/ecc/iccid/validate/8986000000000000001"))
//                 .andExpect(MockMvcResultMatchers.status().isOk())
//                 .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
//                 .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(true));
//
//         // 测试无效ICCID
//         mockMvc.perform(MockMvcRequestBuilders.get("/a/ecc/iccid/validate/invalid"))
//                 .andExpect(MockMvcResultMatchers.status().isOk())
//                 .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
//                 .andExpect(MockMvcResultMatchers.jsonPath("$.data").value(false));
//
//         log.info("ICCID验证测试通过");
//     }
//
//     /**
//      * 测试分页查询接口
//      */
//     @Test
//     public void testPageQuery() throws Exception {
//         String requestBody = " {\n" +
//                 "                    \"page\": 1,\n" +
//                 "                    \"size\": 10\n" +
//                 "                }";
//
//         mockMvc.perform(MockMvcRequestBuilders.post("/a/ecc/iccid/page")
//                         .contentType(MediaType.APPLICATION_JSON)
//                         .content(requestBody))
//                 .andExpect(MockMvcResultMatchers.status().isOk())
//                 .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200))
//                 .andExpect(MockMvcResultMatchers.jsonPath("$.data.page").value(1))
//                 .andExpect(MockMvcResultMatchers.jsonPath("$.data.size").value(10));
//
//         log.info("分页查询测试通过");
//     }
//
//     /**
//      * 测试导入功能（使用模拟Excel文件）
//      */
//     @Test
//     public void testImportIccid() throws Exception {
//         // 创建测试数据
//         List<EccIccIdImportExcel> testData = createTestData();
//
//         // 生成Excel文件字节数组
//         byte[] excelBytes = generateExcelBytes(testData);
//
//         // 创建MockMultipartFile
//         MockMultipartFile file = new MockMultipartFile(
//                 "file",
//                 "test_iccid_import.xlsx",
//                 "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
//                 excelBytes
//         );
//
//         // 执行导入请求
//         mockMvc.perform(MockMvcRequestBuilders.multipart("/a/ecc/iccid/import")
//                         .file(file))
//                 .andExpect(MockMvcResultMatchers.status().isOk())
//                 .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(200));
//
//         log.info("ICCID导入测试通过");
//     }
//
//     /**
//      * 测试导入无效数据（应该返回错误）
//      */
//     @Test
//     public void testImportInvalidIccid() throws Exception {
//         // 创建包含无效ICCID的测试数据
//         List<EccIccidImportExcel> testData = createInvalidTestData();
//
//         // 生成Excel文件字节数组
//         byte[] excelBytes = generateExcelBytes(testData);
//
//         // 创建MockMultipartFile
//         MockMultipartFile file = new MockMultipartFile(
//                 "file",
//                 "test_invalid_iccid_import.xlsx",
//                 "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
//                 excelBytes
//         );
//
//         // 执行导入请求，期望返回错误
//         mockMvc.perform(MockMvcRequestBuilders.multipart("/a/ecc/iccid/import")
//                         .file(file))
//                 .andExpect(MockMvcResultMatchers.status().isOk())
//                 .andExpect(MockMvcResultMatchers.jsonPath("$.code").value(400));
//
//         log.info("无效ICCID导入测试通过");
//     }
//
//     /**
//      * 创建测试数据
//      */
//     private List<EccIccIdImportExcel> createTestData() {
//         List<EccIccIdImportExcel> testData = new ArrayList<>();
//
//         // 创建有效的测试ICCID数据
//         for (int i = 1; i <= 3; i++) {
//             EccIccIdImportExcel excel = new EccIccIdImportExcel();
//             excel.setIccid(String.format("898600000000000000%02d", i));
//             excel.setProductCode("PROD001");
//             excel.setChannelCode("CH001");
//             excel.setBatchNumber("BATCH_TEST_" + System.currentTimeMillis());
//             excel.setValidityPeriod("2025-12-31");
//             testData.add(excel);
//         }
//
//         return testData;
//     }
//
//     /**
//      * 创建包含无效数据的测试数据
//      */
//     private List<EccIccIdImportExcel> createInvalidTestData() {
//         List<EccIccIdImportExcel> testData = new ArrayList<>();
//
//         // 创建无效的ICCID数据（长度不正确）
//         EccIccIdImportExcel excel = new EccIccIdImportExcel();
//         excel.setIccid("invalid_iccid");
//         excel.setProductCode("PROD001");
//         excel.setChannelCode("CH001");
//         excel.setBatchNumber("BATCH_INVALID");
//         excel.setValidityPeriod("2025-12-31");
//         testData.add(excel);
//
//         return testData;
//     }
//
//     /**
//      * 生成Excel文件字节数组
//      */
//     private byte[] generateExcelBytes(List<EccIccidImportExcel> data) throws IOException {
//         ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//
//         EasyExcel.write(outputStream, EccIccidImportExcel.class)
//                 .sheet("ICCID导入")
//                 .doWrite(data);
//
//         return outputStream.toByteArray();
//     }
// }
