package com.yuelan.hermes.commons.util;

import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.job.ecc.ZopNetworkErrorSimCardReissueJob;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR> 2024/11/4
 * @since 2024/11/4
 */
@Slf4j

@RunWith(SpringRunner.class)
@ActiveProfiles("local")
@SpringBootTest(classes = {QuanYiApplication.class})
public class ZopJobTest {

    @Resource
    private ZopNetworkErrorSimCardReissueJob zopNetworkErrorSimCardReissueJob;

    @Test
    public void test() {
        zopNetworkErrorSimCardReissueJob.orderReissueJob(null);
    }
}
