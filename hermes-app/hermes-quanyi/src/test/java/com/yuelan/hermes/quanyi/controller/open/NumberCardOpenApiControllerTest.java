package com.yuelan.hermes.quanyi.controller.open;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Test;

import java.util.Objects;


/**
 * <AUTHOR> 2025/3/17
 * @since 2025/3/17
 */
public class NumberCardOpenApiControllerTest {

    @Test
    public void getNumberCard() {

            // reqBody.put("callbackUrl","https://quanyi-api-ntest.meta-xuantan.com/test/testJson");



    }



    public static void main(String[] args) {
        String apiKey = "HpDyS4jtDTMN";
        String apiSecret = "KpZ8qqmrAX7CBaBfBALM4LKnuFThGLJe";

        String localUrl = "https://qyapi.hzyuelan.com/ecc/reqFreeCard";

        // String prod = "ukmodiRg";//测试环境我爱号码网络联通卡
        // String prod = "3L6c3KAh"; //测试环境-江苏联通
        // String prod = "aSaRj2GY";//测试环境  携云广电卡
        // String prod = "gjYTte5S";//线上环境我爱号码联通卡

        // String prod = "eAzc0Qes";//线上江苏210g
        // String prod = "gjYTte5S";//线上我爱号码安徽
        String prod = "miFptVrE";//线上我爱号码广电卡

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("channelOrderNo", IdUtil.simpleUUID());
        jsonObject.put("idCardName", "王强");
        jsonObject.put("idCardNo", "340222199110286619");
        jsonObject.put("contactPhone", "13989458839");

        jsonObject.put("postProvinceCode", "320000");
        jsonObject.put("postCityCode", "321300");
        jsonObject.put("postDistrictCode", "321302");

        // jsonObject.put("postProvince", "江苏省");
        // jsonObject.put("postCity", "南京市");
        // jsonObject.put("postDistrict", "六合区");
        jsonObject.put("address", "镇江路与宜兴路交叉口宿迁市第一高级中学");

        jsonObject.put("eccProdCode", prod);
        jsonObject.put("orderType", 1);
        jsonObject.put("pageUrl", "http://www.baidu.com");
        jsonObject.put("callbackUrl", "https://quanyi-api-ntest.meta-xuantan.com/test/testJson");
        System.out.println(jsonObject);



        String sign = getSign(apiSecret, jsonObject);

        //--header 'apiKey: HpDyS4jtDTMN' \
        // --header 'sign: 7ebf0b68346e700f52d4d92bd08cb5ff' \
        HttpRequest request = HttpRequest.post(localUrl)
                .header("apiKey", apiKey)
                .header("sign", sign)
                .body(jsonObject.toJSONString());
        System.out.println(request);
        String response = request.execute().body();
        System.out.println("response = " + response);




    }

    public static <T> String getSign(String secret, T data) {
        // 验证签名
        JSONObject dataJson = JSONObject.parseObject(JSONObject.toJSONString(data));
        StringBuilder dataStr = new StringBuilder();
        // 自然排序拼接
        dataJson.keySet().stream().sorted().forEach(key -> {
            Object value = dataJson.get(key);
            if (Objects.nonNull(value)) {
                dataStr.append(key).append("=").append(value).append("&");
            }
        });
        dataStr.append("secret=").append(secret);
        // System.out.println("dataStr=" + dataStr.toString());
        return DigestUtils.md5Hex(dataStr.toString());
    }

}