package com.yuelan.hermes.commons.util.xieyun;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/16 9:53
 * @remark
 */
public class demo {
    public final static String channelCode = "DLI_9";
    public final static String channelKey = "Nb17k4wG0WDLEs4aZPBNxdzC48FZhIc7";
    public final static String OSP_TOKEN_URL = "https://xieyuntech.cn:5443/common/getToken";// 网关平台token生成地址
    public final static String PUB_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDB+Xc9mHdznijuDZF64rhnXNl0S3Q6Paogo2e7o2AOrgeNd54Cc3tBghknvwafF2SH2ZnMluA57c+PygaMVxCjKVFtsKmZRYGbuUNWiO8hJf/5Q9k8ss7D124AmhOQhR2LkBXVq9OWSI8SY8tfK5z3vILPLMVJ3vBK++N/+51yAwIDAQAB";
    public final static String BIZ_URL = "https://xieyuntech.cn:5443";

    public static void main(String[] args) {
        JSONObject resultJson = new JSONObject();
        resultJson.put("areaCode", "");
        Object decoder = decoder(BIZ_URL + "/common/getAreaList", resultJson.toString());
        System.out.println(decoder.toString());
    }

    public static Object decoder(String url, String reqJson) {
        // 获取token
        String token = getToken();
        if (StringUtils.isNotBlank(token)) {
            // 请求数据加密
            String s = AESUtil.enAES(reqJson, channelKey);
            try {
                // 请求头
                HttpHeaders headers = new HttpHeaders();
//                headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
                initSecrityHead(headers, token);

                RestTemplate restTemplate = new RestTemplate();
                MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
                map.add("requestParams", s);
                HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(map, headers);
                ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
                String result = responseEntity.getBody();


                return result;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public static String getToken() {
        try {
            RestTemplate restTemplate = new RestTemplate();

            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            map.add("channelCode", channelCode);
            map.add("channelKey", channelKey);
            String postForEntity = restTemplate.postForObject(OSP_TOKEN_URL, map, String.class);
            JSONObject resultJson = JSONObject.parseObject(postForEntity);
            System.out.println("======成功获取到token: " + postForEntity);
//            Map body = postForEntity.getBody();
            if ("000000" .equals(resultJson.getString("msgCode"))) {
                String token = resultJson.getJSONObject("result").getString("token");
                System.out.println("======成功获取到token: " + token);
                return token;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void initSecrityHead(HttpHeaders headers, String token) {
        try {
            String appCode = RsaSecretUtils.encrypt(channelKey, PUB_KEY);
            headers.set("X-Access-Token", token);
            headers.set("pubkey", PUB_KEY);
            headers.set("appCode", appCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
