package com.yuelan.hermes.quanyi.controller.open;

import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.biz.handler.impl.CbnServerByXieYunImpl;
import com.yuelan.hermes.quanyi.controller.request.NcPhoneKeySearchReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;

/**
 * <AUTHOR> 2025/6/24
 * @since 2025/6/24
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class CbnServerByXieYunImplTest {
    @Autowired
    CbnServerByXieYunImpl cbnServerByXieYunImpl;


    @Test
    public void test() {
        NcPhoneKeySearchReq req = new NcPhoneKeySearchReq();
        //{"eccProdCode":"cDzmy5pa","page":1,"size":10,"provinceCode":"JL00","cityCode":"0436"}
        req.setEccProdCode("cDzmy5pa");
        req.setProvinceCode("ZJ00");
        req.setCityCode("0571");

        System.out.println(cbnServerByXieYunImpl.searchPhone(req, new ArrayList<>()));
    }
}
