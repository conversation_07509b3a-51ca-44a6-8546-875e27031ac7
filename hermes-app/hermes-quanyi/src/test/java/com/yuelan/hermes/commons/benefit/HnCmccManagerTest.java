package com.yuelan.hermes.commons.benefit;

import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.remote.HnCmccManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR> 2025/4/2
 * @since 2025/4/2
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class HnCmccManagerTest {

    @Resource
    private HnCmccManager hnCmccManager;

    @Test
    public void sendSmsCode() throws InterruptedException {
        String mobile = "18873461360";
        UserBenefitOrderReq req = new UserBenefitOrderReq();
        req.setMobile(mobile);
        BenefitProductDO productDO = new BenefitProductDO();
        productDO.setPayChannelPkgId(1);

        System.out.println(hnCmccManager.orderSendSmsCode(req, productDO));

    }

}
