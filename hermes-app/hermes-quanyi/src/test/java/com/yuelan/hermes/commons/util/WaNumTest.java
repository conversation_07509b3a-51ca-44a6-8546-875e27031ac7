package com.yuelan.hermes.commons.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO;
import com.yuelan.hermes.quanyi.mapper.EccAreaDOMapper;
import com.yuelan.hermes.quanyi.remote.WoAiNumManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2024/11/22
 * @since 2024/11/22
 */
@Slf4j
@RunWith(SpringRunner.class)
// 指定测试环境
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class WaNumTest {
    @Autowired
    WoAiNumManager woAiNumManager;
    @Autowired
    EccAreaDOMapper areaMapper;

    @Test
    public void queryNumTest() {
        System.out.println(woAiNumManager.getBroadnetNumByCity("0571", "88"));
    }

    @Test
    public void importGdArea() {

        ExcelReader reader = ExcelUtil.getReader(FileUtil.file("/Users/<USER>/Downloads/广电地址库.xlsx"));
        List<Map<String, Object>> maps = reader.readAll();
        System.out.println(maps);
        //{province_name=广东省, province_code=GD00, city_name=惠州市, city_code=0752, district_name=龙门县, district_code=1647}
        String provinceNameKey = "province_name";
        String provinceCodeKey = "province_code";
        String cityNameKey = "city_name";
        String cityCodeKey = "city_code";
        String districtNameKey = "district_name";
        String districtCodeKey = "district_code";

        List<ExcelArea> excelAreas = new ArrayList<>();
        for (Map<String, Object> map : maps) {
            ExcelArea excelArea = new ExcelArea();
            excelArea.provinceName = (String) map.get(provinceNameKey);
            excelArea.provinceCode = (String) map.get(provinceCodeKey);
            excelArea.cityName = (String) map.get(cityNameKey);
            excelArea.cityCode = (String) map.get(cityCodeKey);
            excelArea.districtName = (String) map.get(districtNameKey);
            excelArea.districtCode = (String) map.get(districtCodeKey);
            excelAreas.add(excelArea);
        }
        final SpEnum spEnum = SpEnum.CBN;
        HashMap<String, Integer> code2IdProvince = new HashMap<>();
        HashMap<String, Integer> code2IdCity = new HashMap<>();
        HashMap<String, Integer> code2IdDistrict = new HashMap<>();
        for (ExcelArea excelArea : excelAreas) {
            String provinceKey = excelArea.provinceCode;
            String cityKey = excelArea.provinceCode + "|" + excelArea.cityCode;
            String districtKey = excelArea.provinceCode + "|" + excelArea.cityCode + "|" + excelArea.districtCode;
            boolean hasProvince = code2IdProvince.containsKey(provinceKey);
            if (!hasProvince) {
                EccAreaDO provinceInfo = new EccAreaDO();
                provinceInfo.setOperator(spEnum.getCode());
                provinceInfo.setAreaName(excelArea.provinceName);
                provinceInfo.setNumCode(excelArea.provinceCode);
                provinceInfo.setPostCode(excelArea.provinceCode);
                provinceInfo.setParentId(0);
                provinceInfo.setLevel(1);
                areaMapper.insert(provinceInfo);
                code2IdProvince.put(provinceKey, provinceInfo.getId());
            }
            boolean hasCity = code2IdCity.containsKey(cityKey);
            if (!hasCity) {
                EccAreaDO cityInfo = new EccAreaDO();
                cityInfo.setOperator(spEnum.getCode());
                cityInfo.setAreaName(excelArea.cityName);
                cityInfo.setNumCode(excelArea.cityCode);
                cityInfo.setPostCode(excelArea.cityCode);
                cityInfo.setParentId(code2IdProvince.get(provinceKey));
                cityInfo.setLevel(2);
                areaMapper.insert(cityInfo);
                code2IdCity.put(cityKey, cityInfo.getId());
            }

            boolean hasDistrict = code2IdDistrict.containsKey(districtKey);
            if (!hasDistrict) {
                EccAreaDO districtInfo = new EccAreaDO();
                districtInfo.setOperator(spEnum.getCode());
                districtInfo.setAreaName(excelArea.districtName);
                districtInfo.setNumCode(null);
                districtInfo.setPostCode(excelArea.districtCode);
                districtInfo.setParentId(code2IdCity.get(cityKey));
                districtInfo.setLevel(3);
                areaMapper.insert(districtInfo);
                code2IdDistrict.put(districtKey, districtInfo.getId());
            }
        }


    }

    @Data
    static class ExcelArea {
        String provinceName;
        String provinceCode;
        String cityName;
        String cityCode;
        String districtName;
        String districtCode;
    }


}
