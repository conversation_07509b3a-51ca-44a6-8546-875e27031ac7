package com.yuelan.hermes.commons.util;

import com.yuelan.hermes.quanyi.biz.manager.GameOrderManager;
import com.yuelan.hermes.quanyi.biz.service.WyService;
import com.yuelan.hermes.quanyi.mapper.GamingOrderItemMapper;
import com.yuelan.hermes.quanyi.mapper.GamingOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> 2024/4/1 13:33
 */
// @RunWith(SpringRunner.class)
// @SpringBootTest(classes = {QuanYiApplication.class})
public class WyTests {

    // // @Test
    // public void test() {
    //     List<String> lines = FileUtil.readUtf8Lines("/Users/<USER>/Desktop/WY.TXT");
    //     String url = "https://qyapi.hzyuelan.com/wy/vouchercp/notify";
    //     for (String line : lines) {
    //         String orderNo = line;
    //         GamingOrderNotifyReq req = new GamingOrderNotifyReq();
    //         req.setOrderNo(orderNo);
    //         req.setSendResult(1);
    //         req.setTimestamp(System.currentTimeMillis());
    //         req.setSupplierOrderNo(orderNo);
    //         req.setTimestamp(System.currentTimeMillis());
    //         String jsonBody = JSONObject.toJSONString(req);
    //         String resp = HttpUtil.post(url, jsonBody);
    //         System.out.println(orderNo + ":" + resp);
    //     }
    //
    // }
    @Autowired
    private GamingOrderItemMapper gamingOrderItemMapper;
    @Autowired
    private WyService wyService;
    @Autowired
    private GameOrderManager gameOrderManager;
    @Autowired
    private GamingOrderMapper gamingOrderMapper;

    // @Test
    // public void test2() {
    //     List<String> lines = FileUtil.readUtf8Lines("/Users/<USER>/Desktop/WY2.TXT");
    //     List<String> resultLine = new ArrayList<>(lines.size());
    //     System.out.println("size="+lines.size());
    //     int index = 0;
    //     for (String line : lines) {
    //         index++;
    //         System.out.println("开始处理："+index+"/"+lines.size());
    //         String[] split = line.split(", ");
    //         System.out.println(split[0] + "= "+split[1]);
    //         String orderNo = split[0];
    //         ObtainStatusEnum statusEnum = split[1].equals("订单过期")?ObtainStatusEnum.FAIL_OBTAINED:ObtainStatusEnum.SUCCESS_OBTAINED;
    //         GamingOrderItemDO gamingOrderItemDO = gamingOrderItemMapper.findByItemNo(orderNo);
    //         if(gamingOrderItemDO==null){
    //             resultLine.add(line+"不存在");
    //             continue;
    //         }
    //         if (Objects.equals(gamingOrderItemDO.getObtainStatus(), ObtainStatusEnum.WAIT_OBTAINED.getCode())) {
    //             //直接更新
    //             resultLine.add(line+"---直接更新");
    //             GamingOrderNotifyReq req = new GamingOrderNotifyReq();
    //             req.setOrderNo(orderNo);
    //             req.setSendTime(System.currentTimeMillis());
    //             req.setSendMessage("模拟回调");
    //             req.setSendResult(statusEnum == ObtainStatusEnum.FAIL_OBTAINED?0:1);
    //             req.setSupplierOrderNo(orderNo);
    //             wyService.notify(req);
    //         }else {
    //             if(Objects.equals(gamingOrderItemDO.getObtainStatus(), statusEnum.getCode())){
    //                 //无需更新
    //                 resultLine.add(line+"---无需更新");
    //             }else {
    //                 //更新冲突
    //                 resultLine.add(line+"---更新冲突");
    //                 System.err.println(line+"---更新冲突"+gamingOrderItemDO.getObtainStatus() +":"+statusEnum.getCode());
    //             }
    //         }
    //         // if(!line.contains("订单过期") && !line.contains("success")){
    //         //     System.out.println(line);
    //         // }
    //     }
    //     FileUtil.writeLines(resultLine,"/Users/<USER>/Desktop/WY2RESULT.TXT","utf-8");
    //
    //
    // }

    // @Test
    // public void test3() {
    //     List<String> lines = FileUtil.readUtf8Lines("/Users/<USER>/Desktop/BU.TXT");
    //     System.out.println(lines.size());
    //     for (String line : lines) {
    //         System.out.println(line + ":");
    //         GamingOrderItemDO orderItemDO = gamingOrderItemMapper.findByItemNo(line);
    //         if (!Objects.equals(orderItemDO.getOrderStatus(), OrderStatusEnum.ABNORMAL.getCode())) {
    //             System.err.println("无需处理" + line);
    //             continue;
    //         }else {
    //             System.out.println("开始处理" + line);
    //         }
    //         GamingOrderDO gamingOrderDO = gamingOrderMapper.selectById(orderItemDO.getOrderId());
    //         gameOrderManager.createOrderItem(gamingOrderDO, orderItemDO, "", Boolean.TRUE);
    //     }
    // }


}
