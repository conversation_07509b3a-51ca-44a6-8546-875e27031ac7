package com.yuelan.hermes.quanyi.biz.service;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;
import java.util.Map;

/**
 * 扣量计算服务测试
 * 验证基于Redis计数器的精确扣量算法
 */
@SpringBootTest
@ActiveProfiles("test")
public class DeductionCalculationServiceTest {

    @Autowired
    private DeductionCalculationService deductionCalculationService;

    @Test
    public void testDeductionPositions() {
        // 测试各种扣量比例的位置配置
        System.out.println("=== 扣量位置配置测试 ===");
        
        int[] rates = {5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95};
        
        for (int rate : rates) {
            List<Integer> positions = deductionCalculationService.getDeductionPositions(rate);
            System.out.printf("%d%% 扣量位置: %s (共%d个位置)\n", rate, positions, positions.size());
            
            // 验证扣量位置数量是否正确
            int expectedCount = rate * 20 / 100;
            if (positions.size() != expectedCount) {
                System.err.printf("警告: %d%%扣量比例期望%d个位置，实际%d个位置\n", 
                    rate, expectedCount, positions.size());
            }
        }
    }

    @Test
    public void testDeductionAccuracy() {
        // 测试扣量精确度
        System.out.println("\n=== 扣量精确度测试 ===");
        
        Long testChannelId = 999L; // 测试渠道ID
        int deductionRate = 20; // 20%扣量
        int testCount = 100; // 测试100次
        
        // 重置计数器
        deductionCalculationService.resetChannelDeductionCounter(testChannelId);
        
        // 模拟扣量计算
        int deductedCount = 0;
        for (int i = 1; i <= testCount; i++) {
            EccOuterChannelDO testChannel = new EccOuterChannelDO();
            testChannel.setOuterChannelId(testChannelId);
            testChannel.setDeductionRate(deductionRate);
            testChannel.setChannelName("测试渠道");
            
            // 使用反射调用私有方法进行测试
            try {
                java.lang.reflect.Method method = DeductionCalculationService.class
                    .getDeclaredMethod("performDeduction", EccOuterChannelDO.class);
                method.setAccessible(true);
                boolean deducted = (Boolean) method.invoke(deductionCalculationService, testChannel);
                
                if (deducted) {
                    deductedCount++;
                }
                
                if (i % 20 == 0) {
                    System.out.printf("前%d次订单，扣量%d次，扣量率: %.1f%%\n", 
                        i, deductedCount, (double) deductedCount / i * 100);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        
        double actualRate = (double) deductedCount / testCount * 100;
        System.out.printf("最终结果: %d次订单中扣量%d次，实际扣量率: %.1f%%，期望扣量率: %d%%\n", 
            testCount, deductedCount, actualRate, deductionRate);
    }

    @Test
    public void testDeductionStats() {
        // 测试扣量统计信息
        System.out.println("\n=== 扣量统计信息测试 ===");
        
        Long testChannelId = 888L;
        int deductionRate = 25; // 25%扣量
        
        // 重置计数器
        deductionCalculationService.resetChannelDeductionCounter(testChannelId);
        
        // 模拟一些订单
        for (int i = 0; i < 45; i++) {
            EccOuterChannelDO testChannel = new EccOuterChannelDO();
            testChannel.setOuterChannelId(testChannelId);
            testChannel.setDeductionRate(deductionRate);
            
            try {
                java.lang.reflect.Method method = DeductionCalculationService.class
                    .getDeclaredMethod("performDeduction", EccOuterChannelDO.class);
                method.setAccessible(true);
                method.invoke(deductionCalculationService, testChannel);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        
        // 获取统计信息
        Map<String, Object> stats = deductionCalculationService.getChannelDeductionStats(testChannelId, deductionRate);
        
        System.out.println("渠道扣量统计信息:");
        stats.forEach((key, value) -> System.out.println(key + ": " + value));
    }

    @Test
    public void testPredictNextDeduction() {
        // 测试下一个订单扣量预测
        System.out.println("\n=== 下一个订单扣量预测测试 ===");
        
        Long testChannelId = 777L;
        int deductionRate = 15; // 15%扣量
        
        // 重置计数器
        deductionCalculationService.resetChannelDeductionCounter(testChannelId);
        
        // 测试前20个位置的预测
        for (int i = 0; i < 20; i++) {
            boolean willDeduct = deductionCalculationService.predictNextDeduction(testChannelId, deductionRate);
            Long currentCount = deductionCalculationService.getChannelDeductionCounter(testChannelId);
            int nextPosition = (int) (currentCount % 20) + 1;
            
            System.out.printf("位置%d: 预测%s扣量\n", nextPosition, willDeduct ? "会" : "不会");
            
            // 模拟执行一次扣量
            EccOuterChannelDO testChannel = new EccOuterChannelDO();
            testChannel.setOuterChannelId(testChannelId);
            testChannel.setDeductionRate(deductionRate);
            
            try {
                java.lang.reflect.Method method = DeductionCalculationService.class
                    .getDeclaredMethod("performDeduction", EccOuterChannelDO.class);
                method.setAccessible(true);
                boolean actualDeducted = (Boolean) method.invoke(deductionCalculationService, testChannel);
                
                if (willDeduct != actualDeducted) {
                    System.err.printf("预测错误！位置%d预测%s，实际%s\n", 
                        nextPosition, willDeduct ? "扣量" : "不扣量", actualDeducted ? "扣量" : "不扣量");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Test
    public void testMultipleCycles() {
        // 测试多个周期的扣量一致性
        System.out.println("\n=== 多周期扣量一致性测试 ===");
        
        Long testChannelId = 666L;
        int deductionRate = 30; // 30%扣量
        
        // 重置计数器
        deductionCalculationService.resetChannelDeductionCounter(testChannelId);
        
        // 测试3个完整周期（60次）
        int[] cycleDeductions = new int[3];
        
        for (int cycle = 0; cycle < 3; cycle++) {
            int deductedInCycle = 0;
            
            for (int i = 0; i < 20; i++) {
                EccOuterChannelDO testChannel = new EccOuterChannelDO();
                testChannel.setOuterChannelId(testChannelId);
                testChannel.setDeductionRate(deductionRate);
                
                try {
                    java.lang.reflect.Method method = DeductionCalculationService.class
                        .getDeclaredMethod("performDeduction", EccOuterChannelDO.class);
                    method.setAccessible(true);
                    boolean deducted = (Boolean) method.invoke(deductionCalculationService, testChannel);
                    
                    if (deducted) {
                        deductedInCycle++;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            
            cycleDeductions[cycle] = deductedInCycle;
            System.out.printf("第%d个周期: 扣量%d次\n", cycle + 1, deductedInCycle);
        }
        
        // 验证每个周期的扣量次数是否一致
        boolean consistent = true;
        for (int i = 1; i < cycleDeductions.length; i++) {
            if (cycleDeductions[i] != cycleDeductions[0]) {
                consistent = false;
                break;
            }
        }
        
        System.out.printf("多周期一致性: %s\n", consistent ? "通过" : "失败");
    }
}
