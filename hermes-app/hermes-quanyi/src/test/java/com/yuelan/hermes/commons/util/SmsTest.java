package com.yuelan.hermes.commons.util;

import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.biz.handler.impl.SmsTemplateOnlyPhoneImpl;
import com.yuelan.hermes.quanyi.biz.service.SmsSendService;
import com.yuelan.hermes.quanyi.common.enums.SmsTypeEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.SmsSendResultBO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> 2024/12/9
 * @since 2024/12/9
 */
@Slf4j

@RunWith(SpringRunner.class)
@ActiveProfiles("local")
@SpringBootTest(classes = {QuanYiApplication.class})
public class SmsTest {
    @Autowired
    SmsSendService smsSendService;

    @Test
    public void testSendSms() {
        //https://g.migufun.com/gddzpddw-13524907923-
        SmsTemplateOnlyPhoneImpl onlyPhone = new SmsTemplateOnlyPhoneImpl("18321645475");
        SmsSendResultBO resultBO = smsSendService.sendSms(SmsTypeEnum.GAMING_GUIDE_REDEEM, onlyPhone);
        System.out.println(resultBO);
        //https://g.migufun.com/djkdzpdsh-18321645475-

    }
}
