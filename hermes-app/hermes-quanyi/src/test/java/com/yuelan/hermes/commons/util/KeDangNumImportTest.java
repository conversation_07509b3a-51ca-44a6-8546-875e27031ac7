package com.yuelan.hermes.commons.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import com.yuelan.hermes.commons.enums.SpEnum;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccAreaDO;
import com.yuelan.hermes.quanyi.mapper.EccAreaDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/12/27
 * @since 2024/12/27
 */
@Slf4j
@RunWith(SpringRunner.class)
// 指定测试环境
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class KeDangNumImportTest {


    @Autowired
    EccAreaDOMapper areaMapper;


    @Test
    public void importGdArea() {

        CsvReader reader = CsvUtil.getReader();
        CsvData data = reader.read(FileUtil.file("/Users/<USER>/Downloads/地址库.csv"));
        List<CsvRow> rows = data.getRows();
        int i = 0;
        for (CsvRow row : rows) {
            // System.out.println(row);
            System.out.println(row.getRawList());
        }
        final SpEnum spEnum = SpEnum.TELECOM;
        HashMap<String, Integer> code2IdProvince = new HashMap<>();
        HashMap<String, Integer> code2IdCity = new HashMap<>();
        HashMap<String, Integer> code2IdDistrict = new HashMap<>();
        for (CsvRow row : rows) {
            // id	parent_code	name	short_name	level	code
            List<String> rawList = row.getRawList();
            String parentCode = rawList.get(1);
            String name = rawList.get(2);
            String shortName = rawList.get(3);
            String level = rawList.get(4);
            String areaCode = rawList.get(5);
            if (Objects.equals(level, "1")) {
                EccAreaDO provinceInfo = new EccAreaDO();
                provinceInfo.setOperator(spEnum.getCode());
                provinceInfo.setAreaName(name);
                provinceInfo.setNumCode(areaCode);
                provinceInfo.setPostCode(areaCode);
                provinceInfo.setParentId(0);
                provinceInfo.setLevel(1);
                areaMapper.insert(provinceInfo);
                code2IdProvince.put(areaCode, provinceInfo.getId());
            } else if (Objects.equals(level, "2")) {
                EccAreaDO cityInfo = new EccAreaDO();
                cityInfo.setOperator(spEnum.getCode());
                cityInfo.setAreaName(name);
                cityInfo.setNumCode(areaCode);
                cityInfo.setPostCode(areaCode);
                cityInfo.setParentId(code2IdProvince.get(parentCode));
                cityInfo.setLevel(2);
                areaMapper.insert(cityInfo);
                code2IdCity.put(areaCode, cityInfo.getId());

            } else if (Objects.equals(level, "3")) {
                EccAreaDO districtInfo = new EccAreaDO();
                districtInfo.setOperator(spEnum.getCode());
                districtInfo.setAreaName(name);
                districtInfo.setNumCode(null);
                districtInfo.setPostCode(areaCode);
                districtInfo.setParentId(code2IdCity.get(parentCode));
                districtInfo.setLevel(3);
                areaMapper.insert(districtInfo);
                code2IdDistrict.put(areaCode, districtInfo.getId());

            } else {
                log.error("level error:{}", level);
            }

        }
    }

}
