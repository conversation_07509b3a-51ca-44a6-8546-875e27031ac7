package com.yuelan.hermes.commons.util.xieyun;


import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;

/**
 * Copyright wuwenhui
 *
 * <AUTHOR>
 */
public class AESUtil {

    public final static String CHART_DEFAULT = "UTF-8";
    private static String IVRANDOM = "0123456789ABEDEF";

    public static byte[] decodeAES(String security, String key) {
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec sp = new IvParameterSpec(IVRANDOM.getBytes(CHART_DEFAULT));
            byte[] content = Base64.decodeBase64(security);
            SecureRandom random = new SecureRandom(key.getBytes());
            SecretKeySpec spec = new SecretKeySpec(key.getBytes(CHART_DEFAULT), "AES");
            cipher.init(Cipher.DECRYPT_MODE, spec, sp);
            return cipher.doFinal(content);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static String enAES(String security, String key) {
        try {
            byte[] content = security.getBytes(CHART_DEFAULT);
            byte[] aeskeys = key.getBytes(CHART_DEFAULT);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            IvParameterSpec sp = new IvParameterSpec(IVRANDOM.getBytes(CHART_DEFAULT));
            SecretKeySpec spec = new SecretKeySpec(aeskeys, "AES");
            cipher.init(Cipher.ENCRYPT_MODE, spec, sp);
            byte[] context = cipher.doFinal(content);
            return Base64.encodeBase64String(context);

        } catch (Exception e) {
        }
        return null;
    }

}
