package com.yuelan.hermes.commons.util;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.biz.service.GamingOrderItemService;
import com.yuelan.hermes.quanyi.common.pojo.domain.GamingOrderItemDO;
import com.yuelan.hermes.quanyi.mapper.GamingOrderItemMapper;
import com.yuelan.hermes.quanyi.mapper.GamingProductMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/9/3 14:41
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class ObtainTimeTest {
    @Autowired
    private GamingOrderItemMapper gamingOrderItemMapper;
    @Autowired
    private GamingOrderItemService gamingOrderItemService;
    @Autowired
    private GamingProductMapper gamingProductMapper;

    @Test
    public void obtainTime() throws InterruptedException {
        int pageNum = 1;
        int pageSize = 1000;
        PageDTO<GamingOrderItemDO> page = new PageDTO<>(pageNum, pageSize);
        do {

            PageDTO<GamingOrderItemDO> pageResult = new LambdaQueryChainWrapper<>(gamingOrderItemMapper)
                    .select(GamingOrderItemDO::getId, GamingOrderItemDO::getExtData, GamingOrderItemDO::getUpdateTime)
                    .eq(GamingOrderItemDO::getObtainStatus, 1)
                    .isNull(GamingOrderItemDO::getObtainTime)
                    .page(page);
            if (pageResult.getRecords().isEmpty()) {
                System.out.println("任务结束");
                break;
            }
            Thread.sleep(1);
            System.out.println("还有:" + pageResult.getPages() * pageResult.getSize());
            List<GamingOrderItemDO> updateList = new ArrayList<>();
            for (GamingOrderItemDO record : pageResult.getRecords()) {
                GamingOrderItemDO updateRecord = new GamingOrderItemDO();
                JSONObject extra = JSON.parseObject(record.getExtData());
                Long sendTime = extra.getLong("sendTime");
                // 时间戳转LocalDateTime
                if (Objects.isNull(sendTime)) {
                    updateRecord.setObtainTime(LocalDateTimeUtil.of(record.getUpdateTime()));
                } else {
                    LocalDateTime obtTime = LocalDateTimeUtil.of(sendTime);
                    updateRecord.setObtainTime(obtTime);
                }
                updateRecord.setId(record.getId());
                updateList.add(updateRecord);
            }
            System.out.println("更新数量:" + updateList.size());
            // 批量更新
            gamingOrderItemService.updateBatchById(updateList);
        } while (page.hasNext());

    }

}
