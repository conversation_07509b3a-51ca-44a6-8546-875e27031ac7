package com.yuelan.hermes.commons.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.yuelan.hermes.quanyi.common.util.MiguFunUtils;
import com.yuelan.hermes.quanyi.controller.request.MiguFunCreateOrderReq;
import org.junit.Test;

import java.util.List;

public class MgOrderTest {

    //    @Test
    public void createOrderTest() {
        String orderNos = "";
        List<String> orderList = StrUtil.split(orderNos, ",");
        for (String orderNo : orderList) {
            String result = createOrder(orderNo);
            System.out.println(orderNo + ":" + result);
        }
    }

    @Test
    public void batchOrderTest() {
        for (int i = 0; i < 3; i++) {
            String orderNo = IdUtil.objectId();
            String result = createOrder(orderNo);
            System.out.println(orderNo + ":" + result);
        }
    }

    private String createOrder(String orderNo) {
        MiguFunCreateOrderReq req = new MiguFunCreateOrderReq();
        req.setSign(null);
        req.setOrderId(orderNo);
        String phone = "189" + RandomUtil.randomNumbers(8);
        req.setUserId(phone);
        req.setCreateTime(System.currentTimeMillis());
        req.setPhoneNum(MiguFunUtils.encryptHex(phone, "qimht1gmcqcmi34h"));
        req.setProductCode("ztlXVJHt");
        req.setExtrInfo(null);

        List<Object> params = Lists.newArrayList(req.getOrderId(), req.getUserId(), req.getCreateTime());
        String sign = MiguFunUtils.sign(params, "dIBJOlaL2V");
        return HttpRequest.post("https://quanyi-api-ntest.meta-xuantan.com/mg/order/create").body(JSON.toJSONString(req)).header("sign", sign).execute().body();
    }

    @Test
    public void creatYongJieWuJianOrder() {

        MiguFunCreateOrderReq req = new MiguFunCreateOrderReq();
        req.setSign(null);
        req.setOrderId(IdUtil.simpleUUID());
        // String phone = "13989458839";
        String phone = "18861870363";
        req.setUserId(phone);
        req.setCreateTime(System.currentTimeMillis());
        req.setPhoneNum(MiguFunUtils.encryptHex(phone, "qimht1gmcqcmi34h"));
        req.setProductCode("hAT7d1EI");
        req.setExtrInfo(null);

        List<Object> params = Lists.newArrayList(req.getOrderId(), req.getUserId(), req.getCreateTime());
        String sign = MiguFunUtils.sign(params, "dIBJOlaL2V");
        System.out.println(HttpRequest.post("https://quanyi-api-ntest.meta-xuantan.com/mg/order/create").body(JSON.toJSONString(req)).header("sign", sign).execute().body());
    }

    @Test
    public void signTest() {
        MiguFunCreateOrderReq req = new MiguFunCreateOrderReq();
        req.setSign(null);
        req.setOrderId("021711005450902268370");
        req.setUserId("193831696130");
        req.setCreateTime(1711005451000L);
        req.setPhoneNum("bd5bcfabc5509f7b4529f4b8d287b03f");
        req.setProductCode("ztlXVJHt");
        req.setExtrInfo("{\"saleChannel\":\"1\"}");

        List<Object> params = Lists.newArrayList(req.getOrderId(), req.getUserId(), req.getCreateTime());
        System.out.println(MiguFunUtils.sign(params, "dIBJOlaL2V"));
    }
}
