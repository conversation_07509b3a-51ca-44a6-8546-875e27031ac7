package com.yuelan.hermes.commons.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.controller.request.RsCreateOrderRequest;

import java.util.List;

public class RsOrderTest {

    //    @Test
    public void createOrderTest() {
        String orderNos = "";
        List<String> orderList = StrUtil.split(orderNos, ",");
        for (String orderNo : orderList) {
            String result = createOrder(orderNo);
            System.out.println(orderNo + ":" + result);
        }
    }

    //    @Test
    public void batchOrderTest() {
        for (int i = 0; i < 1; i++) {
            String orderNo = IdUtil.objectId();
            String result = createOrder(orderNo);
            System.out.println(orderNo + ":" + result);
        }
    }

    private String createOrder(String orderNo) {
        RsCreateOrderRequest orderRequest = new RsCreateOrderRequest();
        orderRequest.setCupdOrderNo(orderNo);
        orderRequest.setNum("1");
        orderRequest.setThirdProductNo("YACHLJFv");
        orderRequest.setMobileNo("***********");
        orderRequest.setUserAccount("***********");
        return HttpRequest.post(" http://10.0.0.226:21000/test/order/virtual/M7e6163eb8e452791000").body(JSON.toJSONString(orderRequest)).execute().body();
    }
}
