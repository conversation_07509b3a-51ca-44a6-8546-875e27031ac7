package com.yuelan.hermes.commons.util;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.biz.service.EccAreaService;
import com.yuelan.hermes.quanyi.common.enums.SpProdEnum;
import com.yuelan.hermes.quanyi.common.enums.ZopMsgEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.EccZopSelectPhoneNumsResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccProductDO;
import com.yuelan.hermes.quanyi.controller.request.EcommerceSimCardSearchReq;
import com.yuelan.hermes.quanyi.remote.ZOPManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;

/**
 * <AUTHOR> 2024/5/6 下午3:51
 */
@Slf4j
@RunWith(SpringRunner.class)
// 指定测试环境
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class ZopCommTest {

    @Autowired
    private ZOPManager zopManager;
    @Autowired
    EccAreaService eccAreaService;

    /**
     * 初始化号卡省份城市信息
     * 隔段时间会更新需要清空数据库再次导入
     */
    @Test
    public void initAreaTable() {
        // 初始化数据表"ecc_area"前需要手动清空表；id传null表示初始化所有省份数据，id有值表示初始化某个省份数据
        eccAreaService.initUnicomAreaV2(null);
    }

    @Test
    public void getZhejiang() {
        zopManager.provinceInfo(new EccProductDO());
    }

    @Test
    public void idCardCheck() {
        zopManager.checkIdCard("340222199110286619", "李磊", new EccProductDO());
    }

    @Test
    public void orderMessage() {
        zopManager.orderMessageGet(SpProdEnum.YL, ZopMsgEnum.ORDER_UPDATE);
    }

    @Test
    public void orderMessageDel() {
        zopManager.orderMessageConsume(SpProdEnum.YL, "1124071531075071", ZopMsgEnum.ORDER_UPDATE);
    }


    @Test
    public void orderQuery() {
        zopManager.orderQuery(new EccProductDO(), "13989458839");
    }

    @Test
    public void selectPhones() {
        EccProductDO productDO = new EccProductDO();
        // EccZopOrderDO zopOrderDO = new EccZopOrderDO();
        productDO.setSpGoodsId("344209274377");
        EcommerceSimCardSearchReq req = new EcommerceSimCardSearchReq();
        req.setPhoneLast4("4686");
        EccZopSelectPhoneNumsResultBO resultBO = zopManager.selectPhoneNumV1(req, productDO);
        System.out.println(JSONObject.toJSONString(resultBO));

    }

    /**
     * 落地页修改之后需要上传落地图图片 返回新的资源id 配置在yml内
     */
    @Test
    public void uploadPageAndGetResourceId() {
        // File file = new File("/Users/<USER>/Downloads/unicom/29quanyi.png");
        File file = new File("/Users/<USER>/Downloads/unicom/province/20250415/80G.jpg");
        String extension = FilenameUtils.getExtension(file.getPath());
        // 前缀
        String prefix = "data:image/" + extension + ";base64,";
        String imgBase64 = prefix + Base64.encode(file);
        EccProductDO eccProductDO = new EccProductDO();
        eccProductDO.setSpProdId(SpProdEnum.YL_PROVINCE_STANDARD.getProdId());
        zopManager.uploadResource(eccProductDO, imgBase64, "345204034020");

        // goodsId=344209274377 响应= {"body":{"resourceId":"3064101642724578"},
        // goodsId=344209274369 响应= {"body":{"resourceId":"2764101642924064"}
    }


}
