package com.yuelan.hermes.commons.test;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class EasyExcelDemo {

    public static void main(String[] args) {
        // 准备数据
        List<Parent> parentList = new ArrayList<>();
        parentList.add(new Parent("父对象1", Arrays.asList(
                new Child("子对象1-1子对象1-1,子对象1-1,子对象1-1,子对象1-1,子对象1-1,子对象1-1", "值1-1"),
                new Child("子对象1-1", "值1-2")
        )));

        parentList.add(new Parent("父对象1", Arrays.asList(
                new Child("子对象1-1", "值1-1"),
                new Child("子对象1-2", "值1-2")
        )));

        parentList.add(new Parent("父对象2", Arrays.asList(
                new Child("子对象2-1", "值2-1"),
                new Child("子对象2-2", "值2-2")
        )));

        // 设置表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        // 设置内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);

        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

        // 写数据到Excel
        String fileName = "/Users/<USER>/Downloads/example.xlsx";
        ExcelWriter excelWriter = EasyExcel.write(fileName)
                .registerWriteHandler(horizontalCellStyleStrategy)
                .build();

        WriteSheet writeSheet =
                EasyExcel.writerSheet("Sheet1")
                        .head(FlatData.class)
                        .build();

        // 收集平铺后的数据
        List<FlatData> flatDataList = new ArrayList<>();
        for (Parent parent : parentList) {
            for (Child child : parent.getChildren()) {
                flatDataList.add(new FlatData(parent.getName(), child.getChildName(), child.getChildValue()));
            }
        }

        // 写数据
        excelWriter.write(flatDataList, writeSheet);

        // 获取 Workbook
        Workbook workbook = excelWriter.writeContext().writeWorkbookHolder().getWorkbook();

        // 合并单元格
        int rowIndex = 1; // 数据从第2行开始，索引为1
        for (Parent parent : parentList) {
            int startRowIndex = rowIndex;
            int endRowIndex = rowIndex + parent.getChildren().size() - 1;
            if (startRowIndex != endRowIndex) {
                workbook.getSheetAt(0).addMergedRegion(new CellRangeAddress(startRowIndex, endRowIndex, 0, 0));
            }
            rowIndex += parent.getChildren().size();
        }

        // 关闭 ExcelWriter
        excelWriter.finish();
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class FlatData {

        @ColumnWidth(20)
        @ExcelProperty("父对象名称")
        private String parentName;

        @ColumnWidth(100)
        @ExcelProperty("子对象名称")
        private String childName;

        @ExcelProperty("子对象值")
        private String childValue;
    }


}
