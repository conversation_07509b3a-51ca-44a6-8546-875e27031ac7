package com.yuelan.hermes.quanyi.biz.manager;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.remote.gaming.LanJinManager;
import com.yuelan.hermes.quanyi.remote.response.LanJinSendItemResp;
import com.yuelan.hermes.quanyi.remote.response.LanJinUserInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 蓝鲸电竞卡管理服务测试类
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class LanJinManagerTest {

    @Autowired
    private LanJinManager lanJinManager;

    /**
     * 测试查看用户信息接口
     */
    @Test
    public void testGetUserInfo() {
        log.info("测试查看用户信息开始");

        LanJinUserInfoResp response = lanJinManager.getUserInfo("AFJ6UB-5", "ios");
        log.info("查看用户信息响应：{}", JSON.toJSONString(response));
    }

    /**
     * 测试发放道具接口
     */
    @Test
    public void testSendItem() {
        log.info("测试发放道具开始");

        LanJinSendItemResp response = lanJinManager.sendItem("AGBMD2-52", "dian_jing_ka_15", "android", IdUtil.fastSimpleUUID());
        log.info("发放道具响应：{}", JSON.toJSONString(response));
    }
}
