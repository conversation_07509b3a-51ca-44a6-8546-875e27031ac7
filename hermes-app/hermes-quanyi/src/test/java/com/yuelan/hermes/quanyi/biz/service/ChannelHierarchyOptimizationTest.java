package com.yuelan.hermes.quanyi.biz.service;

import com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * 渠道层级查询优化测试
 * 测试递归CTE查询的性能和正确性
 */
@SpringBootTest
@ActiveProfiles("test")
public class ChannelHierarchyOptimizationTest {

    @Autowired
    private ChannelHierarchyService channelHierarchyService;

    @Autowired
    private EccOuterChannelDOService eccOuterChannelDOService;

    @Test
    public void testGetParentChannelChainPerformance() {
        // 测试上级渠道链查询性能
        Long testChannelId = 1L; // 替换为实际的测试渠道ID
        
        long startTime = System.currentTimeMillis();
        List<EccOuterChannelDO> parentChain = channelHierarchyService.getParentChannelChain(testChannelId);
        long endTime = System.currentTimeMillis();
        
        System.out.println("上级渠道链查询耗时: " + (endTime - startTime) + "ms");
        System.out.println("上级渠道数量: " + parentChain.size());
        
        // 验证结果
        for (EccOuterChannelDO channel : parentChain) {
            System.out.println("渠道ID: " + channel.getOuterChannelId() + 
                             ", 渠道名称: " + channel.getChannelName() + 
                             ", 上级ID: " + channel.getParentChannelId());
        }
    }

    @Test
    public void testGetAllSubChannelsPerformance() {
        // 测试下级渠道查询性能
        Long testChannelId = 1L; // 替换为实际的测试渠道ID
        
        long startTime = System.currentTimeMillis();
        List<EccOuterChannelDO> subChannels = channelHierarchyService.getAllSubChannels(testChannelId);
        long endTime = System.currentTimeMillis();
        
        System.out.println("下级渠道查询耗时: " + (endTime - startTime) + "ms");
        System.out.println("下级渠道数量: " + subChannels.size());
        
        // 验证结果
        for (EccOuterChannelDO channel : subChannels) {
            System.out.println("渠道ID: " + channel.getOuterChannelId() + 
                             ", 渠道名称: " + channel.getChannelName() + 
                             ", 上级ID: " + channel.getParentChannelId());
        }
    }

    @Test
    public void testGetAllDescendantChannelIdsPerformance() {
        // 测试下级渠道ID查询性能
        Long testChannelId = 1L; // 替换为实际的测试渠道ID
        
        long startTime = System.currentTimeMillis();
        List<Long> descendantIds = eccOuterChannelDOService.getAllDescendantChannelIds(testChannelId);
        long endTime = System.currentTimeMillis();
        
        System.out.println("下级渠道ID查询耗时: " + (endTime - startTime) + "ms");
        System.out.println("下级渠道ID数量: " + descendantIds.size());
        System.out.println("下级渠道IDs: " + descendantIds);
    }

    @Test
    public void testCircularReferenceDetection() {
        // 测试循环引用检测
        // 注意：这个测试需要在测试数据库中创建循环引用的测试数据
        System.out.println("循环引用检测测试 - 需要准备测试数据");
        
        // 示例：如果有循环引用 A -> B -> C -> A
        // 查询应该能够检测到循环并停止递归
    }
}
