package com.yuelan.hermes.commons.test;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> 2024/7/8 下午6:43
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class Parent {
    @ColumnWidth(25)
    @ExcelProperty("父对象名称")
    private String name;


    @ExcelProperty("子对象列表")
    private List<Child> children;

}
