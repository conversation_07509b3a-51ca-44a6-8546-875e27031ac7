package com.yuelan.hermes.commons.benefit;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.biz.handler.impl.BenefitHaiNanDxPayChannel;
import com.yuelan.hermes.quanyi.common.enums.HNDxPayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.common.pojo.properties.HaiNanDxProperties;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.remote.benefit.HaiNanDxManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.net.HttpCookie;
import java.util.UUID;

/**
 * <AUTHOR> 2025/3/26
 * @since 2025/3/26
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class HaiNanDxManagerTest {

    @Resource
    private HaiNanDxManager haiNanDxManager;
    HNDxPayPkgEnum pkg = HNDxPayPkgEnum.HN_PKG_29_YEAR;
    String mobile = "19030925187";
    @Resource
    private BenefitHaiNanDxPayChannel benefitHaiNanDxPayChannel;

    @Test
    public void testSendSmsCode() throws InterruptedException {


        UserBenefitOrderReq req = new UserBenefitOrderReq();
        req.setMobile(mobile);

        BenefitProductDO productDO = new BenefitProductDO();
        productDO.setPayChannelPkgId(pkg.getPkgId());

        boolean b = benefitHaiNanDxPayChannel.orderSendSmsCode(req, productDO);
        System.out.println("发送短信验证码结果: " + b);
    }

    @Test
    public void verifySmsCode() {
        UserBenefitOrderReq req = new UserBenefitOrderReq();
        req.setMobile(mobile);
        req.setSmsCode("772826");

        BenefitProductDO productDO = new BenefitProductDO();
        productDO.setPayChannelPkgId(pkg.getPkgId());

        BenefitOrderDO orderDO = new BenefitOrderDO();
        BenefitPayResultBO resultBO = benefitHaiNanDxPayChannel.getPayUrl(req, productDO, orderDO);
        System.out.println("获取支付链接结果: " + JSONObject.toJSONString(resultBO));

        System.out.println(orderDO.getOrderNo());


    }

    @Test
    public void cookieTest() {
        HttpRequest post = HttpRequest.post("zhihu.com");
        for (HttpCookie cookie : post.execute().getCookies()) {
            String cookieStr = JSONObject.toJSONString(cookie);
            System.out.println(cookieStr);

        }
    }

    /**
     * 测试 getLink 方法
     * 由于 getLink 是私有方法，使用反射来测试
     */
    @Test
    public void testGetLink() {
        try {
            // 准备测试数据
            String testMobile = "19030925187";
            String testOrderNo = "TEST_ORDER_" + UUID.randomUUID().toString().substring(0, 8);
            HNDxPayPkgEnum testPkg = HNDxPayPkgEnum.HN_PKG_29_YEAR;

            // 获取产品配置
            HaiNanDxProperties properties = new HaiNanDxProperties();
            HaiNanDxProperties.ProdConfig prodConfig = properties.getProdMap().get(testPkg);

            // 使用反射获取私有方法
            Method getLinkMethod = HaiNanDxManager.class.getDeclaredMethod(
                "getLink", String.class, String.class, HaiNanDxProperties.ProdConfig.class);
            getLinkMethod.setAccessible(true);

            // 调用方法
            String result = (String) getLinkMethod.invoke(haiNanDxManager, testMobile, testOrderNo, prodConfig);

            // 验证结果
            log.info("getLink 测试结果: {}", result);

            if (result != null) {
                System.out.println("✅ getLink 测试成功 - 获取到链接: " + result);
                // 验证链接格式是否正确
                if (result.startsWith("http")) {
                    System.out.println("✅ 链接格式验证通过");
                } else {
                    System.out.println("❌ 链接格式验证失败 - 不是有效的URL格式");
                }
            } else {
                System.out.println("❌ getLink 测试失败 - 返回结果为null");
            }

        } catch (Exception e) {
            log.error("getLink 测试异常", e);
            System.out.println("❌ getLink 测试异常: " + e.getMessage());
        }
    }

    /**
     * 测试 getLink 方法 - 不同套餐配置
     */
    @Test
    public void testGetLinkWithDifferentPackages() {
        try {
            String testMobile = "19030925187";
            String testOrderNo = "TEST_ORDER_" + UUID.randomUUID().toString().substring(0, 8);
            HaiNanDxProperties properties = new HaiNanDxProperties();

            // 使用反射获取私有方法
            Method getLinkMethod = HaiNanDxManager.class.getDeclaredMethod(
                "getLink", String.class, String.class, HaiNanDxProperties.ProdConfig.class);
            getLinkMethod.setAccessible(true);

            // 测试所有可用的套餐配置
            for (HNDxPayPkgEnum pkgEnum : HNDxPayPkgEnum.values()) {
                HaiNanDxProperties.ProdConfig prodConfig = properties.getProdMap().get(pkgEnum);
                if (prodConfig != null) {
                    try {
                        String result = (String) getLinkMethod.invoke(haiNanDxManager, testMobile, testOrderNo, prodConfig);
                        log.info("套餐 {} 测试结果: {}", pkgEnum.getName(), result);
                        System.out.println(String.format("套餐 [%s] - %s",
                            pkgEnum.getName(),
                            result != null ? "✅ 成功" : "❌ 失败"));
                    } catch (Exception e) {
                        log.error("套餐 {} 测试异常", pkgEnum.getName(), e);
                        System.out.println(String.format("套餐 [%s] - ❌ 异常: %s",
                            pkgEnum.getName(), e.getMessage()));
                    }
                }
            }

        } catch (Exception e) {
            log.error("testGetLinkWithDifferentPackages 测试异常", e);
            System.out.println("❌ 批量套餐测试异常: " + e.getMessage());
        }
    }

    /**
     * 测试 getLink 方法 - 边界条件测试
     */
    @Test
    public void testGetLinkBoundaryConditions() {
        try {
            HaiNanDxProperties properties = new HaiNanDxProperties();
            HaiNanDxProperties.ProdConfig validConfig = properties.getProdMap().get(HNDxPayPkgEnum.HN_PKG_29_YEAR);

            // 使用反射获取私有方法
            Method getLinkMethod = HaiNanDxManager.class.getDeclaredMethod(
                "getLink", String.class, String.class, HaiNanDxProperties.ProdConfig.class);
            getLinkMethod.setAccessible(true);

            System.out.println("=== getLink 边界条件测试 ===");

            // 测试1: 空手机号
            try {
                String result = (String) getLinkMethod.invoke(haiNanDxManager, "", "ORDER123", validConfig);
                System.out.println("空手机号测试: " + (result != null ? "✅ 通过" : "❌ 失败"));
            } catch (Exception e) {
                System.out.println("空手机号测试: ❌ 异常 - " + e.getCause().getMessage());
            }

            // 测试2: 空订单号
            try {
                String result = (String) getLinkMethod.invoke(haiNanDxManager, "19030925187", "", validConfig);
                System.out.println("空订单号测试: " + (result != null ? "✅ 通过" : "❌ 失败"));
            } catch (Exception e) {
                System.out.println("空订单号测试: ❌ 异常 - " + e.getCause().getMessage());
            }

            // 测试3: null配置
            try {
                String result = (String) getLinkMethod.invoke(haiNanDxManager, "19030925187", "ORDER123", null);
                System.out.println("null配置测试: " + (result != null ? "✅ 通过" : "❌ 失败"));
            } catch (Exception e) {
                System.out.println("null配置测试: ❌ 异常 - " + e.getCause().getMessage());
            }

            // 测试4: 无效手机号格式
            try {
                String result = (String) getLinkMethod.invoke(haiNanDxManager, "invalid_phone", "ORDER123", validConfig);
                System.out.println("无效手机号测试: " + (result != null ? "✅ 通过" : "❌ 失败"));
            } catch (Exception e) {
                System.out.println("无效手机号测试: ❌ 异常 - " + e.getCause().getMessage());
            }

        } catch (Exception e) {
            log.error("testGetLinkBoundaryConditions 测试异常", e);
            System.out.println("❌ 边界条件测试异常: " + e.getMessage());
        }
    }

}
