package com.yuelan.hermes.commons.util;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.taobao.api.response.TmallPurchaseCardBuyResponse;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.remote.TaoBaoTopManager;
import com.yuelan.hermes.quanyi.remote.request.TMallCardRechargeReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> 2024/5/6 下午7:40
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class AlibabaTest {
    @Autowired
    private TaoBaoTopManager taoBaoTopManager;

    @Test
    public void goOauthPage() {
        // https://tmdsk.hzyuelan.com/redeem-coupon
        System.out.println(taoBaoTopManager.getOauthUrl("https://tmdsk.hzyuelan.com/redeem-coupon"));
    }

    @Test
    public void code2AccessToken() {
        System.out.println(taoBaoTopManager.getAccessToken("ZVAaI48vQXKfrvYnvFex3GVm2938962"));
    }

    // @Test
    public void sendCardToUser() {
        // 发一分钟测试
        String orderNo = IdUtil.fastSimpleUUID();
        String openUid = "AAEFL6ALAOVVkA9sXqMrKCrG";
        long amount = 1;
        long parVal = 1;
        TMallCardRechargeReq req = new TMallCardRechargeReq(orderNo, openUid, amount, parVal);
        TmallPurchaseCardBuyResponse response = taoBaoTopManager.purchaseAndRecharge(req);
        System.out.println(JSONObject.toJSONString(response));
    }


}
