package com.yuelan.hermes.commons.test;

import cn.hutool.core.util.ReUtil;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.biz.service.EccZopPrettyPhonePoolService;
import com.yuelan.hermes.quanyi.common.enums.PrettyPhoneTagEnum;
import com.yuelan.hermes.quanyi.common.pojo.domain.EccZopPhonePoolDO;
import com.yuelan.hermes.quanyi.mapper.EccZopPhonePoolDOMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2024/8/14 下午2:15
 */
@Slf4j
@RunWith(SpringRunner.class)
// 指定测试环境
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class PrettyPhoneTest {

    @Autowired
    EccZopPhonePoolDOMapper zopPhonePoolDOMapper;
    @Autowired
    EccZopPrettyPhonePoolService eccZopPrettyPhonePoolService;

    @Test
    public void regTest() {
        List<EccZopPhonePoolDO> zopPhonePoolDOS = zopPhonePoolDOMapper.selectList(null);
        List<String> phones = zopPhonePoolDOS.stream().map(EccZopPhonePoolDO::getPhone).collect(Collectors.toList());
        phones.add("15306536600");
        int notMatchCount = 0;
        Map<PrettyPhoneTagEnum, List<String>> typeMap = new LinkedHashMap<>();
        for (String phone : phones) {
            List<PrettyPhoneTagEnum> matchTypes = new ArrayList<>();
            for (PrettyPhoneTagEnum value : PrettyPhoneTagEnum.values()) {
                String reg = value.getRegex();
                if (ReUtil.contains(reg, phone)) {
                    matchTypes.add(value);
                }
            }
            if (!matchTypes.isEmpty()) {
                StringBuilder sb = new StringBuilder(phone);
                for (PrettyPhoneTagEnum matchType : matchTypes) {
                    List<String> phs = typeMap.computeIfAbsent(matchType, k -> new ArrayList<>());
                    phs.add(phone);
                    sb.append("->").append(matchType).append(":").append(ReUtil.getGroup0(matchType.getRegex(), phone));
                }
                System.out.println(sb);
            } else {
                notMatchCount++;
                System.out.println(phone);
            }

        }
        for (PrettyPhoneTagEnum value : PrettyPhoneTagEnum.values()) {
            if (typeMap.get(value) == null) {
                continue;
            }
            System.out.println(value + ":" + typeMap.get(value).size());
        }
        System.out.println("notMatchCount:" + notMatchCount);

    }


    @Test
    public void refreshPrettyPhonePool() {
        eccZopPrettyPhonePoolService.refreshPrettyPhonePool();
    }
}
