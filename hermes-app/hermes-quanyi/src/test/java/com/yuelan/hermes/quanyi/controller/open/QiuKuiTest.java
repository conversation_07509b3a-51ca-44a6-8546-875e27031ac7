package com.yuelan.hermes.quanyi.controller.open;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import java.io.File;

/**
 * <AUTHOR> 2025/5/13
 * @since 2025/5/13
 */

public class QiuKuiTest {
    public static void main(String[] args) {

        String s = FileUtil.readString(new File("/Users/<USER>/Downloads/秋葵.txt"), "UTF-8");
        JSONArray jsonArray = JSONArray.parseArray(s);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject callbackContent = jsonArray.getJSONObject(i).getJSONObject("callback_content");
            System.out.println("发送"+ callbackContent);
            System.out.println(HttpUtil.post("https://cb.qukufun.com/weapp-pddk/qkyl/notify", callbackContent.toJSONString()));
        }
    }
}
