package com.yuelan.hermes.commons.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.yuelan.hermes.quanyi.controller.request.RechargerOrderDetailReq;
import com.yuelan.hermes.quanyi.controller.request.RechargerOrderUnifiedReq;
import com.yuelan.hermes.quanyi.controller.request.SignReq;

import java.util.Map;

public class YlSignUtilTest {

    private static String mchId = "ojb4gmzhaq";
    private static String secretKey = "HeLXnybOji83vSqE";

    //    @Test
    public void test() {
        RechargerOrderUnifiedReq signReq = new RechargerOrderUnifiedReq();
        signReq.setMobile("13782353099");
        signReq.setOutTradeNo(IdUtil.getSnowflakeNextIdStr());
        signReq.setSkuNo("20230219话费100");
        signReq.setNotifyUrl("https://quanyi-api-ntest.meta-xuantan.com/test/notity");
        signReq.setMchId(mchId);
        signReq.setTimestamp(System.currentTimeMillis());

        Map<String, Object> map = BeanUtil.beanToMap(signReq);

        String sign = YlSignUtil.signMD5(secretKey, map, Lists.newArrayList("sign"));
        signReq.setSign(sign);
        System.out.println(JSON.toJSONString(signReq));

    }

    //    @Test
    public void test1() {
        RechargerOrderDetailReq signReq = new RechargerOrderDetailReq();
        signReq.setOrderNo("OM23051659445019610783744");
        signReq.setMchId(mchId);
        signReq.setTimestamp(System.currentTimeMillis());

        Map<String, Object> map = BeanUtil.beanToMap(signReq);

        String sign = YlSignUtil.signMD5(secretKey, map, Lists.newArrayList("sign"));
        signReq.setSign(sign);
        System.out.println(JSON.toJSONString(signReq));

    }

    //    @Test
    public void test2() {
        SignReq signReq = new SignReq();
        signReq.setMchId(mchId);
        signReq.setTimestamp(System.currentTimeMillis());

        Map<String, Object> map = BeanUtil.beanToMap(signReq);

        String sign = YlSignUtil.signMD5(secretKey, map, Lists.newArrayList("sign"));
        signReq.setSign(sign);
        System.out.println(JSON.toJSONString(signReq));

    }
}