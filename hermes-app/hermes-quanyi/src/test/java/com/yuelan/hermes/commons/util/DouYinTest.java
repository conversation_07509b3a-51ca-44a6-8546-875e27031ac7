package com.yuelan.hermes.commons.util;
import com.alibaba.fastjson2.JSONObject;

import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.biz.manager.AdManager;
import com.yuelan.hermes.quanyi.controller.request.PageEventUploadReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0.0
 * @date 2024/8/8
 * @description:
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class DouYinTest {

    @Resource
    private AdManager adManager;
    @Test
    public void adCallBack(){
        PageEventUploadReq req = new PageEventUploadReq();
        req.setAdChannelCode("dy");
        req.setEventCode(20);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("dy_callback", "__LINK__");
        req.setAdExt(jsonObject);
        adManager.adEventUploadByBenefit(req);

    }
}
