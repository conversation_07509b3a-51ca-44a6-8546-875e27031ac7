# 渠道分销系统改进需求规格

## 概述

本需求旨在升级现有渠道分销系统，引入渠道层级管理、扣量计算机制和精细化权限控制。核心功能包括：建立一级/二级渠道分级体系、实现层级扣量计算、支持渠道独立登录及数据权限控制。

## 功能需求

### 1. 渠道层级管理

#### 1.1 渠道分级设置
**用户故事**：作为平台管理员，我希望能为外部渠道建立分级体系，以便建立层级佣金分配关系。

**验收标准**：
- [EARS] 当外部渠道新注册时，系统必须自动分配为一级渠道
- [EARS] 当一级渠道创建下级时，系统必须将新增渠道设为二级渠道
- [EARS] 当渠道创建完成后，渠道的层级等级将不可修改
- [EARS] 当系统存在渠道层级关系时，二级渠道必须关联到具体的上级一级渠道
- [EARS] 当一级渠道切换二级渠道的上级时，系统的历史订单归属必须保持不变

#### 1.2 扣量比例配置
**用户故事**：作为平台管理员，我希望能为不同层级的渠道设置扣量比例，以便建立佣金分配机制。

**验收标准**：
- [EARS] 当平台配置一级渠道时，系统必须支持设置0%-95%的扣量比例，以5%为步进
- [EARS] 当一级渠道配置下属二级渠道时，系统必须支持设置0%-95%的扣量比例，以5%为步进
- [EARS] 当扣量比例未设置时，系统必须默认为0%不扣量
- [EARS] 当扣量比例设置完成后，设置变更必须立即生效，影响下一笔订单的计算

### 2. 渠道登录与认证

#### 2.1 渠道登录体系
**用户故事**：作为外部渠道，我希望能独立登录系统，以便管理我的下游渠道和查看订单。

**验收标准**：
- [EARS] 当外部渠道账号创建完成后，系统必须使用手机号作为登录账号
- [EARS] 当渠道首次登录时，系统必须验证初始密码和短信验证码
- [EARS] 当渠道登录成功后，系统必须根据渠道登录角色（平台/一级/二级）控制可访问功能
- [EARS] 当渠道账号状态为停用时，系统必须允许正常登录但忽略其扣量设置

#### 2.2 权限控制机制
**用户故事**：作为不同层级的渠道，我希望能看到符合权限的订单和数据，以便保持业务操作的隔离性。

**验收标准**：
- [EARS] 当非最底层渠道登录时，系统必须显示"扣量比例"配置列，并允许配置下级扣量
- [EARS] 当最底层渠道登录时，系统必须完全隐藏"扣量"相关字段和列名
- [EARS] 当渠道登录系统时，系统必须确保该渠道无法感知任何上游层级对其的扣量设置

### 3. 订单管理与可见性

#### 3.1 订单归属规则
**用户故事**：作为外部渠道，我希望能查看符合权限的订单，以便管理我的业务发展。

**验收标准**：
- [EARS] 当订单创建时，系统必须根据发展渠道和扣量计算结果，确定该订单对各级渠道的可见性
- [EARS] 当订单的"发展渠道"属于当前渠道或下级时，系统必须将该订单纳入可见范围
- [EARS] 当订单的"扣量渠道"为空或为当前渠道或下级时，系统必须将该订单纳入可见范围
- [EARS] 当渠道切换上级时，系统的历史订单归属必须保持原设置不变

#### 3.2 扣量渠道计算
**用户故事**：作为系统，我希望能根据扣量规则自动计算订单的扣量归属，以便正确隐藏订单。

**验收标准**：
- [EARS] 当订单创建时，系统必须从平台开始，逐级向下计算扣量比例
- [EARS] 当计算过程中首次扣量成功时，系统必须将该扣量上级设为"扣量渠道"
- [EARS] 当所有层级均未扣量时，系统必须将"扣量渠道"字段设为空
- [EARS] 当渠道处于停用状态时，系统必须忽略该渠道的扣量设置

## 数据模型需求

### 4.1 渠道表扩展
- [EARS] 当EccOuterChannelDO存储渠道信息时，系统必须新增parent_channel_id字段存储上级渠道关系
- [EARS] 当EccOuterChannelDO记录扣量比例时，系统必须支持deduction_rate字段存储0-95的整数扣量比例百分比
- [EARS] 当EccOuterChannelDO关联到admin表用户时，系统必须建立一对一的关联关系

### 4.2 订单表扩展
- [EARS] 当EccNcOrderDO存储订单信息时，系统必须新增deduction_channel_id字段存储计算后的扣量渠道ID
- [EARS] 当订单创建时，系统必须保持原有的development_channel_id字段不变
- [EARS] 当历史订单迁移时，系统必须将所有历史订单的deduction_channel_id字段设为空

## 非功能需求

### 5.1 权限隔离要求
- [EARS] 在数据查询时，系统必须确保渠道无法看到任何不属于其权限范围的数据
- [EARS] 在系统展示层面，系统必须对低层级渠道隐藏高层的扣量设置信息
- [EARS] 在操作权限上，渠道只能对其直属下级进行操作，无法操作更深层级的下级

### 5.2 性能与可靠性
- [EARS] 当渠道切换上级时，系统必须确保不影响历史订单的查询性能
- [EARS] 当大量订单创建时，扣量计算过程必须在订单创建的事务内完成
- [EARS] 当渠道登录时，权限验证过程必须实时生效，无需重新登录