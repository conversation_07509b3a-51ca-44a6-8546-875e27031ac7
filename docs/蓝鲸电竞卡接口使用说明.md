# 蓝鲸电竞卡接口使用说明

## 概述

蓝鲸电竞卡接口包括查看用户信息和发放道具两个功能。

## 接口列表

### 1. 查看用户信息

- **接口地址**: `https://mgr.wulinfengyun.com/notify_dianjing_get`
- **请求方式**: POST

### 2. 发放道具

- **接口地址**: `https://mgr.wulinfengyun.com/notify_dianjing_send`
- **请求方式**: POST

## 代码使用示例

### 1. 注入服务

```java
@Autowired
private LanJinManager lanJinManager;
```

### 2. 查看用户信息

```java
// 自动生成时间戳和签名
LanJinUserInfoResp response = lanJinManager.getUserInfo("AFDNU4-1", "ios");
```

### 3. 发放道具

```java
// 自动生成时间戳和签名
LanJinSendItemResp response = lanJinManager.sendItem("AFDNU4-1", "dian_jing_ka_15", "ios");
```

## 配置说明

```yaml
# 蓝鲸电竞卡
lanjin:
  host: https://mgr.wulinfengyun.com
  user-info-path: /notify_dianjing_get
  send-item-path: /notify_dianjing_send
  sign-key: dailaeizndseixnmdexessfd
  timeout: 10000
```

## 卡片类型

- `dian_jing_ka_12` - 12块礼包
- `dian_jing_ka_15` - 15块礼包

## 响应码

- `0` - 成功
- `1` - 失败
