# 选号

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /cbn/api/v/numberSelect:
    post:
      summary: 选号
      deprecated: false
      description: ''
      tags:
        - 中国广电端/中国广电
      parameters:
        - name: token
          in: header
          description: ''
          required: true
          example: '6770362678105073'
          schema:
            type: string
        - name: Content-Type
          in: header
          description: ''
          required: true
          example: application/json;charset=UTF-8
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BroadnetSelectNumberVo'
              description: ''
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/CodeResult%C2%ABList%C2%ABBroadnetSelectDto%C2%BB%C2%BB
              example:
                code: 0
                data:
                  - checkCode: ''
                    number: ''
                message: ''
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 中国广电端/中国广电
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/1215718/apis/api-78526554-run
components:
  schemas:
    BroadnetSelectNumberVo:
      type: object
      properties:
        cityCode:
          type: string
          description: 号码所属市编码
          minLength: 3
          maxLength: 7
          x-apifox-mock: '@string(3,7)'
          nullable: true
        provinceCode:
          type: string
          description: 号码所属省编码
          minLength: 4
          maxLength: 4
          x-apifox-mock: '@string(4,4)'
          nullable: true
        numberKeyword:
          type: string
          description: 号码中间4位关键字
          maxLength: 4
          x-apifox-mock: '@string(0,4)'
          nullable: true
        linkNum:
          type: string
          description: 推广编码
          minLength: 8
          maxLength: 8
          x-apifox-mock: '@string(8,8)'
          nullable: true
      x-apifox-orders:
        - cityCode
        - provinceCode
        - numberKeyword
        - linkNum
      required:
        - linkNum
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    CodeResult«List«BroadnetSelectDto»»:
      type: object
      properties:
        code:
          type: integer
          description: ''
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/BroadnetSelectDto'
            description: ''
          description: ''
          nullable: true
        message:
          type: string
          description: ''
          nullable: true
      x-apifox-orders:
        - code
        - data
        - message
      required:
        - code
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    BroadnetSelectDto:
      type: object
      properties:
        checkCode:
          type: string
          description: 广电号码唯一编码
          nullable: true
        number:
          type: string
          description: 号码
          nullable: true
      x-apifox-orders:
        - checkCode
        - number
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
  securitySchemes: {}
servers:
  - url: https://i.refectman.com/ocs_manage
    description: 正式环境
security: []

```