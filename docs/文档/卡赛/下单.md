# 下单

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /cbn/api/v/tradeOrder:
    post:
      summary: 下单
      deprecated: false
      description: ''
      tags:
        - 中国广电端/中国广电
      parameters:
        - name: token
          in: header
          description: ''
          required: true
          example: '6770362678105073'
          schema:
            type: string
        - name: Content-Type
          in: header
          description: ''
          required: true
          example: application/json;charset=UTF-8
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BroadnetTradeOrderVo'
              description: ''
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: >-
                      编码 200成功 非200表示接口失败(当code为119
                      时，需要以异步回调的状态为准，一般异步回调在20秒内会返回)
                    nullable: true
                  data:
                    $ref: '#/components/schemas/BroadnetOrderResp'
                    description: ''
                  message:
                    type: string
                    description: ''
                    nullable: true
                x-apifox-refs: {}
                x-apifox-orders:
                  - code
                  - data
                  - message
                required:
                  - code
                x-apifox-ignore-properties: []
              example:
                code: 0
                data:
                  realNameAuth: false
                  commonOrderId: ''
                  orderMobile: ''
                message: ''
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 中国广电端/中国广电
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/1215718/apis/api-78526552-run
components:
  schemas:
    BroadnetTradeOrderVo:
      type: object
      properties:
        orderMobile:
          type: string
          description: 所选号码
          nullable: true
        certificateName:
          type: string
          description: 用户名称
          nullable: true
        certificateNumber:
          type: string
          description: 身份证号
          minLength: 15
          maxLength: 18
          x-apifox-mock: '@string(15,18)'
          nullable: true
        mobilePhone:
          type: string
          description: 手机号
          nullable: true
        sendDistrictCode:
          type: string
          description: 配送区编码
          nullable: true
        goodsCityCode:
          type: string
          description: 号码所属市编码
          nullable: true
        address:
          type: string
          description: 配送详细地址
          nullable: true
        numberCheckCode:
          type: string
          description: 号码验证码
          nullable: true
        channelSeqId:
          type: string
          description: 渠道订单号
          nullable: true
        sms:
          type: string
          description: 短信验证码
          nullable: true
        sellerId:
          type: string
          description: 渠道销售号
          nullable: true
        linkNum:
          type: string
          description: 推广编码
          nullable: true
        toutiaoRecord:
          $ref: '#/components/schemas/ToutiaoRecord'
          description: api回传参数 特定渠道专用
        sourceUrl:
          type: string
          description: 渠道落地页（用户下单页）url，长度400字符以内
          nullable: true
      x-apifox-orders:
        - orderMobile
        - certificateName
        - certificateNumber
        - mobilePhone
        - sendDistrictCode
        - goodsCityCode
        - address
        - numberCheckCode
        - channelSeqId
        - sms
        - sellerId
        - linkNum
        - toutiaoRecord
        - sourceUrl
      required:
        - certificateName
        - certificateNumber
        - mobilePhone
        - sendDistrictCode
        - goodsCityCode
        - address
        - linkNum
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    ToutiaoRecord:
      type: object
      properties:
        type:
          type: integer
          description: ''
        url:
          type: string
          description: ''
        linkNum:
          type: string
          description: ''
        apiType:
          type: integer
          description: ''
        cardOrderId:
          type: integer
          description: 号卡订单id
          format: int64
        rate:
          type: number
          description: 回传比例
      x-apifox-orders:
        - type
        - url
        - linkNum
        - apiType
        - cardOrderId
        - rate
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    BroadnetOrderResp:
      type: object
      properties:
        realNameAuth:
          type: boolean
          description: true 需要实名认证 认证不成功  false 不需要实名认证 认证成功
          nullable: true
        commonOrderId:
          type: string
          description: 当值唯为1时表示收单场景 最终认证结果以消息通知为准
          nullable: true
        orderMobile:
          type: string
          description: 订购号码
          nullable: true
      x-apifox-orders:
        - realNameAuth
        - commonOrderId
        - orderMobile
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
  securitySchemes: {}
servers:
  - url: https://i.refectman.com/ocs_manage
    description: 正式环境
security: []

```