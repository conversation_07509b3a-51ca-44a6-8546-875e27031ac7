# 同步消息.URL链接自定义

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /cbnAssist/syncMessage:
    post:
      summary: 同步消息.URL链接自定义
      deprecated: false
      description: ''
      tags:
        - 中国广电端/辅助管理
      parameters:
        - name: token
          in: header
          description: ''
          required: true
          example: '6770362678105073'
          schema:
            type: string
        - name: Content-Type
          in: header
          description: ''
          required: true
          example: application/json;charset=UTF-8
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CmccMessagePushVo'
              description: ''
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodeResult%3F'
              example:
                code: 0
                data: null
                message: ''
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 中国广电端/辅助管理
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/1215718/apis/api-188610635-run
components:
  schemas:
    CmccMessagePushVo:
      type: object
      properties:
        orderId:
          type: string
          description: 移动订单号
          nullable: true
        orderNum:
          type: string
          description: 渠道订单号
          nullable: true
        mobile:
          type: string
          description: 订购号码
          nullable: true
        status:
          type: integer
          description: >-
            100,200,201,202,203,300,301,302,303,304,1000,1005,1010,1015,1020,1025,1030,1035,1040,1055,1065
          nullable: true
        statusDesc:
          type: string
          description: >-
            100：初始状态,200：待支付,201：支付中,202：支付成功,203：支付失败,300：待订购,301：订购中,302：订购成功,303：订购失败,304：订购待重试,1000：待商户确认,1005：预处理成功,1010：预处理失败,1015：备货中,1020：已发货,1025：订购完成,1030：订购失败,1035：已激活,1040：激活失败,1055：t月充值,1065：oao充值，其中303、1030、1035是订购终态
          nullable: true
        updateTime:
          type: string
          description: 修改时间 时间戳(毫秒级)
          x-apifox-mock: '@datetime'
          nullable: true
        activateTime:
          type: string
          description: 激活时间 时间戳(毫秒级)
          x-apifox-mock: '@datetime'
          nullable: true
        monthRecharge:
          type: integer
          description: T月充值
          nullable: true
        nextMonthRecharge:
          type: integer
          description: T+1月充值
          nullable: true
        oaoRechargeStatus:
          type: integer
          description: 是否是oao充值 0否1是
          nullable: true
        shipmentCompanyName:
          type: string
          description: 物流公司
          nullable: true
        shipmentNumber:
          type: string
          description: 物流订单号
          nullable: true
        oaoRechargeGear:
          type: integer
          description: oao充值档位 0 无 1  50元挡 2 100元及以上
          nullable: true
        productId:
          type: string
          description: 第三方透传字段
          nullable: true
        empId:
          type: string
          description: 第三方透传字段
          nullable: true
        flag:
          type: string
          description: 第三方透传字段
          nullable: true
        custMobile:
          type: string
          description: 用户手机号 第三方透传字段
          nullable: true
        custName:
          type: string
          description: 用户名称 第三方透传字段
          nullable: true
      x-apifox-orders:
        - orderId
        - orderNum
        - mobile
        - status
        - statusDesc
        - updateTime
        - activateTime
        - monthRecharge
        - nextMonthRecharge
        - oaoRechargeStatus
        - shipmentCompanyName
        - shipmentNumber
        - oaoRechargeGear
        - productId
        - empId
        - flag
        - custMobile
        - custName
      required:
        - status
        - statusDesc
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    CodeResult?:
      type: object
      properties:
        code:
          type: integer
          description: 编码 200成功 非200表示接口失败
        data:
          description: 数据
          type: 'null'
        message:
          type: string
          description: 消息
      x-apifox-orders:
        - code
        - data
        - message
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
  securitySchemes: {}
servers:
  - url: https://i.refectman.com/ocs_manage
    description: 正式环境
security: []

```