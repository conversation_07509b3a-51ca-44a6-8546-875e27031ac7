# 配送所属市

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /cbn/api/v/findExpressCity:
    get:
      summary: 配送所属市
      deprecated: false
      description: ''
      tags:
        - 中国广电端/中国广电
      parameters:
        - name: provinceCode
          in: query
          description: 市编码
          required: true
          schema:
            type: string
        - name: token
          in: header
          description: ''
          required: true
          example: '6770362678105073'
          schema:
            type: string
        - name: Content-Type
          in: header
          description: ''
          required: true
          example: application/json;charset=UTF-8
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: >-
                  #/components/schemas/CodeResult%C2%ABList%C2%ABBroadnetExpressArea%C2%BB%C2%BB
              example:
                code: 0
                data:
                  - id: 0
                    name: ''
                    code: ''
                    parentCode: ''
                    level: 0
                message: ''
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 中国广电端/中国广电
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/1215718/apis/api-78526550-run
components:
  schemas:
    CodeResult«List«BroadnetExpressArea»»:
      type: object
      properties:
        code:
          type: integer
          description: ''
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/BroadnetExpressArea'
            description: ''
          description: ''
          nullable: true
        message:
          type: string
          description: ''
          nullable: true
      x-apifox-orders:
        - code
        - data
        - message
      required:
        - code
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    BroadnetExpressArea:
      type: object
      properties:
        id:
          type: integer
          description: ''
          nullable: true
        name:
          type: string
          description: ''
          nullable: true
        code:
          type: string
          description: ''
          nullable: true
        parentCode:
          type: string
          description: ''
          nullable: true
        level:
          type: integer
          description: 1省 2市 3区
          nullable: true
      x-apifox-orders:
        - id
        - name
        - code
        - parentCode
        - level
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
  securitySchemes: {}
servers:
  - url: https://i.refectman.com/ocs_manage
    description: 正式环境
security: []

```