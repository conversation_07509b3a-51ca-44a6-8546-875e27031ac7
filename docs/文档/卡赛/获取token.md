# 获取token

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /cbn/api/v/loadToken:
    get:
      summary: 获取token
      deprecated: false
      description: |-
        获取token
        token存放于response header
      tags:
        - 中国广电端/中国广电
      parameters:
        - name: linkNum
          in: query
          description: 链接编码
          required: true
          schema:
            type: string
        - name: token
          in: header
          description: ''
          required: true
          example: '6770362678105073'
          schema:
            type: string
        - name: Content-Type
          in: header
          description: ''
          required: true
          example: application/json;charset=UTF-8
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CodeResult%3F'
              example:
                code: 0
                data: null
                message: ''
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 中国广电端/中国广电
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/1215718/apis/api-78526549-run
components:
  schemas:
    CodeResult?:
      type: object
      properties:
        code:
          type: integer
          description: 编码 200成功 非200表示接口失败
        data:
          description: 数据
          type: 'null'
        message:
          type: string
          description: 消息
      x-apifox-orders:
        - code
        - data
        - message
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
  securitySchemes: {}
servers:
  - url: https://i.refectman.com/ocs_manage
    description: 正式环境
security: []

```