2.1.1业务办理初始化
http://10.17.8.3:80/3th_freerdjx/doPost_initOrder?accessToken=948e141fc10e30436c264633953f4862
http://10.17.8.3:80/3th_freerdjx/doPost_recordBrowseBehavior?accessToken=948e141fc10e30436c264633953f4862
http://10.17.8.3:80/3th_freerdjx/doPost_createCaptcha?accessToken=948e141fc10e30436c264633953f4862
http://10.17.8.3:80/3th_freerdjx/doPost_verifyCaptcha?accessToken=948e141fc10e30436c264633953f4862
http://10.17.8.3:80/3th_freerdjx/doPost_submitOrder?accessToken=948e141fc10e30436c264633953f4862
http://10.17.8.3:80/3th_freerdjx/doPost_productOrderDetail?accessToken=948e141fc10e30436c264633953f4862
http://10.17.8.3:80/3th_freerdjx/doPost_productRecommend?accessToken=948e141fc10e30436c264633953f4862
请求：
{
    "action": "3th_freerdjx",
    "method": "doPost_initOrder",
    "req": {
        "msg": {
            "BUSI_INIT_REQ": {
                "CHANNEL_CODE": "75b0559",
                "SERIAL_NUMBER": "***********"
            }
        }
    }
}
返回：
{"BUSI_INIT_RSP":{"RESP_DESC":"成功","RESP_CODE":"0000","INIT_KEY":"07c840e628d74703","FLOW_ID":"4"}}

2.1.3客户浏览行为埋点接口
https://x.jx116114.com/test/3th_freerdjx/doPost_recordBrowseBehavior
请求：
{
    "action": "3th_freerdjx",
    "method": "doPost_recordBrowseBehavior",
    "req": {
        "msg": {
            "SCAN_RECORD_REQ": {
                "FLOW_ID": "3",
                "INIT_KEY": "8d2f4d3d882a4a77",
                "CHANNEL_CODE": "75x222",
                "SERIAL_NUMBER": "18507003926",
                "COMM_ID": "1001111",
                "COMM_NAME": "沃派39员",
                "PAGE_SHOT": "1234",
                "START_TIME": "2025-04-02 19:00:00",
                "END_TIME": "2025-04-02 19:43:00"
            }
        }
    }
}
返回：
{"SCAN_RECORD_RSP":{"RESP_DESC":"成功","RESP_CODE":"0000","SCAN_KEY":"c2980b230b3f40fb","FLOW_ID":"3"}}

2.1.4下发验证码接口
https://x.jx116114.com/test/3th_freerdjx/doPost_createCaptcha
请求：
{
    "action": "3th_freerdjx",
    "method": "doPost_createCaptcha",
    "req": {
        "msg": {
            "CREATE_CAPTCHA_REQ": {
                "TRADE_ID": "11111110000002",
                "CHANNEL_CODE": "75x222",
                "FLOW_ID": "3",
                "SCAN_KEY": "c2980b230b3f40fb",
                "SERIAL_NUMBER": "***********"
            }
        }
    }
}
返回：
{"CREATE_CAPTCHA_RSP":{"RESP_DESC":"下发验证码成功","GEN_KEY":"fbf1bab253854d81","RESP_CODE":"0000","IDENTIFY_CODE":{"TIMESTAMP":"1744098033199","KEY":"babbfa59caf0433a8b897d9fbce675f6"},"FLOW_ID":"3","TRADE_ID":"11111110000002"}}

2.1.5验证验证码接口
https://x.jx116114.com/test/3th_freerdjx/doPost_verifyCaptcha
请求：
{
    "action": "3th_freerdjx",
    "method": "doPost_verifyCaptcha",
    "req": {
        "msg": {
            "VERIFY_CAPTCHA_REQ": {
                "TRADE_ID": "11111110000002",
                "CHANNEL_CODE": "75x222",
                "FLOW_ID": "3",
                "GEN_KEY": "fbf1bab253854d81",
                "SERIAL_NUMBER": "***********",
                "KEY": "babbfa59caf0433a8b897d9fbce675f6",
                "CODE": "430372",
                "TIMESTAMP": "20250402155315"
            }
        }
    }
}
返回：
{"VERIFY_CAPTCHA_RSP":{"RESP_DESC":"操作成功","SERIAL_NUMBER":"***********","RESP_CODE":"0000","VER_KEY":"379995cd36f64844","FLOW_ID":"3","TRADE_ID":"11111110000002"}}

2.1.16 业务办理下单接口
https://x.jx116114.com/test/3th_freerdjx/doPost_submitOrder
请求：
{
    "action": "3th_freerdjx",
    "method": "doPost_submitOrder",
    "req": {
        "msg": {
            "PRODUCT_ORDER_REQ": {
                "SERIAL_NUMBER": "***********",
                "BUSINESS_ID": "20250331082745313160",
                "PRICE_SUM": 0,
                "PAY_TAG": "9",
                "IS_PRINT_PAPER_LESS": "0",
                "ORDER_TYPE": "2113",
                "COMM_OBJECT": [
                    {
                        "STRATEGY_ID": "1347299-5e9d7-000",
                        "MODIFY_TAG": "0",
                        "COMM_ID": "752209274931"
                    }
                ],
                "ORDER_TIME": "2025-03-31 08:29:55",
                "TRADE_ID": "11111110000002",
                "CHANNEL_CODE": "75x222",
                "PROVINCE_CODE": "75",
                "FLOW_ID": "3",
                "VER_KEY": "14f1b7e936e94450"
            }
        }
    }
}
返回：
{"PRODUCT_ORDER_RSP":{"RESP_DESC":"根据入参businessId未查询到产品推荐记录或订购商品未在该推荐记录中！","LONG_ORDER_FLAG":"0","RESP_CODE":"2005","FLOW_ID":"3","TRADE_ID":"11111110000002","RESP_SUB_CODE":"NOT_RECOMMEND_CHECK","SHORT_ORDER_FLAG":"0"}}

1.1.7.客户运营_订单详情查询接口
https://x.jx116114.com/test/3th_freerdjx/doPost_productOrderDetail
请求：
{
    "action": "3th_freerdjx",
    "method": "doPost_productOrderDetail",
    "req": {
        "msg": {
            "PRODUCT_ORDER_DETAIL_REQ": {
                "ORDER_ID": "2503300218819417",
                "TRADE_ID": "11111110000002",
                "CHANNEL_CODE": "75x222",
                "FLOW_ID": "3"
            }
        }
    }
}
返回：
{"PRODUCT_ORDER_DETAIL_RSP":{"RESP_DESC":"该订单不存在","MAIN_ORDER_STATE":"03","RESP_CODE":"2222","ORDER_ID":"2503300218819417","FLOW_ID":"3"}}