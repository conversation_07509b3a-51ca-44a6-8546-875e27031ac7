天翼爱音乐开放平台

视频彩铃-铃音盒业务分册

V1.0 2025/4/29

| **版本** | **时间**    | **变更说明** | **作者** |
|--------|-----------|----------|--------|
| V1.0   | 2025/4/29 | 定稿       | 余锦锋    |

**目录**

1. [业务描述 3](#_TOC_250016)
2. [铃音合服务权益 3](#_TOC_250015)
3. [业务流程参考 4](#_TOC_250014)
    1. [产品订购接入流程 4](#_TOC_250013)
    2. [H5 计费接入流程 4](#_TOC_250012)
4. [接口详情 5](#_TOC_250011)
    1. [H5 计费下单发起接口（一键开户）-H5portal 5](#_TOC_250010)
    2. [H5 计费订单详情查询 7](#_TOC_250009)
    3. [订购退订消息回调 9](#_TOC_250008)
    4. [包月产品订购关系查询 12](#_TOC_250007)
    5. [包月产品退订 14](#_TOC_250006)
    6. [视频音乐盒信息查询 15](#_TOC_250005)
    7. [视频彩铃信息查询 17](#_TOC_250004)
5. [附录 20](#_TOC_250003)
    1. [平台接口授权机制说明 20](#_TOC_250002)
    2. [JSSDK 接入使用指引 23](#_TOC_250001)
    3. [SDK 使用 24](#_TOC_250000)

# 业务描述

视频彩铃，是中国电信推出的一项由被叫用户定制，在呼叫通话接续等待过程中为主叫用户展示一段短视频的回铃音业务。视频彩铃用户可自行选择设定个性化的短视频，当其作为被叫时，符合条件的主叫用户将看到这段短视频。

主叫用户观看到视频彩铃需具备以下条件：

1、被叫用户开通了视频彩铃功能，并且设置了自己的视频彩铃

2、主叫用户开通了 Volte 高清通话功能

3、主叫用户终端开启了高清通话功能，并且终端型号支持播放视频彩铃

# 铃音合服务权益

1. 每月铃音盒自动更新不少于 10 首视频彩铃，铃音盒内铃音随机播放。
2. 视频彩铃内容包括风景/经典/流行音乐等热门音乐短视频。
3. 用户每月可更换一次其他主题的风景铃音盒。
4. 服务入口: 爱音乐 APP 和中国电信视频彩铃公众号

# 业务流程参考

### 产品订购接入流程

*
*![](data:image/png;base64,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)
**

### H5 计费接入流程

说明：计费联调前，需进行计费信息配置，请与渠道经理确认后再发起调测。 1）请求方式采用 POST （application/x-www-form-urlencoded）
2）计费请求：严格按合作投放报备域名请求，若投放请求有变动，请联系渠道经理更新。注意：为了校验发起支付的域名是否合法，在请求页面的
HTTP 请求里头部(HTTP Header)需要有“Referer”这个字段。Referer 字段会告诉后端请求的原始资源的 URI，否则一律拦截。

![](data:image/jpeg;base64,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)

爱音乐

合作伙伴

# 接口详情

### H5 计费下单发起接口（一键开户）-H5portal

统一通过 JSSDK 对接，见 5.2JSSDK 使用指引

### 接口功能描述

调用该接口生成订单。并返回计费认证 H5 页面地址。用户跳到计费认证页面完成计费。一键开户+订购+铃音设置

### 接口地址

<http://api.118100.cn/openapi/services/v3/packageservice/confirm_open_order>

\_launched_ex.json

### 请求方式

POST （application/x-www-form-urlencoded）

-
    -
        1. **前置流程**
        2. **请求参数说明**

| 参数名               | 必选    | 类型以范<br><br>围 | 备注                         |
|-------------------|-------|---------------|----------------------------|
| mobile            | false | String        | 用户号码<br><br>可在计费确认页面直接获取   |
| product_id        | true  | String        | 7360110000100933           |
| return_url        | true  | String        | 计费认证页面操作后的返回地址             |
| verify_type       | true  | Integer       | 校验类型<br><br>1：短信验证码        |
| column            | false | String        | 节点参数                       |
| remark            | false | String        | 备注                         |
| is_message        | false | Integer       | 是否下发订购通知短信，1：是             |
| ring_id           | false | String        | 铃音编码，不参与鉴权，非合作要求不<br><br>传 |
| external_trade_id | false | String        | 外部流水号、此参数不参与鉴权             |

| deviceno | false | String | 第三方 openid、此参数不参与鉴权 |
|----------|-------|--------|---------------------|

-
    -
        1. **返回参数说明**

| 参数名         | 必选   | 类型及范围  | 备注                                                                                                                                                                           |
|-------------|------|--------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| res_code    | true | String | 执行结果代码<br><br>0 成功                                                                                                                                                           |
| res_message | true | String | 执行结果描述                                                                                                                                                                       |
| fee_url     | true | String | 计费认证页面地址，获取到计费 URL后，需要从报备地址跳转过来，即在请求页 面 的 HTTP 请 求 里 头 部 (HTTP Header)需要有“Referer”这个字段。 Referer 字段会告诉后端请求的原始资源的 URI，否则一律拦截。<br><br>严格按合作投放报备域名请求，若投放请<br><br>求有变动，请联系渠道经理更新。 |

-
    -
        1. **接口内部名称**

IPackageService_confirm_open_order_launched_ex

### H5 计费订单详情查询

-
    -
        1. **接口功能描述**

通过 H5 计费订单号查询订单详情

### 接口地址

<http://api.118100.cn/openapi/services/v3/packageservice/query_h5_order.json>

### 请求方式

GET

### 前置流程

通过 ismp 订购关系回调通知获得 H5 计费订单号

-
    -
        1. **请求参数说明**

| 参数名      | 必选   | 类型以范围  | 备注              |
|----------|------|--------|-----------------|
| order_no | true | String | H5 计费订单号        |
| mdn      | true | String | 用户手机号码，必须与订单号匹配 |

-
    -
        1. **返回参数说明**

| 参数名          | 必选   | 类型及范围   | 备注                                                               |
|--------------|------|---------|------------------------------------------------------------------|
| code         | true | String  | 执行结果代码<br><br>0 成功<br><br>96 订单不存在<br><br>97 参数不能为空<br><br>99 失败 |
| message      | true | String  | 执行结果描述                                                           |
| order_no     | true | String  | 订单号                                                              |
| mobile       | true | String  | 手机号码                                                             |
| verify_type  | true | Integer | 校验类型（0：免密登录 1：短信<br><br>验证码）                                     |
| product_id   | true | String  | 产品 ID                                                            |
| product_name | true | String  | 产品名称                                                             |
| order_time   | true | String  | 下单时间                                                             |
| order_status | true | Integer | 订单状态（1-新建；2-确认成功；<br><br>3-确认失败；4-开户在途；5-开户                      |

|                |      |         | 失败；6-延时体验状态；7-补开户<br><br>中） |
|----------------|------|---------|-----------------------------|
| price          | true | String  | 产品价格（单位为分）                  |
| return_url     | true | String  | 合作方返回地址                     |
| is_openaccount | true | Integer | 是否开户（0-否（默认），1-是）           |

-
    -
        1. **接口内部名称**

IPackageService_queryH5OrderInfo

### 订购退订消息回调

-
    -
        1. **接口功能描述**

通过此接口，将订购退订消息回调给合作伙伴。

合作伙伴平台在收到消息后，返回包含 code 字段的 JSON 应答 0000 代表成功。

当合作伙伴收到爱音乐平台发回来的上传结果信息后，获取上传结果和签名的信息，然后对数字签名进行校验，步骤如下：

1. 合作伙伴将收到请求头部中的 timestamp 字段的取值以及消息体中的 mobile， productid，state 字段的取值采取字符串拼接的方式组成签名原文，按照算法：
   Base64(HmacSHA256(签名原文, keyword))计算签名。

注:签名原文字符串各字段的顺序不能改变；keyword 由爱音乐平台提供；签名算法 JAVA 示例后续章节随附；消息体的格式为 form-data。

1. 合作伙伴将计算获得的签名与请求头部中 signature-hmacsha256 字段的值进行比较，如完全一致，则说明信息没有被篡改。

| 鉴权 header 字段<br><br>名 | 必选   | 类型以范围  | 备注                                                                         |
|-----------------------|------|--------|----------------------------------------------------------------------------|
| deviceid              | true | String | 由爱音乐统一分配                                                                   |
| timestamp             | true | String | 时间戳(精确到秒，格式为：<br><br>年月日时分秒（yyyyMMddHHmmss)                                |
| signature- hmacsha256 | true | String | 签名算法 Base64(HmacSHA256(timestamp+m<br><br>obile+productid+state, keyword)) |

### 接口地址

合作伙伴提供接口 url，爱音乐平台以 post 方式将订购退订消息提交到该 url 上。

### 请求方式

POST

### 前置流程

合作伙伴提供接收回调接口地址后，需交给对应的渠道经理，进行配置及放开网络后需可使用。

-
    -
        1. **请求参数说明**

| 参数名       | 必选    | 类型以范围  | 备注                                            |
|-----------|-------|--------|-----------------------------------------------|
| mobile    | true  | String | 用户手机号                                         |
| productid | true  | String | ISMP 产品 ID                                    |
| state     | true  | String | 状态 0-订购，1-退订,2-延迟计费生效<br><br>（未正式计费）,3-延迟计费退订 |
| time      | true  | String | 操作时间 格式 yyyy-MM-dd<br><br>HH:mm:ss            |
| orderNo   | false | String | 订单唯一标识                                        |

| deviceNo    | false | String | 用户设备 ID                              |
|-------------|-------|--------|--------------------------------------|
| streamingNo | false | String | 订单流水号                                |
| orderTime   | false | String | 权益生效时间<br><br>格式 yyyy-MM-dd HH:mm:ss |
| validTime   | false | String | 权益失效时间<br><br>格式 yyyy-MM-dd HH:mm:ss |
| extOrderNo  | false | String | 外部订单号                                |

-
    -
        1. **返回参数说明**

| 参数名             | 必选   | 类型及范围  | 备注          |
|-----------------|------|--------|-------------|
| **code**        | true | String | **0000 成功** |
| **description** | true | String | 描述          |

-
    -
        1. **签名算法**

public static String generateMac256Signature(String secret, String data) { byte\[\] byteHMAC = null;

try {

Mac mac = Mac.getInstance("HmacSHA256");

SecretKey secretKey = new SecretKeySpec(secret.getBytes("utf-8"), "HmacSHA256");

mac.init(secretKey);

byteHMAC = mac.doFinal(data.getBytes("utf-8"));

} catch (NoSuchAlgorithmException e) {

e.printStackTrace();

} catch (InvalidKeyException e) {

e.printStackTrace();

} catch (IllegalStateException e) {

e.printStackTrace();

} catch (UnsupportedEncodingException e) {

e.printStackTrace();

}

String result = new BASE64Encoder().encode(byteHMAC);

return result;

}

### 调用样例

<http://localhost:8080/ismpNotify.json?mobile=18978094184&productid>

\=135000000000000232931&state=0&time=2015-03-16 12:07:25

### 返回结果

**{**

**"description": "成功",**

**"code": "0000"**

**}**

### 包月产品订购关系查询

-
    -
        1. **接口功能描述**

查询爱音乐包月套餐产品列表

### 接口地址

<http://api.118100.cn/openapi/services/v3/packageservice/querypackagelist.json>

### 请求方式

GET

-
    -
        1. **前置流程**
        2. **请求参数说明**

| 参数名        | 必选    | 类型以范围  | 备注                                |
|------------|-------|--------|-----------------------------------|
| mdn        | true  | String | 手机号                               |
| package_id | false | String | 套餐 ID，可为空字符串，为空字符串时<br><br>查询所有套餐 |

-
    -
        1. **返回参数说明**

| 参数名     | 必选    | 类型及范围            | 备注                                             |
|---------|-------|------------------|------------------------------------------------|
| code    | true  | String           | 执行结果代码<br><br>0000 查询成功；0004 无相关包月套餐列表；其它 查询失败 |
| message | true  | String           | 执行结果描述                                         |
| data    | false | List&lt;JSON&gt; | 套餐列表                                           |

data 的JSON 属性字段说明

| 参数名        | 必选   | 类型及范围  | 备注    |
|------------|------|--------|-------|
| package_id | true | String | 套餐 id |

| order_time       | true  | String | 订购时间, 如 2013-09-05<br><br>00:00:00 |
|------------------|-------|--------|------------------------------------|
| unsubscribe_time | false | String | 退订时间, 如 2013-09-13<br><br>00:00:00 |
| status           | true  | int    | 套餐状态, 0 未退订, 2 退订                  |
| valid_time       | false | String | 套餐有效期，如 2060-12-31<br><br>23:59:59 |

### 接口内部名称

IPackageService_queryPackageList

### 包月产品退订

-
    -
        1. **接口功能描述**

退订指定的包月套餐

### 接口地址

<http://api.118100.cn/openapi/services/v2/package/packageservice/unsubscrib>

ebyemp.json

### 请求方式

POST

### 前置流程

无

-
    -
        1. **请求参数说明**

| 参数名        | 必选   | 类型以范围  | 备注                                             |
|------------|------|--------|------------------------------------------------|
| mdn        | true | String | 手机号                                            |
| package_id | true | String | 套餐 id,如 135000000000000003147,<br><br>表示 5 元包月 |

-
    -
        1. **返回参数说明**

| 参数名         | 必选   | 类型及范围  | 备注                 |
|-------------|------|--------|--------------------|
| res_code    | true | String | 执行结果代码<br><br>0 成功 |
| res_message | true | String | 执行结果描述             |

-
    -
        1. **接口内部名称**

IPackageService_unsubscribeByEmp

### 视频音乐盒信息查询

-
    -
        1. **接口功能描述**

可以根据音乐盒编码查询到视频彩铃音乐盒的详细信息。

### 接口地址

<https://api.118100.cn/openapi/services/v3/qkservice/box/query.json>

### 请求方式

**POST**

-
    -
        1. **前置流程**
        2. **请求参数说明**

| 参数名     | 必选 | 类型以范围  | 备注              |
|---------|----|--------|-----------------|
| boxCode | Y  | String | 视频彩铃风景铃音盒_风光剪   |
|         |    |        | 影：铃音盒           |
|         |    |        | id=910899911051 |
|         |    |        | 视频彩铃经典铃音盒_岁月如   |
|         |    |        | 歌：铃音盒           |
|         |    |        | id=910899999909 |
|         |    |        | 视频彩铃流行铃音盒_爱悦时   |
|         |    |        | 光：铃音盒           |
|         |    |        | id=910899911050 |

-
    -
        1. **返回参数说明**

| 参数名                      | 必选    | 类型及范围     | 备注                           |
|--------------------------|-------|-----------|------------------------------|
| code                     | true  | String    | 执行结果代码<br><br>0000 成功， 其它失败。 |
| message                  | true  | String    | 执行结果描述                       |
| data                     | false | JSON      | 返回的 json 数据                  |
| data 字段说明                |       |           |                              |
| musicbox_code            | true  | String    | 音乐盒编码                        |
| musicbox_name            | true  | String    | 音乐盒名称                        |
| musicbox_price           | true  | int       | 音乐盒价格(分)                     |
| musicbox_expiration_date | true  | String    | 音乐盒有效期 yyyy-MM-dd            |
| rings                    | false | JSONArray |                              |
| rings start              |       |           |                              |
| video_id                 | false | long      | 视频内容 id                      |

| video_name | false | String | 视频名称  |
|------------|-------|--------|-------|
| actor_id   | false | long   | 歌手 id |
| actor_name | false | String | 歌手名称  |
| ring_id    | false | String | 彩铃 id |
| ring_price | false | Int    | 彩铃价格  |
| rings end  |       |        |       |

-
    -
        1. **接口内部名称**

IQukuSerivce_box_query

### 视频彩铃信息查询

-
    -
        1. **接口功能描述**

可以根据铃音盒查询到的内容 id 查询具体的视频彩铃的详细信息。

### 接口地址

<http://api.118100.cn/openapi/services/v3/qkservice/video/query.json>

### 请求方式

**GET/POST**

-
    -
        1. **前置流程**
        2. **请求参数说明**

| 参数名        | 必选 | 类型以范围  | 备注              |
|------------|----|--------|-----------------|
| resourceId | Y  | String | 视频彩铃 id 或者资源 id |

-
    -
        1. **返回参数说明**

| 参数名             | 必选    | 类型及范围     | 备注                                                            |
|-----------------|-------|-----------|---------------------------------------------------------------|
| code            | true  | String    | 执行结果代码<br><br>0000 成功， 其它失败。                                  |
| message         | true  | String    | 执行结果描述                                                        |
| data            | false | JSON      | 返回的 json 数据                                                   |
| data 字段说明       |       |           |                                                               |
| video_name      | true  | String    | 视频名                                                           |
| actor_name      | true  | String    | 歌手名                                                           |
| resource_id     | true  | String    | 资源 id                                                         |
| package_id      | false | String    | 视频所属的包月产品 id                                                  |
| ring_id         | true  | String    | 彩铃 id                                                         |
| price           | true  | int       | 下载价格(单位:分)                                                    |
| expiration_date | true  | String    | 产品有效期 yyyy-mm-dd                                              |
| imageList       | false | JSONArray |                                                               |
| imageList start |       |           |                                                               |
| path            | true  | String    | 图片路径                                                          |
| format          | true  | String    | 图片格式                                                          |
| type            | true  | int       | 类型： 1:封面<br><br>2：微信分享封面                                      |
| imageList end   |       |           |                                                               |
| fileList        | false | JSONArray |                                                               |
| fileList start  |       |           |                                                               |
| type            | true  | int       | 文件类型： 1=彩铃(订购)<br><br>2=彩铃(试播)<br><br>1 为 3gp 格式，2 为 mp4 格式，若 |

|                  |       |        | 界面展示，请使用 mp4 格式为佳。                                                                                                                                                                                                                                                                                                                                         |
|------------------|-------|--------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| quality          | true  | int    | 质量：<br><br>1=9:16 竖屏（540\*960）<br><br>2=16:9 横屏（640\*360）<br><br>3=4:3 横屏（640\*480）<br><br>4=1:1 横屏（640\*640）<br><br>5=横屏（1024\*768）<br><br>6=横屏（1440\*1080）<br><br>7=竖屏（480\*640）<br><br>8=竖屏（768\*1024）<br><br>9=竖屏（1080\*1440）<br><br>10=横屏（1280\*720）<br><br>11=横屏（1920\*1080）<br><br>12=横屏（960\*540）<br><br>13=竖屏（720\*1280）<br><br>14=竖屏（1080\*1920） |
| file_path        | true  | String | 文件路径                                                                                                                                                                                                                                                                                                                                                       |
| file_format      | true  | String | 文件后缀                                                                                                                                                                                                                                                                                                                                                       |
| video_frame_rate | false | int    | 帧率                                                                                                                                                                                                                                                                                                                                                         |
| video_format     | false | String | 视频格式                                                                                                                                                                                                                                                                                                                                                       |
| video_bitrate    | false | int    | 视频比特率                                                                                                                                                                                                                                                                                                                                                      |
| audio_rate       | false | int    | 音频采样率                                                                                                                                                                                                                                                                                                                                                      |
| audio_format     | false | String | 音频格式                                                                                                                                                                                                                                                                                                                                                       |
| audio_bitrate    | false | int    | 音频比特率                                                                                                                                                                                                                                                                                                                                                      |
| channel          | false | int    | 音频声道： 1:单声道<br><br>2:立体声                                                                                                                                                                                                                                                                                                                                   |
| file_size        | true  | Long   | 文件大小(字节)                                                                                                                                                                                                                                                                                                                                                   |
| duration         | true  | Long   | 播放时长(毫秒)                                                                                                                                                                                                                                                                                                                                                   |

fileList end

-
    -
        1. **接口内部名称**

IQukuSerivce_video_query

## 附录

## 平台接口授权机制说明

为了使用爱音乐开放平台提供的 API，你需要先申请一个账号。我们会给每一个第三方合作商一个专属的账号 deviceID 和密码
devicePwd。deviceID 跟 devicePwd 的使用方式跟其他一些协议中的公钥私钥的方案相类似，你可以使用你所熟悉的编程语言将
deviceID和 devicePwd 及相关参数结合，为你发出的每个请求添加签名，以此来向爱音乐开放平台表明自己身份的合法性。

### 请求签名

爱音乐开放平台要求每一个请求都必须在 HTTP 请求的 header 对象设置如下参数：

| 参数名                   | 说明                                                                                                                                                       |
|-----------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------|
| auth-deviceid         | 爱音乐分配                                                                                                                                                    |
| auth-channelid        | 爱音乐分配                                                                                                                                                    |
| auth-timestamp        | 格式:yyyyMMddHHmmss                                                                                                                                        |
| auth-signature-method | 值：HmacSHA256                                                                                                                                             |
| auth-signature        | 认证签名 : BASE64(HmacSHA256(secret, data)) secret:爱音乐分配的秘钥；<br><br>data=auth-deviceid&auth-channelid&auth-timestamp&接口参数 1&接口参数 2&...<br><br>生成 data 的注意事项: |

<table><tbody><tr><th></th><th><ol><li>文档中明确指明不参数鉴权的参数需要忽略</li><li>参数拼接顺序必须与文档中参数列表中的顺序一致；</li><li>如参数取值为空，参数前的拼接符号&amp;不可以省略</li></ol></th></tr></tbody></table>

举例说明：

假如某调用接口请求参数说明如下：

| 参数名                | 必选    | 类型及范围  | 说明                                            |
|--------------------|-------|--------|-----------------------------------------------|
| mdn                | true  | String | 手机号                                           |
| package_i<br><br>d | false | String | 套餐 id,如 135000000000000003147,表示<br><br>5 元包月 |

相关参数值如下：

auth-deviceid=“10000000000000” auth-channelid=“1234” devicePwd=“slie234$ere”

auth-timestamp=“20160214162300” mdn=“18910001234” package_id=“135000000000000003147”

1） 若 package_id 有传值，签名中的 data 参数则为： “10000000000000&1234&20160214162300&18910001234&1350000000000

00003147”

auth-signatur 则为：“wNqfMaSBZYdl2swUvdkRvIQdWbqqVNlzGMGLLKi0axU=” 2） 若 package_id 为空，签名中的 data 参数则为：
“10000000000000&1234&20160214162300&18910001234&”

auth-signature 则为：“uNzFXOdDJhCraJPmsKDK0ssbmLyLhpDD+LT8Z0ZRP9I=”

### 加密算法

public class AuthUtils {

private static final String HMAC_SHA256 = "HmacSHA256";

public static String generateMacSignature( String secret,String data)

{

byte\[\] byteHMAC = null; try {

Mac mac = Mac.getInstance(HMAC_SHA256);

SecretKey secretKey = new SecretKeySpec(secret.getBytes(), HMAC_SHA256);

mac.init(secretKey);

byteHMAC = mac.doFinal(data.getBytes());

} catch (NoSuchAlgorithmException e) {

e.prIntStackTrace();

} catch (InvalidKeyException e) {

e.prIntStackTrace();

}

String result = new BASE64Encoder().encode(byteHMAC);

return result;

}

}

## JSSDK 接入使用指引

### 引入 JS 文件

文件很小，建议在 html 靠前位置引入

&lt;script src="<https://m.imusic.cn/paycenterv3/imusicSdk.js>" &gt;&lt;/script&gt;

### H5 计费 SDK 用户 token 获取

-
    -
        1. **接口功能描述**

（SDK 用）调用**开放平台**接口获取 SDK 使用所需要的用户 token

-
    -
        1. **接口地址** <https://api.118100.cn/openapi/services/v3/packageservice/h5portal_sdk_get_toke> n.json?mobile=

### 请求方式

GET

-
    -
        1. **前置流程**
        2. **请求参数说明**

| 参数名    | 必选   | 类型以范围  | 备注   |
|--------|------|--------|------|
| mobile | true | String | 用户号码 |

-
    -
        1. **返回参数说明**

| 参数名 | 必选 | 类型及范围 | 备注 |
|-----|----|-------|----|

| code       | true  | String | 执行结果代码<br><br>0000 成功 |
|------------|-------|--------|-----------------------|
| data       | true  | String | 执行结果描述                |
| data.token | false | String | 用户 token              |

-
    -
        1. **接口内部名称**

IPackageService_h5portal_sdk_getToken

## SDK 使用

**Ps:回调函数返回值的 code 字段用于区分成功还是失败（code: “0000”为成功，其他为失败）**

1、初始化sdk

imusicSdk.init(sdk_key,(res)=>{ console.log(res, '合作方收到 init 成功回调')

})

传入sdk_key（必传）和回调函数（非必传）

| 参数名     | 必选   | 类型以范围  | 备注                |
|---------|------|--------|-------------------|
| sdk_key | true | String | 给合作方分配的 sdk 使用key |

2、发起订购

imusicSdk.openPorTal(data, function(e) {

console.log("resPortal", e),

"0000" == e.code ? imusicSdk.toPay() : t.tip(e.message)

})

参数说明：data->请求参数（必传），cb->调用回调函数（可在回调函数中调用 toPay 方

法跳转收银台）

| 参数名                | 必选        | 类型以范围      | 备注                                                                                                                                                                                      |
|--------------------|-----------|------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| sdk_key            | true      | String     | 给合作方分配的 sdk 使用key                                                                                                                                                                       |
| **token**          | **true**  | **String** | **用户 token（通过请求开放平台获取）**                                                                                                                                                                |
| mobile             | true      | String     | 用户号码                                                                                                                                                                                    |
| product_id         | true      | String     | 产品 ID                                                                                                                                                                                   |
| remark             | false     | String     | 备注                                                                                                                                                                                      |
| external_tra de_id | false     | String     | 外部订单流水号                                                                                                                                                                                 |
| **page_color**     | **false** | **String** | **订购页色值（可联系渠道经理将页面配置为动态色值显示，默认为：#FF6D04，可选色值如下： #FF1401,#E25D44,#FF5075,#25D39E,#27CC59,#4 079E0,#384BF5,#4689D3,#D9BC80,#E03732,#11C**<br><br>**379,#304BEA,#EA654B,#EC3E50,#3D43EB）** |
| **page_mod e**     | **false** | **String** | **订购页显示模式： 1：夜间模式**<br><br>**0 或不填：正常模式**                                                                                                                                               |

返回信息：

| 参数名                      | 必选    | 类型以范<br><br>围 | 备注                   |
|--------------------------|-------|---------------|----------------------|
| code                     | true  | String        | 0000-成功 其余为失败，建议弹窗提示 |
| data                     | false | Json          |                      |
| data.res_co<br><br>de    | false | String        | 下单返回码 0-成功 其余为失败     |
| data.order_<br><br>no    | false | String        | 下单成功时返回，订单号          |
| data.res_me<br><br>ssage | false | String        | 下单返回描述               |

3、跳转收银台（需在发起订购回调后调用）

imusicSdk.toPay()

4、订购成功或者失败的回调（非必须）

window.iPayCallback = function(res){

}

| 参数名  | 必选   | 类型以范<br><br>围 | 备注                                                |
|------|------|---------------|---------------------------------------------------|
| code | true | String        | 0000-业务受理成功<br><br>0001-业务受理失败<br><br>0002-用户取消订购 |

| data             |       |        |                 |
|------------------|-------|--------|-----------------|
| data.redirectUrl | false | String | 可选回调地址（前端可自行确认） |

5、下发验证码或者提交办理的回调（非必须）

| window.iCallback = function(res){<br><br>} | | | |
| --- | | | | --- | --- | --- |
| 参数名 | 必选 | 类型以范<br><br>围 | 备注 |
| code | true | String | 0003-下发验证码<br><br>0004-成功提交办理 |
| data | | | |
| data.order_no | false | String | 订单号 |