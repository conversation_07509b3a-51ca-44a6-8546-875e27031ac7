package com.yuelan.core.validator.constraintvalidators;

import cn.hutool.core.collection.CollUtil;
import com.yuelan.core.util.LocalEnumUtils;
import com.yuelan.core.validator.constraints.EnumLimit;
import lombok.Setter;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;
import java.util.Objects;

/**
 * 校验传入值是否在枚举code范围内
 */
@Setter
public class EnumInterfaceLimitValidatorImpl implements ConstraintValidator<EnumLimit, Integer> {

    private List<Integer> codes;

    @Override
    public void initialize(EnumLimit constraintAnnotation) {
        this.codes = LocalEnumUtils.getEnumCodes(constraintAnnotation.enumInterface());
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        if (Objects.isNull(value)) {
            return true;
        }
        return CollUtil.isNotEmpty(codes) && codes.contains(value);
    }
}