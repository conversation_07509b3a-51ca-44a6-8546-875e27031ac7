package com.yuelan.core.validator.constraints;

import com.yuelan.core.validator.constraintvalidators.RegexMatchValidatorImpl;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;


/**
 * <p> 输入字符串是否满足正则匹配 </p>
 */
@Target({ANNOTATION_TYPE, METHOD, FIELD, CONSTRUCTOR, PARAMETER})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = RegexMatchValidatorImpl.class)//此处指定了注解的实现类为RegexMatchValidatorImpl
public @interface RegexMatch {

    /**
     * 添加正则表达式属性，作为校验条件
     */
    String regex() default "[0-9]*";

    String message() default "输入字符串格式不正确";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * Defines several {@code @Length} annotations on the same element.
     */
    @Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER})
    @Retention(RUNTIME)
    @Documented
    @interface List {

        RegexMatch[] value();
    }
}