package com.yuelan.core.validator.constraints;


import com.yuelan.core.validator.constraintvalidators.EnumInterfaceLimitValidatorImpl;
import com.yuelan.result.able.IEnum;
import com.yuelan.result.enums.YesOrNoEnum;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = EnumInterfaceLimitValidatorImpl.class)
public @interface EnumLimit {

    String message() default "值必须在{enumInterface}的code范围内";

    /**
     * 枚举class
     */
    Class<? extends IEnum> enumInterface() default YesOrNoEnum.class;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * Defines several {@link EnumLimit} annotations on the same element.
     *
     * @see EnumLimit
     */
    @Target({METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER})
    @Retention(RUNTIME)
    @Documented
    @interface List {

        EnumLimit[] value();
    }
}