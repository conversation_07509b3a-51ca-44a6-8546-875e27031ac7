package com.yuelan.core.util;


import com.yuelan.result.able.IEnum;
import com.yuelan.result.entity.KeyValue;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p> LocalEnumUtils枚举工具类,需要结合IEnum,操作的枚举均需要实现IEnum </p>
 */
public class LocalEnumUtils {

    /**
     * 通过枚举name查找枚举对象
     */
    public static <E extends IEnum> E findByName(Class<E> enumClass, String name) {
        if (Objects.isNull(enumClass)) {
            return null;
        }
        for (E enumeration : enumClass.getEnumConstants()) {
            if (enumeration.name().equals(name)) {
                return enumeration;
            }
        }
        return getDefault(enumClass);
    }

    /**
     * 通过枚举name查找枚举对象
     */
    public static <E extends IEnum> E findByNameWithoutDefault(Class<E> enumClass, String name) {
        if (Objects.isNull(enumClass)) {
            return null;
        }
        for (E enumeration : enumClass.getEnumConstants()) {
            if (enumeration.name().equals(name)) {
                return enumeration;
            }
        }
        return null;
    }

    /**
     * 通过枚举code查找枚举对象
     *
     * @param <E> 所有继承枚举统一接口 EnumInterface
     * @return 查找到得枚举对象, 如果查不到将返回null
     */
    public static <E extends IEnum> E findByCode(Class<E> enumClass, Integer code) {
        if (Objects.isNull(enumClass)) {
            return null;
        }
        if (Objects.isNull(code)) {
            return getDefault(enumClass);
        }
        for (E enumeration : enumClass.getEnumConstants()) {
            if (enumeration.getCode().equals(code)) {
                return enumeration;
            }
        }
        return getDefault(enumClass);
    }

    /**
     * 通过枚举code查找枚举对象,不存在不返回default
     *
     * @param <E> 所有继承枚举统一接口 EnumInterface
     * @return 查找到得枚举对象, 如果查不到将返回null
     */
    public static <E extends IEnum> E findByCodeWithoutDefault(Class<E> enumClass, Integer code) {
        if (Objects.isNull(enumClass)) {
            return null;
        }
        if (Objects.isNull(code)) {
            return null;
        }
        for (E enumeration : enumClass.getEnumConstants()) {
            if (enumeration.getCode().equals(code)) {
                return enumeration;
            }
        }
        return null;
    }

    /**
     * 通过枚举code查找枚举对象,不存在不返回default
     */
    public static <E extends IEnum> List<E> findByCodeListWithoutDefault(Class<E> enumClass, List<Integer> codeList) {
        List<E> result = new ArrayList<>();
        if (codeList == null || codeList.size() == 0) {
            return result;
        }
        for (Integer code : codeList) {
            E e = findByCodeWithoutDefault(enumClass, code);
            if (Objects.nonNull(e)) {
                result.add(e);
            }
        }
        return result;
    }

    /**
     * 根据code获取desc
     */
    public static <E extends IEnum> String getDescByCode(Class<E> enumClass, Integer code) {
        String desc = "";
        E e = findByCodeWithoutDefault(enumClass, code);
        if (null != e) {
            desc = e.getDesc();
        }
        return desc;
    }

    /**
     * 通过枚举desc获取枚举对象
     */
    public static <E extends IEnum> E findByDesc(Class<E> enumClass, String desc) {
        if (Objects.isNull(enumClass)) {
            return null;
        }
        for (E enumeration : enumClass.getEnumConstants()) {
            if (enumeration.getDesc().equals(desc)) {
                return enumeration;
            }
        }
        return getDefault(enumClass);
    }

    /**
     * 通过枚举desc获取枚举对象,不存在不返回default
     */
    public static <E extends IEnum> E findByDescWithoutDefault(Class<E> enumClass, String desc) {
        if (Objects.isNull(enumClass)) {
            return null;
        }
        for (E enumeration : enumClass.getEnumConstants()) {
            if (enumeration.getDesc().equals(desc)) {
                return enumeration;
            }
        }
        return null;
    }

    /**
     * 获取默认枚举
     */
    public static <E extends IEnum> E getDefault(Class<E> enumClass) {
        if (Objects.isNull(enumClass)) {
            return null;
        }
        E defaultEnu = null;
        for (E enumeration : enumClass.getEnumConstants()) {
            if (null != enumeration) {
                defaultEnu = (E) enumeration.getDefault();
                break;
            }
        }
        return defaultEnu;
    }


    /**
     * 获取枚举所有的code列表
     */
    public static <E extends IEnum> List<Integer> getEnumCodes(Class<E> enumClass) {
        if (Objects.isNull(enumClass)) {
            return null;
        }
        return Stream.of(enumClass.getEnumConstants()).map(IEnum::getCode).collect(Collectors.toList());
    }

    /**
     * 获取枚举列表 [key-value]
     */
    public static <E extends IEnum> List<KeyValue<Integer, String>> getEnumKeyValueList(Class<E> enumClass) {
        if (Objects.isNull(enumClass)) {
            return null;
        }
        return Stream.of(enumClass.getEnumConstants()).map(KeyValue::of).collect(Collectors.toList());
    }


    /**
     * 获取枚举 [key-value]
     */
    public static <E extends IEnum> KeyValue<Integer, String> getEnumKeyValue(Class<E> enumClass, Integer code) {
        if (Objects.isNull(enumClass)) {
            return null;
        }
        E byCodeWithoutDefault = findByCodeWithoutDefault(enumClass, code);
        return KeyValue.of(byCodeWithoutDefault);
    }


    /**
     * 判断code值是否在枚举中
     */
    public static <E extends IEnum> boolean contain(Class<E> enumClass, Integer code) {
        if (Objects.isNull(enumClass) || Objects.isNull(code)) {
            return false;
        }
        for (E enumeration : enumClass.getEnumConstants()) {
            if (enumeration.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断给定的code值列表是否在枚举中
     */
    public static <E extends IEnum> boolean containAllCode(Class<E> enumClass, List<Integer> codeList) {
        if (codeList == null || codeList.size() == 0 || Objects.isNull(enumClass)) {
            return false;
        }
        for (Integer code : codeList) {
            if (!contain(enumClass, code)) {
                return false;
            }
        }
        return true;
    }

}
