package com.yuelan.core.util;


import cn.hutool.core.util.StrUtil;

/**
 * <p> 字符串操作 </p>
 */
public class LocalStringUtils extends StrUtil {

    /**
     * 全角转半角
     * <p>
     * 1.全角：指一个字符占用两个标准字符位置。汉字字符和规定了全角的英文字符及国标GB2312-80中的图形符号和特殊字符都是全角字符。一般的系统命令是不用全角字符的，只是在作文字处理时才会使用全角字符。
     * 2.半角：指一字符占用一个标准的字符位置。通常的英文字母、数字键、符号键都是半角的，半角的显示内码都是一个字节。在系统内部，以上三种字符是作为基本代码处理的，所以用户输入命令和参数时一般都使用半角。
     * <p>
     * 范围（无空格）： 全角字符unicode编码从65281~65374（十六进制0xFF01 ~ 0xFF5E） 半角字符unicode编码从33~126（十六进制0x21~ 0x7E）
     * <p>
     * 特例：空格比较特殊，全角为12288（0x3000），半角为 32（0x20） 注： 1. 中文文字永远是全角，只有英文字母、数字键、符号键才有全角半角的概念,一个字母或数字占一个汉字的位置叫全角，占半个汉字的位置叫半角。
     * 2. 引号在中英文、全半角情况下是不同的。
     */
    public static String full2Half(String str) {
        if (isEmpty(str)) {
            return str;
        }
        char c[] = str.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == '\u3000') {
                c[i] = ' ';
            } else if (c[i] > '\uFF00' && c[i] < '\uFF5F') {
                c[i] = (char) (c[i] - 65248);
            }
        }
        return new String(c);
    }

    /**
     * 半角转全角
     */
    public static String half2Full(String str) {
        if (isEmpty(str)) {
            return str;
        }
        char c[] = str.toCharArray();
        for (int i = 0; i < c.length; i++) {
            if (c[i] == ' ') {
                c[i] = '\u3000';
            } else if (c[i] < '\177') {
                c[i] = (char) (c[i] + 65248);
            }
        }
        return new String(c);
    }

    /**
     * 去除空白字符，并全角转半角
     */
    public static String trimFull2Half(String str) {
        return full2Half(StrUtil.trim(str));
    }
}
