package com.yuelan.core.util;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.net.Ipv4Util;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.system.SystemUtil;
import com.yuelan.result.enums.error.BaseErrorCodeEnum;
import com.yuelan.result.exception.BizException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.Date;

/**
 * <p>= 业务单号生成器 </p>
 * <p>
 * &#064;Deprecated  docker 分部署部署有bug，docker环境有bug
 */
@Deprecated
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class LocalBizNoUtils {

    private static long WORKER_ID;
    private static long DATACENTER_ID;
    private static Snowflake snowflake;

    static {
        WORKER_ID = getWorkerId();
        DATACENTER_ID = getDataCenterId();
    }

    /**
     * 获取单例的Twitter的Snowflake 算法生成器对象
     */
    private static Snowflake getSnowflake() {
        if (snowflake != null) {
            return snowflake;
        }
        snowflake = IdUtil.getSnowflake(WORKER_ID, DATACENTER_ID);
        return snowflake;
    }

    /**
     * 32位工作中心节点
     */
    private static Long getWorkerId() {
        String localhostStr = NetUtil.getLocalhostStr();
        if (StringUtils.isBlank(localhostStr)) {
            return RandomUtil.randomLong(0, 32);
        }
        long wkid = Ipv4Util.ipv4ToLong(localhostStr) % 32;
        return (wkid > 0 ? wkid : -wkid);
    }

    /**
     * 32位数据中心节点
     */
    private static Long getDataCenterId() {
        long currentPID = SystemUtil.getCurrentPID();
        return currentPID % 32;
    }


    /**
     * 获取下一个ID
     */
    private static Long getNextId() {
        Snowflake snowflake = getSnowflake();
        return snowflake.nextId();
    }


    /**
     * 生成单号
     */
    public static String genBillNo(String prefix) {
        return genBillNo(prefix, new Date());
    }

    /**
     * 生成单号
     */
    public static String genBillNo(String prefix, Date date) {
        if (StrUtil.isEmpty(prefix)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        return StringUtils.join(prefix, DateUtil.format(date, "yyMM"), getNextId());
    }

    /**
     * 生成单号
     */
    public static String genBillNoHex(String prefix) {
        if (StrUtil.isEmpty(prefix)) {
            throw BizException.create(BaseErrorCodeEnum.PARAMS_ERROR);
        }
        LocalDate now = LocalDate.now();
        Long nextId = getNextId();
        return StringUtils.join(prefix, Integer.toHexString(now.getYear()), Long.toHexString(nextId));
    }
}
