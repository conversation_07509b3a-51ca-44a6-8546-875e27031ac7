package com.yuelan.core.captcha;

import cn.hutool.captcha.generator.CodeGenerator;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <p> 图片验证码生成器 </p>
 *
 * <AUTHOR>
 * @date 2023/8/9
 */
public class CaptchaGenerator implements CodeGenerator {
    private static final long serialVersionUID = -3059225077038551269L;
    /**
     * 基础字符集合，用于随机获取字符串的字符集合
     */
    private static final String BASE_CHAR_NUMBER = "abcdefhkmnstwxyABCDEFGHKMNRSTWXY1234567890";
    /**
     * 验证码长度
     */
    private final int length;

    /**
     * 构造
     */
    public CaptchaGenerator() {
        this(4);
    }

    /**
     * 构造
     *
     * @param length – 生成验证码长度
     */
    public CaptchaGenerator(int length) {
        this.length = length;
    }


    @Override
    public String generate() {
        return RandomUtil.randomString(BASE_CHAR_NUMBER, this.length);
    }

    @Override
    public boolean verify(String code, String userInputCode) {
        if (StrUtil.isNotBlank(userInputCode)) {
            return StrUtil.equalsIgnoreCase(code, userInputCode);
        }
        return false;
    }
}
