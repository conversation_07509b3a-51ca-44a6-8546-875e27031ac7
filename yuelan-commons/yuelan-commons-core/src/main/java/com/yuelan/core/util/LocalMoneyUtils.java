package com.yuelan.core.util;


import com.yuelan.result.enums.MoneyUnit;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p> 金钱转换 </p>
 */
public class LocalMoneyUtils {

    public static BigDecimal fen2yuan(Long money) {
        if (money == null) {
            return null;
        }
        return toYuan(money, MoneyUnit.FEN, 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal li2yuan(Long money) {
        if (money == null) {
            return null;
        }
        return toYuan(money, MoneyUnit.LI, 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal toYuan(Long money, MoneyUnit unit, int scale, RoundingMode roundingMode) {
        if (money == null) {
            return null;
        }
        long rate = MoneyUnit.YUAN.getRate() / unit.getRate();
        return BigDecimal.valueOf(money).divide(BigDecimal.valueOf(rate), scale, roundingMode);
    }

    public static Long yun2Fen(BigDecimal money) {
        if (money == null) {
            return null;
        }
        long rate = MoneyUnit.YUAN.getRate() / MoneyUnit.FEN.getRate();
        return money.multiply(BigDecimal.valueOf(rate)).longValue();
    }
}
