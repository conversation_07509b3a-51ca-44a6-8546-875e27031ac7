package com.yuelan.core.util;


import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TemporalAccessorUtil;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.TemporalAccessor;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * <p> 时间工具类 </p>
 */
public class LocalDateUtils extends DateUtil {

    /**
     * 获取某天的结束时间(不包括毫秒值）
     */
    public static DateTime endOfDayWithoutMillisecond(Date date) {
        return new DateTime(endOfDayWithoutMillisecond(calendar(date)));
    }

    /**
     * 获取某天的结束时间(不包括毫秒值）
     */
    public static Calendar endOfDayWithoutMillisecond(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    /**
     * LocalDateTime To Date
     */
    public static Date toDate(LocalDateTime localDateTime) {
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    private static String getLocalDateString(String dateString) {
        return dateString.length() > 19 ? dateString.substring(0, 19) : dateString;
    }

    /**
     * 当前时间-毫秒级别
     */
    public static String nowMillis() {
        return format(new DateTime(), DatePattern.PURE_DATETIME_MS_PATTERN);
    }

    /**
     * <p>Long类型时间转为{@link DateTime}</p>
     * 只支持毫秒级别时间戳，如果需要秒级别时间戳，请自行×1000
     */
    public static DateTime date(Long date) {
        if (date == null) {
            return null;
        }
        return DateUtil.date(date);
    }

    /**
     * 获取date的时间戳，毫秒
     */
    public static Long getTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.getTime();
    }

    /**
     * 截取秒级别时间，即忽略毫秒部分
     *
     * @param date 日期
     * @return {@link DateTime}
     */
    public static DateTime truncateOfSecond(Date date) {
        if (date == null) {
            return null;
        }
        return beginOfSecond(date);
    }

    /**
     * {@link TemporalAccessor}转换为 时间戳（从1970-01-01T00:00:00Z开始的毫秒数）
     */
    public static Long toEpochMilli(TemporalAccessor temporalAccessor) {
        if (Objects.isNull(temporalAccessor)) {
            return null;
        }
        return TemporalAccessorUtil.toEpochMilli(temporalAccessor);
    }

}
