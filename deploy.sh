#!/bin/bash
set -m

projectName=$1
env=$2
debug_port=$3
echo "projectName = $projectName"
echo "env = $env"
echo "debug_port = $debug_port"

echo "获取服务进程号了"
ISPROCESS=$(ps -efww | grep -i ${projectName} | grep java | awk {'print $2'} | wc -l)
TPROCESS=$(ps -efww | grep -i ${projectName} | grep java | awk {'print $2'})
echo "服务进程号:[$TPROCESS]"

if [ $ISPROCESS = 0 ]; then
  echo "服务未启动"
else
  kill -2 $TPROCESS
  echo "开始停止服务了,请等待30秒"
  sleep 30
fi

ISPROCESS=$(ps -efww | grep -i ${projectName} | grep java | awk {'print $2'} | wc -l)
if [ $ISPROCESS = 0 ]; then
  echo "服务已停止"
else
  kill -9 $TPROCESS
  echo "服务已强制停止"
fi

#项目路径
projectDir=$(pwd)
if [ ! -d "${projectDir}/logs" ]; then
  mkdir ${projectDir}/logs
fi

echo "${projectName} 开始启动了"

if [[ "${env}" == "real" ]]; then
  JAVA_OPTS="-server -Xms1g -Xmx1g -Xmn512m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m -Djava.awt.headless=true -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF8"
else
  JAVA_OPTS="-server -Xms1g -Xmx1g -Xmn512m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=320m -Djava.awt.headless=true -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF8"
fi

DEBUG_OUT=
if [[ -n ${debug_port} ]]; then
  DEBUG_OUT=" -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address="${debug_port}
fi
ENV_OUT="-Dspring.profiles.active=${env}"
JAVA_OUT=${projectDir}/logs/start.log

nohup java ${JAVA_OPTS} ${DEBUG_OUT} ${ENV_OUT} -jar ${projectDir}/${projectName}.jar >${JAVA_OUT} 2>&1 &

echo "${projectName} 部署结束"
echo ${JAVA_OUT}
